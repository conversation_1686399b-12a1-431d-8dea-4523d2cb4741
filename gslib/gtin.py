# encoding=utf8

import re
import logging
from typing import Optional

logger = logging.getLogger(__file__)


# https://www.docin.com/p-3153161390.html?docfrom=rrela


def add_mask_ean13(s: str) -> Optional[str]:
    if not re.match(r'\d{12}', s):
        logger.warning(f'bad s={s}')
        return None
    tot = 0
    for idx, ch in enumerate(s):
        if idx % 2 == 0:
            tot += int(ch)
        else:
            tot += 3 * int(ch)
    mask = tot % 10
    if mask > 0:
        mask = 10 - mask
    return '0' + s + str(mask)


def get_firm_sample_ean13(firm_ean_id: str, offset: int = 0) -> Optional[str]:
    offset_s = f'{offset:0{(12 - len(firm_ean_id))}d}'
    return add_mask_ean13(firm_ean_id + offset_s)


if __name__ == '__main__':
    # ean_id = '8484550'
    # for i in range(212, 230):
    #     ret = get_firm_sample_ean13(firm_ean_id=ean_id, offset=i)
    #     print(f'{i} {ret}')

    print(add_mask_ean13('697431433999'))
