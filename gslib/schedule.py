import time
import requests
import logging
from threading import Lock
logger = logging.getLogger(__name__)

immediately_gs_lock = Lock()
immediately_gs_last_ts = 0


def immediately_gs(credit_code: str, reason: str, annual_report_enable=False, interval_sec=1.0):
    # 杜波接口  立即抓取国家工商和地方工商
    # annual_report_enable 最新年报
    global immediately_gs_last_ts

    with immediately_gs_lock:
        diff_sec = time.time() - immediately_gs_last_ts
        if diff_sec < interval_sec:
            time.sleep(interval_sec-diff_sec)
        immediately_gs_last_ts = time.time()
    dims = []
    if annual_report_enable:
        dims.append('annualreport2')
    ret = requests.post(
        url='http://172.24.114.24/gsxt/v2/update/merge',
        json={
            "companySign": credit_code,
            "record": reason,
            "channelType": 2,
            "channel": {
                "channelGroup": reason,
                "credit_code": credit_code,
                "change_dims": dims,
            }
        },
        verify=False,
        timeout=3,
    )
    logger.info(f'{credit_code} {reason} {ret.status_code} {ret.text}')


if __name__ == '__main__':
    from libs.log2 import setup_logger
    logger = setup_logger()
    for credit_code in ['91310000MADQ75M227',]:
        immediately_gs(credit_code, reason='user_update', annual_report_enable=False)

