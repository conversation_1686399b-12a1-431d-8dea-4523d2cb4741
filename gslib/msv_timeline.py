# encoding=utf8
import json
import logging
from datetime import timedelta
from libs.dt import to_datetime
from libs.req2 import URLPat, ReqManager
logger = logging.getLogger(__name__)

req_mgr = ReqManager(
    use_proxy=False,
    pats=[
        URLPat(name='read_list_version', pat='http://msv-gsdata.jindidata.com/read_list_version', **{'Content-Type': 'application/json'}),
    ]
)


# 主要人员 ABC -> BC时 不能使用非human接口，msv_first不对
def msv_query_human(
        table_name: str,
        key_name: str,
        key_value,
        source: str = None,
) -> dict:
    params = {
        "table_name": table_name,
        f"{key_name}": key_value,
        "human": True,
    }
    if source:
        params['source'] = source

    resp = req_mgr.request(pat='read_list_version', **params)
    if not resp:
        logger.warning(f'msv_query_human fail after retry params={params}')
        return {}
    logger.debug(f'resp={resp.status_code} {resp.text}')

    try:
        d = resp.json()
        errcode, errmsg = d.get('errcode'), d.get('errcode')
        if errcode != 0:
            logger.warning(f'msv_query_human bad response params={params} errcode={errcode} errmsg={errmsg}')
            return {}

    except json.JSONDecodeError:
        logger.warning(f'msv_query_human error json d={resp.text}')
        return {}

    data = d.get('data')
    if not isinstance(data, dict):
        logger.warning('msv_query_human data should be dict')
        return {}
    return data


def msv_src_inv(msv_src):
    return msv_src if msv_src != 'XA' else 'gsxt_page'


# 照面信息 相同核准日期取最新的
# 股东主要人员 相同照面信息 取维度数据最老的
def msv_timeline(
        key_name: str,
        key_value,
        msv_source: str = None,
        enable_company_investor: bool = False,
        enable_company_staff: bool = False,
        cmp_appr_date: bool = False,
) -> dict:
    result = {}
    company_base = msv_query_human(table_name='company_base_info', key_name=key_name, key_value=key_value, source=msv_source)
    company_staff, company_investor = {}, {}
    if enable_company_staff:
        company_staff = msv_query_human(table_name='company_staff', key_name=key_name, key_value=key_value, source=msv_source)
    if enable_company_investor:
        company_investor = msv_query_human(table_name='company_investor', key_name=key_name, key_value=key_value, source=msv_src_inv(msv_source))

    result_item = []  # each of company_base
    for msv_source_i, company_base_versions in company_base.items():
        if msv_source and msv_source_i != msv_source:
            continue
        logger.info(f'base len={len(company_base_versions)}')

        company_staff_item, idx_staff, staff_msv_first, staff_data = [], 0, None, None
        if msv_source_i in company_staff:
            company_staff_item = company_staff[msv_source_i]
            logger.info(f'staff len={len(company_staff_item)}')

        company_investor_item, idx_investor, investor_msv_first, investor_data = [], 0, None, None
        if msv_src_inv(msv_source_i) in company_investor:
            company_investor_item = company_investor[msv_src_inv(msv_source_i)]
            logger.info(f'investor len={len(company_investor_item)}')

        last_appr_date = None
        for company_base_item in company_base_versions:
            msv_first = to_datetime(company_base_item['info']['msv_first'])
            msv_last = to_datetime(company_base_item['info']['msv_last'])
            item_dic = company_base_item['data'][0]
            appr_date = item_dic['approved_time']
            if cmp_appr_date and last_appr_date == appr_date:
                continue
            last_appr_date = appr_date

            while idx_staff < len(company_staff_item):
                staff_msv_first = to_datetime(company_staff_item[idx_staff]['info']['msv_first'])
                staff_msv_last = to_datetime(company_staff_item[idx_staff]['info']['msv_last'])
                staff_data = company_staff_item[idx_staff]['data']
                logger.info(f'at staff base={msv_first} staff={staff_msv_first}')
                # 当前的staff不是在该base周期抓的
                if msv_first + timedelta(seconds=5) > staff_msv_last:
                    idx_staff -= 1
                    break
                idx_staff += 1

            while idx_investor < len(company_investor_item):
                investor_msv_first = to_datetime(company_investor_item[idx_investor]['info']['msv_first'])
                investor_msv_last = to_datetime(company_investor_item[idx_investor]['info']['msv_last'])
                investor_data = company_investor_item[idx_investor]['data']
                logger.info(f'at investor base={msv_first} investor={investor_msv_first}')
                if msv_first + timedelta(seconds=5) > investor_msv_last:
                    idx_investor -= 1
                    break
                idx_investor += 1

            if enable_company_staff:
                logger.info(f'STAFF {msv_first} {msv_last} {appr_date} {idx_staff} {staff_msv_first}')
            if enable_company_investor:
                logger.info(f'INVESTOR {msv_first} {msv_last} {appr_date} {idx_investor} {investor_msv_first}')

            result_item.append((
                msv_first.timestamp(),
                msv_last.timestamp(),
                appr_date,
                item_dic,
                staff_data,
                investor_data,
            ))
        result[msv_source_i] = result_item
        if idx_staff == len(company_staff_item):
            if idx_staff > 0:
                logger.info(f'staff fail to match base')
        elif idx_staff != len(company_staff_item) - 1:
            logger.info(f'staff more than base {len(company_staff_item) - 1 - idx_staff}  {key_name}:{key_value}')

        if idx_investor == len(company_investor_item):
            if idx_investor > 0:
                logger.info(f'investor fail to match base')
        elif idx_investor != len(company_investor_item) - 1:
            logger.info(f'investor more than base {len(company_investor_item) - 1 - idx_investor} {key_name}:{key_value}')

    return result


if __name__ == '__main__':
    from libs.log2 import setup_logger
    logger = setup_logger()
    # 3963535 北京金堤科技有限公司
    ret = msv_timeline(
        key_name='cid',
        key_value=3963535,
        msv_source='XA',
        enable_company_staff=True,
        enable_company_investor=False,
        cmp_appr_date=True,
    )
    logger.info(ret)
