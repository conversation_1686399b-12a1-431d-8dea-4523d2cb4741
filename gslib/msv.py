# encoding=utf8
import json
from typing import Optional, List, Tuple
import requests
import time
import logging
from libs.dt import to_datetime

logger = logging.getLogger(__name__)


def msv_query_base_info(cid: int, table_name='company_base_info', source='XA') -> list:
    try:
        params = {
            "table_name": table_name,
            "cid": cid,
            "source": source,
        }
        response = requests.post(
            url='http://msv-gsdata.jindidata.com/read_list_version',
            headers={
                'Content-Type': 'application/json',
            },
            json=params,
            timeout=8.0,
        )
    except requests.exceptions.ReadTimeout:
        logger.warning('get msv api timeout')
        return []
    if response.status_code != 200:
        logger.warning('bad response=%s %s params=%s', response.status_code, response.text, params)
        time.sleep(0.5)
        return []
    try:
        d = response.json()
        if d.get('errcode', None) != 0:
            logger.warning('bad response=%s %s params=%s', response.text, d, params)
            return []
    except:
        logger.warning(f'ERROR JSON d={response.text}')
        return []
    data = d.get('data', None)
    if not isinstance(data, dict):
        logger.warning('bad response=%s params=%s', response.text, params)
        time.sleep(0.5)
        return []

    source_data = data.get(source, None)
    if source_data is None:
        return []

    if not isinstance(source_data, list):
        logger.warning('bad response=%s params=%s', response.text, params)
        time.sleep(0.5)
        return []

    return list(x[0] for x in source_data)


def msv_query_list_dim(cid: int, table_name, ts=-1, source='XA') -> List[dict]:
    try:
        params = {
            "table_name": table_name,
            "cid": cid,
            "source": source,
        }
        if ts == -1:
            params[ts] = -1

        response = requests.post(
            url='http://msv-gsdata.jindidata.com/read_list_version',
            headers={
                'Content-Type': 'application/json',
            },
            json=params,
            timeout=8.0,
        )
    except requests.exceptions.ReadTimeout:
        logger.warning('get msv api timeout')
        return []
    if response.status_code != 200:
        logger.warning(f'bad response={response.status_code} {response.text} params={params}')
        time.sleep(0.5)
        return []

    d = response.json()
    if d.get('errcode', None) != 0:
        logger.warning(f'bad response={response.text} {d} params={params}')
        return []

    data = d.get('data', None)
    if not isinstance(data, dict):
        logger.warning(f'bad response={response.text} params={params}')
        time.sleep(0.5)
        return []

    source_data = data.get(source, None)
    if source_data is None:
        return []

    if not isinstance(source_data, list):
        logger.warning(f'bad response={response.text} params={params}')
        time.sleep(0.5)
        return []

    if len(source_data) == 0:
        return []

    if ts != -1:
        return source_data

    source_data_first = source_data[0]
    if not isinstance(source_data_first, list):
        logger.warning(f'bad response={response.text} params={params}')
        time.sleep(0.5)
        return []

    return source_data_first


# 主要人员 ABC -> BC时 不能使用上面那个接口，msv_first不对
# 返回 list[ts1, ts2, dict]
def msv_query_list_dim_human(cid: int, table_name, source='XA') -> List[Tuple[int, int, list]]:
    try:
        params = {
            "table_name": table_name,
            "cid": cid,
            "source": source,
            "human": True,
        }

        response = requests.post(
            url='http://msv-gsdata.jindidata.com/read_list_version',
            headers={
                'Content-Type': 'application/json',
            },
            json=params,
            timeout=8.0,
        )
    except requests.exceptions.ReadTimeout:
        logger.warning('get msv api timeout')
        return []
    if response.status_code != 200:
        logger.warning(f'bad response={response.status_code} {response.text} params={params}')
        time.sleep(0.5)
        return []

    try:
        d = response.json()
        if d.get('errcode', None) != 0:
            logger.warning(f'bad response={response.text} {d} params={params}')
            return []
    except:
        logger.warning(f'ERROR JSON d={response.text}')
        return []
    data = d.get('data', None)
    if not isinstance(data, dict):
        logger.warning(f'bad response={response.text} params={params}')
        time.sleep(0.5)
        return []

    source_data = data.get(source, None)
    if source_data is None:
        return []

    if not isinstance(source_data, list):
        logger.warning(f'bad response={response.text} params={params}')
        time.sleep(0.5)
        return []

    if len(source_data) == 0:
        return []

    source_data_ret = []
    for source_data_item in source_data:
        data_list = source_data_item['data']
        ts_from = int(to_datetime(source_data_item['info']['msv_first']).timestamp())
        ts_to = int(to_datetime(source_data_item['info']['msv_last']).timestamp())
        source_data_ret.append((ts_from, ts_to, data_list))

    return source_data_ret


if __name__ == '__main__':
    from libs.log2 import setup_logger
    logger = setup_logger()
    ret = msv_query_base_info(cid=*********, source='XA')
    logger.info(ret)
    # ret = msv_query_list_dim(cid=46500, source='gsxt_page', table_name='company_investor', ts=0)
    # open('output.json', 'w').write(json.dumps(ret, ensure_ascii=False))
