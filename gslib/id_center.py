# encoding=utf8

import re
from typing import Tuple, Optional
import requests
import time
from datetime import date
import logging
from libs.dt import date2str
from gslib.gs_enum import EntityType

logger = logging.getLogger(__name__)


# https://b3sh6jivuw.feishu.cn/docx/F84Vdf7s0ocBPpxokgqcqxHjnud
# id中心查询接口
# param格式错误返回 None， 0
# 查询不到返回 EntityType.UNSET , 0
def id_center_query(
        name: str = None,
        credit_no: str = None,
        reg_number: str = None,
        source: str = 'pygs',
        use_graph_id: bool = False,
        all_match: bool = False,
        type: EntityType = EntityType.UNSET,
        allow_empty_company: bool = False,  # 是否允许写公司数据
        **kwargs,
) -> Tuple[Optional[EntityType], int]:
    if name == '额尔敦巴根':
        return EntityType.HUMAN, 2275885815

    # entity_type, eid = EntityType.UNSET, 0
    params = dict(source=source, config=dict())
    if isinstance(name, str):
        params.update(name=name)
    if isinstance(credit_no, str) and len(credit_no) == 18:
        params.update(unique_code=credit_no)
    if isinstance(reg_number, str) and len(reg_number) == 15:
        params.update(reg_num=reg_number)
    if all_match:
        params['config'].update(all_match=True)
    if type != EntityType.UNSET:
        params.update(type=type.value)
    if allow_empty_company:
        params['config'].update(allow_empty_company=allow_empty_company)
    if 'event_date' in kwargs:
        if isinstance(kwargs['event_date'], date):
            params.update(event_date=date2str(kwargs['event_date']))

    if 'hk_num' in kwargs:
        # if isinstance(kwargs['hk_num'], str) and re.fullmatch(r'(F?)(\d{7})', kwargs['hk_num']):
        if isinstance(kwargs['hk_num'], str):
            params.update(code_type=2)
            params.update(unique_code=kwargs['hk_num'])

    if all(x not in params for x in ['name', 'unique_code', 'reg_num']):
        logger.warning('bad query kwargs=%s', params)
        return None, 0

    if params.get('type') == int(EntityType.ORG) and isinstance(name, str) and len(name) < 4 and credit_no is None:
        logger.warning('forbidden query short-name ge ti without credit_no kwargs=%s', kwargs)
        return None, 0
    try:
        response = requests.post(
            url='http://idcenter-gsdata.jindidata.com/api/id-center/entity/v1',
            headers={
                'Authentication': 'kTVgF9CrgWVgEiJjuMjpDpqfo97ztYmD',
                'Content-Type': 'application/json',
            },
            json=params,
            timeout=30.0,
        )
    except (requests.exceptions.ReadTimeout, requests.exceptions.ConnectTimeout):
        logger.warning(f'get id_center api timeout {params}')
        return None, 0
    if response.status_code != 200:
        logger.warning(f'bad response={response.status_code} {response.text} {params}')
        time.sleep(0.5)
        return None, 0

    d = response.json()
    if d.get('code', None) != 200:
        logger.warning(f'id center refused, input illegal {response.text} {params}')
        return EntityType.TYPE3, 0
    # logger.info('d=%s', d)
    entity_type = EntityType(d.get('data', {}).get('best_id_type', 0))
    eid = d.get('data', {}).get('best_id', 0) if not use_graph_id else d.get('data', {}).get('best_gid', 0)
    logger.debug('ok params=%s ret=%s,%s', params, entity_type, eid)
    return entity_type, eid


def id_center_add_name_index(cid: int, name: str):
    try:
        response = requests.post(
            url='http://idcenter-gsdata.jindidata.com/gsxt/idCenter/addNameIndex',
            headers={
                'Authentication': 'kTVgF9CrgWVgEiJjuMjpDpqfo97ztYmD',
                'Content-Type': 'application/json',
            },
            json={
                'name': name,
                'cid': cid,
            },
            timeout=3.0,
        )
        text = response.text.replace('\n', ' ')
        logger.info(f'response={response.status_code} {text} {cid} {name}')
    except (requests.exceptions.ReadTimeout, requests.exceptions.ConnectTimeout):
        logger.warning('id_center api timeout')
        time.sleep(1)


if __name__ == '__main__':
    # from libs.log import setup_logger

    # logger = setup_logger()

    ret = id_center_query(reg_number='130181600948387')
    print(ret)

    # def debug_me(**kwargs):
    #     ret = id_center_query(**kwargs)
    #     logger.info(f'kwargs={kwargs} ret={ret}')

    # 张伟 2238986105 1994-06-14 *************** 92370481MA3KNWY50B
    # ret = id_center_query(credit_no='53370000MJD6757485', use_graph_id=True)
    # ret = id_center_query(reg_number='230607000191010')  # 注册号
    # id_center_query(credit_no='92370481MA3KNWY50B', reg_number='***************', all_match=True)
    # ret = id_center_query(name='张伟111', type=EntityType.HUMAN, use_graph_id=True)
    # ret = id_center_query(name='张飞', type=EntityType.ORG)
    # id_center_query(name='北京万源通科技有限公司', type=EntityType.ORG, event_date=date(year=2018, month=1, day=1))
    # debug_me(name='中国邮政集团有限公司', hk_num='911000000000192463')

    # debug_me(credit_no='92120111MA05TX6Y61')

    # debug_me(name='Hong Kong Vision Care Company Limited', type=EntityType.UNSET, allow_empty_company=False)
    # debug_me(name='美国沙曼丽萌宠国际有限公司', hk_num='76179732', allow_empty_company=True, type=EntityType.ORG)

    # id_center_add_name_index(name='阿克拉发来的撒拉胯', cid=2522692556)

#     s = """
#     北京钱袋网保险代理有限责任公司
# 杭州淘天供应链有限公司
# 佛山一达通企业服务有限公司
# 杭州源牛电子商务有限公司
# 广西一达通企业服务有限公司
# 杭州源猫电子商务有限公司
# 上海别样红信息技术有限公司
# 成都银隆畅达科技有限公司
# 青岛匠福装饰工程有限公司
#     """
#     for a in s.split():
#         print(id_center_query(name=a)[1])


