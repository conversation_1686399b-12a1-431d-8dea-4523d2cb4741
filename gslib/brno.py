# encoding=utf8

import re
import logging
from typing import Optional

logger = logging.getLogger(__file__)


# refer https://yauhingyiu.blogspot.com/2017/05/check-digit_66.html
def _add_mask_brno(s: str) -> Optional[str]:
    if not re.fullmatch(r'\d{7}', s):
        logger.warning(f'bad s={s}')
        return None
    tot = int(s[0]) * 2 + int(s[1]) * 9 + int(s[2]) * 8 + int(s[3]) * 7 + int(s[4]) * 4 + int(s[5]) * 3 + int(s[6]) * 2
    mask = tot % 10
    if mask > 0:
        mask = 10 - mask
    return s + str(mask)


def get_brno(brno: str, offset: int = 0) -> Optional[str]:
    if not re.fullmatch(r'\d{8}', brno):
        return brno
    return _add_mask_brno(f'{int(brno[:-1]) + offset:07d}')


def fetch_brno(brno: str) -> Optional[str]:
    return _add_mask_brno(brno)


if __name__ == '__main__':
    start = 0
    for i in range(start, start+100):
        ret = get_brno(brno='77712955', offset=i)
        print(f'{i} {ret}')

    # print(fetch_brno('76097140'))
