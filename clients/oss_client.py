# encoding=utf8

import oss2
import logging
from libs.dt import to_datetime
from libs.env import get_env_prop
from public_utils_configs.util.TycSecureUtil import TycSecureUtil
logger = logging.getLogger(__name__)


class OSSClient:
    def __init__(self, bucket_name):
        app_name = get_env_prop('oss.tyc_dyn.app_name')
        env = get_env_prop('oss.tyc_dyn.env')
        ak = get_env_prop('oss.tyc_dyn.ak')
        sk = get_env_prop('oss.tyc_dyn.sk')
        sec = TycSecureUtil(app_name=app_name, env=env, ak=ak, sk=sk)
        access_key_id, secret_access_key = sec.decrypt_obs()
        self.access_key_id = access_key_id
        self.access_key_secret = secret_access_key
        self.oss_end_point = get_env_prop('oss.tyc_dyn.end_point')
        self.oss_bucket_name = bucket_name
        self.auth = oss2.Auth(self.access_key_id, self.access_key_secret)
        self.bucket = oss2.Bucket(self.auth, self.oss_end_point, self.oss_bucket_name)

    def get_file_content_by_name_from_oss(self, key):
        if key is None or len(key) == 0:
            return None
        if not self.does_object_exist(key):
            return ""
        content = self.bucket.get_object(key)
        res = content.read()
        print(key, to_datetime(content.last_modified))
        if content.client_crc != content.server_crc:
            logger.info("The CRC checksum between client and server is inconsistent!")
        if res:
            return res.decode('utf-8')
        return None

    def put_content_to_oss(self, key, content):
        result = self.bucket.put_object(key, content)
        if result:
            logger.info(f"oss_put_status: {result.status}")
            logger.info(f"oss_request_id: {result.request_id}")
            logger.info(f"oss_etag {result.etag}")
            return result.status
        return None

    def does_object_exist(self, file_path):
        flag = False
        exist = self.bucket.object_exists(file_path)
        if exist:
            flag = True
        return flag

    # file_name 为需要保留的文件名
    def delete_file_on_oss(self, path, file_name):
        keys_obj = oss2.ObjectIterator(self.bucket, prefix=path)
        keys = []
        for obj in keys_obj:
            print("file:", obj.key)
            keys.append(obj.key)
        # 没有文件需要删除
        if not keys or len(keys) == 0:
            return True
        # 只剩下指定不删除的文件，直接返回
        has_special_file = file_name and file_name != '' and self.does_object_exist(path + file_name)
        if len(keys) == 1 and has_special_file:
            return True
        # 若需要排除某个特定的文件，或文件数量多于1000个（理论上每次最多获取100条，不会超过1000条），则一个一个的删除
        # 否则，批量删除
        if has_special_file or len(keys) >= 1000:
            for key in keys:
                if has_special_file and key != (path + file_name):
                    self.bucket.delete_object(key)
        else:
            key_list = []
            for key in keys:
                key_list.append(key)
            result = self.bucket.batch_delete_objects(key_list)
            print("delete_keys", "\n".join(result.deleted_keys))
        # 判断是否已删除完成，若可能没有删除完，则递归删除
        # 这里100，表明可能本次获取没有获取所有文件，需要获取剩余的keys并删除
        # 采用递归删除的方式
        if len(keys) >= 100:
            return self.delete_file_on_oss(path, file_name)
        return True

    # 删除某个文件
    def delete_object_from_oss(self, key):
        if key is None or len(key) == 0:
            return True
        self.bucket.delete_object(key)
        return False

    # 通过传入的路径得到其下的文件list
    def get_all_keys_from_oss(self, path):
        if path is None or len(path) == 0:
            return None
        result_list = []
        list_object = oss2.ObjectIterator(self.bucket, prefix=path)
        for item in list_object:
            result_list.append(item.key)
        return result_list

    # 拷贝文件
    def copy_object(self, source_key, destination_key):
        self.bucket.copy_object(self.oss_bucket_name, source_key, destination_key)

    # 拷贝操作-文件夹
    def copy_folder(self, source_folder, destination_folder):
        keys = self.get_all_keys_from_oss(source_folder)
        if keys is None:
            return
        if len(keys) == 0:
            return
        for key in keys:
            destination_key = key.replace(source_folder, destination_folder)
            self.copy_object(key, destination_key)


if __name__ == "__main__":
    oss_db = OSSClient(bucket_name='jindi-oss-companyinfo')
    a = oss_db.get_file_content_by_name_from_oss(
        "company/out_data/sn_data/8c9116c22ec74713ef77ee11aeea2ab7/company_base_info.json")
    print(a)
