# encoding=utf8

import time
import logging
from typing import Generator
from clients.redis._redis import Redis

logger = logging.getLogger(__name__)


# queue 基于redis zset or list
# 1 超过队列长度，阻塞N秒
# 2 支持插队
# 3 支持延迟队列 当生效时，强制使用zset
class RedisQueue(object):
    def __init__(self, name, max_length=1024, use_zset=True, delay_secs=0, **kwargs):
        self.ts_delta_1 = 1000000000
        self.name: str = name
        self.max_length: int = max_length
        self.use_zset = use_zset
        self.delay_secs = delay_secs
        if self.delay_secs > 0 and not self.use_zset:
            self.use_zset = True
            logger.info(f'force use zset when delay_secs > 0')
        self.redis = Redis(**kwargs)

    def generate(self, ) -> Generator[str, None, None]:
        while True:
            s = self.pop()
            if s is None:
                logger.warning('no data from %s, sleep for...', self.name)
                time.sleep(5)
                continue
            yield s

    def pop(self, ):
        if self.use_zset:
            data = self.redis.zpopmin(self.name, count=1)
            if len(data) == 0:
                return None
            value, score = data[0]
            if score < self.ts_delta_1:
                score += self.ts_delta_1
            delta_secs = time.time() - score
            if delta_secs < self.delay_secs:
                time.sleep(self.delay_secs - delta_secs)
                logger.info(f'sleep for delay delta_secs={delta_secs} value={value}')
            return data[0][0]
        else:
            data = self.redis.lpop(self.name)
        return data

    def push(self, value, realtime=False, wait=5.0):
        length = self.redis.zcard(self.name) if self.use_zset else self.redis.llen(self.name)
        max_length = 10000000 if realtime else self.max_length
        if length > max_length and wait > 0:
            logger.warning(f'sleep for length {length} greater than {max_length}')
            time.sleep(wait)
        if self.use_zset:
            score = time.time()
            if realtime:
                score -= self.ts_delta_1
            return self.redis.zadd(self.name, {value: score})
        else:
            push_func = self.redis.lpush if realtime else self.redis.rpush
            return push_func(self.name, value)


if __name__ == '__main__':
    from libs.log import setup_logger
    from libs.env import ConstantProps
    logger = setup_logger()

    conn = Redis(**ConstantProps.PROPS_GS_REDIS_ONLINE, db=2)

    logger.info(f'{conn.hlen("channel_report:qx_report_offsets_qx_realtime")}')
    # logger.info(f'{conn.set("test", "123")}')
    # logger.info(f'{conn.get("test")}')

    conn = RedisQueue(name='test', **ConstantProps.PROPS_GS_REDIS_ONLINE, db=2, use_zset=True)
    logger.info(f'get {conn.pop()}')
