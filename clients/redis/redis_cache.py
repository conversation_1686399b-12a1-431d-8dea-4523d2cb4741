# encoding=utf8

import time
import logging
from typing import Generator
from clients.redis._redis import Redis

logger = logging.getLogger(__name__)


# cache 基于redis zset
# 1 超过cache大小，阻塞N秒
# 2 LRU
# 3 过期淘汰
# 4 遍历
class RedisCache(object):
    def __init__(self, name, max_length=1024, expire_secs=86400, **kwargs):
        self.name: str = name
        self.max_length: int = max_length
        self.expire_secs: int = expire_secs
        self.redis = Redis(**kwargs)

    def fresh(self, value):
        cur_length = self.redis.zcard(self.name)
        if cur_length > self.max_length:
            logger.warning(f'sleep for greater than {self.max_length}')
            time.sleep(1.0)
        return self.redis.zadd(self.name, {value: time.time()})

    def scan(self, ) -> Generator[str, None, None]:
        cur_length = self.redis.zcard(self.name)
        cur_ts = time.time()
        for value, score in self.redis.zrange(self.name, 0, cur_length - 1, withscores=True):
            if score + self.expire_secs < cur_ts:
                self.redis.zrem(self.name, value)
            else:
                yield value


if __name__ == '__main__':
    from libs.log import setup_logger
    from libs.env import ConstantProps
    logger = setup_logger()
    redis_cache = RedisCache(name='test', **ConstantProps.PROPS_GS_REDIS_ONLINE, db=2)
    # redis_cache.fresh('123')
    for o in redis_cache.scan():
        logger.info(f'item={o}')
