# encoding=utf8

import logging
import redis
from clients.tunnel import TunnelMixin

logger = logging.getLogger(__name__)


# 增加TunneledClient功能
class Redis(redis.Redis, TunnelMixin):
    def __init__(self, host='localhost', port=6379, password=None, db=0, tunnel=None, **kwargs):
        self.host, self.port, self.tunnel = host, port, tunnel
        self.mix()
        super().__init__(
            host=self.host,
            port=self.port,
            password=password,
            db=db,
            decode_responses=True,
            **kwargs,
        )


if __name__ == '__main__':
    redis = Redis(**{
        "host": "redis-2b0f46e5-545a-4c21-a4a4-053daff1ee7b.cn-north-4.dcs.myhuaweicloud.com",
        "port": 6379,
        "password": "rdsnhgot0akp17Xq",
        "db": 1,
    })
