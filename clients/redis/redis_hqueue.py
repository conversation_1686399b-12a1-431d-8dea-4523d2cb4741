# encoding=utf8

import logging
import time
from unittest import TestCase
from clients.redis._redis import Redis
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


# 带有key去重功能的zset。
class RedisHQueue(object):
    def __init__(self, name, **kwargs):
        self.redis = Redis(**kwargs)
        self.name: str = name
        self.push_fn = self.redis.register_script(
            """
            redis.call('ZADD', KEYS[1], ARGV[1], ARGV[2])
            return redis.call('HSET', KEYS[2], ARGV[2], ARGV[3])
            """
        )
        self.pop_fn = self.redis.register_script(
            """
            local k = redis.call('ZPOPMIN', KEYS[1], 1)
            if #k > 0 then
                local v = redis.call('HGET', KEYS[2], k[1])
                redis.call('HDEL', KEYS[2], k[1])
                table.insert(k, 2, v)
            end
            return k
            """
        )

    def push(self, key, value, score=None):
        score = score or time.time()
        return self.push_fn(keys=[f'{self.name}:queue', f'{self.name}:hash'], args=[score, key, value])

    def pop(self):
        return self.pop_fn(keys=[f'{self.name}:queue', f'{self.name}:hash'], args=[])


class TestRedisHQueue(TestCase):
    def setUp(self):
        self.queue = RedisHQueue(name='test_hq', **ConstantProps.PROPS_GS_REDIS_TEST)

    def test1(self):
        self.assertEqual(self.queue.push('key1', 'value1'), 1)
        self.assertEqual(self.queue.push('key1', 'value2'), 0)
        self.assertEqual(self.queue.pop()[1], 'value2')
        self.assertEqual(len(self.queue.pop()), 0)
