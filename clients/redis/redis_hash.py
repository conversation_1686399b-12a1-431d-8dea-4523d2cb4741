# encoding=utf8

import logging
from clients.redis._redis import Redis

logger = logging.getLogger(__name__)


class RedisHash(object):
    def __init__(self, name, **kwargs):
        self.redis = Redis(**kwargs)
        self.name: str = name

    def get(self, key):
        return self.redis.hget(self.name, key=key)

    def set(self, key, value):
        return self.redis.hset(self.name, key=key, value=value)
