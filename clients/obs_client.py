# -*- coding: utf-8 -*-
import json
from typing import List, Generator, <PERSON>ple
import pytz
from dateutil.parser import parse
from datetime import datetime
import logging
from obs import ObsClient
from obs.client import GetResult
from libs.env import get_env_prop, get_props_obs
from public_utils_configs.util.TycSecureUtil import TycSecureUtil

logger = logging.getLogger(__name__)


class OBSClient(object):
    def __init__(self, inst_name: str = 'obs.tyc_dyn', bucket_name: str = 'jindi-oss-gsxt-eventlog'):
        if inst_name == 'obs.tyc_dyn':
            app_name = get_env_prop('obs.tyc_dyn.app_name')
            env = get_env_prop('obs.tyc_dyn.env')
            ak = get_env_prop('obs.tyc_dyn.ak')
            sk = get_env_prop('obs.tyc_dyn.sk')
            sec = TycSecureUtil(app_name=app_name, env=env, ak=ak, sk=sk)
            access_key_id, secret_access_key = sec.decrypt_obs()
            obs_cfg_dic = {
                'access_key_id': access_key_id,
                'secret_access_key': secret_access_key,
                'server': get_env_prop('obs.tyc_dyn.server'),
            }
        else:
            obs_cfg_dic = get_props_obs(inst_name)

        self.obs_client = ObsClient(**obs_cfg_dic)
        self.bucket_name = bucket_name

    def __del__(self):
        self.obs_client.close()

    def get(self, path):
        obj = self.obs_client.getObject(self.bucket_name, path)
        data = obj.body.response.read().decode('utf8')
        return data

    def get_content(self, path, with_ts=False):
        obj = self.obs_client.getObject(self.bucket_name, path)
        if obj.status == 404:
            return None
        data = obj.body.response.read()
        if not with_ts:
            return data
        ts = parse(obj.body.lastModified).astimezone(pytz.timezone('Asia/Chongqing'))
        return data, ts

    def exists_file(self, f):
        b = self.obs_client.getObjectMetadata(self.bucket_name, f)
        if b.status == 404:
            return False
        return True

    def put(self, path, data):
        # 可以多级目录，不存在则创建
        result = self.obs_client.putObject(self.bucket_name, path, data)
        return result.status

    def delete(self, path, recursive=False):
        # 只能删除单个文件，或者空目录
        # 无论path是否存在 返回值都是204
        if recursive:
            files = self.list(prefix=path)
            logger.info(f'recursive delete {len(files)}')
            for file in files:
                self.obs_client.deleteObject(self.bucket_name, file)
        result = self.obs_client.deleteObject(self.bucket_name, path)
        return result.status

    def list(self, prefix, size=1024, recursive=True):
        # 目录不能查看大小 可以使用路径的任意前缀查询
        response = self.obs_client.listObjects(self.bucket_name, prefix=prefix, max_keys=size)
        if response.status < 300:
            names: List[str] = list(x['key'] for x in response.body.contents)
            if recursive:
                return names
            set_ = set()
            for name in names:
                name2 = '/'.join(name.split('/')[:-1])
                set_.add(name2)
            return list(set_)
        raise RuntimeError('obs获取对象失败 code:{} message:{}'.format(response.errorCode, response.errorMessage))

    # 可以使用路径的任意前缀查询
    def list2(self, prefix: str) -> Generator[Tuple[str, datetime], None, None]:
        marker = None
        while True:
            response: GetResult = self.obs_client.listObjects(self.bucket_name, prefix=prefix, marker=marker)
            if response.status < 300:
                for x in response.body.contents:
                    yield x['key'], parse(x['lastModified'])  # '2024/04/19 08:11:49'
            if response.body.is_truncated:
                marker = response.body.next_marker
            else:
                break

    def exists_dir(self, path: str) -> bool:
        """
        检查目录是否存在
        Args:
            path: 目录路径，以'/'结尾
        Returns:
            bool: 目录存在返回True，否则返回False
        """
        # 确保路径以'/'结尾
        if not path.endswith('/'):
            path += '/'

        try:
            # 尝试列出目录下的对象
            response = self.obs_client.listObjects(self.bucket_name, prefix=path, max_keys=1)
            # 如果状态码小于300且有内容，说明目录存在
            return response.status < 300 and len(response.body.contents) > 0
        except Exception as e:
            logger.warning(f'Check directory existence failed: {path}, error: {e}')
            return False


if __name__ == '__main__':
    from libs.log2 import setup_logger

    logger = setup_logger()
    client_ = OBSClient(bucket_name='jindi-oss-gsxt')

    # lst = list(client_.list2('octopus/company/0/72164983/'))
    # for item in lst:
    #     logger.info(f'item={item}')

    print(client_.exists_dir('hk'))
