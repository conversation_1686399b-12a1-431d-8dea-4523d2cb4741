# 提供股东相关外部接口服务
[program:handle_special_equity_ratio]
directory=/home/<USER>/python_project/special_equity_ratio
command=/home/<USER>/.pyenv/shims/python special_equity_ratio.py
autostart=true
autorestart=true
user=work
stderr_logfile=/data/pm2/equity_ratio/special_equity_ratio_error.log
stdout_logfile=/data/pm2/equity_ratio/special_equity_ratio.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1


# 股比计算服务
[program:handle_equity_ratio]
directory=/home/<USER>/python_project/tyc_work
command=/home/<USER>/.pyenv/shims/gunicorn -c pm2/gunicorn.conf --chdir main/equity_ratio main_service:equity_ratio_app
autostart=true
autorestart=true
user=work
stderr_logfile=/data/pm2/tyc_work/equity_ratio/error.log
stdout_logfile=/data/pm2/tyc_work/equity_ratio/out.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 7



# 公司表监听服务
[program:equity_ratio_company]
directory=/home/<USER>/python_project/equityfunding/project/kafka
command=/home/<USER>/.pyenv/shims/python kafka_company.py
autostart=true
autorestart=true
user=work
stderr_logfile=/data/pm2/equityfunding/company/error.log
stdout_logfile=/data/pm2/equityfunding/company/out.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1


# 年报股东监听
[program:equity_ratio_report_shareholder]
directory=/home/<USER>/python_project/equityfunding/project/kafka
command=/home/<USER>/.pyenv/shims/python kafka_report_shareholder.py
autostart=true
autorestart=true
user=work
stderr_logfile=/data/pm2/equityfunding/report_shareholder/error.log
stdout_logfile=/data/pm2/equityfunding/report_shareholder/out.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

# 公示股东表监听
[program:equity_ratio_company_investor]
directory=/home/<USER>/python_project/equityfunding/project/kafka
command=/home/<USER>/.pyenv/shims/python kafka_company_investor.py
autostart=true
autorestart=true
user=work
stderr_logfile=/data/pm2/equityfunding/company_investor/error.log
stdout_logfile=/data/pm2/equityfunding/company_investor/out.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

# 公司公示股东监听
[program:equity_ratio_company_investor_entpub]
directory=/home/<USER>/python_project/equityfunding/project/kafka
command=/home/<USER>/.pyenv/shims/python kafka_company_investor_entpub.py
autostart=true
autorestart=true
user=work
stderr_logfile=/data/pm2/equityfunding/company_investor_entpub/error.log
stdout_logfile=/data/pm2/equityfunding/company_investor_entpub/out.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1


# 上市股东表监听
[program:equity_ratio_shareholder]
directory=/home/<USER>/python_project/equityfunding/project/kafka
command=/home/<USER>/.pyenv/shims/python kafka_shareholder.py
autostart=true
autorestart=true
user=work
stderr_logfile=/data/pm2/equityfunding/shareholder/error.log
stdout_logfile=/data/pm2/equityfunding/shareholder/out.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

# IPO申报股东表监听
[program:equity_ratio_ipo_shareholder]
directory=/home/<USER>/python_project/equityfunding/project/kafka
command=/home/<USER>/.pyenv/shims/python kafka_ipo_shareholder.py
autostart=true
autorestart=true
user=work
stderr_logfile=/data/pm2/equityfunding/ipo_shareholder/error.log
stdout_logfile=/data/pm2/equityfunding/ipo_shareholder/out.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

# 港股股东表监听
[program:equity_ratio_hk_shareholder]
directory=/home/<USER>/python_project/equityfunding/project/kafka
command=/home/<USER>/.pyenv/shims/python kafka_hk_shareholder.py
autostart=true
autorestart=true
user=work
stderr_logfile=/data/pm2/equityfunding/hk_shareholder/error.log
stdout_logfile=/data/pm2/equityfunding/hk_shareholder/out.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

# 上市状态监听
[program:equity_ratio_company_bond_plates]
directory=/home/<USER>/python_project/equityfunding/project/kafka
command=/home/<USER>/.pyenv/shims/python kafka_company_bond_plates.py
autostart=true
autorestart=true
user=work
stderr_logfile=/data/pm2/equityfunding/company_bond_plates/error.log
stdout_logfile=/data/pm2/equityfunding/company_bond_plates/out.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

# 屏蔽年报联系方式
[program:hide_annual_report_process]
directory=/home/<USER>/python_project/hide_annual_report
command=/home/<USER>/.pyenv/versions/3.9.9/bin/python /home/<USER>/python_project/hide_annual_report/hide_annual_report_platform.py
autostart=true
autorestart=true
user=work
stderr_logfile=/data/pm2/annual_report/hide_annual_report_error.log
stdout_logfile=/data/pm2/annual_report/hide_annual_report_out.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1


[program:hide_annual_report_server]
directory=/home/<USER>/python_project/hide_annual_report
command=/home/<USER>/.pyenv/shims/gunicorn -c gunicorn.conf hide_annual_report_server:hide_annual_report --preload
autostart=true
autorestart=true
user=work
stderr_logfile=/data/pm2/annual_report/hide_annual_report_interface_error.log
stdout_logfile=/data/pm2/annual_report/hide_annual_report_interface_out.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1


# 已下是重构的新脚本启动配置信息
# 非上市股东解析服务
;[program:unlist_shareholder_prd_flask_20241224]
;directory=/home/<USER>/python_project/equityfunding_server_2024/equityfunding
;command=/home/<USER>/.pyenv/shims/gunicorn -c /home/<USER>/python_project/equityfunding_server_2024/equityfunding/offline_process/gunicorn.conf --chdir /home/<USER>/python_project/equityfunding_server_2024/equityfunding/offline_process unlisted_shareholder_process_flask:app
;user=work
;autorestart=true
;autostart=true
;stopasgroup=true
;killasgroup=true
;stderr_logfile=/data/shareholder_dimension_log/unlist_shareholder_prd_flask/error.log
;stdout_logfile=/data/shareholder_dimension_log/unlist_shareholder_prd_flask/out.log
;redirect_stderr = true
;stdout_logfile_maxbytes = 1GB
;stdout_logfile_backups = 1
;startsecs=5


# 非上市数据入库服务
;[program:serve_exec_sql]
;directory=/home/<USER>/python_project/equityfunding_server_2024/equityfunding
;command=/home/<USER>/.pyenv/shims/gunicorn -c offline_process/exec_update_sql_serve/gunicorn.conf --chdir offline_process/exec_update_sql_serve serve_exec_sql:app
;user=work
;autorestart=true
;autostart=true
;stopasgroup=true
;killasgroup=true
;stderr_logfile=/data/shareholder_dimension_log/serve_exec_sql/error.log
;stdout_logfile=/data/shareholder_dimension_log/serve_exec_sql/out.log
;redirect_stderr = true
;stdout_logfile_maxbytes = 1GB
;stdout_logfile_backups = 1
;startsecs=5

# 新队列消费脚本
[program:realtime_process_20241220]
command=/home/<USER>/.pyenv/shims/python3 /home/<USER>/python_project/equity_processor_service/realtime_process.py
directory=/home/<USER>/python_project/equity_processor_service
environment=PYTHONPATH="/home/<USER>/python_project/equity_processor_service"
user=work
autorestart=true
autostart=true
stderr_logfile=/data/shareholder_dimension_log/realtime_process/error2.log
stdout_logfile=/data/shareholder_dimension_log/realtime_process/out2.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1
;numprocs=3
;process_name=%(program_name)s_%(process_num)02d

;[program:realtime_process_test]
;command=/home/<USER>/.pyenv/shims/python3 /home/<USER>/python_project/equity_processor_service_test/equity_processor_service/realtime_process.py
;directory=/home/<USER>/python_project/equity_processor_service_test/equity_processor_service
;environment=PYTHONPATH="/home/<USER>/python_project/equity_processor_service"
;user=work
;autorestart=true
;autostart=true
;stderr_logfile=/data/shareholder_dimension_log/realtime_process/error_test.log
;stdout_logfile=/data/shareholder_dimension_log/realtime_process/out_test.log
;redirect_stderr = true
;stdout_logfile_maxbytes = 1GB
;stdout_logfile_backups = 1

# 公司数据获取接口
[program:company_date_sever_20241224]
directory=/home/<USER>/python_project/equityfunding_server_2024/company_date_sever/getcompanydata
command=/home/<USER>/.pyenv/shims/gunicorn -c gunicorn.conf serve:app
user=work
autorestart=true
autostart=true
stopasgroup=true
killasgroup=true
stderr_logfile=/data/shareholder_dimension_log/company_date_sever/error.log
stdout_logfile=/data/shareholder_dimension_log/company_date_sever/out.log
redirect_stderr=true
stdout_logfile_maxbytes=1GB
stdout_logfile_backups=1
startsecs=5

# 上市最新公示数据服务
;[program:listed_shareholder_process_flask_20241226]
;command=/home/<USER>/.pyenv/shims/python3 /home/<USER>/python_project/equityfunding_server_2024/equityfunding/offline_process/listed_shareholder_process_flask.py
;directory=/home/<USER>/python_project/equityfunding_server_2024/equityfunding/offline_process
;environment=PYTHONPATH="/home/<USER>/python_project/equityfunding_server_2024/equityfunding"
;user=work
;autorestart=true
;autostart=true
;stderr_logfile=/data/shareholder_dimension_log/listed_shareholder_process_flask/listed_shareholder_process_flask_error.log
;stdout_logfile=/data/shareholder_dimension_log/listed_shareholder_process_flask/listed_shareholder_process_flask_out.log
;redirect_stderr = true
;stdout_logfile_maxbytes = 1GB
;stdout_logfile_backups = 1

# 上市入库服务
;[program:listed_shareholder_exec_flask_20241227]
;command=/home/<USER>/.pyenv/shims/python3 /home/<USER>/python_project/equityfunding_server_2024/equityfunding/offline_process/listed_shareholder_exec_flask.py
;directory=/home/<USER>/python_project/equityfunding_server_2024/equityfunding/offline_process
;environment=PYTHONPATH="/home/<USER>/python_project/equityfunding_server_2024/equityfunding"
;user=work
;autorestart=true
;autostart=true
;stderr_logfile=/data/shareholder_dimension_log/listed_shareholder_exec_flask/listed_shareholder_exec_flask_error.log
;stdout_logfile=/data/shareholder_dimension_log/listed_shareholder_exec_flask/listed_shareholder_exec_flask_out.log
;redirect_stderr = true
;stdout_logfile_maxbytes = 1GB
;stdout_logfile_backups = 1

# 工商登记&非上市最新公示接口服务
[program:shareholder_systematic_selector_20250107]
directory=/home/<USER>/zhiguo/20241225/tyc_work_2024
command=/home/<USER>/.pyenv/shims/gunicorn -c config/gunicorn.conf handler_sever:handle_app
user=work
autorestart=true
autostart=true
stopasgroup=true
killasgroup=true
stderr_logfile=/data/shareholder_dimension_log/shareholder_systematic_selector/error.log
stdout_logfile=/data/shareholder_dimension_log/shareholder_systematic_selector/out.log
redirect_stderr=true
stdout_logfile_maxbytes=1GB
stdout_logfile_backups=1
startsecs=5

# 上市非上市解析入库整合接口
[program:equity_processor_service_20250113]
directory=/home/<USER>/python_project/equity_processor_service
command=/home/<USER>/.pyenv/shims/gunicorn -c config/gunicorn.conf structuring_warehousing_service:app
user=work
autorestart=true
autostart=true
stopasgroup=true
killasgroup=true
stderr_logfile=/data/shareholder_dimension_log/equity_processor_service/logs/error2.log
stdout_logfile=/data/shareholder_dimension_log/equity_processor_service/logs/out2.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1
startsecs=5