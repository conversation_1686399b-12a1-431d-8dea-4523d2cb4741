[program:CodsCrawler]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_crawler.py --conf-name cods.json --entry-name credit
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:CodsParser]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_parser.py --conf-name cods.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:company_sync_npo]
directory = /home/<USER>/pygs-work-parent/apps/company_sync
command = /home/<USER>/pygs-work-parent/venv/bin/python3 company_sync_main.py --dump-workers 2 --process-workers 4 --processor-class NPOProcessor
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:company_sync_new_org]
directory = /home/<USER>/pygs-work-parent/apps/company_sync
command = /home/<USER>/pygs-work-parent/venv/bin/python3 company_sync_main.py --dump-workers 2 --process-workers 4 --processor-class NewORGProcessor
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:company_sync_law_firm]
directory = /home/<USER>/pygs-work-parent/apps/company_sync
command = /home/<USER>/pygs-work-parent/venv/bin/python3 company_sync_main.py --dump-workers 2 --process-workers 4 --processor-class LawFirmProcessor
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:company_sync_gov_unit]
directory = /home/<USER>/pygs-work-parent/apps/company_sync
command = /home/<USER>/pygs-work-parent/venv/bin/python3 company_sync_main.py --dump-workers 2 --process-workers 4 --processor-class GovUnitProcessor
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:company_sync_hk]
directory = /home/<USER>/pygs-work-parent/apps/company_sync
command = /home/<USER>/pygs-work-parent/venv/bin/python3 company_sync_main.py --dump-workers 2 --process-workers 4 --processor-class HKProcessor
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:company_sync_foundation]
directory = /home/<USER>/pygs-work-parent/apps/company_sync
command = /home/<USER>/pygs-work-parent/venv/bin/python3 company_sync_main.py --dump-workers 2 --process-workers 4 --processor-class FoundationProcessor
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null


[program:gsxt_tools_server]
directory = /home/<USER>/pygs-work-parent/apps/gsxt_tools_server
command = /home/<USER>/pygs-work-parent/venv/bin/streamlit run gsxt_tools.py
autostart = true
autorestart = true
startsecs = 1
user = work
stderr_logfile = /dev/null
stdout_logfile = /home/<USER>/pygs-work-parent/logs/gsxt_tools_server.log
redirect_stderr = true
stdout_logfile_maxbytes = 2GB
stdout_logfile_backups = 1

[program:octopus_api]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/gunicorn -c octopus_api_conf.py apps.octopus.octopus_api:app
autostart = true
autorestart = true
startsecs = 1
user = work
stderr_logfile = /dev/null
stdout_logfile = /home/<USER>/pygs-work-parent/logs/octopus_api.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 9

[program:octopus_feedback]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_feedback.py --dump-worker-num 4 --process-worker-num 20
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:octopus_timeout]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_timeout.py
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:octopus_platform]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_platform.py
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null



[program:octopus_scheduler_brno_hk]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name hk.brno.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:octopus_scheduler_credit_china_npo]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name china_npo.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:octopus_scheduler_credit_credit_china]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name credit_china.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:octopus_scheduler_credit_gsxt_app]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gsxt_app.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:octopus_scheduler_credit_gds]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gds.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:octopus_scheduler_credit_cods]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name cods.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_scheduler_credit_acftu]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name acftu.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_scheduler_credit_institution_gj]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name institution_gj.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_scheduler_law_firm_gj]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name law_firm_gj.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null


[program:octopus_scheduler_credit_icp]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name icp.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null


[program:octopus_scheduler_credit_tax_owe_announce]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name tax_owe_announce.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null


[program:park_qx_sync]
directory = /home/<USER>/pygs-work-parent/apps/park
command = /home/<USER>/pygs-work-parent/venv/bin/python3 park_qx_sync.py --worker-num 8
autostart = true
autorestart = true
startsecs = 1
user = work
stderr_logfile = /dev/null
stdout_logfile = /home/<USER>/pygs-work-parent/logs/park_qx_sync.log
redirect_stderr = true
stdout_logfile_maxbytes = 2GB
stdout_logfile_backups = 1



[program:qx_eid]
directory = /home/<USER>/pygs-work-parent/apps/wangbangxu
command = /home/<USER>/pygs-work-parent/venv/bin/python3 qx_eid.py --dump-num 4 --idx-num 256 --process-num 1
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /home/<USER>/pygs-work-parent/logs/qx_eid.log
redirect_stderr = true
stdout_logfile_maxbytes = 2GB
stdout_logfile_backups = 1

[program:qx_eid_update_cid_null]
directory = /home/<USER>/pygs-work-parent/apps/wangbangxu
command = /home/<USER>/pygs-work-parent/venv/bin/python3 qx_eid_update_cid_null.py
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /home/<USER>/pygs-work-parent/logs/qx_eid_update_cid_null.log
redirect_stderr = true
stdout_logfile_maxbytes = 2GB
stdout_logfile_backups = 1

[program:sanwu_company_update]
directory = /home/<USER>/pygs-work-parent/apps/wangbangxu
command = /home/<USER>/pygs-work-parent/venv/bin/python3 sanwu_company_update.py
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /home/<USER>/pygs-work-parent/logs/sanwu_company_update.log
redirect_stderr = true
stdout_logfile_maxbytes = 2GB
stdout_logfile_backups = 1

[program:reg_institute_area_info]
directory = /home/<USER>/pygs-work-parent/apps/reg_institute_area_info
command = /home/<USER>/pygs-work-parent/venv/bin/gunicorn -c api_conf.py apps.reg_institute_area_info.api:app
autostart = true
autorestart = true
startsecs = 1
user = work
stderr_logfile = /dev/null
stdout_logfile = /home/<USER>/pygs-work-parent/logs/reg_institute_area_info.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 2



[program:spider_acftu_crawler]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_crawler.py --conf-name acftu.json --entry-name credit
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:spider_acftu_parser]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_parser.py --conf-name acftu.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null


[program:qudao_touci]
directory = /home/<USER>/pygs-work-parent
command = /home/<USER>/pygs-work-parent/venv/bin/python3 apps/dingliangyi/非企/qudao投词.py
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:spider_hk_crawler]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_crawler.py --conf-name hk.json --entry-name brno --backup-days 1
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:spider_hk_parser]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_parser.py --conf-name hk.json --backup-days 1
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

;[program:tyc2_total_sholder_input_binlog]
;directory = /home/<USER>/pygs-work-parent/apps/tyc2_total_sholder
;command = /home/<USER>/pygs-work-parent/venv/bin/python3 tyc2_total_sholder_input_binlog.py --dump-workers 4
;autostart = true
;autorestart = true
;user = work
;stderr_logfile = /dev/null
;stdout_logfile = /home/<USER>/pygs-work-parent/logs/tyc2_total_sholder_input_binlog.log
;redirect_stderr = true
;stdout_logfile_maxbytes = 2GB
;stdout_logfile_backups = 1
;
;[program:tyc2_total_sholder_main]
;directory = /home/<USER>/pygs-work-parent/apps/tyc2_total_sholder
;command = /home/<USER>/pygs-work-parent/venv/bin/python3 tyc2_total_sholder_main.py --process-workers 24
;autostart = true
;autorestart = true
;user = work
;stderr_logfile = /dev/null
;stdout_logfile = /dev/null
;redirect_stderr = true
;stdout_logfile_maxbytes = 2GB
;stdout_logfile_backups = 1

[program:spider_gov_unit_crawler]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_crawler.py  --conf-name gov_unit.json --entry-name credit
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:spider_gov_unit_parser]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_parser.py  --conf-name gov_unit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
