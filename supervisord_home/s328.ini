[program:GdsCrawler]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_crawler.py --conf-name gds.json --entry-name credit
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:GdsParser]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_parser.py --conf-name gds.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gs_spider_gdzh_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/zhuhai.json --max-workers 30
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_gdzh_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/zhuhai.json  --max-workers 10
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:hk_report]
directory = /home/<USER>/pygs-work-parent
command = /home/<USER>/pygs-work-parent/venv/bin/python3 apps/dingliangyi/hk/trigger.py
autostart = false
autorestart = false
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:hk_report_feishu]
directory = /home/<USER>/pygs-work-parent
command = /home/<USER>/pygs-work-parent/venv/bin/python3 apps/dingliangyi/hk/report.py
autostart = false
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:hk_report2]
directory = /home/<USER>/pygs-work-parent
command = /home/<USER>/pygs-work-parent/venv/bin/python3 apps/dingliangyi/hk/trigger2.py
autostart = false
autorestart = false
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:qxb_touci]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 非企/qxb_touci.py
autostart = false
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:hk_name_change_touci]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 hk/name_change_touci.py
autostart = false
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:qudao_touci]
directory = /home/<USER>/pygs-work-parent
command = /home/<USER>/pygs-work-parent/venv/bin/python3 apps/dingliangyi/非企/qudao投词.py
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:npo_touci]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 npo/list.py
autostart = false
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:law_touci]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 law/douci.py
autostart = false
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:law_add_edu_pic]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 law/update_edu_pic.py
autostart = false
autorestart = false
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:law_avater]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 law/avater.py
autostart = false
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:lawyer2law_team]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 law/lawyer2law_team.py
autostart = false
autorestart = false
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gov_unit_touci]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gov_unit/touci.py
autostart = false
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gdgz_licence]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gdgz/main.py
autostart = false
autorestart = false
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:schedule_report_fresh_year]
directory = /home/<USER>/pygs-work-parent/apps/wangbangxu
command = /home/<USER>/pygs-work-parent/venv/bin/python3 schedule_report_fresh_year.py
autostart = false
autorestart = true
user = work
stderr_logfile = /home/<USER>/pygs-work-parent/logs/schedule_report_fresh_year.log
stdout_logfile = /home/<USER>/pygs-work-parent/logs/schedule_report_fresh_year.log

[program:pesticide_register]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 pesticide/pesticide_register.py
autostart = false
autorestart = false
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:notice]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_notice/notice.py
autostart = false
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:notice2]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_notice/notice2.py
autostart = false
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:sz_plugins_crawler]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 SZ_fastapi_server.py
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null