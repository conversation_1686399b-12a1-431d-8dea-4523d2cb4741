[program:octopus_gtp_bj]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gtp_bj.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_gtp_fjxm]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gtp_fjxm.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:octopus_gtp_gs]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gtp_gs.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_gtp_gx]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gtp_gx.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_gtp_heb]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gtp_heb.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_gtp_jx]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gtp_jx.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_gtp_lndl]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gtp_lndl.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_gtp_sc]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gtp_sc.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_gtp_sx]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gtp_sx.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_gtp_tj]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gtp_tj.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_gtp_xj]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gtp_xj.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_gtp_yn]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gtp_yn.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_gtp_zjnb]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name gtp_zjnb.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[group:octopus_gtp]
programs = octopus_gtp_bj,octopus_gtp_fjxm,octopus_gtp_gs,octopus_gtp_gx,octopus_gtp_heb,octopus_gtp_jx,octopus_gtp_lndl,octopus_gtp_sc,octopus_gtp_sx,octopus_gtp_tj,octopus_gtp_xj,octopus_gtp_yn,octopus_gtp_zjnb


[program:octopus_scheduler_credit_foundation]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name foundation.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:octopus_scheduler_credit_china_charity]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name china_charity.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null


[program:FoundationCrawler]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_crawler.py --conf-name foundation.json --entry-name credit
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:FoundationParser]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_parser.py --conf-name foundation.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:CharityCrawler]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_crawler.py --conf-name charity.json --entry-name credit
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:CharityParser]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_parser.py --conf-name charity.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null


[program:spider_china_npo_crawler]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_crawler.py --conf-name china_npo.json --entry-name credit
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:spider_china_npo_parser]
directory = /home/<USER>/pygs-work-parent/apps/spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 spider_parser.py --conf-name china_npo.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:npo_list]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 npo/list.py
autostart = false
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

;[program:notice]
;directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
;command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_notice/notice.py
;autostart = false
;autorestart = true
;user = work
;stderr_logfile = /dev/null
;stdout_logfile = /dev/null
;
;[program:notice2]
;directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
;command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_notice/notice.py
;autostart = false
;autorestart = true
;user = work
;stderr_logfile = /dev/null
;stdout_logfile = /dev/null