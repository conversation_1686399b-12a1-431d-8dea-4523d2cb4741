[program:gs_spider_app2_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/app2.json --max-workers 20
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_app2_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/app2.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gs_spider_cd_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/cd.json --max-workers 1
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_cd_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/cd.json --max-workers 1
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gs_spider_bj_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/beijing.json --max-workers 40
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_bj_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/beijing.json --max-workers 15
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gs_spider_gdgz2_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/gdgz2.json --max-workers 10
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_gdgz2_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/gdgz2.json --max-workers 1
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null



[program:gs_spider_guizhou_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/guizhou.json --max-workers 40
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_guizhou_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/guizhou.json --max-workers 2
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gs_spider_jiangsu_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/jiangsu.json --max-workers 30
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_jiangsu_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/jiangsu.json --max-workers 2
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gs_spider_hainan_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/hainan.json --max-workers 40
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_hainan_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/hainan.json --max-workers 3
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gs_spider_heilongjiang_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/heilongjiang.json --max-workers 40
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_heilongjiang_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/heilongjiang.json --max-workers 3
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gs_spider_sh_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/shanghai.json --max-workers 8
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_sh_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/shanghai.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_jx_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/jx.json --max-workers 25
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_jx_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/jx.json --max-workers 5
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gs_spider_sz2_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/sz2.json --max-workers 5
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_sz2_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/sz2.json --max-workers 1
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gs_spider_sz_new_list]
directory = /home/<USER>/pygs-work-parent
command = /home/<USER>/pygs-work-parent/venv/bin/python3 apps/dingliangyi/sz/list.py
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gs_spider_tj_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/tj.json --max-workers 25
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_tj_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/tj.json --max-workers 5
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null


[program:gs_spider_xm_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/xiamen.json --max-workers 6
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_xm_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/xiamen.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:report_channel_trigger_nbsb]
directory = /home/<USER>/pygs-work-parent/apps/wangbangxu
command = /home/<USER>/pygs-work-parent/venv/bin/python3 20240327_report_channel_trigger.py --kafka-inst kafka.tyc.channel_out1 --topic subject_025341 --filter NBSB
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /home/<USER>/pygs-work-parent/logs/report_channel_trigger_nbsb.log
redirect_stderr = true
stdout_logfile_maxbytes = 2GB
stdout_logfile_backups = 1


[program:snapshots]
directory = /home/<USER>/pygs-work-parent/apps/yuzhiguo
command = /home/<USER>/pygs-work-parent/venv/bin/python3 snapshots_are_new.py
autostart = true
autorestart = true
user = work
stderr_logfile = /home/<USER>/pygs-work-parent/logs/snapshots_are_new_err.log
stdout_logfile = /home/<USER>/pygs-work-parent/logs/snapshots_are_new_out.log
redirect_stderr = true
stdout_logfile_maxbytes = 2GB
stdout_logfile_backups = 1

[program:shareholder_name_clean]
directory = /home/<USER>/pygs-work-parent/apps/yuzhiguo
command = /home/<USER>/pygs-work-parent/venv/bin/python3 shareholder_name_clean.py
autostart = true
autorestart = true
user = work
stderr_logfile = /home/<USER>/pygs-work-parent/logs/shareholder_name_clean_err.log
stdout_logfile = /home/<USER>/pygs-work-parent/logs/shareholder_name_clean_out.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

[program:shareholder_name_update]
directory = /home/<USER>/pygs-work-parent/apps/yuzhiguo
command = /home/<USER>/pygs-work-parent/venv/bin/python3 shareholder_name_update.py
autostart = true
autorestart = true
user = work
stderr_logfile = /home/<USER>/pygs-work-parent/logs/shareholder_name_update_err.log
stdout_logfile = /home/<USER>/pygs-work-parent/logs/shareholder_name_update_out.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

[program:qxb_touci]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 非企/qxb_touci.py
autostart = false
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:name_change_touci]
directory = /home/<USER>/pygs-work-parent/apps/dingliangyi
command = /home/<USER>/pygs-work-parent/venv/bin/python3 hk/name_change_touci.py
autostart = false
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null