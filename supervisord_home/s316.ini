[program:octopus_abn_bj]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_bj.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_gd]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_gd.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_gd_sz]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_gd_sz.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_gs]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_gs.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_gx]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_gx.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_han]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_han.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_heb]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_heb.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_hen]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_hen.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_hlj]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_hlj.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_hun]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_hun.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_jx]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_jx.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_ln]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_ln.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_sc]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_sc.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_tj]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_tj.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

# add at 20141126
[program:octopus_abn_nx]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_nx.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_qh]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_qh.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_sd_qd]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_sd_qd.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_sd]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_sd.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_sh]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_sh.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_sx]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_sx.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_snx]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_snx.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_fj_xm]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_fj_xm.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:octopus_abn_zj]
directory = /home/<USER>/pygs-work-parent/apps/octopus
command = /home/<USER>/pygs-work-parent/venv/bin/python3 octopus_scheduler.py --conf-name abn_zj.credit.json
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[group:octopus_abn]
programs=octopus_abn_bj,octopus_abn_gd,octopus_abn_gd_sz,octopus_abn_gs,octopus_abn_gx,octopus_abn_han,octopus_abn_heb,octopus_abn_hen,octopus_abn_hlj,octopus_abn_hun,octopus_abn_jx,octopus_abn_ln,octopus_abn_sc,octopus_abn_tj,octopus_abn_nx,octopus_abn_qh,octopus_abn_sd_qd,octopus_abn_sd,octopus_abn_sh,octopus_abn_snx,octopus_abn_sx,octopus_abn_fj_xm,octopus_abn_zj


[program:gs_correction_api]
directory=/home/<USER>/pygs-work-parent/apps/gs_correction_api
command=/home/<USER>/pygs-work-parent/venv/bin/gunicorn -c start.py apps.gs_correction_api.api:app
autostart=true
autorestart=true
startsecs=1
user=work
stderr_logfile=/dev/null
stdout_logfile=/home/<USER>/pygs-work-parent/logs/gs_correction_api.log
redirect_stderr = true
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 3


;[program:generated_appr_change_info.binlog]
;directory = /home/<USER>/pygs-work-parent/apps/
;command = ../venv/bin/python3 generated_appr_change_info/main_realtime_mysql_binlog.py
;autostart = true
;autorestart = true
;startsecs=1
;user=work
;stderr_logfile=/dev/null
;stdout_logfile=../logs/generated_appr_change_info.binlog.log
;redirect_stderr = true
;stdout_logfile_maxbytes = 2GB
;stdout_logfile_backups = 1
;
;
;[program:generated_appr_change_info.process]
;directory = /home/<USER>/pygs-work-parent/apps/
;command = ../venv/bin/python3 generated_appr_change_info/main_process.py --worker-num 10
;autostart = true
;autorestart = true
;startsecs=1
;user=work
;stderr_logfile=/dev/null
;stdout_logfile=../logs/generated_appr_change_info.main_process.log
;redirect_stderr = true
;stdout_logfile_maxbytes = 2GB
;stdout_logfile_backups = 1


[program:crawl_qx_status]
directory = /data/mzb/timing_dimension_update/yh
command = python3 qx_gs_company_status_from_kafka.py
autostart = true
autorestart = true
user = work
stderr_logfile = /data/mzb/timing_dimension_update/logs/qx_status.log
stdout_logfile = /data/mzb/timing_dimension_update/logs/qx_status.log
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

;[program:crawl_qx_random_check]
;directory = /data/mzb/timing_dimension_update/yh
;command = python3 qx_gs_random_check_from_kafka.py
;autostart = true
;autorestart = true
;user = work
;stderr_logfile = /data/mzb/timing_dimension_update/logs/qx_random.log
;stdout_logfile = /data/mzb/timing_dimension_update/logs/qx_random.log
;stdout_logfile_maxbytes = 1GB
;stdout_logfile_backups = 1

[program:crawl_yh_status]
directory = /data/mzb/timing_dimension_update/yh
command = python3 yh_gs_company_status_from_kafka.py
autostart = true
autorestart = true
user = work
stderr_logfile = /data/mzb/timing_dimension_update/logs/yh_status.log
stdout_logfile = /data/mzb/timing_dimension_update/logs/yh_status.log
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

;[program:crawl_yh_checkups]
;directory = /data/mzb/timing_dimension_update/yh
;command = python3 yh_gs_check_info_from_kafka.py
;autostart = true
;autorestart = true
;user = work
;stderr_logfile = /data/mzb/timing_dimension_update/logs/yh_check.log
;stdout_logfile = /data/mzb/timing_dimension_update/logs/yh_check.log
;stdout_logfile_maxbytes = 1GB
;stdout_logfile_backups = 1

;[program:crawl_yh_random_check]
;directory = /data/mzb/timing_dimension_update/yh
;command = python3 yh_gs_random_check_from_kafka.py
;autostart = true
;autorestart = true
;user = work
;stderr_logfile = /data/mzb/timing_dimension_update/logs/yh_random.log
;stdout_logfile = /data/mzb/timing_dimension_update/logs/yh_random.log
;stdout_logfile_maxbytes = 1GB
;stdout_logfile_backups = 1

[program:crawl_jx_status]
directory = /data/mzb/timing_dimension_update/yh
command = python3 jx_gs_company_status_from_kafka.py
autostart = true
autorestart = true
user = work
stderr_logfile = /data/mzb/timing_dimension_update/logs/jx_status.log
stdout_logfile = /data/mzb/timing_dimension_update/logs/jx_status.log
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

;[program:consumer_check_info]
;directory = /data/mzb/timing_dimension_update/consumer_redis
;command = python3 consumer_check_info.py
;autostart = true
;autorestart = true
;user = work
;stderr_logfile = /data/mzb/timing_dimension_update/logs/consumer_check_info.log
;stdout_logfile = /data/mzb/timing_dimension_update/logs/consumer_check_info.log
;stdout_logfile_maxbytes = 1GB
;stdout_logfile_backups = 1

[program:consumer_company_status]
directory = /data/mzb/timing_dimension_update/consumer_redis
command = python3 consumer_company_status.py
autostart = true
autorestart = true
user = work
stderr_logfile = /data/mzb/timing_dimension_update/logs/consumer_status.log
stdout_logfile = /data/mzb/timing_dimension_update/logs/consumer_status.log
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

;[program:consumer_random_check]
;directory = /data/mzb/timing_dimension_update/consumer_redis
;command = python3 consumer_random_check.py
;autostart = true
;autorestart = true
;user = work
;stderr_logfile = /data/mzb/timing_dimension_update/logs/consumer_random_check.log
;stdout_logfile = /data/mzb/timing_dimension_update/logs/consumer_random_check.log
;stdout_logfile_maxbytes = 1GB
;stdout_logfile_backups = 1

[program:channel_random_check_to_mysql]
directory = /data/mzb/timing_dimension_update/yh_channel_data
command = python3 yh_random_check_info_to_mysql.py
autostart = true
autorestart = true
user = work
stderr_logfile = /data/mzb/timing_dimension_update/yh_channel_data/logs/channel_random_check_to_mysql.log
stdout_logfile = /data/mzb/timing_dimension_update/yh_channel_data/logs/channel_random_check_to_mysql.log
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

[program:channel_random_check_result_to_mysql]
directory = /data/mzb/timing_dimension_update/yh_channel_data
command = python3 yh_random_check_result_to_mysql.py
autostart = true
autorestart = true
user = work
stderr_logfile = /data/mzb/timing_dimension_update/yh_channel_data/logs/yh_random_check_result_to_mysql.log
stdout_logfile = /data/mzb/timing_dimension_update/yh_channel_data/logs/yh_random_check_result_to_mysql.log
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

[program:channel_random_check]
directory = /data/mzb/timing_dimension_update/yh_channel_data
command = python3 yh_random_check.py
autostart = true
autorestart = true
user = work
stderr_logfile = /data/mzb/timing_dimension_update/yh_channel_data/logs/yh_random_check_record.log
stdout_logfile = /data/mzb/timing_dimension_update/yh_channel_data/logs/yh_random_check_record.log
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

[program:channel_check]
directory = /data/mzb/timing_dimension_update/yh_channel_data
command = python3 yh_check_info.py
autostart = true
autorestart = true
user = work
stderr_logfile = /data/mzb/timing_dimension_update/yh_channel_data/logs/yh_check_record.log
stdout_logfile = /data/mzb/timing_dimension_update/yh_channel_data/logs/yh_check_record.log
stdout_logfile_maxbytes = 1GB
stdout_logfile_backups = 1

