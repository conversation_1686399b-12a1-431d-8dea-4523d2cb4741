# encoding=utf8

import enum
import logging
from typing import Optional
from pydantic import Field
from entity.deps.entity import BaseEntity

logger = logging.getLogger(__name__)


class SpiderCode(int, enum.Enum):
    UNFILLED = -1
    SUCCESS = 0
    GIVE_UP = 1  # octopus表示不再重试，放弃  例如解析失败
    FAIL = 2
    # PARSE_ERROR = 3  # 解析失败 效果 等价于 GIVE_UP
    CLUE_BAD_AT_ID_CENTER = 4  # ID中心返回非新企（solver生成）
    ENTRY_BAD_AT_ID_CENTER = 5  # ID中心返回非更新（solver生成）
    CODE_6 = 6  # octopus无效
    CODE_7 = 7  # octopus无效
    CODE_8 = 8  # octopus无效
    CODE_9 = 9  # octopus无效
    CODE_10 = 10  # octopus无效
    SEARCH_EMPTY = 11
    CLUE_EXIST = 12  # octopus无效
    CODE_13 = 13  # octopus无效
    CODE_14 = 14  # octopus无效
    UPDATE_NO_EXIST = 15  # octopus无效
    CODE_16 = 16  # octopus无效
    TIMEOUT = 99  # 超时检测
    CODE_201 = 201  # octopus无效
    CODE_202 = 202  # octopus无效


class SelectorLog(BaseEntity):
    send_ts: int = Field(default=-1)
    receive_ts: int = Field(default=-1)
    # 调度原因 octopus有效 工商有效
    reason: str = Field(default=None)
    # redis_key: str = Field(alias='redisKey', default=None)
    # dispatch_id: int = Field(alias='dispatchId', default=None)
    item_name: str
    inst_name: str
    word: str
    info: Optional[dict] = Field(default=None)
    try_id: int = Field(default=0)
    meta: Optional[str] = Field(default=None)
    weight: int = Field(default=-1)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def get_info(self, key) -> Optional:
        if self.info is None:
            return None
        return self.info.get(key, None)


class SpiderLog(BaseEntity):
    receive_ts: int = Field(default=-1)
    send_ts: int = Field(default=-1)
    item_insert: bool = Field(default=False)  # 当SpiderCode=SUCCESS有效
    ab_info: dict = Field(default=dict())
    spider_data: dict

    def __init__(self, **kwargs):
        if kwargs.get('spider_data', None) is None:
            kwargs['spider_data'] = {}
        super().__init__(**kwargs)


# class CrawlerLog(CustomModel):
#    # 单位：秒  抓取接收时间  octopus有效 工商有效
#    receive_ts: int = Field(alias='crawlerReceiveTs', default=-1)
#    # 单位：秒  抓取完成时间  octopus有效 工商有效
#    send_ts: int = Field(alias='crawlerSendTs', default=-1)
#
#    def __init__(self, **kwargs):
#        super().__init__(**kwargs)
#
#
# class ParserLog(CustomModel):
#    # 单位：秒  解析接收时间  octopus有效 工商有效
#    receive_ts: int = Field(alias='parserReceiveTs', default=-1)
#    # 单位：秒  解析完成时间  octopus有效 工商有效
#    send_ts: int = Field(alias='parserSendTs', default=-1)
#    # 变更信息 octopus有效 工商有效
#    ab_info: dict = Field(alias='abInfo', default=dict())
#
#    def __init__(self, **kwargs):
#        super().__init__(**kwargs)


class Eventlog(BaseEntity):
    # 去重id，正则判断其类型
    # ${cid}_${ts}  老的工商更新调度
    # clue_${clue_id}_${ts} 老的工商新增调度
    # octopus_${entry_name}_${entry_id}_${inst_name}_${ts} 统一调度id
    event_id: str
    # octopus有效 兼容工商 是否是新增调度 True/False
    is_clue: bool
    # octopus无效 兼容工商 cid
    cid: Optional[int] = Field(default=None, alias='companyId')

    # 抓取模块返回值 octopus有效 工商无效
    spider_code: SpiderCode = Field(default=SpiderCode.UNFILLED)

    # octopus无效 工商有效
    crawler_type: int = Field(alias='crawlerType', default=1)
    # 抓取模块返回值 octopus有效 工商有效 大部分枚举值octopus不使用， 参考枚举类
    crawler_code: int = Field(alias='crawlerCode', default=-1)
    # 解析模块返回值 octopus无效 工商有效 大部分枚举值octopus不使用， 参考枚举类
    parser_code: int = Field(alias='parserCode', default=-1)
    # 入库模块返回值 octopus无效 工商有效
    fusion_code: int = Field(alias='fusionCode', default=-1)

    # 优先级 octopus有效（值域0-999） 工商有效（值域无限制）
    # priority: float = Field(default=0.0)

    selector: SelectorLog
    spider: SpiderLog
    crawler: dict = Field(default=dict())  # octopus无效
    parser: dict = Field(default=dict())  # octopus无效
    fusion: dict = Field(default=dict())  # octopus无效
    dims: dict = Field(default=dict())  # octopus无效
    channel: dict = Field(default=dict())  # 渠道直接入库数据

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
