# encoding=utf8

import logging
from typing import List, Dict, Optional
from pydantic import Field
from entity.deps.entity import BaseEntity

logger = logging.getLogger(__name__)


class BinlogEntity(BaseEntity):
    data: List[Dict]
    database: str
    es: int
    id: int
    is_ddl: bool = Field(alias='isDdl')
    mysql_type: Dict = Field(alias='mysqlType')
    old: Optional[List[Dict]] = Field(default=None)
    pk_names: List[str] = Field(alias='pkNames')
    sql: str
    sql_type: Dict = Field(alias='sqlType')
    table: str
    ts: int
    type: str

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
