# encoding=utf8

import re
import logging
from functools import lru_cache
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import get_props_mysql
from typing import Optional, List

logger = logging.getLogger(__name__)


class ListedShareholder(BaseEntity):
    id: int = Field(default=0)
    gid: int = Field(alias='graphId')
    shareholder_gid: Optional[int] = Field(alias='shareholder_graphId', default=None)
    proportion: float

    def __init__(self, **kwargs):
        proportion = kwargs.get('proportion', None)
        if isinstance(proportion, str) and re.fullmatch(r'(\d+\.?\d*)%', proportion):
            kwargs['proportion'] = float(proportion[:-1]) * 0.01

        if 'shareholder_graphId' in kwargs:
            if kwargs['shareholder_graphId'] == '':
                kwargs['shareholder_graphId'] = None

        kwargs['graphId'] = int(kwargs['graphId'])
        super().__init__(**kwargs)


class ListedShareholderDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in get_props_mysql('mysql.tyc.zx.zhuanxiang_rds110').items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'data_listed_company.shareholder')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', ListedShareholder)
        super().__init__(**kwargs)

    def get_shareholder_list(self, gid: int) -> List[ListedShareholder]:
        sql = f"""select * from {self.db_tb_name} where graphId=%s and publishDate=(
            select max(publishDate) from {self.db_tb_name} where graphId=%s) and type=1 and isDeleted=0"""
        items: List[ListedShareholder] = []
        for d in self.mysql_client.select_many(sql, args=(str(gid), str(gid))):
            item = self._to_entity(d)
            if not item:
                continue
            items.append(item)
        return items

    @lru_cache(maxsize=50000)
    def is_listing_company(self, gid: int) -> bool:
        items = self.get_many_ex(values=[str(gid), 1, 0], fields=['graphId', 'type', 'isDeleted'])
        return any(item is not None for item in items)


if __name__ == '__main__':
    from libs.log import setup_logger
    logger = setup_logger()
    dao = ListedShareholderDao()
    ret = dao.is_listing_company(2218922)
    logger.info(f'{ret}')
