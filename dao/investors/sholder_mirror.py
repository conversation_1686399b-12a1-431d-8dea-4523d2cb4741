# encoding=utf8

import logging
from datetime import datetime
from pydantic import Field
from typing import Optional
from libs.env import get_props_mysql
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao

logger = logging.getLogger(__name__)


# 股东镜像数据
class SholderMirror(BaseEntity):
    id: int
    company_gid: int
    total: int
    oss_url: Optional[str]
    deleted: int = Field(default=0)
    create_time: datetime
    update_time: datetime

    def __init__(self, **kwargs):
        if 'oss_url' in kwargs and kwargs['oss_url'] == '':
            kwargs['oss_url'] = None
        super().__init__(**kwargs)


class SholderMirrorDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in get_props_mysql('mysql.tyc.other.rds231').items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_history_sholder_mirror')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', SholderMirror)
        super().__init__(**kwargs)


if __name__ == '__main__':
    from libs.log import setup_logger
    logger = setup_logger()
    dao = SholderMirrorDao(batch_size=1000)
    for o in dao.get_many(*********):
        logger.info('%s', o)
