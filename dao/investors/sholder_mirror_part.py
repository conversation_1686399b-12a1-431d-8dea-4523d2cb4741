# encoding=utf8

import logging
from copy import copy
from datetime import datetime, date
from pydantic import Field
from typing import Optional, List
from libs.env import get_props_mysql
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from dao.deps.mysql_sharding_dao import MySQLShardingDao
from gslib.gs_enum import EntityType

logger = logging.getLogger(__name__)


# 股东镜像数据
class SholderMirrorPart(BaseEntity):
    id: int = Field(default=0)
    company_graph_id: int
    changed_date: date = Field(alias='changed_time')
    shareholder_graph_id: int
    shareholder_name: Optional[str] = Field(default=None)
    shareholder_type: Optional[EntityType] = Field(default=None)
    amount: Optional[float] = Field(default=None)  # 认缴的出资额
    amount_unit: str = Field(default='')  # 认缴的出资额单位
    percent: Optional[float] = Field(default=None)  # percent
    source: Optional[str] = Field(default=None)  # 数据源可以是多个，由","分隔，它们可以是变更记录,年报,公司公示,工商公示,第三方数据'
    detailed_source: Optional[str] = Field(default=None)  # 详细数据来源，可以是多个，由","分隔，例如: 变更记录-股东-2016-08-30,年报-2016
    is_new: Optional[bool] = Field(default=None)  # 相较上一个股东列表是否是新增股东
    amount_increment: Optional[float] = Field(default=None)  # 出资额增量
    percent_increment: Optional[float] = Field(default=None)  # 持股比例增量
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)
    deleted: int = Field(default=0)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class SholderMirrorPartDao(MySQLShardingDao):
    def __init__(self, **kwargs):
        super().__init__(sharding_key='company_graph_id', pk_name='id', entity_class=SholderMirrorPart, **kwargs)

    # 给定v 给出 分库分表位置
    @classmethod
    def do_sharding(cls, v: int) -> int:
        return 0 * 10 + v % 10

    @classmethod
    def gen_dao_list(cls, **kwargs) -> List[MySQLDao]:
        dao_list = list()
        for tb_id in range(10):
            dao_args_copy = copy(kwargs)
            dao_list.append(
                MySQLDao(
                    **get_props_mysql('mysql.tyc.other.rds240'),
                    db_tb_name=f'strategy_algorithm.history_shareholders_mirror_{tb_id}',
                    **dao_args_copy,
                )
            )
        return dao_list


if __name__ == '__main__':
    from libs.log import setup_logger
    logger = setup_logger()
    dao = SholderMirrorPartDao(batch_size=1000)
    for o in dao.get_many(*********):
        logger.info('%s', o)
