# encoding=utf8

import logging
from datetime import date
from pydantic import Field
from typing import List, Optional
from libs.env import ConstantProps
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.dt import to_date
from gslib.gs_enum import EntityType

logger = logging.getLogger(__name__)


# 2.0股东信息
class InvestorSnap(BaseEntity):
    id: int = Field(default=0)
    gid: int
    investor_gid: int
    investor_name: str
    investor_type: EntityType
    subscript_detail: str
    actual_detail: str
    data_source: Optional[str] = Field(default=None)
    snap_date: date
    property1: Optional[str] = Field(default=None)


class InvestorSnapDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_NG.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.gsxt_investor_snap_copy')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', InvestorSnap)
        super().__init__(**kwargs)

    # 删除所有股东，并返回股东数量
    def remove_by_gid(self, gid) -> int:
        items: List[InvestorSnap] = list(self.get_many(gid, 'gid'))
        for item in items:
            self.delete(item.id)
        logger.info(f'gid={gid} count={len(items)}')
        return len(items)

    # 批量更新股东id
    def update_investor_gid(self, gid_prop2, gid):
        sql = f'update {self.db_tb_name} set investor_gid=%s where investor_gid=%s'
        ret = self.mysql_client.execute(sql, args=(gid, gid_prop2))
        if ret > 0:
            logger.info(f'gid {gid_prop2}->{gid} count={ret}')

    # 批量更新股东id
    def update_investor_gid_type3(self, gid):
        sql = f'update {self.db_tb_name} set investor_gid=0 where gid=%s and investor_type=3'
        ret = self.mysql_client.execute(sql, args=(gid, ))
        if ret > 0:
            logger.info(f'gid={gid} count={ret}')

    def add(self, gid, investor_gid, investor_name, investor_type, subscript_detail, actual_detail, data_source, snap_date):
        sql = f'insert ignore into {self.db_tb_name} (gid, investor_gid, investor_name, investor_type, subscript_detail, actual_detail, data_source, snap_date) values(%s, %s, %s,%s, %s, %s,%s, %s)'
        ret = self.mysql_client.execute(sql, args=(gid, investor_gid, investor_name, investor_type, subscript_detail, actual_detail, data_source, snap_date))
        return ret == 1


if __name__ == '__main__':
    from libs.log import setup_logger
    logger = setup_logger()
    dao = InvestorSnapDao()
    items = list(dao.get_many_ex(values=[5976220292, to_date('2023-03-13')], fields=['gid', 'snap_date']))
    items[0].investor_gid = 1
    # items[1].investor_name = '张山'
    logger.info(f'items={items}')
    dao.save_by_group(
        items=items,
        group_fields=['gid', 'snap_date'],
        key_fields=['investor_gid', ]
    )
