# encoding=utf8

import logging
from datetime import datetime
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from gslib.gs_enum import EntityType
from typing import Optional, List

logger = logging.getLogger(__name__)


class EquityRatio(BaseEntity):
    id: int = Field(default=0)
    company_graph_id: int
    company_name: Optional[str] = Field(default=None)
    company_name_alias: str = Field(default='')
    shareholder_graph_id: int
    shareholder_name: Optional[str] = Field(default=None)
    shareholder_name_alias: str = Field(default='')
    shareholder_type: EntityType

    amount: Optional[float] = Field(default=None)
    capital: Optional[str] = Field(default=None)
    capital_actl: Optional[str] = Field(default=None, alias='capitalActl')
    percent: Optional[float] = Field(default=None)
    source: Optional[int] = Field(default=None)
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)
    deleted: int = Field(default=0)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class EquityRatioDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.equity_ratio')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', EquityRatio)
        super().__init__(**kwargs)

    def update_shareholder_id(self, gid_old: int, gid_new) -> int:
        sql = 'update ignore {} set shareholder_graph_id=%s where shareholder_graph_id=%s limit 128'.format(self.db_tb_name)
        ret = self.mysql_client.execute(sql, args=(gid_new, gid_old))
        return ret


if __name__ == '__main__':
    from libs.log import setup_logger
    logger = setup_logger()
    dao = EquityRatioDao(**ConstantProps.PROPS_GS_TEST)
    items: List[EquityRatio] = list(dao.get_many(2, 'company_graph_id'))
    items[0].shareholder_name = 'wbx'

    item = EquityRatio.from_dict(
        {

        }
    )
    items = [item, item]
    # items[0].id = 0

    ret = dao.save_by_group(items, ['company_graph_id', ], ['shareholder_type', 'shareholder_graph_id'], save_mode=0)
    logger.info(f'{ret}')
