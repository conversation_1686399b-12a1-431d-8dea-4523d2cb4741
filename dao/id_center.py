# encoding=utf8


import logging
import hashlib
import copy
from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao, PKType
from dao.deps.mysql_sharding_dao import MySQLShardingDao
from entity.deps.entity import BaseEntity
from pydantic import Field
from typing import List, Optional
from dao.deps.mysql_dao import EntityType

logger = logging.getLogger(__name__)


class CreditCodeIndex(BaseEntity):
    id: int
    credit_code: str
    company_id: int
    deleted: int = Field(default=0)


class CreditCodeIndexDao(MySQLShardingDao):
    def __init__(self, **kwargs):
        super().__init__(sharding_key='credit_code', **kwargs)

    # 给定v 给出 分库分表位置
    @classmethod
    def do_sharding(cls, v: str) -> int:
        digest = hashlib.md5(v.encode('utf-8')).hexdigest()
        return int(digest[-2], 16) * 16 + int(digest[-1], 16)

    @classmethod
    def gen_dao_list(cls, **kwargs) -> List[MySQLDao]:
        dao_list = list()
        for db_id in range(16):
            for tb_id in range(16):
                db_tb_name = f'idcenter_index_{hex(db_id)[-1]}.credit_code_new_index_{hex(tb_id)[-1]}'
                dao_list.append(
                    MySQLDao(
                        **ConstantProps.PROPS_GS_INNER_RW,
                        db_tb_name=db_tb_name,
                        **copy.copy(kwargs),
                    )
                )
        return dao_list

    def get_by_pk(self, pk: PKType, raw=False) -> Optional[EntityType]:
        dao = self.mysql_dao_list[self.do_sharding(pk)]
        return dao.get(pk, raw=raw)


class RegNumberIndexDao(MySQLShardingDao):
    def __init__(self, **kwargs):
        super().__init__(sharding_key='reg_number', **kwargs)

    # 给定v 给出 分库分表位置
    @classmethod
    def do_sharding(cls, v: str) -> int:
        digest = hashlib.md5(v.encode('utf-8')).hexdigest()
        return int(digest[-2], 16) * 16 + int(digest[-1], 16)

    @classmethod
    def gen_dao_list(cls, **kwargs) -> List[MySQLDao]:
        dao_list = list()
        for db_id in range(16):
            for tb_id in range(16):
                db_tb_name = f'idcenter_index_{hex(db_id)[-1]}.reg_number_new_index_{hex(tb_id)[-1]}'
                dao_list.append(
                    MySQLDao(
                        **ConstantProps.PROPS_GS_INNER_RW,
                        db_tb_name=db_tb_name,
                        **copy.copy(kwargs),
                    )
                )
        return dao_list


if __name__ == '__main__':
    from libs.log import setup_logger

    logger = setup_logger()
    credit_code_index_dao = CreditCodeIndexDao()
    reg_number_index_dao = RegNumberIndexDao()
    logger.info(f'{credit_code_index_dao.get("92230110MMMMMM236F")}')
    logger.info(f'{credit_code_index_dao.get("91310114MAD4R0TY4Q")}')
    logger.info(f'{reg_number_index_dao.get("110105017402165")}')
