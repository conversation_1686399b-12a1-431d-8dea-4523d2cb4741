from datetime import datetime
from typing import Optional
from pydantic import Field, conint

from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps


class SolverMinuteCount(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    item_name: Optional[str] = Field(default='')
    inst_name: Optional[str] = Field(default='')
    spider_code: Optional[int] = Field(default='')
    is_clue: Optional[int] = Field(default='')
    reason: Optional[str] = Field(default='')
    minute: Optional[int] = Field(default='')
    count: Optional[int] = Field(default='')

    def logic_validate(self) -> bool:
        return True


class SolverMinuteCountDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'octopus.solver_minute_count')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', SolverMinuteCount)
        super().__init__(**kwargs)

    def select(self, *args, **kwargs):
        if args[3] == 'XA':
            sql = "SELECT * FROM {} WHERE minute BETWEEN %s AND %s and (spider_code=0 or spider_code=11) and item_name=%s and inst_name=%s".format(
                self.db_tb_name)
        else:
            sql = "SELECT * FROM {} WHERE minute BETWEEN %s AND %s and spider_code=0 and item_name=%s and inst_name=%s".format(
                self.db_tb_name)
        num = 0
        for d in self.mysql_client.select_many(sql, args):
            num += d.get('count') if d.get('count') else 0
        return num


if __name__ == '__main__':
    d = SolverMinuteCountDao(**ConstantProps.PROPS_GS_TEST).get(9, 'id')
    print(d)
