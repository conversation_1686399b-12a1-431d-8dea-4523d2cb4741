# encoding=utf8

import enum
import logging
from typing import Optional, List
from datetime import datetime
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class TaskType(enum.IntEnum):
    BRNO_HK = 3  # 香港公司商业编码 或者 公司注册码
    CHINA_NPO = 50  # 社会组织总站更新
    CREDIT_CHINA = 10  # 信用中国更新双公示
    GDS = 67  # 经营商品
    CODS = 69
    ACFTU = 70
    ICP = 11
    GSXT_GJ_PLUGIN = 90  # 总局插件更新
    INSTITUTION_GJ = 91  # 事业单位在线
    LAW_FIRM_GJ = 92  # 律所

    HIDE_REPORT_PHONE = 4
    HIDE_REPORT_POSTAL = 7

    OTHERS = 0  # 不处理的类型


class TaskStatus(str, enum.Enum):
    CREATE = '创建'
    EXECUTE = '执行'
    FAILED = '失败'
    SUCCESS = '成功'


# 整合平台任务表
class PlatformTask(BaseEntity):
    id: int  # 自增id
    group_id: str  # 任务组id
    task_type: TaskType
    belong: str
    param: str  # 用户输入
    status: TaskStatus = Field(default=TaskStatus.CREATE)
    record: str = Field(default='')
    update_time: datetime = Field(default_factory=datetime.now)

    def __init__(self,  **kwargs):
        if 'task_type' in kwargs:
            if kwargs['task_type'] in list(x.value for x in TaskType):
                kwargs['task_type'] = TaskType(kwargs['task_type'])
            else:
                kwargs['task_type'] = TaskType.OTHERS
        super().__init__(**kwargs)


class PlatformTaskDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_INNER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'internal.platform_task')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', PlatformTask)
        super().__init__(**kwargs)

    def set_task(self, task_id, status: TaskStatus, record=''):
        sql = f'update ignore {self.db_tb_name} set status=%s, record=%s where id=%s'
        ret = self.mysql_client.execute(sql, args=(status.value, record, task_id))
        logger.info(f'task_id={task_id} status={status} record={record} ret={ret}')
        return ret == 1

    def get_tasks(self, limit=10000) -> List[PlatformTask]:
        sql = f'select * from {self.db_tb_name} where status in ("创建", "执行") order by id desc limit %s'
        items: List[PlatformTask] = []
        for d in self.mysql_client.select_many(sql, args=(limit, )):
            item = self._to_entity(d)
            if item is not None:
                items.append(item)
        return items


if __name__ == '__main__':
    from libs.log2 import setup_logger
    logger = setup_logger()
    dao = PlatformTaskDao()
    for task in dao.get_tasks(limit=50):
        logger.info(f'task={task}')
