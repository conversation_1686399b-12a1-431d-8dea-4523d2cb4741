# encoding=utf8

import json
import enum
import logging
from datetime import datetime
from typing import Optional, Any
from pydantic import Field
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao

logger = logging.getLogger(__name__)


class ItemFlag(int, enum.Enum):
    OK = 0  # 正常
    DELETED = 1  # 删除
    FORBIDDEN = 2  # 屏蔽 和删除目前效果一样，后面再根据需求做区分
    CLUE_DUP = 3  # 新增有效 重复线索
    CLUE_BAD = 4  # 错误的线索格式
    CLUE_DONE = 5  # 新增有效 抓取到数据 对应的entry_id 在 info 中
    CLUE_EXPIRE = 6  # 新增有效，线索过期，一般7天


# 调度单元 分为 entry/clue
class Item(BaseEntity):
    id: int = Field(default=0)  # 自增id 业务上不使用
    word: str  # 调度单元唯一标识 可以是搜索词，对于company表 可以是cid 具有唯一性
    info: Optional[dict] = Field(default=None)  # json 用于存储实体其他信息
    item_index: str = Field(default='')  # 可筛选索引 用于业务 默认是'' 有些表没有此字段 需要可以默认加
    code: int  # 随机索引因子int 用于调度遍历 具有唯一性
    flag: ItemFlag = Field(default=ItemFlag.OK)  # 用于标记有效性 无索引
    realtime: int = Field(default=0)  # 时效性索引标记
    # inst_map: Optional[Dict[str, Inst]] = Field(default=None)  # json 用于存储调度实例信息
    create_time: datetime = Field(default_factory=datetime.now)  # 创建时间 数据库自动写入
    update_time: datetime = Field(default_factory=datetime.now)  # 更新时间 数据库自动写入

    def __init__(self,  **kwargs):
        if 'item_index' not in kwargs:
            kwargs['item_index'] = ''
        if 'info' in kwargs and isinstance(kwargs['info'], str):
            kwargs['info'] = json.loads(kwargs['info'])
        super().__init__(**kwargs)


class ItemDao(MySQLDao):
    def __init__(self, name: str, is_clue: bool, **kwargs):
        for k, v in ConstantProps.PROPS_GS_INNER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', f'octopus.{"clue" if is_clue else "entry"}_{name}')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', Item)
        super().__init__(**kwargs)

    def set_info(self, word, do_update=True, **info) -> bool:
        item: Item = self.get(value=word, field='word')
        if not item:
            logger.warning(f'no item for word={word}')
            return False
        if item.info is None:
            item.info = dict()
        changed = False
        for k, v in info.items():
            if k not in item.info or (item.info[k] != v and do_update):
                item.info[k] = v
                changed = True
        if changed:
            updated, inserted, pk = self.save_by_cmp(item=item, fields=['word', ])
            return updated and not inserted
        return False

    def get_info(self, word, key) -> Optional[Any]:
        item: Item = self.get(value=word, field='word')
        if not item:
            logger.warning(f'no item for word={word}')
            return None
        return (item.info or {}).get(key, None)


if __name__ == '__main__':
    from libs.log2 import setup_logger
    logger = setup_logger()
    dao = ItemDao(name='company', is_clue=False)
    # ret = dao.set_info(word='5', flag1=True, flag2=3.1, flag3='abc', flag4=-1)
    ret = dao.get_info(word='5', key='flag2')
    logger.info(f'{ret}')
