from datetime import datetime, date
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
import json
from pydantic import Field, conint
from typing import Optional, TypeVar, Dict
import re
import random

EntityType = TypeVar('EntityType', BaseEntity, Dict)


class CharityEntity(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    charity_name: Optional[str] = Field(default='')
    certificate_code: Optional[str] = Field(default='')
    establish_date: date = Field(default=date(1970, 1, 1))
    department: Optional[str] = Field(default='')
    pretax_deduction_eligibility: int = Field(default=0)
    pretax_deduction_eligibility_validity_from: Optional[str] = Field(default='')
    pretax_deduction_eligibility_validity_to: Optional[str] = Field(default='')
    public_raise_eligibility: int = Field(default=0)
    register_date: date = Field(default=date(1970, 1, 1))
    address: Optional[str] = Field(default='')
    business_scope: Optional[str] = Field(default='')
    email: Optional[str] = Field(default='')
    fax: Optional[str] = Field(default='')
    website: Optional[str] = Field(default='')
    weibo: Optional[str] = Field(default='')
    telephone: Optional[str] = Field(default='')
    wechat: Optional[str] = Field(default='')
    network_platform: Optional[str] = Field(default='')
    legal: Optional[str] = Field(default='')
    people: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)
    deleted: int = Field(default=0)


class CharityDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.charity')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CharityEntity)
        super().__init__(**kwargs)
