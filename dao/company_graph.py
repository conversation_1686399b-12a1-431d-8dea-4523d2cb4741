# encoding=utf8

from typing import Optional
from pydantic import Field, conint
import logging
from datetime import datetime, date, timedelta
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from libs.collection import sub_if
from gslib.reg_number import reg_number_valid
from gslib.credit_code import credit_code_valid
from gslib.gs_enum import EntityType

logger = logging.getLogger(__name__)


class CompanyGraph(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=None)
    cid: conint(strict=True, ge=0) = Field(alias='company_id', default=None)
    cgid: conint(strict=True, ge=0) = Field(alias='graph_id', default=None)
    deleted: int = Field(default=0)

    def logic_validate(self) -> bool:
        return True


class CompanyGraphDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_graph')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanyGraph)
        super().__init__(**kwargs)

    def set_deleted(self, cid, v=1) -> bool:
        sql = 'update ignore {} set deleted=%s where company_id=%s and deleted=0'.format(self.db_tb_name)
        ret = self.mysql_client.execute(sql, args=(v, cid,))
        logger.info('set deleted cid=%s ret=%s', cid, ret)
        return ret > 0

    def set_un_deleted(self, cid) -> bool:
        sql = 'update ignore {} set deleted=0 where company_id=%s and deleted!=0'.format(self.db_tb_name)
        ret = self.mysql_client.execute(sql, args=(cid,))
        if ret > 0:
            logger.info('set un deleted cid=%s ret=%s', cid, ret)
        return ret > 0

    def get_by_cid(self, cid, ) -> Optional[CompanyGraph]:
        sql = f'select * from {self.db_tb_name} where company_id=%s and deleted=0'
        for d in self.mysql_client.select_many(sql, args=(cid,)):
            item = self._to_entity(d)
            if item is not None:
                return item
        return None

    def get_by_gid(self, gid, ) -> Optional[CompanyGraph]:
        sql = f'select * from {self.db_tb_name} where graph_id=%s and deleted=0'
        for d in self.mysql_client.select_many(sql, args=(gid,)):
            item = self._to_entity(d)
            if item is not None:
                return item
        return None


if __name__ == '__main__':
        logger.info('x=%s', 's')
