# encoding=utf8

import re
import time
import logging
from abc import abstractmethod
from datetime import datetime, timedelta
from typing import List, Optional, Generator, Tuple
from concurrent.futures.thread import ThreadPoolExecutor
from libs.concurrent import BoundedExecutor
from libs.dt import to_datetime, datetime2str
from clients.redis.redis_hash import RedisHash
from dao.deps.mysql_dao import EntityType
from dao.deps.mysql_dao import MySQLDao, PKType, Type

logger = logging.getLogger(__name__)


# 分库分表的 MySQLDao
class MySQLShardingDao(object):
    def __init__(self, sharding_key, pk_name=None, entity_class: Type[EntityType] = None, **kwargs):
        self.sharding_key = sharding_key
        self.pk_name = pk_name or sharding_key
        self.entity_class = entity_class
        self.mysql_dao_list: List[MySQLDao] = self.gen_dao_list(pk_name=pk_name, entity_class=entity_class, **kwargs)

    # 给定v 给出 分库分表位置
    @classmethod
    @abstractmethod
    def do_sharding(cls, v) -> int:
        pass

    @classmethod
    @abstractmethod
    def gen_dao_list(cls, **kwargs) -> List[MySQLDao]:
        pass

    def get(self, value, field=None, sharding_value=None, raw=False) -> Optional[EntityType]:
        sharding_value = sharding_value or value
        field = field or self.sharding_key
        dao = self.mysql_dao_list[self.do_sharding(sharding_value)]
        return dao.get(value, field, raw=raw)

    def get_ex(self, values: List, fields: List[str], sharding_value=None, raw=False) -> Optional[EntityType]:
        sharding_value = sharding_value or values[0]
        dao = self.mysql_dao_list[self.do_sharding(sharding_value)]
        return dao.get_ex(values, fields, raw=raw)

    def get_many(self, value, field=None, sharding_value=None, raw=False) -> Generator[EntityType, None, None]:
        sharding_value = sharding_value or value
        field = field or self.sharding_key
        dao = self.mysql_dao_list[self.do_sharding(sharding_value)]
        return dao.get_many(value, field, raw=raw)

    def scan(self, part_id: int, **kwargs) -> Generator[EntityType, None, None]:
        if part_id < 0 or part_id >= len(self.mysql_dao_list):
            raise RuntimeError('bad part_id={} {}'.format(part_id, self))
        for x in self.mysql_dao_list[part_id].scan(**kwargs):
            yield x

    def scan_iter(self, part_id: int, process_pool, process_fn, *process_args, **kwargs) -> Tuple[Optional[PKType], List[EntityType]]:
        if part_id < 0 or part_id >= len(self.mysql_dao_list):
            raise RuntimeError('bad part_id={} {}'.format(part_id, self))
        offset, items = self.mysql_dao_list[part_id].scan_iter(**kwargs)
        process_pool.submit(process_fn, items, *process_args)
        return offset, items

    # 目前只支持 int 或者 datetime
    @staticmethod
    def _sharding_scan_get_redis_offset(part_id, redis_offset: RedisHash = None) -> Optional[PKType]:
        if not redis_offset:
            return None
        offset_str = redis_offset.get('%.5d' % part_id)
        if offset_str:
            if re.fullmatch(r'\d+', offset_str):
                return int(offset_str)
            offset_dt = to_datetime(offset_str)
            if offset_dt is not None:
                return offset_dt
            raise RuntimeError('error read offset from redis {}'.format(offset_str))
        return None

    @staticmethod
    def _sharding_scan_write_redis_offset(part_id, redis_offset: RedisHash, offset):
        if not redis_offset:
            return None
        if isinstance(offset, datetime):
            offset = datetime2str(offset)
        else:
            offset = int(offset)
        redis_offset.set('%.5d' % part_id, offset)

    # 读取分库分表数据，并批量返回
    def sharding_scan(
            self,
            process_fn,
            *process_args,
            process_workers: int = 1,  # 处理线程数
            scan_workers: int = 1,  # 读取线程数
            part_num: int = None,  # 只读取part个分表
            redis_offset: RedisHash = None,  # 偏移量redis存储 None表示不存储
            init_offset=None,  # 优先使用redis 其次才使用init_offset
            infinite_wait_secs: int = 0,  # 0表示取完数立即退出，否则等待
            **kwargs,  # 参考MySQLShardingDao.scan
    ):
        with (ThreadPoolExecutor(max_workers=scan_workers, thread_name_prefix='scan') as scan_pool,
              BoundedExecutor(max_workers=process_workers, thread_name_prefix='process') as process_pool):
            future_info = {}
            for part_id in range(part_num or len(self.mysql_dao_list)):
                if init_offset is not None:
                    if self._sharding_scan_get_redis_offset(part_id, redis_offset) is None:
                        self._sharding_scan_write_redis_offset(part_id, redis_offset, init_offset)
                offset = self._sharding_scan_get_redis_offset(part_id, redis_offset)
                if offset is None:
                    logger.warning('no init_offset and redis offset')
                else:
                    future = scan_pool.submit(
                        self.scan_iter,
                        part_id=part_id,
                        process_pool=process_pool,
                        process_fn=process_fn,
                        start=offset,
                        *process_args,
                        **kwargs,
                    )
                    future_info[future] = [part_id, datetime.min]

            while len(future_info) > 0:
                logger.debug(f'future_info count={len(future_info)}')
                changed = False
                for future in list(future_info.keys()):
                    part_id, done_ts = future_info[future]
                    if future.done():
                        if done_ts == datetime.min:
                            future_info[future][1] = datetime.now()
                            changed = True
                            offset_next, items = future.result()
                            if offset_next is not None:  # 有效的offset 需要同步
                                self._sharding_scan_write_redis_offset(part_id, redis_offset, offset_next)
                            if offset_next is None:
                                if infinite_wait_secs == 0:
                                    del future_info[future]
                                    continue
                                else:  # 需要无限scan且达到offset最大值状态 则wait
                                    continue
                            # 需要立即执行的任务
                            del future_info[future]
                            start = self._sharding_scan_get_redis_offset(part_id, redis_offset)
                            logger.info(f'new task part_id={part_id} start={start}')
                            future_next = scan_pool.submit(
                                self.scan_iter,
                                part_id=part_id,
                                process_pool=process_pool,
                                process_fn=process_fn,
                                start=start,
                                *process_args,
                                **kwargs,
                            )
                            future_info[future_next] = [part_id, datetime.min]
                            continue
                        if done_ts + timedelta(seconds=infinite_wait_secs) < datetime.now():
                            del future_info[future]
                            start = self._sharding_scan_get_redis_offset(part_id, redis_offset)
                            logger.info(f'new task part_id={part_id} start={start}')
                            future_next = scan_pool.submit(
                                self.scan_iter,
                                part_id=part_id,
                                process_pool=process_pool,
                                process_fn=process_fn,
                                start=start,
                                *process_args,
                                **kwargs,
                            )
                            future_info[future_next] = [part_id, datetime.min]

                if not changed:
                    logger.info(f'no future changed, sleep for ...')
                    time.sleep(5)
