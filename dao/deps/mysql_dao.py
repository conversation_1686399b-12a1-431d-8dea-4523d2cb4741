# encoding=utf8

import json
import time
import logging
from enum import Enum
from typing import Type, Dict, TypeVar, Optional, Generator, List, Tuple
from datetime import date, datetime
from pymysql import ProgrammingError
from libs.collection import split_parts
from libs.dt import to_datetime, to_date
from clients.mysql_client import MySQLClient
from entity.deps.entity import BaseEntity

logger = logging.getLogger(__name__)

EntityType = TypeVar('EntityType', BaseEntity, Dict)
PKType = TypeVar('PKType', int, str, datetime, date)


# MySQLDao：对应一张具体的物理表
# 只有一次SQL的方法放到dao层，多个SQL构成事务放到service层
class MySQLDao(object):
    """ 在做更新 或者 插入时忽略的列"""
    ignore_fields = ['id', 'update_time', 'create_time', 'updatetime', 'createTime']

    def __init__(
            self,
            db_tb_name: str,
            pk_name: str = 'id',
            batch_size: int = 2000,
            entity_class: Type[EntityType] = None,
            **kwargs,
    ):
        self.db_tb_name: str = db_tb_name  # 指定库表名称
        self.pk_name: str = pk_name  # 主键
        self.batch_size: int = batch_size  # 批量读取数据的大小
        self.entity_class: Type[EntityType] = entity_class or dict  # 实体类
        self.mysql_client = MySQLClient(**kwargs)

    def _to_entity(self, d: Optional[Dict], raw: bool = False) -> Optional[EntityType]:
        if d is None:
            return None
        if self.entity_class is dict or raw:
            return d
        return self.entity_class.from_dict(d)

    def get(self, value, field: str = None, raw=False, ) -> Optional[EntityType]:
        field = field or self.pk_name
        sql = f'select * from {self.db_tb_name} where {field} = %s limit 1'
        d = self.mysql_client.select(sql, args=(value,))
        return self._to_entity(d, raw=raw)

    def get_many(self, value, field: str = None, raw=False) -> Generator[EntityType, None, None]:
        field = field or self.pk_name
        eq = 'is' if value is None else '='
        sql = f'select * from {self.db_tb_name} where {field} {eq} %s limit %s'
        for d in self.mysql_client.select_many(sql, args=(value, self.batch_size)):
            item = self._to_entity(d, raw=raw)
            if item is not None:
                yield item

    def get_many_batch(self, values: List[PKType], field: str = None, condition=None, raw=False) -> Generator[EntityType, None, None]:
        field = field or self.pk_name
        condition = ' ' if condition is None else f' and {condition}'
        for value_part in split_parts(values, self.batch_size):
            in_value = '(' + ','.join('"{}"'.format(x) for x in value_part) + ')'
            sql = f'select * from {self.db_tb_name} where {field} in {in_value} {condition}'
            for d in self.mysql_client.select_many(sql):
                item = self._to_entity(d, raw=raw)
                if item is not None:
                    yield item

    def get_ex(self, values: List, fields: List[str], raw=False) -> Optional[EntityType]:
        if len(fields) == 0 or len(fields) != len(values):
            logger.warning(f'error fields={fields} values={values}')
            return
        sql_where = ' and '.join(f'{field}=%s' for field in fields)
        sql = f'select * from {self.db_tb_name} where {sql_where}'
        d = self.mysql_client.select(sql, args=values)
        return self._to_entity(d, raw=raw)

    def get_many_ex(self, values: List, fields: List[str], raw=False) -> Generator[EntityType, None, None]:
        if len(fields) == 0 or len(fields) != len(values):
            logger.warning(f'error fields={fields} values={values}')
            return
        sql_where = ' and '.join(f'{field}=%s' for field in fields)
        sql = f'select * from {self.db_tb_name} where {sql_where} limit {self.batch_size}'
        for d in self.mysql_client.select_many(sql, args=values):
            item = self._to_entity(d, raw=raw)
            if item is not None:
                yield item

    def get_max_pk(self) -> PKType:
        sql = 'select max({}) from {}'.format(self.pk_name, self.db_tb_name)
        d = self.mysql_client.select(sql)
        return d['max({})'.format(self.pk_name)]

    @staticmethod
    def _get_entity_real_value(v_):
        if isinstance(v_, Enum):
            return v_.value
        if isinstance(v_, dict):
            if len(v_) == 0:
                return None
            return json.dumps(v_, ensure_ascii=False)
        return v_

    @staticmethod
    def _cmp_equal_entity(k, v1, v2, cmp_func):
        v1_ = cmp_func(v1) if cmp_func else v1
        v2_ = cmp_func(v2) if cmp_func else v2

        if isinstance(v1_, datetime) and isinstance(v2_, str):
            v2_ = to_datetime(v2_)
        elif isinstance(v1_, date) and isinstance(v2_, str):
            v2_ = to_date(v2_)
        elif isinstance(v1_, str) and isinstance(v2_, datetime):
            v1_ = to_datetime(v1_)
        elif isinstance(v1_, str) and isinstance(v2_, date):
            v1_ = to_date(v1_)

        equal = (v2_ == '' or v1_ == v2_)
        if v1 != v2 and equal:
            logger.info(f'equal by mode k={k} [{v1}({v1_})] -> [{v2}({v2_})]')
        return equal

    def save_by_cmp(
            self,
            item: EntityType,  # 变更后的值
            item_old_read_db=True,  # 是否从数据库读取item_old
            item_old: EntityType = None,  # 当item_old_read_db=False有效
            fields=None,
            ignore_fields=None,
            only_insert_fields=None,  # 只insert 不update的字段列表
            ignore_update_to_none=True,  # 忽略更新为none
            ignore_update_from_not_none=False,  # 忽略非none的更新
            cmp_func=None,  # 对比字段加工函数
    ) -> Tuple[bool, bool, int]:  # 返回值是 是否变更流量  是否insert item.id
        if 'id' not in self.entity_class.model_fields:
            raise RuntimeError(f'need auto increase id field {self.entity_class}')
        if not isinstance(item, self.entity_class):
            raise RuntimeError(f'bad type {item} -> {self.entity_class}')
        item_new = item.to_dict() if isinstance(item, BaseEntity) else item
        fields = fields or [self.pk_name, ]
        values = []
        for field in fields:
            if field not in item_new:
                raise RuntimeError(f'no fields={fields} in item_new={item_new}')
            values.append(item_new[field])

        only_insert_fields = only_insert_fields or []
        ignore_fields = ignore_fields or self.ignore_fields
        keys, values_new, values_old = [], [], []

        if item_old_read_db:
            item_old = self.get_ex(values=values, fields=fields, raw=True)
        item_old = item_old.to_dict() if isinstance(item_old, BaseEntity) else item_old
        # logger.info(f'{item_old} {values} {fields}')

        for k, value_new in item_new.items():
            if k in ignore_fields:
                continue
            value_old = (item_old or {}).get(k, None)
            if ignore_update_to_none and value_new is None:
                continue
            if k in only_insert_fields and k in (item_old or {}):
                continue
            if ignore_update_from_not_none and value_old not in [None, '', 0]:
                logger.info(f'ignore update from not none {k} {value_old} {value_new}')
                continue
            if not self._cmp_equal_entity(k, value_old, value_new, cmp_func):
                # logger.info(f'diff {k} {value_old} {value_new}')
                keys.append(k)
                values_new.append(self._get_entity_real_value(value_new))
                values_old.append(self._get_entity_real_value(value_old))
        diff_info = '#'.join(f'{keys[i]}:{values_old[i]}->{values_new[i]}' for i in range(len(keys)))

        logger.info(f'{self.__class__.__name__} insert={item_old is None} keys={fields}:{values} diff={diff_info}')

        # return False, False, 0
        if len(keys) == 0:
            # logger.info(f'no field to save {item}')
            return False, False, item_old['id']

        if item_old is None:
            sql_sets = ', '.join(f'{key}=%s' for key in keys)
            sql = f'insert ignore into {self.db_tb_name} set {sql_sets} '
            row_id = self.mysql_client.insert(sql, args=values_new)
            return (row_id > 0), (row_id > 0), row_id
        else:
            sql_sets = ', '.join(f'{key}=%s' for key in keys)
            sql_condition = ' and '.join(f'{field}=%s' for field in fields)
            sql = f'update ignore {self.db_tb_name} set {sql_sets} where {sql_condition} limit 1'
            affect_rows = self.mysql_client.execute(sql, args=values_new + values)
            return affect_rows == 1, False, item_old['id']

    # 根据 uniq_index (key0, key1, key2, ...) 进行 deleted/update/insert操作
    # uniq_index 分为 group_fields + key_fields 都不能为空
    def save_by_group(
            self,
            items: List[BaseEntity],
            group_fields: List[str],
            key_fields: List[str],
            save_mode=0,  # 0 item可以更新，1 item删除&插入 2 items删除&插入, 3 items_old需要为空
            group_values: List = None,  # 为空时，通过items
    ) -> Tuple[int, int, int]:
        delete_count, update_count, insert_count = 0, 0, 0
        if self.pk_name != 'id':
            raise RuntimeError('pk_name should be id')
        group_data: Dict[Tuple, List] = dict()
        for item in items:
            if not isinstance(item, self.entity_class):
                raise RuntimeError(f'bad type {item} -> {self.entity_class}')
            item2 = item.to_dict() if isinstance(item, BaseEntity) else item
            # logger.info(f'item2={item2}')
            group_values_tmp = list(item2[group_field] for group_field in group_fields)
            if group_values is None:
                group_values = group_values_tmp
            elif any(group_values[i] != group_values_tmp[i] for i in range(len(group_fields))):
                raise RuntimeError(f'{self.__class__.__name__} diff group_values {group_values} / {group_values_tmp}')
            uniq_values = tuple(item2[field] for field in group_fields + key_fields)
            if uniq_values in group_data:
                logger.warning(f'dup uniq_values={uniq_values}')
            else:
                group_data[uniq_values] = [None, item, None]

        items_old = list(self.get_many_ex(values=group_values, fields=group_fields))
        if save_mode == 3 and len(items_old) > 0:
            logger.info(f'{self.__class__.__name__} {group_fields} {key_fields} {group_values} d={delete_count} u={update_count} i={insert_count}')
            return delete_count, update_count, insert_count
        for item in items_old:
            item2 = item.to_dict()
            uniq_values = tuple(item2[field] for field in group_fields + key_fields)
            if uniq_values not in group_data:
                group_data[uniq_values] = [item, None, None]
            else:
                group_data[uniq_values][0] = item

        # 判断 has_change 以及判断是否相等
        has_change = False
        for uniq_values in group_data.keys():
            item_old, item_new, diff = group_data[uniq_values][0], group_data[uniq_values][1], False
            if item_old is None or item_new is None:
                has_change = True
                group_data[uniq_values][2] = True
                continue
            for field in item_old.model_fields:
                if field in self.ignore_fields:
                    continue
                if getattr(item_old, field) != getattr(item_new, field):
                    diff = True
            if diff:
                has_change = True
            group_data[uniq_values][2] = diff

        for item_old, item_new, diff in group_data.values():
            # logger.info(f'{item_old} {item_new} {diff}')
            if not has_change:
                pass  # do nothing
            elif save_mode == 2:
                if item_old:
                    delete_count += 1
                    self.delete(item_old.id)
                if item_new:
                    insert_count += 1
                    self.save_by_cmp(item=item_new, item_old_read_db=False, fields=group_fields + key_fields)
            elif save_mode == 1:
                if diff:
                    if item_old:
                        delete_count += 1
                        self.delete(item_old.id)
                    if item_new:
                        insert_count += 1
                        self.save_by_cmp(item=item_new, item_old_read_db=False, fields=group_fields + key_fields)
            elif diff:
                if item_old is None:
                    insert_count += 1
                    self.save_by_cmp(item=item_new, item_old_read_db=False, fields=group_fields + key_fields)
                elif item_new is None:
                    delete_count += 1
                    self.delete(item_old.id)
                else:
                    update_count += 1
                    item_new.id = item_old.id
                    self.save_by_cmp(item=item_new, item_old_read_db=False, item_old=item_old, fields=group_fields + key_fields)
        logger.info(f'{self.__class__.__name__} {group_fields} {key_fields} {group_values} d={delete_count} u={update_count} i={insert_count}')
        return delete_count, update_count, insert_count

    def delete(self, pk: PKType) -> bool:
        sql = 'delete from {} where {}=%s limit 1'.format(self.db_tb_name, self.pk_name)
        affect_rows = self.mysql_client.execute(sql, args=(pk,))
        return affect_rows == 1

    # 根据索引循环遍历数据， 基于scan_iter
    def scan(
            self,
            start=None,  # 遍历起始位置 exclusive
            scan_key=None,  # 遍历键值, 默认采用pk
            total=None,  # 总共取数量，不加则无限制
            infinite=False,  # 是否循环等待
            infinite_sleep_secs=8,  # 循环scan等待时长
            **kwargs,
    ) -> Generator[EntityType, None, None]:
        start = start or -1
        scan_key = scan_key or self.pk_name
        count = 0
        while True:
            next_start, items = self.scan_iter(
                start=start,
                scan_key=scan_key,
                **kwargs,
            )
            for item in items:
                yield item
                count += 1
                if total is not None and count >= total:
                    break
            logger.info(f'db_tb={self.db_tb_name} start {start}->{next_start}')

            if infinite:
                if next_start is not None:
                    start = next_start
                else:
                    logger.info(f'sleep {infinite_sleep_secs} for next scan')
                    time.sleep(infinite_sleep_secs)
            else:
                start = next_start

            if start is None or (total is not None and count >= total):
                break

    def scan_iter(
            self,
            start: PKType,  # 遍历起始位置 不包括，需要明确指明
            scan_key=None,  # 遍历键值, 默认采用pk
            condition=None,  # 用在scan_key后面
            raw=False,  # 以entity对象还是原生dict方式返回数据
    ) -> Tuple[Optional[PKType], List[EntityType]]:
        if start is None:
            raise RuntimeError('start param cannot be None')
        scan_key = scan_key or self.pk_name
        condition = '' if not condition else f' and {condition}'
        sql = f'select * from {self.db_tb_name} where {scan_key} > %s {condition}  limit %s'
        next_start, items = None, []
        for d in self.mysql_client.select_many(sql=sql, args=(start, self.batch_size)):
            next_start = d[scan_key]
            item = self._to_entity(d, raw=raw)
            if item is not None:
                items.append(item)
        logger.info(f'db_tb={self.db_tb_name} start={start}->{next_start} count={len(items)}')
        return next_start, items

    def table_exists(self) -> bool:
        try:
            self.get(1)
        except ProgrammingError:
            return False
        return True

    # SQL里面基于数据表的唯一索引 然后又显示指定
    # 后续使用 save_by_cmp DUPLICATED
    def insert_or_update(self, dup_keys: List[str], d: Dict[str, PKType]) -> bool:
        sql_insert, sql_values, sql_update, sql_insert_args, sql_update_args = [], [], [], [], []
        # sql = f"""insert into {self.db_tb_name} (credit_code, eid, cid, ts) values(%s,%s,%s,%s)
        # on duplicate key update eid=%s, cid=%s, ts=%s"""
        match_dup_key = False
        for k, v in d.items():
            sql_insert.append(k)
            sql_values.append('%s')
            sql_insert_args.append(v)
            if k not in dup_keys:
                sql_update.append(f'{k}=%s')
                sql_update_args.append(v)
            else:
                match_dup_key = True
        if not match_dup_key or len(sql_update) == 0:
            logger.warning(f'bad dup_keys={dup_keys} d={d}')
            return False
        sql_insert_s = ','.join(sql_insert)
        sql_values_s = ','.join(sql_values)
        sql_update_s = ','.join(sql_update)
        sql = f'insert into {self.db_tb_name} ({sql_insert_s}) values({sql_values_s}) on duplicate key update {sql_update_s}'
        ret = self.mysql_client.execute(sql, args=sql_insert_args + sql_update_args)
        return ret == 1
