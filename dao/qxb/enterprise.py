# encoding=utf8

import re
from copy import copy
from datetime import date, datetime
from typing import List, Type, Optional
from pydantic import Field
from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao
from dao.deps.mysql_sharding_dao import MySQLShardingDao
from entity.deps.entity import BaseEntity


class Enterprise(BaseEntity):
    eid: str
    reg_number: str = Field(default='', alias='reg_no')
    credit_code: str = Field(default='', alias='credit_no')
    org_number: str = Field(default='', alias='org_no')

    name: str = Field(default='', alias='format_name')  # 人名个体户问题

    category: Optional[str] = Field(default=None)  # 企业类型

    org_type: str = Field(default='', alias='econ_kind')  # 企业类型 个体工商户 有限责任公司(自然人投资或控股)
    reg_capital: str = Field(default='', alias='regist_capi')  # 注册资本 3.8万元人民币
    actual_capital: str = Field(default='', alias='actual_capi')  # 实缴资本 497.5
    business_scope: Optional[str] = Field(default=None, alias='scope')  # 经营范围

    from_date: Optional[date] = Field(default=None, alias='term_start')  # 营业期限 开始 2021-03-08
    to_date: Optional[date] = Field(default=None, alias='term_end')  # 营业期限 结束 2021-03-08

    approve_date: Optional[date] = Field(default=None, alias='check_date')  # 核准日期 2021-03-08

    reg_institute: Optional[str] = Field(default=None, alias='belong_org')  # 登记机关

    legal_name: Optional[str] = Field(default=None, alias='oper_name')  # 法人
    # oper_type: str = Field(default=None, alias='oper_type')  # 法人类型 P/E ?
    # oper_name_id : str = Field(default=None, alias='oper_name_id')  # 法人 247cbf6a768a5d43c54d1742dcf55d6f

    establish_date: Optional[date] = Field(default=None, alias='start_date')  # 成立日期 2021-03-08
    end_date: Optional[date] = Field(default=None, alias='end_date')  # ？ 注吊销日期
    reg_status: Optional[str] = Field(default=None, alias='status')  # 企业状态
    reg_location: Optional[str] = Field(default=None, alias='address')  # 地址
    # _type: int = Field(default=None, alias='type')  # ? =0
    # type_desc: str = Field(default=None, alias='type_desc') # ? =家庭经营
    titles: Optional[str] = Field(default=None, alias='titles')  # 投资人姓名/法定代表人/执行事务合伙人
    # longitude: float = Field(default=None, alias='longitude')  # ?
    # latitude: float = Field(default=None, alias='latitude')  # ?
    # gd_longitude: float = Field(default=None, alias='gd_longitude')  # ?
    # gd_latitude: float = Field(default=None, alias='gd_latitude')  # ?
    # obj_id : str = Field(default=None, alias='obj_id')  # 实体去重？ 5c2425d079112e9e4a0038ed
    # source: str = Field(default=None, alias='source')  # ？1/13/14
    tax_number: Optional[str] = Field(default=None, alias='tax_no')
    # collegues_num: str = Field(default=None, alias='collegues_num')  # 从业人数 3人
    # description: str = Field(default=None, alias='description')  # ?
    # created_time
    # last_update_time
    # logo_url
    # status_code
    # econ_type
    # department
    # url
    # org_type
    row_update_time: datetime = Field(alias='row_update_time')  # 数据更新日期 2021-03-08
    # province_code district_code title_code econ_kind_code
    reg_capital_new: Optional[float] = Field(default=None, alias='regist_capi_new')  # 注册资本 double 0.001
    currency_unit: Optional[str] = Field(default=None, alias='currency_unit')  # 币种 CNY
    # belong_org_code
    # credit_area_code
    revoke_reason: Optional[str]
    revoke_date: Optional[str]
    logout_reason: Optional[str]
    logout_date: Optional[str]

    # group_name group_abbreviation
    # revoked_certificates new_status_code
    # type_new category_new

    def __init__(self, **kwargs):
        if kwargs.get('start_date', '') == '':
            kwargs['start_date'] = None
        if kwargs.get('end_date', '') == '':
            kwargs['end_date'] = None

        if kwargs.get('term_start', '') in ['', '永续经营', '永久', '无期限']:
            kwargs['term_start'] = kwargs.get('start_date', None)
        if kwargs.get('term_end', '') in ['', '永续经营', '永久', '无期限']:
            kwargs['term_end'] = None

        if kwargs.get('check_date', '') == '':
            kwargs['check_date'] = None

        super().__init__(**kwargs)


# 分库分表的 MySQLDao
class EnterpriseDao(MySQLShardingDao):
    def __init__(self, **kwargs):
        super().__init__(sharding_key='eid', pk_name='eid', entity_class=Enterprise, **kwargs)

    # 给定v 给出 分库分表位置
    @classmethod
    def do_sharding(cls, v: str) -> int:
        if not re.fullmatch('[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', v):
            raise ValueError('bad format eid={}'.format(v))
        db_id = int('0x' + v[-1], 16)
        tb_id = int('0x' + v[-2], 16)
        return db_id * 16 + tb_id

    @classmethod
    def gen_dao_list(cls, **kwargs) -> List[MySQLDao]:
        return cls.gen_dao_list_qxb(
            db_prefix='db_enterprise',
            tb_prefix='t_enterprise',
            **kwargs,
        )

    @classmethod
    def gen_dao_list_qxb(cls, db_prefix: str, tb_prefix: str, **kwargs) -> List[MySQLDao]:
        dao_list = list()
        for db_id in range(16):
            for tb_id in range(16):
                dao_args_copy = copy(kwargs)
                dao_list.append(
                    MySQLDao(
                        **ConstantProps.PROPS_QX_LIST[db_id],
                        db_tb_name='{}_{}.{}_{}'.format(db_prefix, db_id, tb_prefix, tb_id),
                        **dao_args_copy,
                    )
                )
        return dao_list


if __name__ == '__main__':
    from libs.log import setup_logger

    logger = setup_logger()
    sharding_dao = EnterpriseDao()
    logger.info('dao list=%s', [dao.db_tb_name for dao in sharding_dao.mysql_dao_list])
    logger.info('%s', sharding_dao.get('0016d650-d702-443f-a4a7-8dd9c1a8b910'))
