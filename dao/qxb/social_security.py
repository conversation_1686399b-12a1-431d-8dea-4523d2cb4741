# encoding=utf8

import logging
from typing import List
from datetime import datetime
from pydantic import Field, conint
from entity.deps.entity import BaseEntity
from dao.deps.mysql_sharding_dao import MySQLDao
from dao.qxb.enterprise import EnterpriseDao
from dao.qxb.report_detail import ReportDetailDao

logger = logging.getLogger(__name__)


class SocialSecurity(BaseEntity):
    eid: str
    report_year: conint(strict=False, ge=2000, le=2099)
    row_update_time: datetime = Field(alias='row_update_time')

    yanglaobx_num: str
    dw_yanglaobx_js: str
    bq_yanglaobx_je: str
    dw_yanglaobx_je: str

    shiyebx_num: str
    dw_shiyebx_js: str
    bq_shiyebx_je: str
    dw_shiyebx_je: str

    yiliaobx_num: str
    dw_yiliaobx_js: str
    bq_yiliaobx_je: str
    dw_yiliaobx_je: str

    gongshangbx_num: str
    bq_gongshangbx_je: str
    dw_gongshangbx_je: str

    shengyubx_num: str
    dw_shengyubx_js: str
    bq_shengyubx_je: str
    dw_shengyubx_je: str

    dw_je_display: int  # 0 2不公示 1公示
    dw_js_display: int
    bq_je_display: int

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        logger.debug(
            'display eid=%s year=%s %s%s%s',
            self.eid, self.report_year,
            self.bq_je_display, self.dw_js_display, self.dw_je_display,
        )

        if self.dw_je_display != 1:
            self.dw_yiliaobx_je = '企业选择不公示'
            self.dw_shengyubx_je = '企业选择不公示'
            self.dw_gongshangbx_je = '企业选择不公示'
            self.dw_shiyebx_je = '企业选择不公示'
            self.dw_yanglaobx_je = '企业选择不公示'

        if self.dw_js_display != 1:
            self.dw_yiliaobx_js = '企业选择不公示'
            self.dw_yanglaobx_js = '企业选择不公示'
            self.dw_shengyubx_js = '企业选择不公示'
            self.dw_shiyebx_js = '企业选择不公示'

        if self.bq_je_display != 1:
            self.bq_yiliaobx_je = '企业选择不公示'
            self.bq_shengyubx_je = '企业选择不公示'
            self.bq_gongshangbx_je = '企业选择不公示'
            self.bq_shiyebx_je = '企业选择不公示'
            self.bq_yanglaobx_je = '企业选择不公示'


class SocialSecurityDao(ReportDetailDao):
    def __init__(self, **kwargs):
        kwargs.setdefault('entity_class', SocialSecurity)
        super().__init__(**kwargs)

    @classmethod
    def gen_dao_list(cls, **kwargs) -> List[MySQLDao]:
        return EnterpriseDao.gen_dao_list_qxb(
            db_prefix='db_enterprise',
            tb_prefix='t_social_security',
            **kwargs,
        )


if __name__ == '__main__':
    from libs.log import setup_logger
    logger = setup_logger()
    sharding_dao = SocialSecurityDao()
    o = sharding_dao.get_by_eid_year('a80c81ff-ca8a-48e8-bb88-fa784e015da4', 2022)
    logger.info(f'{o}')
