# encoding=utf8

import logging
from typing import List, Optional
from datetime import datetime, date
from pydantic import Field, conint
from libs.dt import to_date
from entity.deps.entity import BaseEntity
from dao.deps.mysql_sharding_dao import MySQLShardingDao, MySQLDao
from dao.qxb.enterprise import EnterpriseDao

logger = logging.getLogger(__name__)


class ReportDetail(BaseEntity):
    eid: str
    report_year: conint(strict=False, ge=2000, le=2099) = Field(default=None)
    credit_no: Optional[str] = Field(default=None)  # 统一信用代码
    reg_no: Optional[str] = Field(default=None)  # 注册号
    company_name: str = Field(alias='name', default='')  # 企业名称
    report_date: Optional[date] = Field(default=None)  # 年报发布日期
    net_amount: str = Field(default='')  # net_amount
    debit_amount: str = Field(default='')  # debit_amount
    prac_person_num: Optional[str] = Field(default=None)  # prac_person_num
    telephone: Optional[str] = Field(default=None)  # telephone
    email: Optional[str] = Field(default=None)  # email
    sale_income: str = Field(default='')  # sale_income
    profit_total: str = Field(default='')  # profit_total
    profit_reta: str = Field(default='')  # profit_reta
    tax_total: str = Field(default='')  # tax_total
    total_equity: str = Field(default='')  # total_equity
    address: Optional[str] = Field(default=None)  # address
    fare_scope: Optional[str] = Field(default=None)  # fare_scope
    oper_name: Optional[str] = Field(default=None)  # 法人
    zip_code: Optional[str] = Field(default=None)  # zip_code
    serv_fare_income: str = Field(default='')  # serv_fare_income
    reg_capi: Optional[str] = Field(default=None)  # reg_capi
    indiv_form_mode: Optional[str] = Field(default=None)  # indiv_form_mode
    collegues_num: Optional[str] = Field(default=None)  # 30人
    status: Optional[str] = Field(default=None)  # 开业/正常经营  个体工商户选择不公示 开业
    guarantee_items: Optional[str] = Field(default=None)  # guarantee_items
    invest_items: Optional[str] = Field(default=None)  # invest_items
    websites: Optional[str] = Field(default=None)  # websites
    regist_capi_info: Optional[str] = Field(default=None)  # regist_capi_info
    change_records: Optional[str] = Field(default=None)  # change_records
    branch: Optional[str] = Field(default=None)  # branch
    stock_changes: Optional[str] = Field(default=None)  # stock_changes
    invest_situation: Optional[str] = Field(default=None)  # invest_situation
    matter_record: Optional[str] = Field(default=None)  # matter_record
    update_records: Optional[str] = Field(default=None)  # update_records
    partners: Optional[str] = Field(default=None)  # 年报股东信息

    row_update_time: datetime  # XXX
    local_row_update_time: datetime  # XXX

    def __init__(self, **kwargs):
        if kwargs.get('report_year', '') == '':
            kwargs['report_year'] = None

        report_date = kwargs.get('report_date', None)
        if isinstance(report_date, str):
            report_date = report_date.strip()
        kwargs['report_date'] = to_date(report_date)

        if kwargs.get('update_records', '') == '':
            kwargs['update_records'] = '[]'

        if kwargs.get('partners', '') == '':
            kwargs['partners'] = '[]'

        if kwargs.get('stock_changes', '') == '':
            kwargs['stock_changes'] = '[]'

        if kwargs.get('change_records', '') == '':
            kwargs['change_records'] = '[]'

        if kwargs.get('regist_capi_info', '') == '':
            kwargs['regist_capi_info'] = '[]'

        if kwargs.get('websites', '') == '':
            kwargs['websites'] = '[]'

        if kwargs.get('invest_items', '') == '':
            kwargs['invest_items'] = '[]'

        if kwargs.get('guarantee_items', '') == '':
            kwargs['guarantee_items'] = '[]'

        super().__init__(**kwargs)


# 分库分表的 MySQLDao
class ReportDetailDao(MySQLShardingDao):
    def __init__(self, **kwargs):
        kwargs.setdefault('sharding_key', 'eid')
        kwargs.setdefault('pk_name', 'eid')
        kwargs.setdefault('entity_class', ReportDetail)
        super().__init__(**kwargs)

    # 给定v 给出 分库分表位置
    @classmethod
    def do_sharding(cls, v: str) -> int:
        return EnterpriseDao.do_sharding(v)

    @classmethod
    def gen_dao_list(cls, **kwargs) -> List[MySQLDao]:
        return EnterpriseDao.gen_dao_list_qxb(
            db_prefix='db_enterprise_reports',
            tb_prefix='t_report_details',
            **kwargs,
        )

    def get_by_eid_year(self, eid, report_year):
        dao = self.mysql_dao_list[self.do_sharding(eid)]
        sql = f'select * from {dao.db_tb_name} where eid = %s and report_year=%s limit 1'
        d = dao.mysql_client.select(sql, args=(eid, report_year))
        return dao._to_entity(d)


if __name__ == '__main__':
    from libs.log import setup_logger
    logger = setup_logger()
    sharding_dao = ReportDetailDao()
    o = sharding_dao.get_by_eid_year('d8c56ada-6dd5-4672-9ee8-323901cba3fb', 2023)
    logger.info(f'{o}')
