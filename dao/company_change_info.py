# encoding=utf8

import logging
from datetime import datetime
from typing import Generator, Optional

from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class CompanyChangeInfo(BaseEntity):
    id: int
    company_id: int
    change_item: str
    change_time: datetime
    content_before: str
    content_after: str

    def __init__(self, **kwargs):
        if kwargs['content_before'] is None:
            kwargs['content_before'] = ''
        if kwargs['content_after'] is None:
            kwargs['content_after'] = ''
        if kwargs['change_item'] is None:
            kwargs['change_item'] = ''
        super().__init__(**kwargs)


class CompanyChangeInfoDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_change_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanyChangeInfo)
        super().__init__(**kwargs)

    def get_many(self, field, value, raw=False) -> Generator[CompanyChangeInfo, None, None]:
        sql = f'select * from {self.db_tb_name} where {field} = %s'
        for d in self.mysql_client.select_many(sql, args=(value,)):
            item = self._to_entity(d, raw=raw)
            if item is not None:
                yield item
