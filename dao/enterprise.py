# encoding=utf8

import logging
from six import itervalues,iterkeys
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class Enterprise(BaseEntity):
    id: int
    graph_id: int
    deleted:int


class EnterpriseDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_NG.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.enterprise')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', Enterprise)
        self.placeholder = '%s'
        super().__init__(**kwargs)

    def set_deleted(self, cid, v=1) -> bool:
        sql = 'update ignore {} set deleted=%s where company_id=%s and deleted=0'.format(self.db_tb_name)
        ret = self.mysql_client.execute(sql, args=(v, cid,))
        logger.info('set deleted cid=%s ret=%s', cid, ret)
        return ret > 0

    def set_un_deleted(self, cid) -> bool:
        sql = 'update ignore {} set deleted=0 where company_id=%s and deleted!=0'.format(self.db_tb_name)
        ret = self.mysql_client.execute(sql, args=(cid,))
        if ret > 0:
            logger.info('set un deleted cid=%s ret=%s', cid, ret)
        return ret > 0

    def insert_many(self, values: list,is_replace=False):
        insert = "INSERT ignore INTO" if not is_replace else "REPLACE INTO"
        if values:
            _keys = "`" + "`, `".join((k for k in values[0])) + "`"
            _values = ", ".join([
                                    self.placeholder,
                                ] * len(values[0]))
            sql = f"{insert} {self.db_tb_name} ({_keys}) VALUES ({_values})"
            data = [tuple(itervalues(item)) for item in values]
        else:
            sql = f"{insert} {self.db_tb_name} DEFAULT VALUES"
        try:
            if values is None:
                rowcount = self.mysql_client.execute(sql)
            else:
                rowcount = self.mysql_client.execute(sql, data)
        except BaseException:
            raise
        return rowcount

    def insert(self, values: dict,is_replace=False):
        insert = "INSERT INTO" if not is_replace else "REPLACE INTO"
        if values:
            _keys = "`" + "`, `".join((k for k in values)) + "`"
            _values = ", ".join([
                                    self.placeholder,
                                ] * len(values))
            sql = f"{insert} {self.db_tb_name} ({_keys}) VALUES ({_values})"
        else:
            sql = f"{insert} {self.db_tb_name} DEFAULT VALUES"
        try:
            if values is None:
                rowcount = self.mysql_client.execute(sql)
            else:
                rowcount = self.mysql_client.execute(sql,
                                        tuple(itervalues(values)))
        except BaseException:
            print("E"*100)
        return rowcount
