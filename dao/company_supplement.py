# encoding=utf8

import logging
from typing import Optional
from datetime import datetime
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class CompanySupplement(BaseEntity):
    id: int = Field(default=0)
    company_id: int
    name: str
    establish_date: Optional[datetime] = Field(default=None)
    company_org_code: str = Field(default='')
    industry_code: str = Field(default='')
    ent_type: int = Field(default=0)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class CompanySupplementDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_INNER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'internal.company_supplement')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanySupplement)

        super().__init__(**kwargs)
