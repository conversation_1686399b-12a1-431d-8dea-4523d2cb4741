# encoding=utf8

import logging
from typing import Optional, List
from datetime import datetime
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class GSXTHistoryName(BaseEntity):
    id: int
    cid: int
    history_name: str
    type: int = Field(default=0)
    pub_time: Optional[datetime] = Field(default=None)
    mark: int = Field(default=0)
    source: int = Field(default=0)
    version: int = Field(default=0)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class GSXTHistoryNameDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'history.gsxt_history_names')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', GSXTHistoryName)
        super().__init__(**kwargs)

    def add(self, cid, cur_name: str, new_name: str) -> bool:
        if not isinstance(cur_name, str) or len(cur_name) == 0:
            return False
        if not isinstance(new_name, str) or len(new_name) == 0:
            return False
        if cur_name == new_name:
            return False
        items: List[GSXTHistoryName] = sorted(self.get_many(value=cid, field='cid'), key=lambda x: x.version, reverse=True)
        if len(items) > 0:
            if items[0].history_name == cur_name:
                new_version = -1
            elif items[0].history_name == new_name:
                self.delete(items[0].id)
                return self.add(cid, cur_name, new_name)
            else:
                new_version = items[0].version + 1
        else:
            new_version = 0

        if new_version >= 0:
            sql = f'insert ignore into {self.db_tb_name} set cid=%s, history_name=%s, pub_time=%s, version=%s'
            ret = self.mysql_client.insert(sql, args=(cid, cur_name, None, new_version))
            return ret > 0
        return False


if __name__ == '__main__':
    from libs.log2 import setup_logger

    logger = setup_logger()
    dao = GSXTHistoryNameDao()
    dao.add(135446293, '南京秋枫文化艺术培训中心', '南京黄钟秋枫文化艺术培训学校')
