# encoding=utf8

import logging
from typing import Optional
from datetime import datetime
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class CompanyOtherInfo(BaseEntity):
    id: int = Field()
    undo_date: Optional[datetime] = Field(default=None)
    undo_reason: Optional[str] = Field(default=None)  # 撤销
    cancel_date: Optional[datetime] = Field(default=None)
    cancel_reason: Optional[str] = Field(default=None)  # 注销
    revoke_date: Optional[datetime] = Field(default=None)
    revoke_reason: Optional[str] = Field(default=None)  # 吊销

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def __hash__(self):
        return self.cid


class CompanyOtherInfoDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_other_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanyOtherInfo)

        super().__init__(**kwargs)



