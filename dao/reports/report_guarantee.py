# encoding=utf8

import logging
from typing import Optional
from datetime import datetime
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class ReportGuarantee(BaseEntity):
    id: int = Field(default=0)
    report_id: int = Field(alias='annualreport_id')
    creditor: Optional[str] = Field(default=None)
    obligor: Optional[str] = Field(default=None)
    credito_type: Optional[str] = Field(default=None)
    credito_amount: Optional[str] = Field(default=None)
    credito_term: Optional[str] = Field(default=None)
    guarantee_term: Optional[str] = Field(default=None)
    guarantee_way: Optional[str] = Field(default=None)
    guarantee_scope: Optional[str] = Field(default=None)
    create_time: datetime = Field(default_factory=datetime.now, alias='createTime')

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class ReportGuaranteeDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.report_out_guarantee_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', ReportGuarantee)
        super().__init__(**kwargs)
