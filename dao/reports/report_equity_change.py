# encoding=utf8

import logging
from typing import Optional
from datetime import datetime, date
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from gslib.gs_enum import EntityType
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class ReportEquityChange(BaseEntity):
    id: int = Field(default=0)
    report_id: int = Field(alias='annualreport_id')
    investor_name: str
    investor_id: Optional[int] = Field(default=0)
    investor_type: Optional[EntityType] = Field(default=EntityType.UNSET)
    ratio_before: Optional[str] = Field(default=None)
    ratio_after: Optional[str] = Field(default=None)
    change_time: Optional[date] = Field(default=None)
    create_time: datetime = Field(default_factory=datetime.now, alias='createTime')

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class ReportEquityChangeDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.report_equity_change_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', ReportEquityChange)
        super().__init__(**kwargs)
