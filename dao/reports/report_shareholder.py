# encoding=utf8

import logging
from typing import Optional
from datetime import datetime, date
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from gslib.gs_enum import EntityType

logger = logging.getLogger(__name__)


class ReportShareholder(BaseEntity):
    id: int = Field(default=0)
    report_id: int = Field(alias='annual_report_id')
    investor_name: str
    investor_name_clean: str = Field(default='')
    investor_id: int = Field(default=0)
    investor_type: EntityType = Field(default=EntityType.UNSET)
    subscribe_amount: Optional[str] = Field(default=None)
    subscribe_time: Optional[date] = Field(default=None)
    subscribe_type: Optional[str] = Field(default=None)
    paid_amount: Optional[str] = Field(default=None)
    paid_time: Optional[date] = Field(default=None)
    paid_type: Optional[str] = Field(default=None)

    create_time: datetime = Field(default_factory=datetime.now, alias='createTime')

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class ReportShareholderDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.report_shareholder')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', ReportShareholder)
        super().__init__(**kwargs)
