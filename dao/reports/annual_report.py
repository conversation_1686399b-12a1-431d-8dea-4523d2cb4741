# encoding=utf8

import logging
from datetime import datetime
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from typing import Optional

logger = logging.getLogger(__name__)


class AnnualReport(BaseEntity):
    id: int = Field(default=0)
    company_id: int
    report_year: str
    company_name: Optional[str] = Field(default=None)
    credit_code: Optional[str] = Field(default=None)
    reg_number: Optional[str] = Field(default=None)
    phone_number: Optional[str] = Field(default=None)
    postcode: Optional[str] = Field(default=None)
    postal_address: Optional[str] = Field(default=None)
    email: Optional[str] = Field(default=None)
    manage_state: Optional[str] = Field(default=None)
    employee_num: Optional[str] = Field(default=None)
    operator_name: Optional[str] = Field(default=None)
    total_assets: Optional[str] = Field(default='企业选择不公示')
    total_equity: Optional[str] = Field(default='企业选择不公示')
    total_sales: Optional[str] = Field(default='企业选择不公示')
    total_profit: Optional[str] = Field(default='企业选择不公示')
    prime_bus_profit: Optional[str] = Field(default='企业选择不公示')
    retained_profit: Optional[str] = Field(default='企业选择不公示')
    total_tax: Optional[str] = Field(default='企业选择不公示')
    total_liability: Optional[str] = Field(default='企业选择不公示')
    create_time: datetime = Field(default_factory=datetime.now, alias='createTime')

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class AnnualReportDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.annual_report')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', AnnualReport)
        super().__init__(**kwargs)
