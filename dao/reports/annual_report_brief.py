# encoding=utf8

import logging
from datetime import datetime
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from typing import Optional

logger = logging.getLogger(__name__)


class AnnualReportBrief(BaseEntity):
    id: int = Field(default=0)
    company_id: int
    report_year: str
    company_name: Optional[str] = Field(default=None)  # 冗余字段 不使用
    published_date: Optional[str] = Field(default=None)
    data_type: int = Field(default=0)  # '纸质年报 0：非纸质，1：纸质',
    ent_type: str = Field(default='')  # ' 年报类型 e - 企业； sfc - 农专； pb - 个体',
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    def __init__(self, credit_code=None, **kwargs):
        # 暂时不处理ent_type 非业务字段
        # if isinstance(credit_code, str):
        #     if credit_code.startswith('91'):
        #         kwargs['ent_type'] = 'e'
        #     elif credit_code.startswith('92'):
        #         kwargs['ent_type'] = 'pb'
        #     elif credit_code.startswith('93'):
        #         kwargs['ent_type'] = 'sfc'
        super().__init__(**kwargs)


class AnnualReportBriefDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.annual_report_brief')
        kwargs.setdefault('pk_name', 'company_id')
        kwargs.setdefault('entity_class', AnnualReportBrief)
        super().__init__(**kwargs)
