# encoding=utf8

import logging
from typing import Optional
from datetime import datetime
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class ReportInvestment(BaseEntity):
    id: int = Field(default=0)
    report_id: int = Field(alias='annual_report_id')
    outcompany_id: Optional[int] = Field(default=0)
    outcompany_name: str
    reg_num: Optional[str] = Field(default=None)
    credit_code: Optional[str] = Field(default=None)
    create_time: datetime = Field(default_factory=datetime.now, alias='createTime')

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class ReportInvestmentDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.report_outbound_investment')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', ReportInvestment)
        super().__init__(**kwargs)
