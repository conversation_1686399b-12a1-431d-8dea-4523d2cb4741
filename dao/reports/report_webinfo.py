# encoding=utf8

import logging
from datetime import datetime
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from typing import Optional

logger = logging.getLogger(__name__)


class ReportWebInfo(BaseEntity):
    id: int = Field(default=0)
    report_id: int = Field(alias='annualreport_id')
    web_type: Optional[str] = Field(default=None)
    name: Optional[str] = Field(default=None)
    website: Optional[str] = Field(default=None)
    create_time: datetime = Field(default_factory=datetime.now, alias='createTime')

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class ReportWebInfoDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.report_webinfo')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', ReportWebInfo)
        super().__init__(**kwargs)
