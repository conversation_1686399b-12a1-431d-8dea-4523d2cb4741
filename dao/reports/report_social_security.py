# encoding=utf8

import logging
from typing import Optional
from datetime import datetime, date
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from gslib.gs_enum import EntityType
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class ReportSocialSecurity(BaseEntity):
    id: int = Field(default=0)
    report_id: int = Field(alias='annaulreport_id')
    endowment_insurance: str = Field(default='')
    unemployment_insurance: str = Field(default='')
    medical_insurance: str = Field(default='')
    employment_injury_insurance: str = Field(default='')
    maternity_insurance: str = Field(default='')
    endowment_insurance_base: str = Field(default='企业选择不公示')
    unemployment_insurance_base: str = Field(default='企业选择不公示')
    medical_insurance_base: str = Field(default='企业选择不公示')
    maternity_insurance_base: str = Field(default='企业选择不公示')
    endowment_insurance_pay_amount: str = Field(default='企业选择不公示')
    unemployment_insurance_pay_amount: str = Field(default='企业选择不公示')
    medical_insurance_pay_amount: str = Field(default='企业选择不公示')
    employment_injury_insurance_pay_amount: str = Field(default='企业选择不公示')
    maternity_insurance_pay_amount: str = Field(default='企业选择不公示')
    endowment_insurance_owe_amount: str = Field(default='企业选择不公示')
    unemployment_insurance_owe_amount: str = Field(default='企业选择不公示')
    medical_insurance_owe_amount: str = Field(default='企业选择不公示')
    employment_injury_insurance_owe_amount: str = Field(default='企业选择不公示')
    maternity_insurance_owe_amount: str = Field(default='企业选择不公示')
    create_time: datetime = Field(default_factory=datetime.now, alias='createTime')

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class ReportSocialSecurityDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.report_social_security_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', ReportSocialSecurity)
        super().__init__(**kwargs)
