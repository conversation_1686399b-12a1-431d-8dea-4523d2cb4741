# encoding=utf8
from typing import Optional
from pydantic import Field, conint
from datetime import datetime
from resx.base_model import BaseModel
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps

class CompanyHk(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    company_id: Optional[int] = Field(default=None)
    br_num: str = Field(default='')
    company_num: Optional[str] = Field(default='')
    base: Optional[str] = Field(default='hk')
    name: Optional[str] = Field(default='')
    name_s: Optional[str] = Field(default='')
    name_cn: Optional[str] = Field(default='')
    name_cn_s: Optional[str] = Field(default='')
    name_en: Optional[str] = Field(default='')
    company_org_type: Optional[str] = Field(default='')
    company_org_type_s: Optional[str] = Field(default='')
    estiblish_time: Optional[datetime] = Field(default=None)
    reg_status: Optional[str] = Field(default='')
    reg_status_s: Optional[str] = Field(default='')
    remarks: Optional[str] = Field(default='')
    remarks_s: Optional[str] = Field(default='')
    liquidation_mode: Optional[str] = Field(default='')
    liquidation_mode_s: Optional[str] = Field(default='')
    to_time: Optional[datetime] = Field(default=None)
    mortgage: Optional[str] = Field(default='')
    mortgage_s: Optional[str] = Field(default='')
    important_items: Optional[str] = Field(default='')
    important_items_s: Optional[str] = Field(default='')
    source: Optional[str] = Field(default='')
    crawledtime: Optional[datetime] = Field(default_factory=datetime.now)
    updatetime: Optional[datetime] = Field(default_factory=datetime.now)
    deleted: int = Field(default=0)
    property1: Optional[str] = Field(default='')
    property2: Optional[str] = Field(default='')
    property3: Optional[str] = Field(default='')
    exist_report: Optional[int] = Field(default=0)

class CompanyHkDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_hk')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanyHk)

        super().__init__(**kwargs)
