# encoding=utf8

import logging
from datetime import date

from pydantic import Field

from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class CompanyRegCapital(BaseEntity):
    id: int = Field(alias='id')
    company_id: int
    reg_capital: str
    create_time: date
    update_time: date
    deleted: int

    def logic_validate(self) -> bool:
        return True


class CompanyRegCapitalDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_reg_capital')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanyRegCapital)

        super().__init__(**kwargs)

if __name__ == '__main__':
    from libs.log import setup_logger
    logger = setup_logger()
    dao = CompanyDao(batch_size=1000, entity_class=None)
    for o in dao.scan(total=200, start=0):
        logger.info('%s', o)

