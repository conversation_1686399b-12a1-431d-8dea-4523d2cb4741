# encoding=utf8

import logging
from typing import Optional
from datetime import datetime, date, timedelta
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from libs.collection import sub_if
from gslib.reg_number import reg_number_valid
from gslib.credit_code import credit_code_valid
from gslib.gs_enum import EntityType
from typing import Type, Dict, TypeVar, Optional, Generator, List, Tuple

logger = logging.getLogger(__name__)


class CompanyStaffSort(BaseEntity):
    id: int
    company_gid: int
    name: str
    position: str
    deleted: int


class CompanyStaffSortDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_staff_sort')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanyStaffSort)
        super().__init__(**kwargs)

    # def get_many(self, field, value, raw=False) -> Generator[CompanyStaffSort, None, None]:
    #     sql = f'select * from {self.db_tb_name} where {field} = %s'
    #     for d in self.mysql_client.select_many(sql, args=(value,)):
    #         item = self._to_entity(d, raw=raw)
    #         if item is not None:
    #             yield item
