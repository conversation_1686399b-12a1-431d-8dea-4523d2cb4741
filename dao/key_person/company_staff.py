# encoding=utf8

import logging
from datetime import datetime, date, timedelta
from pydantic import Field, conint
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from libs.collection import sub_if
from gslib.reg_number import reg_number_valid
from gslib.credit_code import credit_code_valid
from gslib.gs_enum import EntityType
from typing import Type, Dict, TypeVar, Optional, Generator, List, Tuple

logger = logging.getLogger(__name__)


class CompanyStaff(BaseEntity):
    id: Optional[int] = Field(default=None)
    cid: Optional[int] = Field(alias='company_id', default=None)
    staff_id: Optional[int] = Field(alias='staff_id', default=None)
    position: Optional[str] = Field(alias='staff_type_name', default=None)


class CompanyStaffDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_staff')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanyStaff)
        super().__init__(**kwargs)


if __name__ == '__main__':
    dao = CompanyStaffDao()
    for item in dao.scan(start=1, total=1000):
        print(item)
