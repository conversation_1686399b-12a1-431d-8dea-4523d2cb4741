# encoding=utf8

from typing import Optional
import logging
from pydantic import Field, conint
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from six import itervalues, iterkeys

logger = logging.getLogger(__name__)


class Human(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=None)
    name: str


class HumanDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.human')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', Human)
        self.placeholder = '%s'
        super().__init__(**kwargs)

    def get_by_name_source(self, name, source) -> Optional[Human]:
        sql = f'select * from {self.db_tb_name} where name=%s and sourceflag=%s limit 1'
        d = self.mysql_client.select(sql, args=(name, source,))
        return self._to_entity(d)
    def insert(self, values: dict, is_replace=False):
        insert = "INSERT INTO" if not is_replace else "REPLACE INTO"
        if values:
            _keys = "`" + "`, `".join((k for k in values)) + "`"
            _values = ", ".join([
                                    self.placeholder,
                                ] * len(values))
            sql = f"{insert} {self.db_tb_name} ({_keys}) VALUES ({_values})"
        else:
            sql = f"{insert} {self.db_tb_name} DEFAULT VALUES"
        try:
            if values is None:
                rowcount = self.mysql_client.execute(sql)
            else:
                rowcount = self.mysql_client.execute(sql,
                                                     tuple(itervalues(values)))
        except BaseException:
            print("E" * 100)
        return rowcount


class HumanGraph(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=None)
    hid: conint(strict=True, ge=0) = Field(alias='human_id', default=None)
    hgid: conint(strict=True, ge=0) = Field(alias='graph_id', default=None)
    deleted: int = Field(default=0)


class HumanGraphDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.human_graph')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', HumanGraph)
        super().__init__(**kwargs)

    def get_by_hid(self, hid, ) -> Optional[HumanGraph]:
        sql = f'select * from {self.db_tb_name} where human_id=%s and deleted=0'
        for d in self.mysql_client.select_many(sql, args=(hid,)):
            item = self._to_entity(d)
            if item is not None:
                return item
        return None
