# encoding=utf8

import logging
from datetime import datetime, date, timedelta
from pydantic import Field, conint
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class NacaoBasic(BaseEntity):
    ent_id: int = Field(alias='entid')
    ent_name: str = Field(alias='ENTNAME', default='')
    credit_code: str = Field(alias='UNISCID', default='')
    update_time: datetime = Field(alias='updated')

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class NacaoBasicDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_CHANNEL.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'nacao_new.t_nacao_basic')
        kwargs.setdefault('pk_name', 'entid')
        kwargs.setdefault('entity_class', NacaoBasic)
        super().__init__(**kwargs)


if __name__ == '__main__':
    from libs.log2 import setup_logger
    logger = setup_logger()
    dao = NacaoBasicDao(batch_size=20)
    for item in dao.scan(scan_key='updated', start='2024-01-05 01:18:35', total=100):
        logger.info(f'{item}')

