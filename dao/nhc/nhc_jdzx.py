from datetime import datetime
from time import sleep
from typing import Optional
from pydantic import Field, conint
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps


class NhcJdzx(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    name_hospital: Optional[str] = Field(default='')
    org_category: Optional[str] = Field(default='')
    addr_medical: Optional[str] = Field(default='')
    addr_hospital: Optional[str] = Field(default='')
    Issuing: Optional[str] = Field(default='')
    # create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)

    def logic_validate(self) -> bool:
        return True


class NhcJdzxDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.nhc_jdzx')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', NhcJdzx)
        super().__init__(**kwargs)

    def i_u_by_any(self, *args, **kwargs):
        items = self.get(kwargs['name_hospital'], 'name_hospital')
        if items:
            fields = ['id']
            kwargs['id'] = dict(items)['id']
        else:
            fields = ['name_hospital']
        item = NhcJdzx.from_dict(kwargs)
        self.save_by_cmp(item, fields=fields)


    def acquire(self, *args, **kwargs):
        sql = f'select max(id) from {self.db_tb_name};'

        with self.mysql_client.get_conn() as conn:
            conn.execute(sql, (), is_select=True)
            result = conn.cursor.fetchone()['max(id)']

        return result
        # p = result // args[0]
        # res = list()
        # for i in range(0, args[0]):
        #     sql = f'-- select * from {self.db_tb_name} limit %s, %s'
        #     value = (i * p, (i+1)*p)
        #     res.append(self.mysql_client.select_many(sql, value))
        # return res




if __name__ == '__main__':
    NhcJdzxDao = NhcJdzxDao(**ConstantProps.PROPS_GS_TEST)
    for i in NhcJdzxDao.acquire(10):
        print(i)
        sleep(1)
