from time import sleep
from typing import Optional
from pydantic import Field, conint
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps


class NhcCompany(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    name: Optional[str] = Field(default='')
    base: Optional[str] = Field(default='')
    # create_time: Optional[datetime] = Field(default_factory=datetime.now)

    def logic_validate(self) -> bool:
        return True


class NhcCompanyDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.nhc_company')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', NhcCompany)
        super().__init__(**kwargs)

    def i_u_by_any(self, *args, **kwargs):
        items = self.get(kwargs['name'], 'name')
        if items:
            fields = ['id']
            kwargs['id'] = dict(items)['id']
        else:
            fields = ['name']
        item = NhcCompany.from_dict(kwargs)
        self.save_by_cmp(item, fields=fields)


    def acquire(self, *args, **kwargs):
        sql = f'select max(id) from {self.db_tb_name};'

        with self.mysql_client.get_conn() as conn:
            conn.execute(sql, (), is_select=True)
            result = conn.cursor.fetchone()['max(id)']

        return result




