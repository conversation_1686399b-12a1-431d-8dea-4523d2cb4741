from datetime import datetime
from typing import Optional
from pydantic import Field, conint
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps


class NhcHospital(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    name_hospital: str = Field(default='')
    province: Optional[str] = Field(default='')
    office: Optional[str] = Field(default='')
    reg_code: str = Field(default='')
    address: Optional[str] = Field(default='')
    diagnosis_sub: Optional[str] = Field(default='')
    hospital_level: Optional[str] = Field(default='')
    legal_name: Optional[str] = Field(default='')
    principal_name: Optional[str] = Field(default='')
    validity_start: Optional[datetime] = Field(default_factory=datetime.now)
    validity_end: Optional[datetime] = Field(default_factory=datetime.now)
    deleted: int = Field(default=0)
    # create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)

    def logic_validate(self) -> bool:
        return True


class NhcHospitalDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.nhc_hospital')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', NhcHospital)
        super().__init__(**kwargs)

    def i_u_by_any(self, *args, **kwargs):
        items = self.get(kwargs['reg_code'], 'reg_code')
        if items:
            fields = ['id']
            kwargs['id'] = dict(items)['id']
        else:
            fields = ['reg_code']
        item = NhcHospital.from_dict(kwargs)
        self.save_by_cmp(item, fields=fields)


    def acquire(self, *args, **kwargs):
        sql = f'select max(id) from {self.db_tb_name};'

        with self.mysql_client.get_conn() as conn:
            conn.execute(sql, (), is_select=True)
            result = conn.cursor.fetchone()['max(id)']

        return result

