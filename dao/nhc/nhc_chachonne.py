from datetime import datetime
from typing import Optional
from pydantic import Field, conint
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps


class NhcChachonne(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    name_hospital: str = Field(default='')
    create_time: Optional[datetime] = Field(default_factory=datetime.now)


    def logic_validate(self) -> bool:
        return True


class NhcChachonneDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.nhc_chachonne')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', NhcChachonne)
        super().__init__(**kwargs)

    def insert(self, *args, **kwargs):

        fields = ['name_hospital']
        item = NhcChachonne.from_dict({'name_hospital': args[0]})
        _, insert, _ = self.save_by_cmp(item, fields=fields)
        return insert


if __name__ == '__main__':
    NhcChachonneDao = NhcChachonneDao(**ConstantProps.PROPS_GS_TEST)
    NhcChachonneDao.insert('啦啦啦医院')