# encoding=utf8

import json
from typing import Optional, Dict

from pydantic import Field, conint
import logging
from datetime import datetime
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao, EntityType
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class DemoEntity(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)  # 不写入数据库，只读取  AUTO INCREASE
    json_data: dict
    str_field: str = Field(default='')
    create_time: datetime = Field(default_factory=datetime.now)  # 不写入数据库，只读取
    update_time: datetime = Field(default_factory=datetime.now)  # 不写入数据库，只读取

    def __init__(self, **kwargs):
        json_data_ = kwargs.get('json_data', None)
        if json_data_ is None:
            kwargs['json_data'] = {}
        elif isinstance(json_data_, str):
            kwargs['json_data'] = json.loads(json_data_)
        super().__init__(**kwargs)


# 是 dao的职责 提供  dict <->  BaseEntity 之间的转换
class DemoDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_TEST.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.demo')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', DemoEntity)
        super().__init__(**kwargs)


if __name__ == '__main__':
    from libs.log2 import setup_logger
    logger = setup_logger()
    dao = DemoDao()
    # ent = DemoEntity.from_dict(
    #     {
    #         "json_data": None
    #     }
    # )
    # ent.json_data['a'] = 1
    # ent.json_data['b'] = 'abc'
    #
    # dao.save_by_pk(ent)

    ent: DemoEntity = dao.get(4)
    # ent: DemoEntity = dao.get('1213123123', 'barcode')
    logger.info(f'ent={ent}')
    ent.json_data = None
    dao.save_by_pk(ent)