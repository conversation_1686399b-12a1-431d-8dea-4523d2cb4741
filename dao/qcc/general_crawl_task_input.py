# encoding=utf8

import logging
from typing import Optional
from datetime import datetime, date, timedelta
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from libs.collection import sub_if
from gslib.reg_number import reg_number_valid
from gslib.credit_code import credit_code_valid
from gslib.gs_enum import EntityType
from typing import Type, Dict, TypeVar, Optional, Generator, List, Tuple

logger = logging.getLogger(__name__)


class GeneralCrawlTaskInput(BaseEntity):
    id: int
    task_name: str
    status: int
    input: str
    ext_info: Optional[str] = Field(default=None)
    create_time: datetime
    update_time: datetime


class GeneralCrawlTaskInputDao(MySQLDao):

    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_ZX_PLATFORM.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'platform.general_crawl_task_input')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', GeneralCrawlTaskInput)
        self.placeholder = '%s'
        super().__init__(**kwargs)

    def get_many_by_task_name_and_id(self, task_name, create_time: str, task_id: int, raw=False) -> Generator[
        GeneralCrawlTaskInput, None, None]:
        sql = f'select * from {self.db_tb_name} where task_name = %s and create_time > %s and id > %s limit 10'
        for d in self.mysql_client.select_many(sql, args=(task_name, create_time, task_id,)):
            item = self._to_entity(d, raw=raw)
            if item is not None:
                yield item

    def get_one_by_task_name_and_firm_id(self, task_name, create_time: str, firm_id: str, raw=False) -> Optional[
        GeneralCrawlTaskInput]:
        sql = f'select * from {self.db_tb_name} where task_name = %s and create_time > %s and status = 1 and `input` like %s limit 1'
        d = self.mysql_client.select(sql, args=(task_name, create_time, '%' + firm_id + '%',))
        return self._to_entity(d, raw=raw)


if __name__ == '__main__':
    from libs.log import setup_logger

    logger = setup_logger()
    dao = GeneralCrawlTaskInputDao(batch_size=1000)
    # dao.get_many()
    # for item in dao.scan(total=200, start=0):
    #     logger.info('%s', item)
