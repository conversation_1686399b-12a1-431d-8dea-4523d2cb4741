from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from pydantic import Field, conint
from typing import Optional
import json
from datetime import datetime


class CodeEntity(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    firm_name: str = Field(default='')
    firm_id: int = Field(default=0)
    barcode_prefix: str = Field(default='')
    certificate_code: str = Field(default='')
    other_info: dict
    deleted: int = Field(default=0)
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    def __init__(self, **kwargs):
        json_data_ = kwargs.get('other_info', None)
        if json_data_ is None:
            kwargs['other_info'] = {}
        elif isinstance(json_data_, str):
            kwargs['other_info'] = json.loads(json_data_)
        super().__init__(**kwargs)


class CodeDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_INNER_RW.items():
            # for k, v in ConstantProps.PROPS_GS_TEST.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.gds_firm_code')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CodeEntity)
        super().__init__(**kwargs)

    def update_barcodes(self, firm_name, barcodes: list):
        firm = self.get(firm_name, 'firm_name')
        other_info = firm.other_info
        other_info['barcodes'] = barcodes
        sql = f'update {self.db_tb_name} set other_info=%s where id=%s'
        return self.mysql_client.execute(sql, (json.dumps(other_info, ensure_ascii=False), firm.id))

    def update_barcode(self, barcode_prefix, barcode):
        firm = self.get(barcode_prefix, 'barcode_prefix')
        data_ = firm.other_info
        data_.update({'barcode': barcode})
        sql = f'update {self.db_tb_name} set other_info=%s where barcode_prefix=%s'
        return self.mysql_client.execute(sql, (json.dumps(data_, ensure_ascii=False), barcode_prefix))


if __name__ == '__main__':
    code_entity = CodeEntity.from_dict({
        "firm_name": 'firm_name_',
        "firm_id": '123',
        "barcode_prefix": '3435134',
        "certificate_code": 'avfvfdvdfv',
        "other_info": {}
    })
    print(code_entity.__class__.__name__)
