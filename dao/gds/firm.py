from datetime import datetime
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
import json
from pydantic import Field, conint
from typing import Optional, TypeVar, Dict
import re

EntityType = TypeVar('EntityType', BaseEntity, Dict)


class FirmEntity(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    firm_id: int = Field(default=0)
    cn_name: str = Field(default='')
    en_name: str = Field(default='')
    company_id: conint(strict=True, ge=0) = Field(default=0)
    address: str = Field(default='')
    register_date: Optional[datetime] = Field(default=datetime.strptime('0001-01-01', '%Y-%m-%d'))
    website: str = Field(default='')
    product_count: conint(strict=True, ge=0) = Field(default=0)
    brand_count: conint(strict=True, ge=0) = Field(default=0)
    micro_site: int = Field(default=0)
    certificate_code: str = Field(default='')
    json_data: dict
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)

    def __init__(self, **kwargs):
        json_data_ = kwargs.get('json_data', None)
        if json_data_ is None:
            kwargs['json_data'] = {}
        elif isinstance(json_data_, str):
            kwargs['json_data'] = json.loads(json_data_)
        super().__init__(**kwargs)


class FirmDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_INNER_RW.items():
            # for k, v in ConstantProps.PROPS_GS_TEST.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.gds_firm')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', FirmEntity)
        super().__init__(**kwargs)

    def save_data(self, item: EntityType, fields_no: list):
        item_dict = item.to_dict()
        item_dict['json_data'] = json.dumps(item_dict['json_data'])
        item_dict['update_time'] = datetime.now()
        keys = list(item_dict.keys())[1:]
        values = []
        for value in list(item_dict.values())[1:]:
            if re.match(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}', str(value)):
                value = datetime.strptime(value[:19], '%Y-%m-%dT%H:%M:%S')
            if isinstance(value, str):
                value = value.strip()
            values.append(value)
        sql = (f'insert into {self.db_tb_name} ({", ".join(keys)}) values ({("%s," * len(values))[:-1]}) '
               f'on duplicate key update {", ".join(f"{key}=values({key})" for key in keys if key not in fields_no)}')
        affect_rows = self.mysql_client.execute(sql, args=values)
        return affect_rows == 2


if __name__ == '__main__':
    firm_dao = FirmDao()
    entity = firm_dao.get(750627, 'company_id')
    firm_dao.save_data(entity, ["firm_id", "create_time"])
