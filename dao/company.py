# encoding=utf8

import logging
from datetime import datetime, date, timedelta
from pydantic import Field, conint
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from libs.collection import sub_if
from gslib.reg_number import reg_number_valid
from gslib.credit_code import credit_code_valid
from gslib.gs_enum import EntityType
from typing import Type, Dict, TypeVar, Optional, Generator, List, Tuple

logger = logging.getLogger(__name__)


class Company(BaseEntity):
    cid: int = Field(alias='id')
    base: Optional[str] = Field(default=None)
    name: str
    establish_date: Optional[date] = Field(default=None, alias='estiblish_time')
    approved_date: Optional[date] = Field(default=None, alias='approved_time')
    reg_number: Optional[str] = Field(default=None)
    credit_code: Optional[str] = Field(default=None, alias='property1')
    company_org_type: Optional[str] = Field(default=None)

    # 重要字段
    last_crawled_time: Optional[datetime] = Field(default=None, alias='crawledtime')
    parent_id: Optional[int] = Field(default=None)
    prop2: Optional[int] = Field(alias='property2', default=None)
    source_flag: Optional[str] = Field(default=None)
    reg_status: Optional[str] = Field(default=None)
    from_date: Optional[date] = Field(default=None, alias='from_time')
    to_date: Optional[date] = Field(default=None, alias='to_time')
    reg_capital: Optional[str] = Field(default=None)
    reg_institute: Optional[str] = Field(default=None)
    reg_location: Optional[str] = Field(default=None)
    business_scope: Optional[str] = Field(default=None)

    # 法人信息
    legal_name: Optional[str] = Field(default=None, alias='legal_person_name')
    legal_id: Optional[int] = Field(default=0, alias='legal_person_id')
    legal_type: Optional[EntityType] = Field(default=None, alias='legal_person_type')

    def __init__(self, **kwargs):
        sub_if(kwargs, 'from_time', '0000-00-00 00:00:00', None)
        sub_if(kwargs, 'to_time', '0000-00-00 00:00:00', None)
        sub_if(kwargs, 'estiblish_time', '0000-00-00 00:00:00', None)
        if 'estiblish_time' in kwargs:
            if isinstance(kwargs['estiblish_time'], datetime):
                kwargs['estiblish_time'] = kwargs['estiblish_time'].date()
        sub_if(kwargs, 'base', '', None)
        sub_if(kwargs, 'property2', '', None)

        super().__init__(**kwargs)

    def logic_validate(self) -> bool:
        if not credit_code_valid(self.credit_code):
            return False
        if not reg_number_valid(self.reg_number):
            return False
        if self.establish_date:
            if self.establish_date < date(year=1970, month=1, day=1):
                return False
            if self.establish_date > (datetime.now() + timedelta(days=30)).date():
                return False
            if self.approved_date and self.approved_date < self.establish_date:
                return False
        return True

    def __hash__(self):
        return self.cid


class CompanyDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', Company)

        super().__init__(**kwargs)

    def update_company_org_type(self, cid, company_org_type) -> Optional[Company]:
        sql = f'update {self.db_tb_name} set company_org_type = \'{company_org_type}\' where id = {cid}'
        ret = self.mysql_client.execute(sql)
        logger.info('set deleted cid=%s ret=%s', cid, ret)
        return ret > 0

    def update_source_flag(self, cid, name, prefix) -> bool:
        for i in range(10):
            sf = prefix if i == 0 else f'{prefix}_{i}'
            sql = f'update ignore {self.db_tb_name} set name=%s, source_flag=%s where id=%s'
            ret = self.mysql_client.execute(sql, args=(name, sf, cid,))
            if ret > 0:
                logger.info(f'sf updated cid={cid} name={name}, sf={sf} ret={ret}')
                return True
        return False


company_dao = CompanyDao()
TEST_COMPANY: Company = company_dao.get('*********')  # 全维度测试有限责任公司


class CompanyPercentageDao(CompanyDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_INNER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'internal.company_percentage')
        super().__init__(**kwargs)

    def get_many_by_min_id(self, task_id: int, raw=False) -> Generator[
        Company, None, None]:
        sql = f'select * from {self.db_tb_name} where id > %s order by id asc limit 10'
        for d in self.mysql_client.select_many(sql, args=(task_id,)):
            item = self._to_entity(d, raw=raw)
            if item is not None:
                yield item


class CompanyGraph(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=None)
    cid: conint(strict=True, ge=0) = Field(alias='company_id', default=None)
    cgid: conint(strict=True, ge=0) = Field(alias='graph_id', default=None)
    deleted: int = Field(default=0)

    def logic_validate(self) -> bool:
        return True


class CompanyGraphDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_graph')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanyGraph)
        super().__init__(**kwargs)

    def set_deleted(self, cid, v=1) -> bool:
        sql = 'update ignore {} set deleted=%s where company_id=%s and deleted=0'.format(self.db_tb_name)
        ret = self.mysql_client.execute(sql, args=(v, cid,))
        logger.info('set deleted cid=%s ret=%s', cid, ret)
        return ret > 0

    def set_un_deleted(self, cid) -> bool:
        sql = 'update ignore {} set deleted=0 where company_id=%s and deleted!=0'.format(self.db_tb_name)
        ret = self.mysql_client.execute(sql, args=(cid,))
        if ret > 0:
            logger.info('set un deleted cid=%s ret=%s', cid, ret)
        return ret > 0

    def get_by_cid(self, cid, ) -> Optional[CompanyGraph]:
        sql = f'select * from {self.db_tb_name} where company_id=%s and deleted=0'
        for d in self.mysql_client.select_many(sql, args=(cid,)):
            item = self._to_entity(d)
            if item is not None:
                return item
        return None


if __name__ == '__main__':
    # company_graph_dao = CompanyGraphDao()
    # f = open('/tmp/a.txt', 'r')
    # for gid in f.read().split():
    #     o: CompanyGraph = company_graph_dao.get(value=int(gid), field='graph_id')
    #     if not o:
    #         print(f'errr {gid}')
    #         continue
    #     print(o.cid)

    company_dao = CompanyDao()
    a: Company = company_dao.get('黑河新基德经贸有限责任公司', 'name')
    print(a.credit_code)
