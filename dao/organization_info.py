# encoding=utf8

import logging
from datetime import datetime
from typing import Optional

from pydantic import Field, conint

from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from libs.collection import sub_if

logger = logging.getLogger(__name__)


class OrganizationInfo(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    company_gid: Optional[int]
    company_id: Optional[int]
    org_name: str = Field(default='')
    org_province: Optional[str] = Field(default='')
    unified_social_credit_code: Optional[str] = Field(default='')

    registration_authority: Optional[str] = Field(default='')
    business_unit: Optional[str] = Field(default='')
    legal_person: Optional[str] = Field(default='')
    legal_person_id: int = Field(default=0)
    registration_date: Optional[datetime] = Field(default=None)

    expiry_date: Optional[str] = Field(default='')

    registered_capital: Optional[str] = Field(default='')
    reg_status: Optional[str] = Field(default='')
    website: Optional[str] = Field(default='')
    phone: Optional[str] = Field(default='')
    registration_number: Optional[str] = Field(default='')
    org_types: Optional[str] = Field(default='')
    address: Optional[str] = Field(default='')
    industry_category: Optional[str] = Field(default='')
    business_scope: Optional[str] = Field(default='')
    org_source: Optional[str] = Field(default='')
    deleted: int = Field(default=0)
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    def __init__(self, **kwargs):
        sub_if(kwargs, 'registration_date', '0000-00-00', None)
        super().__init__(**kwargs)

    def logic_validate(self) -> bool:
        return True


class OrganizationInfoDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.organization_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', OrganizationInfo)
        super().__init__(**kwargs)

    def get_one_by_credit_code(self, credit_code, raw=False, ) -> Optional[OrganizationInfo]:
        sql = f'select * from {self.db_tb_name} where unified_social_credit_code = %s and deleted = 0 limit 1'
        d = self.mysql_client.select(sql, args=(credit_code,))
        return d;
        # return self._to_entity(d, raw=raw)

    def update_by_id(self, cid, org_name, registration_authority, legal_person, legal_person_id, registration_date,
                     expiry_date, registered_capital, reg_status, business_scope, org_source) -> bool:
        sql = (
            'update ignore {} set org_name=\'{}\',registration_authority=\'{}\',legal_person=\'{}\',legal_person_id={},registration_date=\'{}\',expiry_date=\'{}\',registered_capital=\'{}\',reg_status=\'{}\',business_scope=\'{}\',org_source=\'{}\' where id={} '
            .format(self.db_tb_name, org_name, registration_authority, legal_person, legal_person_id, registration_date,
                    expiry_date, registered_capital, reg_status, business_scope, org_source, cid))
        ret = 0
        print(sql)
        ret = self.mysql_client.execute(sql)
        logger.info('set deleted cid=%s ret=%s', cid, ret)
        return ret > 0

    def insert_by(self,credit_no, org_name, registration_authority, legal_person, legal_person_id, registration_date, expiry_date,
                  registered_capital, reg_status, business_scope, org_source) -> bool:
        # sql = 'update ignore {} set deleted=%s where company_id=%s and deleted=0'.format(self.db_tb_name)
        sql = f'insert into {self.db_tb_name} (unified_social_credit_code,org_name,registration_authority, legal_person, legal_person_id,registration_date, expiry_date,registered_capital,reg_status, business_scope, org_source) values(\'{credit_no}\',\'{org_name}\',\'{registration_authority}\',\'{legal_person}\',{legal_person_id},\'{registration_date}\',\'{expiry_date}\',\'{registered_capital}\',\'{reg_status}\',\'{business_scope}\',\'{org_source}\')'
        ret = 0
        print(sql)
        ret = self.mysql_client.execute(sql)
        logger.info('set deleted cid=%s ret=%s', ret)
        return ret > 0


if __name__ == '__main__':
    logger.info('x=%s', 's')
