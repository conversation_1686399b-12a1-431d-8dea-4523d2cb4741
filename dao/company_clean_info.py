# encoding=utf8

import logging
from typing import Optional
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class CompanyCleanInfo(BaseEntity):
    cid: int = Field(alias='id')
    alias: Optional[str] = Field(default=None)
    reg_capital: Optional[str] = Field(default=None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def __hash__(self):
        return self.cid


class CompanyCleanInfoDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_clean_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanyCleanInfo)

        super().__init__(**kwargs)



