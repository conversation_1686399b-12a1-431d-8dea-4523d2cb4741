# encoding=utf8

import logging
from typing import Optional
from datetime import datetime, date, timedelta
from pydantic import Field, conint
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class BranchGs(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    cid: str
    gsxt_source: Optional[str] = Field(default='')
    branch_name: Optional[str] = Field(default='')
    branch_cid: str
    history_time: datetime = Field(default_factory=datetime.now)
    other_info: Optional[str] = Field(default=None)
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)


class BranchGsDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'internal.branch_gs')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', BranchGs)

        super().__init__(**kwargs)



