# encoding=utf8

import logging
from typing import Optional
from datetime import datetime, date, timedelta
from pydantic import Field, conint
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from gslib.gs_enum import EntityType

logger = logging.getLogger(__name__)


class BranchDig(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    cid: str
    branch_name: Optional[str] = Field(default='')
    branch_cid: str
    source: Optional[int] = Field(default=0)
    is_delete: Optional[int] = Field(default=0)
    other_info: Optional[str] = Field(default=None)
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)


class BranchDigDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'internal.branch_dig')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', BranchDig)

        super().__init__(**kwargs)

    def i_u_by_any(self, *args, **kwargs):
        items_new = list()
        for item in args:
            items_new.append(BranchDig.from_dict(item))

        self.save_by_group(items_new, ['cid'], ['branch_cid'])



