from datetime import datetime
from typing import Optional
from pydantic import Field, conint
from resx.base_model import BaseModel


class HkAnnualBrief(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    hk_id: Optional[int] = Field(default=0)
    report_year: Optional[str] = Field(default='')
    return_date: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)
