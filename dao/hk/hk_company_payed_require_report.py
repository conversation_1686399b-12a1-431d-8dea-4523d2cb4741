# encoding=utf8
from typing import List
from datetime import datetime
from pydantic import Field, conint
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import get_props_mysql


class HKCompanyPayedRequireReport(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    graph_id: int  # UNIQ KEY
    pay_time: int = Field(default=1)
    status: int
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)


class HKCompanyPayedRequireReportDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in get_props_mysql(inst='mysql.tyc.other.backend_1').items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'biz.hk_company_payed_require_report')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', HKCompanyPayedRequireReport)
        super().__init__(**kwargs)

    def set_task(self, gid, status):
        sql = f'update {self.db_tb_name} set status = %s where graph_id = %s limit 1'
        ret = self.mysql_client.execute(sql, args=(status, gid))
        return ret

    def success_task(self, gid):
        sql = f'update {self.db_tb_name} set status = %s where graph_id = %s limit 1'
        ret = self.mysql_client.execute(sql, args=(1, gid))
        return ret

    def get_tasks(self) -> List[HKCompanyPayedRequireReport]:
        sql = f'select * from {self.db_tb_name} where status=%s'
        items: List = list()
        for o in self.mysql_client.select_many(sql, args=(0,), ):
            item = self.entity_class.from_dict(o)
            if item is not None:
                items.append(item)
        return items


def main():
    from libs.log import setup_logger
    logger = setup_logger()
    o = HKCompanyPayedRequireReportDao()
    items_ = o.get_tasks()
    logger.info(f'items={items_}')


if __name__ == '__main__':
    main()
