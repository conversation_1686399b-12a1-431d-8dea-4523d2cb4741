from datetime import datetime
from typing import Optional
from pydantic import Field, conint

from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps


class HkPayInfo(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=None)
    hk_id: Optional[int] = Field(default=0)
    last_pay_time: Optional[datetime] = Field(default=None)

    def logic_validate(self) -> bool:
        return True


class HkPayInfoDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.hk_pay_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', HkPayInfo)
        super().__init__(**kwargs)

    def i_u_by_any(self, *args, **kwargs):
        self.save_by_cmp(item=HkPayInfo.from_dict(kwargs), fields=['hk_id'])


if __name__ == '__main__':
    info = dict()
    info.update({'hk_id': 2})
    info.update({'last_pay_time': datetime.now()})
    HkPayInfoDao(**ConstantProps.PROPS_GS_TEST).save_by_cmp(item=HkPayInfo.from_dict(info), fields=['hk_id'])