from datetime import datetime
from typing import Optional
from pydantic import Field, conint

from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps


class HkRegisterCharges(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    hk_id: int
    register_name: Optional[str] = Field(default='')
    register_time: Optional[str] = Field(default='')
    file_name: Optional[str] = Field(default='')
    save_time: Optional[str] = Field(default='')
    file_num: Optional[str] = Field(default='')
    file_pages: Optional[str] = Field(default='')
    file_sizes: Optional[str] = Field(default='')
    file_overview: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)


class HkRegisterChargesDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.hk_register_charges')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', HkRegisterCharges)
        super().__init__(**kwargs)

    def i_u_by_any(self, *args, **kwargs):
        items_new = list(map(lambda x: HkRegisterCharges.from_dict(x), args))
        self.save_by_group(items_new, ['hk_id'], ['file_num'])



