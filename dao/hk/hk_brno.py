from datetime import datetime
from typing import Optional
from pydantic import Field, conint

from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps


class HkBrno(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    hk_id: Optional[int] = Field(default=0)
    brno: Optional[str] = Field(default='')

    def logic_validate(self) -> bool:
        return True


class HkBrnoDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.hk_brno')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', HkBrno)
        super().__init__(**kwargs)

    def i_u_by_any(self, *args, **kwargs):
        self.save_by_cmp(item=HkBrno.from_dict(kwargs), fields=['hk_id'])


if __name__ == '__main__':
    info = dict()
    info.update({'hk_id': 2})
    info.update({'brno': 1234})
    HkBrnoDao(**ConstantProps.PROPS_GS_TEST).save_by_cmp(item=HkBrno.from_dict(info), fields=['hk_id'])