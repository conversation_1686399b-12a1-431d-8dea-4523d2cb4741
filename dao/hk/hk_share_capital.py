from datetime import datetime

from typing import Optional
from pydantic import Field, conint

from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps


class HkShareCapital(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=None)
    hk_id: Optional[int] = Field(default=0)
    issued: Optional[str] = Field(default='')
    paid_up: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)

    def logic_validate(self) -> bool:
        return True


class HkShareCapitalDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.hk_share_capital')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', HkShareCapital)
        super().__init__(**kwargs)


    def i_u_by_any(self, *args, **kwargs):
        items = self.get(kwargs['hk_id'], 'hk_id')
        if items:
            fields = ['id']
            kwargs['id'] = dict(items)['id']
        else:
            fields = ['hk_id']
        item = HkShareCapital.from_dict(kwargs)
        if item is None:
            raise Exception('hk_share_capital输入格式不对')
        sign, i_u, row = self.save_by_cmp(item, fields=fields)

        return sign
