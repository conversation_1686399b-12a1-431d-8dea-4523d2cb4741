from datetime import datetime
from typing import Optional
from pydantic import Field, conint
from resx.base_model import BaseModel


class HkCompanyAuthorized(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=None)
    hk_id: Optional[int] = Field(default=0)
    cn_name: Optional[str] = Field(default='')
    en_name: Optional[str] = Field(default='')
    address: Optional[str] = Field(default='')
    cr_no: Optional[str] = Field(default='')
    passport_no: Optional[str] = Field(default='')
    passport_issuing_place: Optional[str] = Field(default='')
    appointment: Optional[datetime] = Field(default=None)
    entity_type: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default=None)
    update_time: Optional[datetime] = Field(default=None)
