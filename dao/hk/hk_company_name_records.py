from datetime import datetime
from typing import Optional
from pydantic import Field, conint

from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps


class HkCompanyNameRecords(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    hk_id: int
    history_name_cn: str = Field(default='')
    history_name_en: str = Field(default='')
    change_time: datetime
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)


class HkCompanyNameRecordsDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.hk_company_name_records')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', HkCompanyNameRecords)
        super().__init__(**kwargs)

    def i_u_by_any(self, *args, **kwargs):
        items_new = list()
        for item in args:
            items_new.append(HkCompanyNameRecords.from_dict(item))

        self.save_by_group(items_new, ['hk_id'], ['change_time'])


if __name__ == '__main__':
    pass
