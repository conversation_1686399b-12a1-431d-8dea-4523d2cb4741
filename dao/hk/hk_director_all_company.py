from datetime import datetime
from typing import Optional
from pydantic import Field, conint
from resx.base_model import BaseModel


class HkDirectorAllCompany(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    director_id: Optional[int] = Field(default=0)
    company_num: Optional[str] = Field(default='')
    cr_no: Optional[str] = Field(default='')
    passport_no: Optional[str] = Field(default='')
    company_name: Optional[str] = Field(default='')
    company_type: Optional[str] = Field(default='')
    remark: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default=None)
    update_time: Optional[datetime] = Field(default=None)
