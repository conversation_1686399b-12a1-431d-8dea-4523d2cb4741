from datetime import datetime
from typing import Optional
from pydantic import Field, conint
from resx.base_model import BaseModel


class HkDirectorsNew(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=None)
    hk_id: int = Field(default=0)
    name_english: Optional[str] = Field(default='')
    name_chinese: Optional[str] = Field(default='')
    cr_no: Optional[str] = Field(default='')
    passport_no: Optional[str] = Field(default='')
    passport_iss_country: Optional[str] = Field(default='')
    entity_type: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default=None)
    update_time: Optional[datetime] = Field(default=None)
