from datetime import datetime
from typing import Optional
from pydantic import Field, conint
from resx.base_model import BaseModel


class HkAnnualCapital(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    hk_id: int = Field(default=0)
    shares_class: Optional[str] = Field(default='')
    currency: Optional[str] = Field(default='')
    issued_total_number: Optional[str] = Field(default='')
    issued_total_amount: Optional[str] = Field(default='')
    issued_total_amount_paid: Optional[str] = Field(default='')
    update_time: Optional[datetime] = Field(default_factory=datetime.now)
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
