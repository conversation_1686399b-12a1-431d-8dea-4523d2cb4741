from datetime import datetime
from typing import Optional, Union
from pydantic import Field, conint

from resx.base_model import BaseModel


class HkImageList(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    hk_id: int = Field(default=0)
    image_id_str: str = Field(default='')
    image_id: int = Field(default=0)
    image_name: Optional[str] = Field(default='')
    image_name_s: Optional[str] = Field(default='')
    filing_date: Union[datetime, str, None] = Field(default=None)
    pages: Optional[int] = Field(default=0)
    size: Optional[str] = Field(default='')
    state: Optional[str] = Field(default='')
    image_type: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)
