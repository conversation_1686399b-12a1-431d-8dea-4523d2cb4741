from datetime import datetime
from typing import Optional, Union
from pydantic import Field, conint

from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps


class HkPdfDownloadRecord(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=None)
    record_id: Union[int, str] = Field(default=0)
    obs_url: Optional[str] = Field(default='')
    download_time: Optional[datetime] = Field(default='0000-00-00 00:00:00')
    pdf_source: Optional[int] = Field(default=0)
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)

    def logic_validate(self) -> bool:
        return True


class HkPdfDownloadRecordDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.hk_pdf_download_record')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', HkPdfDownloadRecord)
        super().__init__(**kwargs)

    def i_u_by_any(self, *args, **kwargs):
        items = self.get(kwargs['record_id'], 'record_id')
        if items:
            fields = ['id']
            kwargs['id'] = dict(items)['id']
        else:
            fields = ['record_id']
        kwargs['download_time'] = datetime.now()
        item = HkPdfDownloadRecord.from_dict(kwargs)
        if item is None:
            raise Exception('hk_pdf_download_record输入格式不对')
        sign, i_u, row = self.save_by_cmp(item, fields=fields)
        return row


if __name__ == '__main__':
    barcode = 23502043506
    HkPdfDownloadRecordDao().i_u_by_any(**{'record_id': barcode, 'obs_url': f'hk_pdf/{str(barcode)}.pdf', 'download_time': datetime.now(), 'pdf_source': 2})
