from datetime import datetime
from typing import Optional
from pydantic import Field, conint
from resx.base_model import BaseModel


class HkAnnualShareholders(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    hk_id: Optional[int] = Field(default=0)
    shares_class: Optional[str] = Field(default='')
    shareholder_name: Optional[str] = Field(default='')
    address: Optional[str] = Field(default='')
    current_holding: Optional[str] = Field(default='')
    ratio: Optional[str] = Field(default='')
    remarks: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)
    name_en: Optional[str] = Field(default='')
    name_cn: Optional[str] = Field(default='')
    inv_id: Optional[int] = Field(default=0)
    inv_type: Optional[int] = Field(default=2)
