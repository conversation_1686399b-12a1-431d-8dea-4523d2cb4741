from datetime import datetime
from typing import Optional
from pydantic import Field, conint

from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps


class HkRegisteredOfficeNew(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    hk_id: int = Field(default=0)
    registered_office: Optional[str] = Field(default='')
    office_type: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)

    def logic_validate(self) -> bool:
        return True


class HkRegisteredOfficeNewDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.hk_registered_office_new')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', HkRegisteredOfficeNew)
        super().__init__(**kwargs)

    def i_u_by_any(self, *args, **kwargs):
        items = self.get(kwargs['hk_id'], 'hk_id')

        if items:
            fields = ['id']
            kwargs['id'] = dict(items)['id']
        else:
            fields = ['hk_id']
        item = HkRegisteredOfficeNew.from_dict(kwargs)
        if item is None:
            raise Exception('hk_registered_office_new输入格式不对')
        sign, i_u, row = self.save_by_cmp(item, fields=fields)

        return sign
