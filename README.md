# pygs-work-parent

工商组标准化项目集合

1 Python版本使用3.9

2 严格遵守reqirements.txt

3 代码组织标准参考demo项目


重要依赖包
* venv/bin/pip install  -i https://pypi.tuna.tsinghua.edu.cn/simple XXX
* requests 网络请求 
* beautifulsoup4 html解析
* DBUtils (数据库)连接池
* pydantic 实体类
* redis Redis连接
* sshtunnel ssh隧道通信
* PyYAML 配置文件格式
* netifaces IP 网络信息
* pymysql 数据库链接
* jsonpickle JSON序列化
* kafka-python kafka读写
* supervisor 任务管理
* flask 服务开发
* gunicorn gevent 服务部署 

目录结构
* libs 库文件
* apps 目录，字目录都是文件 
* logs 日志目录，软链接


部署：
mkdir /data/pygs_logs/   ln -s /data/pygs_logs/ logs
************  /home/<USER>

安装Python
scp  -i ~/Documents/work.pem  /Users/<USER>/Downloads/Python-3.9.18.tar.xz work@************:~/python
tar -xf Python-3.9.18.tar.xz
./configure --prefix=/home/<USER>/python/python/
make && make install
../python/python/bin/python3 -m venv venv
venv/bin/pip3 install -r requirements.txt  -i https://pypi.tuna.tsinghua.edu.cn/simple

./venv/bin/pip3 install --index-url http://*************:3141/simple/ --upgrade public-utils-configs --trusted-host *************


../venv/bin/supervisord  -c supervisord.conf
../venv/bin/supervisorctl update gs_spider_sh_crawler
 

本地依赖：
./venv/bin/pip3 install --index-url http://*************:3141/simple/ --upgrade public-utils-configs --trusted-host *************
./venv/bin/pip3 install resx --upgrade --index-url http://*************:3141/simple/ --trusted-host *************                           
