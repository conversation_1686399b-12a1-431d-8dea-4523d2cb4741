# encoding=utf8

import logging
from typing import List, Generator, Dict
from itertools import groupby

logger = logging.getLogger(__name__)


def split_parts(inputs, sz: int) -> Generator[List, None, None]:
    buf = []
    for x in inputs:
        buf.append(x)
        if len(buf) == sz:
            yield buf
            buf.clear()
    if len(buf) > 0:
        yield buf


def sub_if(d: Dict, k, old, new):
    if k in d and d[k] == old:
        d[k] = new


# refer: https://stackoverflow.com/questions/29645415/python-zip-by-key
def zip_by_key(lst0: List, lst1: List, key_func=None):
    lst0 = list((x, 0) for x in lst0)
    lst1 = list((x, 1) for x in lst1)
    key_func = (lambda x: x[0][0]) if not key_func else (lambda x: key_func(x[0]))
    for k, v in groupby(sorted(lst0 + lst1, key=key_func), key_func):
        v = list(v)
        if len(v) == 1:
            if v[0][1] == 0:
                yield v[0][0], None
            else:
                yield None, v[0][0]
        elif v[0][1] == 0:
            yield v[0][0], v[1][0]
        else:
            yield v[1][0], v[0][0]


if __name__ == '__main__':
    a = [(3, 1), (2, 5)]
    b = [(2, 1), (1, 0)]
    print(f'{list(zip_by_key(a, b))}')
