# encoding=utf8

import sys
import traceback
import netifaces
import os
import re
import yaml
from functools import lru_cache
from typing import Optional, List


# 获取当前设备的网卡IP，这里假设机器只有一个IP
# https://stackoverflow.com/questions/24196932/how-can-i-get-the-ip-address-from-nic-in-python
@lru_cache(None)
def get_my_ip() -> Optional[str]:
    for if_name in netifaces.interfaces():
        interface = netifaces.ifaddresses(if_name)
        if netifaces.AF_INET not in interface:
            continue
        if len(interface[netifaces.AF_INET]) == 0:
            continue
        ip: str = interface[netifaces.AF_INET][0].get('addr', None)
        if not ip:
            continue
        if ip != '127.0.0.1' and not ip.startswith('169.254'):
            print('get_my_ip -> ', ip)
            return ip
    return None


# 获取默认的项目根目录
@lru_cache(None)
def get_pygs_dir():
    cur_path = os.path.abspath(__file__)
    while True:
        new_path = os.path.dirname(cur_path)
        if new_path == cur_path:
            print('error get get_env_yml_path, at root path %s, no env.yaml? exit 1' % cur_path)
            exit(1)
        cur_path = new_path
        env_yml_path = os.path.join(cur_path, 'env.yml')
        if os.path.isfile(env_yml_path):
            return cur_path


# 选取匹配上的第一个，如果没有匹配上，则表示只能使用默认配置
@lru_cache(None)
def get_env() -> Optional[str]:
    my_ip = get_my_ip()
    if my_ip is None:
        print('get_env_prop cannot get my_ip, exit 1')
        exit(1)
    with open(os.path.join(get_pygs_dir(), 'env.yml'), 'rb') as f:
        item = yaml.safe_load(f)
        for pat, env in item.get('env', {}).items():
            if re.fullmatch(pat, my_ip):
                return env
    return None


@lru_cache(None)
def detect_tunnel():
    return get_env() == 'env_tyc_office'


# 获取配置值 根据ip正则匹配获取环境
# real_path需要包含path，且优先选取env对比的上的real_path
@lru_cache(None)
def get_env_prop(path: str, default=None):
    env = get_env()
    with open(os.path.join(get_pygs_dir(), 'env.yml'), 'rb') as f:
        item = yaml.safe_load(f)
        prop = None
        if env is not None:
            prop = _dfs_travel_path(item, [env, ] + path.split('.'))
        if not prop:
            prop = _dfs_travel_path(item, path.split('.'))
        print('get_env_prop %s  %s -> %s' % (env, path, prop))
        if prop is None:
            if default is None:
                raise ValueError('prop=%s not found' % path)
            else:
                return default
        else:
            return prop


def _dfs_travel_path(item, path: List[str]):
    # print('item=%s path=%s' % (item, path))
    if len(path) == 0:
        return item
    if isinstance(item, dict):
        path_sub = path[0]
        if path_sub in item:
            x = _dfs_travel_path(item[path_sub], path[1:])
            if x is not None:
                return x
        if 'default' in item:
            return _dfs_travel_path(item['default'], path[1:])
    return None


def get_props_mysql(inst: str):
    return {
        'host': get_env_prop(inst + '.host', default='localhost'),
        'port': get_env_prop(inst + '.port', default=3306),
        'user': get_env_prop(inst + '.user', default='work'),
        'password': get_env_prop(inst + '.password', default='work'),
    }


def get_props_redis(inst: str):
    return {
        'host': get_env_prop(inst + '.host', default='localhost'),
        'port': get_env_prop(inst + '.port', default=6379),
        'password': get_env_prop(inst + '.password', default='work'),
    }


def get_props_obs(inst: str):
    return {
        'server': get_env_prop(inst + '.server'),
        'access_key_id': get_env_prop(inst + '.access_key_id'),
        'secret_access_key': get_env_prop(inst + '.secret_access_key'),
    }


def get_props_kafka(inst: str):
    return {
        'bootstrap_servers': get_env_prop(inst + '.bootstrap_servers'),
    }


class ConstantProps:
    PROPS_GS_INNER_RO = get_props_mysql(inst='mysql.tyc.gs.hw_gsxt_inner_ro1')
    PROPS_GS_INNER_RW = get_props_mysql(inst='mysql.tyc.gs.hw_gsxt_inner_rw')
    PROPS_GS_OUTER_RO = get_props_mysql(inst='mysql.tyc.gs.hw_gsxt_outer_ro3')
    PROPS_GS_OUTER_RW = get_props_mysql(inst='mysql.tyc.gs.hw_gsxt_outer_rw')
    PROPS_GS_NG = get_props_mysql(inst='mysql.tyc.gs.hw_gsxt_ng_rw')
    PROPS_CHANNEL = get_props_mysql(inst='mysql.tyc.other.channel')

    rds113_hw2_test = get_props_mysql(inst='mysql.tyc.zx.rds113-hw2-test')

    PROPS_GS_TEST = get_props_mysql(inst='mysql.tyc.gs.hw_gsxt_test')
    PROPS_QX_LIST = [get_props_mysql(inst='mysql.tyc.qxb.' + chr(ord('a') + db_id // 4)) for db_id in range(16)]

    PROPS_ZX_RDS111 = get_props_mysql(inst='mysql.tyc.zx.zhuanxiang_rds111')
    PROPS_ZX_RDS110 = get_props_mysql(inst='mysql.tyc.zx.zhuanxiang_rds110')
    PROPS_ZX_RDS108 = get_props_mysql(inst='mysql.tyc.zx.zhuanxiang_rds108')
    PROPS_ZX_PLATFORM = get_props_mysql(inst='mysql.tyc.zx.zhuanxiang_platform')

    PROPS_JX_SFTP = {
        'hostname': get_env_prop('tyc.tunnel_al.host'),
        'username': get_env_prop('tyc.tunnel_al.user'),
        'password': get_env_prop('tyc.tunnel_al.password'),
    }

    PROPS_GS_REDIS_ONLINE = get_props_redis('redis.tyc.gs')
    PROPS_GS_REDIS_TEST = get_props_redis('redis.tyc.gs_test')

    PROPS_GS_KAFKA_ONLINE = get_props_kafka('kafka.tyc.gs')
    PROPS_GS_KAFKA_DATA = get_props_kafka('kafka.tyc.gs_data')
    PROPS_GS_KAFKA_TEST = get_props_kafka('kafka.tyc.gs_test')
    PROPS_KAFKA_CANAL_PROD = get_props_kafka('kafka.tyc.canal_prod')


def get_stack_info():
    s = ''.join(traceback.format_stack()[:-1] + traceback.format_exception(*sys.exc_info())[1:])
    # s = s.replace('\n', '').replace('\r', ' ')
    return s


if __name__ == '__main__':
    # print(get_my_ip())
    for key, value in ConstantProps.rds113_hw2_test.items():
        print(key, value)
