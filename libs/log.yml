version: 1

# 将信息传播到配置文件的跟日志记录器中
disable_existing_loggers: False

formatters:
  simple:
    format: '%(levelname)-8s %(threadName)s %(asctime)s %(filename)s:%(lineno)-3d %(funcName)s - %(message)s'

handlers:
  console:
    class: logging.StreamHandler
    level: DEBUG
    formatter: simple
    stream: ext://sys.stdout

  file_handler:
    class: logging.handlers.TimedRotatingFileHandler
    level: INFO
    formatter: simple
    filename: ${app_log}  # update by proc
    # maxBytes: 2048576000 # 2GB
    when: ${when}
    backupCount: ${backup_count}   # update by proc
    encoding: utf8

  #file_warn:
  #  class: logging.handlers.WatchedFileHandler
  #  level: WARN
  #  formatter: simple
  #  filename: ${app_name}.warn.log  # update by proc
  #  encoding: utf8

loggers:
  __main__:  # 启动程序
    level: DEBUG
    handlers: [console, file_handler]
    propagate: False

  apps:
    level: INFO
    handlers: [console, file_handler]
    propagate: False

  #clients:
  #  level: INFO
  #  handlers: [console, file_handler]
  #  propagate: False

root:  # 默认
  level: INFO
  handlers: [console, file_handler]