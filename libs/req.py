# encoding=utf8
import time
import logging
import urllib3
import base64
from typing import Dict, Optional
from threading import Lock, current_thread
from requests import Session, Response
from libs.env import get_pygs_dir

logger = logging.getLogger(__name__)
project_dir = get_pygs_dir()


# 每个线程保持session进行请求
# 重试的时候清理掉 proxyBase 达到切换proxy的逻辑
class SessionRequestManager(object):
    def __init__(self):
        self.sessions: Dict[int, Session] = dict()
        self.sessions_lock = Lock()
        self.default_proxies = {"https": "http://************:30636", "http": "http://************:30636"}
        self.default_cert = ('{}/{}'.format(project_dir, 'conf/rootCA.crt'), '{}/{}'.format(project_dir, 'conf/rootCA.key'))
        self.default_headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 MicroMessenger/6.8.0(0x16080000) NetType/WIFI MiniProgramEnv/Mac MacWechat/WMPF XWEB/30515',
            'Accept-Language': 'zh-CN,zh',
        }
        urllib3.disable_warnings()

    def request(
            self,
            url,
            response_validate_func=None,
            response_validate_ctx=None,
            headers=None,
            post_data=None,  # None则做GET、否则做POST
            timeout=3.0,
            tries=3,  # 总共试几次
            use_proxy=True,  # 是否使用代理，默认使用
            proxies=None,  # None则采用默认代理default_proxies
            force_new_session=False,  # 强制重置session
            # cert=None,  # 证书 默认证书在conf目录
    ) -> Optional[Response]:
        with self.sessions_lock:
            tid = current_thread().ident
            if tid not in self.sessions or force_new_session:
                self.sessions[tid] = Session()
            session = self.sessions[tid]

        response_validate_func = response_validate_func or self.response_validate_default
        headers = headers or self.default_headers
        proxies_ori = proxies or {}
        proxies = None if not use_proxy else (proxies or self.default_proxies)

        for try_id in range(tries):
            start_ts = time.time()
            try:
                if post_data is not None:
                    response = session.post(
                        url=url,
                        headers=headers,
                        proxies=proxies,
                        data=post_data,
                        timeout=timeout,
                        verify=False,
                    )
                else:
                    response = session.get(
                        url=url,
                        headers=headers,
                        proxies=proxies,
                        timeout=timeout,
                        verify=False,
                    )
            except Exception as e:  # requests.ReadTimeout
                response = None
                logger.warning('exception in request e=%s url=%s', e, url)

            cost = time.time() - start_ts
            length = len(response.text) if response else 0
            code = response.status_code if response else 0
            proxy_ip = proxies_ori.get('http', None) or self.get_session_proxy(session)
            logger.info(f'try={try_id} proxy={proxy_ip} code={code} length={length} cost={cost:0.2f} url={url}')
            if response is None:
                del session.cookies['proxyBase']
                continue
            ret = response_validate_func(response, response_validate_ctx)
            if not ret:
                del session.cookies['proxyBase']
            else:
                logger.info(f'OK try={try_id} proxy={proxy_ip} code={code} length={length} cost={cost:0.2f} url={url}')
                return response
        logger.warning(f'exceed max try, return response=None url={url}')
        return None

    @staticmethod
    def get_session_proxy(session: Session) -> Optional[str]:
        encode_ip = session.cookies.get_dict().get('proxyBase', None)
        if not encode_ip:
            return session.proxies.get('http')
        return base64.standard_b64decode(encode_ip).decode()

    @staticmethod
    def response_validate_default(response: Response, response_validate_ctx=None):
        if response.status_code >= 400:
            return None
        if len(response.text) > 0:
            return True
        return False


if __name__ == '__main__':
    pass
