# encoding=utf8

import os
import logging.config
import yaml
from libs.env import get_pygs_dir


def setup_logger(
        use_file_log=False,  # 使用标准输出作为日志 此时后续选项无效
        app_name='main',  # 应用名称
        backup_count=1,  # 备份数
        rotate_by_day=True,  # 按天备份
    ):

    if not use_file_log:
        logging.basicConfig(
            format='%(levelname)-8s %(threadName)s %(asctime)s %(filename)s:%(lineno)-3d %(funcName)s - %(message)s',
            level=logging.INFO,
        )
        return logging.getLogger(__name__)

    log_yml_path = f'{get_pygs_dir()}/libs/log.yml'

    log_config_data = {
        '${app_log}': f'{get_pygs_dir()}/logs/{app_name}.log',
        '${backup_count}': backup_count,
        '${when}': 'midnight' if rotate_by_day else 'H'
    }
    fin = open(file=log_yml_path, mode='r', encoding='utf-8')
    log_yml_content = fin.read()
    for var, replacement in log_config_data.items():
        log_yml_content = log_yml_content.replace(var, str(replacement))

    config_dict = yaml.load(stream=log_yml_content, Loader=yaml.FullLoader)
    logging.config.dictConfig(config=config_dict)

    return logging.getLogger('__main__')
