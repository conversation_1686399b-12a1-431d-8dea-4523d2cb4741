# encoding=utf8

from typing import List
import json
import logging
import requests
from requests_toolbelt import MultipartEncoder

logger = logging.getLogger(__name__)

open_id_dict = {
    '所有人': 'all',
    '史安琪': 'ou_51e8bc246873e0f4c31176484dc6f72b',
    '丁良益': 'ou_cd3c38e542a51827dd48c3655b545c5c',
    '于志国': 'ou_66775216bebe1d65e8d9226de6b70ace',
    '王帮旭': 'ou_8502bcf8edd9fc542defdea8492bc64e',
    '金炜': 'ou_4048a112b97d5e09408bbd5034f9dd96',
    '薛秋雨': 'ou_4654cf587d9fb496122152a81fc937c8',
    '张晓永': 'ou_98ecdf1cd534de8d8a6e605a424e6551',
    '张鑫明': 'ou_aaefa731a515cff950d77b414c65c3c9',
    '唐杰成': 'ou_db3fd038bbaae64836f212c6402793da',
    '潘仕江': 'ou_604d8012c65cc30ae641947f23d3071c',
    '杜波': 'ou_e5f08c8f224a6d548c9a3365b4e86cf9',
    '阮齐明': 'ou_e60e68b1cdfd3c5aa4b01955ad8009e9',
}

# https://open.feishu.cn/open-apis/bot/v2/hook/fa593330-a4ee-40c1-936f-447892785924
def send_feishu_message(
        ats: List[str] = None,  # 需要at到的人
        text: str = '',  # 需要发送的文本 可以是空
        url: str = '' #  webhook地址
) -> bool:
    content_text = ''.join(f'<at user_id=\"{open_id_dict[at]}\"></at>' for at in (ats or []) if at in open_id_dict)
    content_text += '\n' + text
    # url = 'https://open.feishu.cn/open-apis/bot/v2/hook/fa593330-a4ee-40c1-936f-447892785924'
    headers = {
        "Content-Type": "application/json; charset=utf-8",
    }
    payload = {
        "msg_type": "text",
        "content": {
            "text": content_text,
        }
    }
    response = requests.post(url=url, data=json.dumps(payload), headers=headers)
    logger.info(f'{response.status_code} {response.text}')
    return True


def get_tenant_access_token(app_id, app_secret):
    # app_id = "cli_a5d070aa16241013"
    # app_secret = "oCRAdOWS6B84cQugh896AckkdqcpBAa3"
    url = ' https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal'
    headers = {
        "Content-Type": "application/json; charset=utf-8",
    }
    payload_data = {
        "app_id": app_id,
        "app_secret": app_secret
    }
    response = requests.post(url=url, data=json.dumps(payload_data), headers=headers).json()
    tenant_access_token = response['tenant_access_token']
    # print(f'tenant_access_token={tenant_access_token}')
    return tenant_access_token


def upload_file(file, app_id, app_secret, file_name='1.pdf'):
    url = "https://open.feishu.cn/open-apis/im/v1/files"
    multi_form = MultipartEncoder({'file_type': 'stream',  'file_name': file_name, 'file': file})
    headers = {
        'Authorization': f'Bearer {get_tenant_access_token(app_id, app_secret)}',
        'Content-Type': multi_form.content_type,
    }
    response = requests.post(url, headers=headers, data=multi_form)
    # print(response.headers['X-Tt-Logid'])  # for debug or oncall
    try:
        key = response.json()['data']['file_key']
    except:
        logger.warning(f'error get key {response}')
        return None
    logger.info(f'{key} {multi_form.content_type}')
    return key


def upload_image(image, app_id, app_secret):
    url = "https://open.feishu.cn/open-apis/im/v1/images"
    multi_form = MultipartEncoder({'image_type': 'message', 'image': image})
    headers = {
        'Authorization': f'Bearer {get_tenant_access_token(app_id, app_secret)}',
        'Content-Type': multi_form.content_type,
    }
    response = requests.post(url, headers=headers, data=multi_form)
    # print(response.headers['X-Tt-Logid'])  # for debug or oncall
    try:
        key = response.json()['data']['image_key']
    except:
        logger.warning(f'error get key {response}')
        return None
    logger.info(f'{key} {multi_form.content_type}')
    return key


def send_feishu_message_attachments_sub(chat_id, msg_type, msg_content, app_id, app_secret):
    url = "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id"
    payload = json.dumps({
        "receive_id": chat_id,
        "msg_type": msg_type,
        "content": json.dumps(msg_content)
    })
    headers = {
        'Authorization': f'Bearer {get_tenant_access_token(app_id, app_secret)}',
        'Content-Type': 'application/json'
    }
    response = requests.post(url, headers=headers, data=payload)
    logger.info(f'{response.status_code} {response.text}')
    return True


# send file https://open.feishu.cn/document/server-docs/im-v1/file/create
# get chat_id  https://open.feishu.cn/document/server-docs/group/chat/list?appId=cli_a5d070aa16241013&open_in_browser=true
#  工商抓取小队 oc_ff3954e7a265e2b30587f82d401d80f9
def send_feishu_message_attachments(chat_id: str, ats=None, text='', image=None, file=None, file_name='1.txt') -> bool:
    app_id = "cli_a5d070aa16241013"
    app_secret = "oCRAdOWS6B84cQugh896AckkdqcpBAa3"
    if ats or len(text) > 0:
        content_text = ''.join(f'<at user_id=\"{open_id_dict[at]}\"></at>' for at in (ats or []) if at in open_id_dict)
        content_text += '\n' + text
        send_feishu_message_attachments_sub(chat_id, 'text', {'text': content_text}, app_id, app_secret)
    if image:
        image_key = upload_image(image, app_id, app_secret)
        if not image_key:
            logger.warning(f'no image_key for image')
            return False
        send_feishu_message_attachments_sub(chat_id, 'image', {'image_key': image_key}, app_id, app_secret)
    if file:
        file_key = upload_file(file, app_id, app_secret, file_name=file_name)
        if not file_key:
            logger.warning(f'no image_key for file')
            return False
        send_feishu_message_attachments_sub(chat_id, 'file', {'file_key': file_key}, app_id, app_secret)

    return True


if __name__ == '__main__':
    send_feishu_message(ats=['王帮旭'], text='test王帮旭', url='https://open.feishu.cn/open-apis/bot/v2/hook/3664d1a3-046a-48b3-aa5c-78e7d48aec5d')
