import json
import re
from typing import Dict, Tuple
import argparse
import csv
import time
import pydantic
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor
from dao.company import Company, CompanyDao
from dao.reports.annual_report import AnnualReportDao, AnnualReport
from dao.reports.annual_report_brief import AnnualReportBriefDao, AnnualReportBrief
from clients.redis.redis_queue import RedisQueue
from gslib.msv import msv_query_base_info
from libs.dt import to_date

logger = setup_logger()
fs = dict()
process_code_stat = Counter()
company_dao = CompanyDao()


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    NOT_GSXT = auto()
    SMALL_GROUP = auto()

    BAD_INST_CFG = auto()
    OK = auto()
    MISS = auto()


class TaskContext(pydantic.BaseModel):
    reg_inst: str
    company_count: int = -1
    code: ProcessCode = pydantic.Field(default=ProcessCode.INITIAL)
    context: Dict = pydantic.Field(default_factory=dict)
    ts: float = pydantic.Field(default_factory=time.time)


loc_map = {}
for line_ in open('area_code_dat.txt', 'r'):
    province, city, district, name1 = line_.strip().split(' ')
    if city == 'None':
        loc_map[province] = (province, None, None)
        loc_map[name1] = (province, None, None)
    elif district == 'None':
        loc_map[city] = (province, city, None)
        loc_map[name1] = (province, city, None)
    else:
        loc_map[district] = (province, city, district)
        loc_map[name1] = (province, city, district)
logger.info(f'loc_map size {len(loc_map)}')


inst_title_set = {
    '工商行政管理局',
    '市场监督管理局',
    '行政审批局',
    '行政审批和政务服务局',
    '工商行政和质量技术监管局',
    '工商所',
}
special_district_set = {
    '高新技术产业开发区',
    '经济技术开发区',
    '长江新区',
    '高新区',
    '天府新区',
    '东湖新技术开发区',
    '新旧动能转换先行区',
    '转型综合改革示范区',
    '江北新区',
    '西湖管理区',
    '金普新区',
}


def process(tc: TaskContext):

    for pat in pats:
        mo = re.search(pat, tc.reg_inst)
        if mo:
            target = None
            for s in mo.groups()[::-1]:
                if s in area_code_province_index:
                    # logger.info(f'GET {s} -> {area_code_province_index[s]} ')
                    if not target:
                        target = area_code_province_index[s]
                    else:
                        target1 = area_code_province_index[s]
                        eq = True
                        for k in range(3):
                            if target1[k] != target[k] and target1[k] != 'None' and target[k] != 'None':
                                eq = False
                        if not eq:
                            logger.info(f'CHANGE {s} {target} -> {target1}')
                            target = target1
            if not target:
                continue
            # logger.info(f'OUTPUT {tc.reg_inst}  {target}')
            tc.context['output'] = target
            tc.code = ProcessCode.OK
            return

    if tc.reg_inst in bad_reg_inst_set:
        tc.code = ProcessCode.BAD_INST_CFG
        return

    logger.info(f'MISS {tc.reg_inst} company_count={tc.company_count} {area_code_info}')
    tc.code = ProcessCode.MISS


companies_total, companies_ok = 0, 0


def callback_fn(f: Future):
    global companies_ok, companies_total
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    companies_total += tc.company_count
    if tc.code == ProcessCode.OK:
        companies_ok += tc.company_count
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat} ok_ratio={companies_ok/companies_total}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')

    with BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool:
        for did, reg_inst in enumerate(reg_inst_index):
            if did >= args.lid_max:
                break
            tc = TaskContext(reg_inst=reg_inst)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat} ok_ratio={companies_ok/companies_total}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='reg_institute_area_info.py')
    ap.add_argument('--worker-num', type=int, default=1)
    ap.add_argument('--lid-max', type=int, default=1000)

    main(ap.parse_args())

    # tc_ = TaskContext(reg_inst='长沙经济技术开发区管理委员会')
    # process(tc_)
    # logger.info(tc_)

