# -*- coding: utf-8 -*-

import pydantic
from flask import Flask, request
from apps.reg_institute_area_info.reg_institute_area_info import process, TaskContext

app = Flask('reg_institute_area_info')


@app.route('/reg_institute_area_info/<reg_inst>', methods=['GET'])
def reg_institute_area_info_query(reg_inst):
    tc = TaskContext(reg_inst=reg_inst)
    process(tc)
    return tc.model_dump_json()


if __name__ == '__main__':
    app.run()
