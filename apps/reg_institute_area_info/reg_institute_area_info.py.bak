import json
import re
from typing import Dict, Tuple
import argparse
import csv
import time
import pydantic
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor
from dao.company import Company, CompanyDao
from dao.reports.annual_report import AnnualReportDao, AnnualReport
from dao.reports.annual_report_brief import AnnualReportBriefDao, AnnualReportBrief
from clients.redis.redis_queue import RedisQueue
from gslib.msv import msv_query_base_info
from libs.dt import to_date

logger = setup_logger()
fs = dict()
process_code_stat = Counter()
company_dao = CompanyDao()


area_code_province_index = dict()
for line_ in open('area_code_dat.txt', 'r'):
    province, city, district, name1 = line_.strip().split(' ')
    if province not in area_code_province_index:
        area_code_province_index[province] = province, city, district
    if city != 'None' and city not in area_code_province_index:
        area_code_province_index[city] = province, city, district
    if district != 'None' and district not in area_code_province_index:
        area_code_province_index[district] = province, city, district
    if name1 != 'None' and name1 not in area_code_province_index:
        area_code_province_index[name1] = province, city, district
area_code_province_index['彬县'] = ('陕西省', '咸阳市', '彬州市')
area_code_province_index['潮安县'] = ('广东省', '潮州市', '潮安区')
area_code_province_index['察北管理区'] = ('河北省', '张家口市', 'None')
logger.info(f'area_code_province_index size {len(area_code_province_index)}')

reg_inst_index = dict()
with open(f'reg_institute_area_info.input.2.csv', 'r') as fin:
    reader = csv.DictReader(fin)
    for did, d in enumerate(reader):
        reg_inst = d['reg_institute']
        area_code = d['area_code']
        count_num = int(d['count'])
        sample = d['credit_code_example']
        # credit_code_example = d['credit_code_example']
        if reg_inst not in reg_inst_index:
            reg_inst_index[reg_inst] = dict()
        reg_inst_index[reg_inst][area_code] = (count_num, sample)
    logger.info(f'reg_inst_index size {len(reg_inst_index)}')


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    NOT_GSXT = auto()
    SMALL_GROUP = auto()

    BAD_INST_CFG = auto()
    OK = auto()
    MISS = auto()


class TaskContext(pydantic.BaseModel):
    reg_inst: str
    company_count: int = -1
    code: ProcessCode = pydantic.Field(default=ProcessCode.INITIAL)
    context: Dict = pydantic.Field(default_factory=dict)
    ts: float = pydantic.Field(default_factory=time.time)


bad_reg_inst_set = {
    '六郎市场监管所',
    '怀来县人民政府',
    '夏家营市场监管所',
    '藕池市场监督管理所',
    '上津市场监管所',
    '文化宫街工商所',
    '绰河源市场监督管理所',
    '自由贸易试验区临港新片区市场监督管理局',
    '苏滁现代产业园市场监督管理局',
    '经济技术开发区市场监督管理局',
    '罗桥市场监管所',
    '岐亭市场监管所',
    '蔡家庙市场监督管理所',
    '铁炉市场监管所',
}
pats = [
    # 省?{市}{机构}
    # 灵宝市工商行政和质量技术监管局专业工商质监所
    r'^(.*?省)?(.+?市)(民政局|中心所|工商所|工商行政管理局|市场监督管理局|行政审批局|市场监管管理局|行政审批和政务服务局|工商行政和质量技术监管局)',

    # 省?市?{县}{机构}
    # 赫章县民政局
    # 山西省汾西县工商行政管理局
    # 南京市浦口区行政审批局
    r'^(.*?省)?(.*?[市州])?(.+?[区县]?)(民政局|中心所|工商所|工商行政管理局|市场监督管理局|行政审批局|市场监管管理局|行政审批和政务服务局|市场和质量监督管理局|市场监管局|商务和市场监督管理局|食品药品工商质量监督管理局)',

    # 市?{县}{地名}{机构}
    # 枞阳县义津市场监督管理所
    # 广州市荔湾区金花市场监管所
    # 翁牛特旗乌丹第一市场监督管理所
    r'^(.*?市)?(.+?[市区县旗]).{2,5}(第[一二三四])?市场(和质量)?监督管理[一二三四]?所',

    # {开发区}{机构}
    # 涉及特殊 开发区的
    # 洛阳高新技术产业开发区市场监督管理局辛店市场监管所
    # 济南新旧动能转换先行区管理委员会市场监管局
    r'^(.+?)(高新技术产业开发区|经济技术开发区|长江新区|高新区|天府新区|东湖新技术开发区|新旧动能转换先行区|转型综合改革示范区|江北新区|西湖管理区|金普新区)(管理委员会|市场监督管理局|市场监管局|行政审批局|食品药品工商质量监督管理局)',

    # 新疆生产建设兵团第七师市场监督管理局
    r'^(新疆)生产建设兵团',

    # 苏州高新区（虎丘区）行政审批局
    r'^(.+?市?)高新区（(.+?[区县]?)）行政审批局',

    # 顺义分局杨镇工商所
    r'^(.+)分局',

    # 南山局
    r'^(.+)局$',

]


def process(tc: TaskContext):
    # area_code_info: Dict[str, Tuple[int, str]]
    area_code_info = reg_inst_index.get(tc.reg_inst, {})

    if area_code_info:
        tc.company_count = sum(x[0] for x in area_code_info.values())

    if 0 < tc.company_count <= 100:
        tc.code = ProcessCode.SMALL_GROUP
        return

    if tc.company_count > 0 and all(not x[1].startswith('9') for x in area_code_info.values()):
        tc.code = ProcessCode.NOT_GSXT
        return

    # len = 1
    if len(area_code_info) == 1:
        area_code = list(area_code_info.keys())[0]
        if area_code in area_code_province_index:
            tc.context['output'] = area_code_province_index[area_code]
            tc.code = ProcessCode.OK
            return

    # *********
    if tc.company_count > 0 and re.fullmatch(r'\d{8,9}', tc.reg_inst):
        tot, ok = 0, 0
        for area_code, (count, sample) in area_code_info.items():
            tot += count
            if tc.reg_inst.startswith(area_code):
                ok += count
        if ok / tot > 0.9:
            if tc.reg_inst[:6] in area_code_province_index:
                # logger.info(f'OUTPUT {tc.reg_inst}  {area_code_province_index[tc.reg_inst[:6]]}')
                tc.context['output'] = area_code_province_index[tc.reg_inst[:6]]
                tc.code = ProcessCode.OK
                return

    for pat in pats:
        mo = re.search(pat, tc.reg_inst)
        if mo:
            target = None
            for s in mo.groups()[::-1]:
                if s in area_code_province_index:
                    # logger.info(f'GET {s} -> {area_code_province_index[s]} ')
                    if not target:
                        target = area_code_province_index[s]
                    else:
                        target1 = area_code_province_index[s]
                        eq = True
                        for k in range(3):
                            if target1[k] != target[k] and target1[k] != 'None' and target[k] != 'None':
                                eq = False
                        if not eq:
                            logger.info(f'CHANGE {s} {target} -> {target1}')
                            target = target1
            if not target:
                continue
            # logger.info(f'OUTPUT {tc.reg_inst}  {target}')
            tc.context['output'] = target
            tc.code = ProcessCode.OK
            return

    if tc.reg_inst in bad_reg_inst_set:
        tc.code = ProcessCode.BAD_INST_CFG
        return

    logger.info(f'MISS {tc.reg_inst} company_count={tc.company_count} {area_code_info}')
    tc.code = ProcessCode.MISS


companies_total, companies_ok = 0, 0


def callback_fn(f: Future):
    global companies_ok, companies_total
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code != ProcessCode.INITIAL:
        # logger.info(f'TASK {tc.__dict__}')
        pass
    companies_total += tc.company_count
    if tc.code == ProcessCode.OK:
        companies_ok += tc.company_count
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat} ok_ratio={companies_ok/companies_total}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')

    with BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool:
        for did, reg_inst in enumerate(reg_inst_index):
            if did >= args.lid_max:
                break
            tc = TaskContext(reg_inst=reg_inst)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat} ok_ratio={companies_ok/companies_total}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='reg_institute_area_info.py')
    ap.add_argument('--worker-num', type=int, default=1)
    ap.add_argument('--lid-max', type=int, default=1000)

    main(ap.parse_args())

    # tc_ = TaskContext(reg_inst='长沙经济技术开发区管理委员会')
    # process(tc_)
    # logger.info(tc_)

