CREATE TABLE  prism.reg_institute_area_info (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `reg_institute` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '登记机关名称',
    `province` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '省 默认则表示非本层级',
    `city` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '地市 默认则表示非本层级',
    `district` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '区县 默认则表示非本层级',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT 'create_time',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'update_time',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uniq_idx_reg_institute` (`reg_institute`),
    INDEX `idx_update_time` (`update_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=UTF8MB4 COMMENT 'prism.reg_institute_area_info';
