import re
import itertools
from libs.req import SessionRequestManager


# mgr = SessionRequestManager()
#
# resp = mgr.request('https://www.mca.gov.cn/mzsj/xzqh/2023/202301xzqh.html', use_proxy=False)
#
# open('area_code.html', 'wb').write(resp.content)


import bs4

soup = bs4.BeautifulSoup(markup=open('area_code.html', 'rb'), features='lxml')
items = soup.select('table>tr')

minzu_s = '汉族、壮族、满族、回族、苗族、维吾尔族、土家族、彝族、蒙古族、藏族、布依族、侗族、瑶族、朝鲜族、白族、哈尼族、哈萨克族、黎族、傣族、畲族、傈僳族、仡佬族、东乡族、高山族、拉祜族、水族、佤族、纳西族、羌族、土族、仫佬族、锡伯族、柯尔克孜族、达斡尔族、景颇族、毛南族、撒拉族、布朗族、塔吉克族、阿昌族、 普米族、鄂温克族、怒族、京族、基诺族、 德昂族、保安族、俄罗斯族、裕固族、乌孜别克族、门巴族、鄂伦春族、独龙族、塔塔尔族、赫哲族、珞巴族'
minzu_set = set(x.strip() for x in minzu_s.split('、'))
print(f'minzu_set len {len(minzu_set)}')

fp = open('area_code_dat.txt', 'w')

province, city, district = None, None, None
for item in items:
    td_items = item.select('td')
    text = item.text.strip()
    if len(td_items) == 10:
        code = td_items[1].text
        name = td_items[2].text
        if not re.match(r'\d{6}', code):
            continue
        if name.startswith('  '):
            name = name[2:].strip()
            if name.endswith('*'):
                name = name[:-1]
            kind = 'district'
            mo = re.fullmatch(r'(.+?)(自治县|市|县|区|旗|特区|各族自治县|林区|新区|自治旗|联合旗|矿区)', name)
        elif name.startswith(' '):
            name = name[1:]
            kind = 'city'
            mo = re.fullmatch(r'(.+?)(市|自治州|地区|盟)', name)
        else:
            kind = 'province'
            mo = re.fullmatch(r'(.+?)(省|市|特别行政区|自治区)', name)
        if not mo:
            print(f'ERR REGEX {name}')
            exit()

        name1, abbr = mo.groups()
        # 测试 民族结尾
        for minzu in itertools.chain(minzu_set, minzu_set, minzu_set):
            if name1.endswith(minzu) and len(name1) - len(minzu) > 1:  # 鄂温克族自治旗
                name1 = name1[:-len(minzu)]
            if len(minzu) > 2 and name1.endswith(minzu[:-1]) and len(name1) - (len(minzu)-1) >= 2:
                name1 = name1[:-len(minzu[:-1])]
        # 岷县
        if len(name1) == 1:
            name1 = name1 + abbr

        if kind == 'province':
            province = name
            city = None
            district = None
        elif kind == 'city':
            city = name
            district = None
        else:
            if city is None:
                city = province
            if name == '石河子市':
                city = province
            district = name

        # print(f'{province} {city} {district} {name1}')
        fp.write(f'{province} {city} {district} {name1}\n')
        fp.write(f'{province} {city} {district} {code}\n')
