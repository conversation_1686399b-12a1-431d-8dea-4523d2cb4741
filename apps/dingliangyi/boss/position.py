positions = [{'code': 100101, 'name': 'Java'}, {'code': 100102, 'name': 'C/C++'}, {'code': 100103, 'name': 'PHP'},
             {'code': 100109, 'name': 'Python'}, {'code': 100106, 'name': 'C#'}, {'code': 100107, 'name': '.NET'},
             {'code': 100116, 'name': 'Golang'}, {'code': 100114, 'name': 'Node.js'}, {'code': 100121, 'name': '语音/视频/图形开发'},
             {'code': 101313, 'name': '高性能计算工程师'}, {'code': 100124, 'name': 'GIS工程师'}, {'code': 100125, 'name': '区块链工程师'},
             {'code': 100123, 'name': '全栈工程师'}, {'code': 100199, 'name': '其他后端开发'}, {'code': 100901, 'name': '前端开发工程师'},
             {'code': 100202, 'name': 'Android'}, {'code': 100203, 'name': 'iOS'}, {'code': 100209, 'name': 'U3D'},
             {'code': 100211, 'name': 'UE4'}, {'code': 100210, 'name': 'Cocos'}, {'code': 100212, 'name': '技术美术'},
             {'code': 100208, 'name': 'JavaScript'}, {'code': 100213, 'name': '鸿蒙开发工程师'}, {'code': 100301, 'name': '测试工程师'},
             {'code': 100309, 'name': '软件测试'}, {'code': 100302, 'name': '自动化测试'}, {'code': 100303, 'name': '功能测试'},
             {'code': 100305, 'name': '测试开发'}, {'code': 100308, 'name': '硬件测试'}, {'code': 100307, 'name': '游戏测试'},
             {'code': 100304, 'name': '性能测试'}, {'code': 100310, 'name': '渗透测试'}, {'code': 100703, 'name': '测试经理'},
             {'code': 100401, 'name': '运维工程师'}, {'code': 100405, 'name': 'IT技术支持'}, {'code': 100403, 'name': '网络工程师'},
             {'code': 100407, 'name': '网络安全'}, {'code': 100404, 'name': '系统工程师'}, {'code': 100402, 'name': '运维开发工程师'},
             {'code': 100406, 'name': '系统管理员'}, {'code': 100409, 'name': 'DBA'}, {'code': 290166, 'name': '电脑/打印机维修'},
             {'code': 100408, 'name': '系统安全'}, {'code': 100410, 'name': '技术文档工程师'}, {'code': 101306, 'name': '图像算法'},
             {'code': 100117, 'name': '自然语言处理算法'}, {'code': 101310, 'name': '大模型算法'}, {'code': 100104, 'name': '数据挖掘'},
             {'code': 101311, 'name': '规控算法'}, {'code': 101312, 'name': 'SLAM算法'}, {'code': 100118, 'name': '推荐算法'},
             {'code': 100115, 'name': '搜索算法'}, {'code': 101305, 'name': '语音算法'}, {'code': 101309, 'name': '风控算法'},
             {'code': 101313, 'name': '高性能计算工程师'}, {'code': 100120, 'name': '算法工程师'}, {'code': 101307, 'name': '算法研究员'},
             {'code': 101301, 'name': '机器学习'}, {'code': 101302, 'name': '深度学习'}, {'code': 101308, 'name': '自动驾驶系统工程师'},
             {'code': 130121, 'name': '数据标注/AI训练师'}, {'code': 101201, 'name': '售前技术支持'},
             {'code': 101202, 'name': '售后技术支持'}, {'code': 101299, 'name': '销售技术支持'}, {'code': 160303, 'name': '客户成功'},
             {'code': 100511, 'name': '数据分析师'}, {'code': 100508, 'name': '数据开发'}, {'code': 100507, 'name': '数据仓库'},
             {'code': 100506, 'name': 'ETL工程师'}, {'code': 100104, 'name': '数据挖掘'}, {'code': 100512, 'name': '数据架构师'},
             {'code': 100514, 'name': '爬虫工程师'}, {'code': 100122, 'name': '数据采集'}, {'code': 100515, 'name': '数据治理'},
             {'code': 100601, 'name': '项目经理/主管'}, {'code': 100606, 'name': '实施工程师'}, {'code': 100605, 'name': '实施顾问'},
             {'code': 100607, 'name': '需求分析工程师'}, {'code': 100603, 'name': '项目专员/助理'}, {'code': 100701, 'name': '技术经理'},
             {'code': 100704, 'name': '架构师'}, {'code': 100702, 'name': '技术总监'}, {'code': 100705, 'name': 'CTO/CIO'},
             {'code': 100707, 'name': '技术合伙人'}, {'code': 100706, 'name': '运维总监'}, {'code': 101101, 'name': '其他技术职位'},
             {'code': 101401, 'name': '电子工程师'}, {'code': 100801, 'name': '硬件工程师'}, {'code': 100802, 'name': '嵌入式软件工程师'},
             {'code': 100808, 'name': 'FPGA开发'}, {'code': 100804, 'name': '单片机'}, {'code': 100806, 'name': '驱动开发工程师'},
             {'code': 100811, 'name': 'PCB工程师'}, {'code': 101408, 'name': '电子维修技术员'}, {'code': 100816, 'name': '射频工程师'},
             {'code': 100805, 'name': '电路设计'}, {'code': 100807, 'name': '系统集成'}, {'code': 100818, 'name': '光学工程师'},
             {'code': 100809, 'name': 'DSP开发'}, {'code': 100819, 'name': '电源工程师'}, {'code': 300801, 'name': '电池工程师'},
             {'code': 101405, 'name': '集成电路IC设计'}, {'code': 101406, 'name': '数字IC验证工程师'},
             {'code': 101407, 'name': '模拟版图设计工程师'}, {'code': 101409, 'name': '芯片测试工程师'},
             {'code': 101410, 'name': 'DFT工程师'}, {'code': 101403, 'name': 'FAE'}, {'code': 101411, 'name': '数字前端设计师'},
             {'code': 101412, 'name': '数字后端工程师'}, {'code': 101413, 'name': '模拟IC设计工程师'},
             {'code': 101402, 'name': '电气工程师'}, {'code': 101404, 'name': '电气设计工程师'}, {'code': 100803, 'name': '自动化工程师'},
             {'code': 300310, 'name': '机电工程师'}, {'code': 220223, 'name': '建筑机电工程师'}, {'code': 100601, 'name': '项目经理/主管'},
             {'code': 100606, 'name': '实施工程师'}, {'code': 100605, 'name': '实施顾问'}, {'code': 100817, 'name': '硬件项目经理'},
             {'code': 100603, 'name': '项目专员/助理'}, {'code': 101011, 'name': '通信项目专员'}, {'code': 101012, 'name': '通信项目经理'},
             {'code': 101001, 'name': '通信技术工程师'}, {'code': 101002, 'name': '通信研发工程师'},
             {'code': 101003, 'name': '数据通信工程师'}, {'code': 101018, 'name': '光网络工程师'},
             {'code': 101007, 'name': '有线传输工程师'}, {'code': 101013, 'name': '核心网工程师'},
             {'code': 101010, 'name': '通信标准化工程师'}, {'code': 100403, 'name': '网络工程师'}, {'code': 101005, 'name': '宽带装维'},
             {'code': 101008, 'name': '无线/天线工程师'}, {'code': 101201, 'name': '售前技术支持'},
             {'code': 101202, 'name': '售后技术支持'}, {'code': 101299, 'name': '销售技术支持'}, {'code': 100401, 'name': '运维工程师'},
             {'code': 100404, 'name': '系统工程师'}, {'code': 100410, 'name': '技术文档工程师'}, {'code': 110101, 'name': '产品经理'},
             {'code': 110108, 'name': '产品专员/助理'}, {'code': 110302, 'name': '高级产品管理岗'}, {'code': 120302, 'name': '用户研究'},
             {'code': 110106, 'name': '电商产品经理'}, {'code': 110110, 'name': 'AI产品经理'}, {'code': 110105, 'name': '数据产品经理'},
             {'code': 110103, 'name': '移动产品经理'}, {'code': 180501, 'name': '金融产品经理'},
             {'code': 130133, 'name': '跨境电商产品开发'}, {'code': 110111, 'name': '化妆品产品经理'},
             {'code': 110109, 'name': '硬件产品经理'}, {'code': 110401, 'name': '其他产品职位'}, {'code': 100607, 'name': '需求分析工程师'},
             {'code': 110107, 'name': '游戏策划'}, {'code': 120305, 'name': '系统策划'}, {'code': 120303, 'name': '游戏数值策划'},
             {'code': 110303, 'name': '游戏制作人'}, {'code': 130305, 'name': '客服专员'}, {'code': 130306, 'name': '客服主管'},
             {'code': 130304, 'name': '客服经理'}, {'code': 130303, 'name': '网络客服'}, {'code': 130308, 'name': '电话客服'},
             {'code': 130302, 'name': '售后客服'}, {'code': 130301, 'name': '售前客服'}, {'code': 130111, 'name': '新媒体运营'},
             {'code': 130122, 'name': '直播运营'}, {'code': 170108, 'name': '视频运营'}, {'code': 130104, 'name': '内容运营'},
             {'code': 130113, 'name': '微信运营'}, {'code': 130117, 'name': '国内电商运营'}, {'code': 130124, 'name': '跨境电商运营'},
             {'code': 130107, 'name': '品类运营'}, {'code': 130126, 'name': '淘宝运营'}, {'code': 130127, 'name': '天猫运营'},
             {'code': 130128, 'name': '京东运营'}, {'code': 130129, 'name': '拼多多运营'}, {'code': 130130, 'name': '亚马逊运营'},
             {'code': 130133, 'name': '跨境电商产品开发'}, {'code': 130132, 'name': '阿里国际站运营'},
             {'code': 130131, 'name': '速卖通运营'}, {'code': 130118, 'name': '运营助理/专员'}, {'code': 130102, 'name': '产品运营'},
             {'code': 130101, 'name': '用户运营'}, {'code': 130106, 'name': '商家运营'}, {'code': 130103, 'name': '数据/策略运营'},
             {'code': 130112, 'name': '社群运营'}, {'code': 130108, 'name': '游戏运营'}, {'code': 130110, 'name': '网站运营'},
             {'code': 130120, 'name': '内容审核'}, {'code': 130121, 'name': '数据标注/AI训练师'}, {'code': 130134, 'name': '外卖运营'},
             {'code': 130123, 'name': '车辆运营'}, {'code': 130116, 'name': '线下拓展运营'}, {'code': 290314, 'name': '商场运营'},
             {'code': 130203, 'name': '文案编辑'}, {'code': 130201, 'name': '主编/副主编'}, {'code': 130204, 'name': '网站编辑'},
             {'code': 210101, 'name': '医学编辑'}, {'code': 130405, 'name': '运营经理/主管'}, {'code': 130402, 'name': '运营总监'},
             {'code': 130403, 'name': 'COO'}, {'code': 130404, 'name': '客服总监'}, {'code': 130501, 'name': '其他运营职位'},
             {'code': 140301, 'name': '销售专员'}, {'code': 140310, 'name': '电话销售'}, {'code': 140314, 'name': '网络销售'},
             {'code': 140307, 'name': '渠道销售'}, {'code': 140304, 'name': '大客户代表'}, {'code': 140317, 'name': '客户经理'},
             {'code': 140305, 'name': 'BD经理'}, {'code': 140316, 'name': '销售工程师'}, {'code': 140302, 'name': '销售经理/主管'},
             {'code': 140402, 'name': '销售总监'}, {'code': 160103, 'name': '销售VP'}, {'code': 160102, 'name': '城市经理'},
             {'code': 160101, 'name': '区域总监'}, {'code': 140309, 'name': '销售助理'}, {'code': 130119, 'name': '销售运营'},
             {'code': 160301, 'name': '商务专员'}, {'code': 140107, 'name': '商务经理'}, {'code': 140403, 'name': '商务总监'},
             {'code': 160304, 'name': '招商'}, {'code': 160303, 'name': '客户成功'}, {'code': 250203, 'name': '外贸业务员'},
             {'code': 250201, 'name': '外贸经理'}, {'code': 240111, 'name': '货代/物流销售'}, {'code': 250205, 'name': '海外销售'},
             {'code': 190601, 'name': '课程顾问'}, {'code': 190603, 'name': '留学顾问'}, {'code': 230201, 'name': '汽车销售'},
             {'code': 230202, 'name': '汽车配件销售'}, {'code': 160401, 'name': '置业顾问'}, {'code': 160403, 'name': '地产中介'},
             {'code': 220403, 'name': '地产招商'}, {'code': 290302, 'name': '导购'}, {'code': 160501, 'name': '服装导购'},
             {'code': 290312, 'name': '珠宝销售'}, {'code': 210414, 'name': '美容顾问'}, {'code': 210406, 'name': '化妆品导购'},
             {'code': 160502, 'name': '家装导购'}, {'code': 210610, 'name': '会籍顾问'}, {'code': 280103, 'name': '旅游顾问'},
             {'code': 210502, 'name': '医药代表'}, {'code': 210506, 'name': '医疗器械销售'}, {'code': 210803, 'name': '药店店员'},
             {'code': 210801, 'name': '药店店长'}, {'code': 210505, 'name': '医美咨询'}, {'code': 210504, 'name': '健康顾问'},
             {'code': 210507, 'name': '口腔咨询师'}, {'code': 140313, 'name': '广告销售'}, {'code': 140504, 'name': '会展活动销售'},
             {'code': 140501, 'name': '会议活动销售'}, {'code': 180506, 'name': '理财顾问'}, {'code': 180701, 'name': '保险顾问'},
             {'code': 180401, 'name': '信用卡销售'}, {'code': 180801, 'name': '证券经纪人'}, {'code': 160201, 'name': '其他销售职位'},
             {'code': 150104, 'name': '人力资源专员/助理'}, {'code': 150403, 'name': '人力资源经理/主管'},
             {'code': 150108, 'name': '人力资源总监'}, {'code': 150102, 'name': '招聘'}, {'code': 150103, 'name': 'HRBP'},
             {'code': 150105, 'name': '培训'}, {'code': 150109, 'name': '员工关系'}, {'code': 150110, 'name': '组织发展'},
             {'code': 150111, 'name': '企业文化'}, {'code': 150106, 'name': '薪酬绩效'}, {'code': 260108, 'name': '猎头顾问'},
             {'code': 150201, 'name': '行政专员/助理'}, {'code': 150401, 'name': '行政经理/主管'}, {'code': 150209, 'name': '行政总监'},
             {'code': 150202, 'name': '前台'}, {'code': 150207, 'name': '后勤'}, {'code': 150205, 'name': '经理助理'},
             {'code': 150210, 'name': '文员'}, {'code': 140802, 'name': '企业党建'}, {'code': 150211, 'name': '档案管理'},
             {'code': 150208, 'name': '商务司机'}, {'code': 100603, 'name': '项目专员/助理'}, {'code': 150203, 'name': '法务专员/助理'},
             {'code': 150506, 'name': '法务经理/主管'}, {'code': 150507, 'name': '法务总监'}, {'code': 150504, 'name': '法律顾问'},
             {'code': 260201, 'name': '律师'}, {'code': 150601, 'name': '其他职能职位'}, {'code': 150301, 'name': '会计'},
             {'code': 150311, 'name': '总账会计'}, {'code': 150310, 'name': '成本会计'}, {'code': 150304, 'name': '结算会计'},
             {'code': 150313, 'name': '外勤会计'}, {'code': 150312, 'name': '建筑/工程会计'}, {'code': 150306, 'name': '审计'},
             {'code': 150305, 'name': '税务'}, {'code': 150402, 'name': '财务经理/主管'}, {'code': 150308, 'name': '财务总监/VP'},
             {'code': 150404, 'name': 'CFO'}, {'code': 150302, 'name': '出纳'}, {'code': 150307, 'name': '风控'},
             {'code': 150303, 'name': '财务顾问'}, {'code': 150314, 'name': '统计员'}, {'code': 150316, 'name': '财务分析/财务BP'},
             {'code': 300601, 'name': '普工/操作工'}, {'code': 300624, 'name': '包装工'}, {'code': 300628, 'name': '学徒工'},
             {'code': 300630, 'name': '搬运工/装卸工'}, {'code': 300623, 'name': '组装工'}, {'code': 300604, 'name': '焊工'},
             {'code': 300605, 'name': '氩弧焊工'}, {'code': 300610, 'name': '车工'}, {'code': 300613, 'name': '钳工'},
             {'code': 300631, 'name': '切割工'}, {'code': 300616, 'name': '钣金工'}, {'code': 300622, 'name': '注塑工'},
             {'code': 300619, 'name': '折弯工'}, {'code': 300611, 'name': '磨工'}, {'code': 300633, 'name': '模具工'},
             {'code': 300612, 'name': '铣工'}, {'code': 300617, 'name': '抛光工'}, {'code': 300621, 'name': '喷塑工'},
             {'code': 300614, 'name': '钻工'}, {'code': 300615, 'name': '铆工'}, {'code': 300620, 'name': '电镀工'},
             {'code': 300606, 'name': '电工'}, {'code': 300635, 'name': '弱电工'}, {'code': 300618, 'name': '机修工'},
             {'code': 300311, 'name': 'CNC数控操机/编程员'}, {'code': 300608, 'name': '木工'}, {'code': 300609, 'name': '油漆工'},
             {'code': 300627, 'name': '锅炉工'}, {'code': 300602, 'name': '叉车工'}, {'code': 300603, 'name': '铲车司机'},
             {'code': 300634, 'name': '挖掘机司机'}, {'code': 300201, 'name': '质量管理/测试工程师'}, {'code': 300208, 'name': '质检员'},
             {'code': 300402, 'name': '实验室技术员'}, {'code': 300205, 'name': '体系工程师'}, {'code': 300206, 'name': '体系审核员'},
             {'code': 300204, 'name': '产品认证工程师'}, {'code': 300203, 'name': '失效分析工程师'},
             {'code': 300202, 'name': '可靠度工程师'}, {'code': 250108, 'name': '供应商质量工程师'},
             {'code': 210122, 'name': '医疗器械生产/质量管理'}, {'code': 230109, 'name': '汽车质量工程师'},
             {'code': 300209, 'name': '计量工程师'}, {'code': 300301, 'name': '机械工程师'}, {'code': 300306, 'name': '机械结构工程师'},
             {'code': 300321, 'name': '家电/3C结构工程师'}, {'code': 300308, 'name': '工艺工程师'},
             {'code': 300304, 'name': '设备维修保养工程师'}, {'code': 300303, 'name': '机械设备工程师'},
             {'code': 300310, 'name': '机电工程师'}, {'code': 300305, 'name': '机械制图员'}, {'code': 300314, 'name': '模具工程师'},
             {'code': 300313, 'name': '夹具工程师'}, {'code': 300309, 'name': '材料工程师'}, {'code': 300307, 'name': '工业工程师(IE)'},
             {'code': 300319, 'name': '仿真工程师'}, {'code': 100813, 'name': '热设计工程师'}, {'code': 300316, 'name': '注塑工程师'},
             {'code': 300315, 'name': '焊接工程师'}, {'code': 300317, 'name': '铸造/锻造工程师'}, {'code': 300318, 'name': '液压工程师'},
             {'code': 300312, 'name': '冲压工程师'}, {'code': 300320, 'name': '装配工程师'}, {'code': 300802, 'name': '电机工程师'},
             {'code': 101402, 'name': '电气工程师'}, {'code': 101404, 'name': '电气设计工程师'}, {'code': 100803, 'name': '自动化工程师'},
             {'code': 300103, 'name': '车间主任'}, {'code': 300104, 'name': '生产组长/拉长'}, {'code': 300102, 'name': '生产总监'},
             {'code': 300101, 'name': '厂长'}, {'code': 300108, 'name': '生产跟单/文员'},
             {'code': 300107, 'name': '生产计划/物料管理(PMC)'}, {'code': 300106, 'name': '生产设备管理'}, {'code': 300110, 'name': '厂务'},
             {'code': 300207, 'name': '生产安全员'}, {'code': 300903, 'name': 'EHS工程师'}, {'code': 300210, 'name': '安全评价师'},
             {'code': 300402, 'name': '实验室技术员'}, {'code': 300401, 'name': '化工工程师'}, {'code': 300406, 'name': '食品/饮料研发'},
             {'code': 300405, 'name': '化妆品研发'}, {'code': 300404, 'name': '涂料研发'}, {'code': 300407, 'name': '化工项目经理'},
             {'code': 300510, 'name': '服装/纺织/皮革跟单'}, {'code': 300509, 'name': '打样/制版'},
             {'code': 300501, 'name': '服装/纺织设计'}, {'code': 120615, 'name': '鞋类设计师'}, {'code': 300507, 'name': '面料辅料开发'},
             {'code': 300629, 'name': '缝纫工'}, {'code': 300632, 'name': '样衣工'}, {'code': 300511, 'name': '量体师'},
             {'code': 300637, 'name': '裁剪工'}, {'code': 300801, 'name': '电池工程师'}, {'code': 300802, 'name': '电机工程师'},
             {'code': 230102, 'name': '车身/造型设计'}, {'code': 230106, 'name': '汽车电子工程师'},
             {'code': 101308, 'name': '自动驾驶系统工程师'}, {'code': 300803, 'name': '线束设计'},
             {'code': 230110, 'name': '内外饰设计工程师'}, {'code': 230105, 'name': '动力系统工程师'},
             {'code': 230103, 'name': '底盘工程师'}, {'code': 230107, 'name': '汽车零部件设计'}, {'code': 230101, 'name': '汽车设计'},
             {'code': 230109, 'name': '汽车质量工程师'}, {'code': 230210, 'name': '总装工程师'}, {'code': 230108, 'name': '汽车项目管理'},
             {'code': 230111, 'name': '总布置工程师'}, {'code': 300903, 'name': 'EHS工程师'}, {'code': 300905, 'name': '环境采样/检测员'},
             {'code': 300902, 'name': '环评工程师'}, {'code': 300901, 'name': '环保工程师'}, {'code': 300904, 'name': '碳排放管理师'},
             {'code': 300701, 'name': '其他生产制造职位'}, {'code': 290303, 'name': '店员/营业员'}, {'code': 290201, 'name': '收银'},
             {'code': 290302, 'name': '导购'}, {'code': 160501, 'name': '服装导购'}, {'code': 290312, 'name': '珠宝销售'},
             {'code': 210406, 'name': '化妆品导购'}, {'code': 160502, 'name': '家装导购'}, {'code': 290307, 'name': '理货/陈列员'},
             {'code': 290311, 'name': '促销员'}, {'code': 290308, 'name': '防损员'}, {'code': 290304, 'name': '门店店长'},
             {'code': 290226, 'name': '储备店长'}, {'code': 290305, 'name': '督导/巡店'}, {'code': 290309, 'name': '卖场经理'},
             {'code': 290314, 'name': '商场运营'}, {'code': 210405, 'name': '美容师'}, {'code': 210410, 'name': '美容店长'},
             {'code': 210408, 'name': '美体师'}, {'code': 210414, 'name': '美容顾问'}, {'code': 290802, 'name': '美容导师'},
             {'code': 210608, 'name': '美甲美睫师'}, {'code': 210407, 'name': '纹绣师'}, {'code': 210607, 'name': '发型师'},
             {'code': 210409, 'name': '美发助理/学徒'}, {'code': 290801, 'name': '养发师'}, {'code': 210609, 'name': '化妆/造型/服装'},
             {'code': 210403, 'name': '理疗师'}, {'code': 290118, 'name': '产后康复师'}, {'code': 210404, 'name': '针灸推拿'},
             {'code': 210412, 'name': '按摩师'}, {'code': 210411, 'name': '足疗师'}, {'code': 210415, 'name': '采耳师'},
             {'code': 210401, 'name': '营养师/健康管理师'}, {'code': 210305, 'name': '康复治疗师'}, {'code': 290106, 'name': '保洁'},
             {'code': 290122, 'name': '保洁主管'}, {'code': 290108, 'name': '保姆'}, {'code': 290109, 'name': '月嫂'},
             {'code': 290110, 'name': '育婴师'}, {'code': 290111, 'name': '护工'}, {'code': 290169, 'name': '收纳师'},
             {'code': 290105, 'name': '保安'}, {'code': 290117, 'name': '保安主管/队长'}, {'code': 290121, 'name': '消防中控员'},
             {'code': 290120, 'name': '押运员'}, {'code': 290112, 'name': '安检员'}, {'code': 290123, 'name': '消防维保员'},
             {'code': 290114, 'name': '家电维修'}, {'code': 290113, 'name': '手机维修'}, {'code': 290166, 'name': '电脑/打印机维修'},
             {'code': 290124, 'name': '电动车/摩托车维修'}, {'code': 230204, 'name': '汽车维修'}, {'code': 230205, 'name': '汽车美容'},
             {'code': 230213, 'name': '洗车工'}, {'code': 230209, 'name': '汽车改装'}, {'code': 230203, 'name': '汽车服务顾问'},
             {'code': 230208, 'name': '4S店店长/维修站长'}, {'code': 230207, 'name': '二手车评估师'},
             {'code': 230206, 'name': '汽车查勘定损'}, {'code': 230214, 'name': '加油员'}, {'code': 290601, 'name': '宠物美容'},
             {'code': 290602, 'name': '宠物医生'}, {'code': 190705, 'name': '健身教练'}, {'code': 190701, 'name': '舞蹈老师'},
             {'code': 190706, 'name': '篮球教练'}, {'code': 210601, 'name': '瑜伽老师'}, {'code': 210603, 'name': '游泳教练'},
             {'code': 190707, 'name': '跆拳道教练'}, {'code': 190708, 'name': '武术教练'}, {'code': 190709, 'name': '轮滑教练'},
             {'code': 210613, 'name': '救生员'}, {'code': 190719, 'name': '乒乓球教练'}, {'code': 190766, 'name': '足球教练'},
             {'code': 190720, 'name': '羽毛球教练'}, {'code': 190769, 'name': '拳击教练'}, {'code': 190312, 'name': '体育/体能老师'},
             {'code': 240305, 'name': '网约车司机'}, {'code': 240306, 'name': '代驾司机'}, {'code': 240307, 'name': '驾校教练'},
             {'code': 150208, 'name': '商务司机'}, {'code': 240301, 'name': '货运司机'}, {'code': 240308, 'name': '客运司机'},
             {'code': 100311, 'name': '无人机飞手'}, {'code': 290701, 'name': '花艺师'}, {'code': 290702, 'name': '婚礼策划'},
             {'code': 290313, 'name': '网吧网管'}, {'code': 210610, 'name': '会籍顾问'}, {'code': 280103, 'name': '旅游顾问'},
             {'code': 210109, 'name': '验光师'}, {'code': 170602, 'name': '摄影/摄像师'}, {'code': 170626, 'name': '剧本杀主持人'},
             {'code': 170628, 'name': '儿童引导师'}, {'code': 170613, 'name': '放映员'}, {'code': 290402, 'name': '游戏陪玩'},
             {'code': 290401, 'name': '其他服务业职位'}, {'code': 290202, 'name': '服务员'}, {'code': 290201, 'name': '收银'},
             {'code': 290107, 'name': '礼仪/迎宾/接待'}, {'code': 290216, 'name': '传菜员'}, {'code': 290212, 'name': '餐饮学徒'},
             {'code': 290203, 'name': '厨师'}, {'code': 290219, 'name': '中餐厨师'}, {'code': 290222, 'name': '烧烤师傅'},
             {'code': 290220, 'name': '西餐厨师'}, {'code': 290221, 'name': '日料厨师'}, {'code': 290218, 'name': '凉菜厨师'},
             {'code': 290213, 'name': '面点师'}, {'code': 290208, 'name': '后厨'}, {'code': 290209, 'name': '配菜打荷'},
             {'code': 290217, 'name': '洗碗工'}, {'code': 290224, 'name': '水台/水产员'}, {'code': 290206, 'name': '餐饮店长'},
             {'code': 290207, 'name': '餐饮前厅经理/领班'}, {'code': 290226, 'name': '储备店长'}, {'code': 290215, 'name': '厨师长'},
             {'code': 290214, 'name': '行政总厨'}, {'code': 290228, 'name': '餐饮督导'}, {'code': 290204, 'name': '咖啡师'},
             {'code': 290210, 'name': '茶艺师'}, {'code': 290223, 'name': '奶茶店店员'}, {'code': 290227, 'name': '调酒师'},
             {'code': 290225, 'name': '面包/烘焙师'}, {'code': 290211, 'name': '蛋糕/裱花师'}, {'code': 290205, 'name': '送餐员'},
             {'code': 130134, 'name': '外卖运营'}, {'code': 290102, 'name': '酒店前台'}, {'code': 290107, 'name': '礼仪/迎宾/接待'},
             {'code': 290103, 'name': '客房服务员'}, {'code': 290104, 'name': '酒店经理'}, {'code': 290115, 'name': '酒店前厅经理'},
             {'code': 290116, 'name': '客房经理'}, {'code': 290158, 'name': '民宿管家'}, {'code': 280103, 'name': '旅游顾问'},
             {'code': 280104, 'name': '导游'}, {'code': 280201, 'name': '旅游产品经理'}, {'code': 280106, 'name': '讲解员'},
             {'code': 280101, 'name': '计调'}, {'code': 280105, 'name': '票务员'}, {'code': 280102, 'name': '签证专员'},
             {'code': 280301, 'name': '其他旅游职位'}, {'code': 190301, 'name': '教师'}, {'code': 190309, 'name': '英语教师'},
             {'code': 190302, 'name': '助教'}, {'code': 190317, 'name': '数学教师'}, {'code': 190316, 'name': '语文教师'},
             {'code': 190303, 'name': '高中教师'}, {'code': 190304, 'name': '初中教师'}, {'code': 190305, 'name': '小学教师'},
             {'code': 190321, 'name': '家教'}, {'code': 190318, 'name': '物理教师'}, {'code': 190319, 'name': '化学教师'},
             {'code': 190307, 'name': '理科教师'}, {'code': 190314, 'name': '日语教师'}, {'code': 190320, 'name': '生物教师'},
             {'code': 190308, 'name': '文科教师'}, {'code': 190245, 'name': '地理教师'}, {'code': 190315, 'name': '其他外语教师'},
             {'code': 190306, 'name': '幼教'}, {'code': 190322, 'name': '托管老师'}, {'code': 190323, 'name': '早教老师'},
             {'code': 190326, 'name': '保育员'}, {'code': 190324, 'name': '感统训练教师'}, {'code': 190202, 'name': '教务管理'},
             {'code': 190201, 'name': '校长/副校长'}, {'code': 190204, 'name': '班主任/辅导员'}, {'code': 190203, 'name': '教学管理'},
             {'code': 190205, 'name': '园长/副园长'}, {'code': 190313, 'name': '就业老师'}, {'code': 190705, 'name': '健身教练'},
             {'code': 190706, 'name': '篮球教练'}, {'code': 190707, 'name': '跆拳道教练'}, {'code': 190708, 'name': '武术教练'},
             {'code': 190709, 'name': '轮滑教练'}, {'code': 190719, 'name': '乒乓球教练'}, {'code': 190766, 'name': '足球教练'},
             {'code': 190720, 'name': '羽毛球教练'}, {'code': 190769, 'name': '拳击教练'}, {'code': 190770, 'name': '台球教练/助教'},
             {'code': 210603, 'name': '游泳教练'}, {'code': 210601, 'name': '瑜伽老师'}, {'code': 210613, 'name': '救生员'},
             {'code': 210612, 'name': '普拉提老师'}, {'code': 190312, 'name': '体育/体能老师'}, {'code': 190311, 'name': '美术教师'},
             {'code': 190701, 'name': '舞蹈老师'}, {'code': 190712, 'name': '书法教师'}, {'code': 190310, 'name': '音乐教师'},
             {'code': 190716, 'name': '播音主持教师'}, {'code': 190713, 'name': '钢琴教师'}, {'code': 190715, 'name': '古筝教师'},
             {'code': 190714, 'name': '吉他教师'}, {'code': 190710, 'name': '表演教师'}, {'code': 190767, 'name': '架子鼓老师'},
             {'code': 190768, 'name': '围棋老师'}, {'code': 190717, 'name': '乐高教师'}, {'code': 190711, 'name': '机器人教师'},
             {'code': 190718, 'name': '少儿编程老师'}, {'code': 190105, 'name': '培训师'}, {'code': 190504, 'name': '拓展培训'},
             {'code': 190406, 'name': 'IT培训讲师'}, {'code': 190501, 'name': '财会培训讲师'}, {'code': 190101, 'name': '课程设计'},
             {'code': 190102, 'name': '课程编辑'}, {'code': 190107, 'name': '培训策划'}, {'code': 190104, 'name': '培训研究'},
             {'code': 190106, 'name': '其他教育产品研发职位'}, {'code': 190601, 'name': '课程顾问'}, {'code': 190603, 'name': '留学顾问'},
             {'code': 190801, 'name': '其他教育培训职位'}, {'code': 120106, 'name': '平面设计'}, {'code': 120117, 'name': '美工'},
             {'code': 120119, 'name': '设计师助理'}, {'code': 120118, 'name': '包装设计'}, {'code': 120105, 'name': 'UI设计师'},
             {'code': 120101, 'name': '视觉设计师'}, {'code': 140603, 'name': '广告设计'}, {'code': 120201, 'name': 'UX/交互设计师'},
             {'code': 120123, 'name': '修图师'}, {'code': 120302, 'name': '用户研究'}, {'code': 220205, 'name': '室内设计'},
             {'code': 220217, 'name': '软装设计师'}, {'code': 120611, 'name': '展览/展示设计'}, {'code': 120608, 'name': '陈列设计'},
             {'code': 120612, 'name': '照明设计'}, {'code': 220206, 'name': '园林/景观设计'}, {'code': 120614, 'name': '舞美设计师'},
             {'code': 120116, 'name': 'CAD绘图员'}, {'code': 120604, 'name': '家具设计'}, {'code': 120602, 'name': '工业设计'},
             {'code': 120606, 'name': '珠宝设计'}, {'code': 120613, 'name': '家具拆单员'}, {'code': 300501, 'name': '服装/纺织设计'},
             {'code': 120615, 'name': '鞋类设计师'}, {'code': 120107, 'name': '3D设计师'}, {'code': 120121, 'name': '插画师'},
             {'code': 120120, 'name': '动画设计'}, {'code': 120110, 'name': '原画师'}, {'code': 120122, 'name': '漫画师'},
             {'code': 120113, 'name': '游戏场景'}, {'code': 120114, 'name': '游戏角色'}, {'code': 120112, 'name': '游戏UI设计'},
             {'code': 120111, 'name': '游戏特效'}, {'code': 120115, 'name': '游戏动作'}, {'code': 120306, 'name': '游戏主美术'},
             {'code': 120401, 'name': '设计总监/经理'}, {'code': 120404, 'name': '视觉设计总监/经理'},
             {'code': 220212, 'name': '建筑施工项目经理'}, {'code': 220209, 'name': '工程造价'}, {'code': 220208, 'name': '工程监理'},
             {'code': 260114, 'name': '工程咨询'}, {'code': 220218, 'name': '施工员'}, {'code': 220211, 'name': '资料员'},
             {'code': 220220, 'name': '材料员'}, {'code': 220219, 'name': '测绘/测量'}, {'code': 220225, 'name': '施工安全员'},
             {'code': 220226, 'name': '工程检测员'}, {'code': 220103, 'name': '工程招投标'}, {'code': 220205, 'name': '室内设计'},
             {'code': 220217, 'name': '软装设计师'}, {'code': 220222, 'name': '装修项目经理'}, {'code': 220702, 'name': '装修监理'},
             {'code': 220701, 'name': '装修造价'}, {'code': 220401, 'name': '物业经理'}, {'code': 220406, 'name': '物业管理员'},
             {'code': 220404, 'name': '综合维修工'}, {'code': 220407, 'name': '物业工程主管'}, {'code': 300635, 'name': '弱电工'},
             {'code': 220405, 'name': '绿化工'}, {'code': 290105, 'name': '保安'}, {'code': 290117, 'name': '保安主管/队长'},
             {'code': 290121, 'name': '消防中控员'}, {'code': 290123, 'name': '消防维保员'},
             {'code': 220204, 'name': '土木/土建/结构工程师'}, {'code': 220206, 'name': '园林/景观设计'},
             {'code': 220203, 'name': '建筑设计师'}, {'code': 220202, 'name': '建筑工程师'}, {'code': 220213, 'name': '弱电工程师'},
             {'code': 220223, 'name': '建筑机电工程师'}, {'code': 220214, 'name': '给排水工程师'}, {'code': 220215, 'name': '暖通工程师'},
             {'code': 220216, 'name': '幕墙工程师'}, {'code': 220221, 'name': 'BIM工程师'}, {'code': 220224, 'name': '消防工程师'},
             {'code': 301004, 'name': '水利工程师'}, {'code': 220207, 'name': '城乡规划设计'}, {'code': 220102, 'name': '房地产项目管理'},
             {'code': 220101, 'name': '房地产策划'}, {'code': 220302, 'name': '房地产估价师'}, {'code': 160401, 'name': '置业顾问'},
             {'code': 160403, 'name': '地产中介'}, {'code': 220403, 'name': '地产招商'}, {'code': 300606, 'name': '电工'},
             {'code': 300604, 'name': '焊工'}, {'code': 300608, 'name': '木工'}, {'code': 300625, 'name': '空调工'},
             {'code': 300609, 'name': '油漆工'}, {'code': 300626, 'name': '电梯工'}, {'code': 300639, 'name': '泥瓦工'},
             {'code': 300638, 'name': '水电工'}, {'code': 220601, 'name': '其他房地产职位'}, {'code': 170610, 'name': '主播'},
             {'code': 170625, 'name': '带货主播'}, {'code': 130122, 'name': '直播运营'}, {'code': 170629, 'name': '游戏主播'},
             {'code': 170621, 'name': '中控/场控/助播'}, {'code': 210609, 'name': '化妆/造型/服装'},
             {'code': 170611, 'name': '演员/配音员'}, {'code': 170617, 'name': '艺人助理'}, {'code': 170620, 'name': '主持人/DJ'},
             {'code': 170630, 'name': '模特'}, {'code': 170602, 'name': '摄影/摄像师'}, {'code': 170601, 'name': '导演/编导'},
             {'code': 170605, 'name': '经纪人/星探'}, {'code': 170616, 'name': '编剧'}, {'code': 170615, 'name': '制片人'},
             {'code': 170609, 'name': '影视策划'}, {'code': 170608, 'name': '影视发行'}, {'code': 170603, 'name': '视频剪辑'},
             {'code': 170606, 'name': '后期制作'}, {'code': 120120, 'name': '动画设计'}, {'code': 120123, 'name': '修图师'},
             {'code': 170622, 'name': '灯光师'}, {'code': 170614, 'name': '录音/音效'}, {'code': 170604, 'name': '音频编辑'},
             {'code': 170624, 'name': '影视特效'}, {'code': 170626, 'name': '剧本杀主持人'}, {'code': 170628, 'name': '儿童引导师'},
             {'code': 170613, 'name': '放映员'}, {'code': 170627, 'name': '剧本杀编剧'}, {'code': 120614, 'name': '舞美设计师'},
             {'code': 140604, 'name': '策划经理'}, {'code': 140605, 'name': '广告文案'}, {'code': 140202, 'name': '广告客户执行'},
             {'code': 140601, 'name': '广告创意策划'}, {'code': 140407, 'name': '创意总监'}, {'code': 140603, 'name': '广告设计'},
             {'code': 140602, 'name': '美术指导'}, {'code': 140607, 'name': '广告制作'}, {'code': 140611, 'name': '广告审核'},
             {'code': 170212, 'name': '广告/会展项目经理'}, {'code': 130203, 'name': '文案编辑'}, {'code': 170104, 'name': '作者/撰稿人'},
             {'code': 170102, 'name': '编辑'}, {'code': 170109, 'name': '印刷排版'}, {'code': 170106, 'name': '校对录入'},
             {'code': 170101, 'name': '记者/采编'}, {'code': 130204, 'name': '网站编辑'}, {'code': 130201, 'name': '主编/副主编'},
             {'code': 170105, 'name': '出版发行'}, {'code': 170627, 'name': '剧本杀编剧'}, {'code': 170501, 'name': '其他传媒职位'},
             {'code': 140101, 'name': '市场营销策划'}, {'code': 140111, 'name': '海外市场'}, {'code': 140109, 'name': '活动策划执行'},
             {'code': 140506, 'name': '会务/会展执行'}, {'code': 140505, 'name': '会务/会展策划'}, {'code': 140401, 'name': '市场总监'},
             {'code': 140404, 'name': 'CMO'}, {'code': 140104, 'name': '市场推广/地推'}, {'code': 130109, 'name': '网络推广'},
             {'code': 140115, 'name': '游戏推广'}, {'code': 140608, 'name': '媒介投放'}, {'code': 140609, 'name': '媒介商务BD'},
             {'code': 140204, 'name': '媒介专员'}, {'code': 140206, 'name': '媒介经理/总监'}, {'code': 140116, 'name': '信息流优化师'},
             {'code': 140105, 'name': 'SEO'}, {'code': 140106, 'name': 'SEM'}, {'code': 140112, 'name': '政府关系'},
             {'code': 140804, 'name': '项目申报专员'}, {'code': 140801, 'name': '政策研究'}, {'code': 140803, 'name': '社工'},
             {'code': 140203, 'name': '品牌公关'}, {'code': 140405, 'name': '公关总监'}, {'code': 140604, 'name': '策划经理'},
             {'code': 140605, 'name': '广告文案'}, {'code': 140202, 'name': '广告客户执行'}, {'code': 140601, 'name': '广告创意策划'},
             {'code': 140407, 'name': '创意总监'}, {'code': 140603, 'name': '广告设计'}, {'code': 140602, 'name': '美术指导'},
             {'code': 140607, 'name': '广告制作'}, {'code': 140611, 'name': '广告审核'}, {'code': 170212, 'name': '广告/会展项目经理'},
             {'code': 260109, 'name': '市场调研'}, {'code': 140114, 'name': '选址开发'}, {'code': 140108, 'name': '商业数据分析'},
             {'code': 140313, 'name': '广告销售'}, {'code': 140504, 'name': '会展活动销售'}, {'code': 140501, 'name': '会议活动销售'},
             {'code': 140701, 'name': '其他市场职位'}, {'code': 240103, 'name': '物流专员'}, {'code': 240104, 'name': '物流经理'},
             {'code': 240402, 'name': '物流总监'}, {'code': 240105, 'name': '物流运营'}, {'code': 240106, 'name': '物流跟单'},
             {'code': 240108, 'name': '调度员'}, {'code': 240110, 'name': '运输经理/主管'}, {'code': 240119, 'name': '跟车员'},
             {'code': 240113, 'name': '水/空/陆运操作'}, {'code': 240111, 'name': '货代/物流销售'},
             {'code': 240109, 'name': '物流/仓储项目经理'}, {'code': 240302, 'name': '集装箱管理'},
             {'code': 240206, 'name': '配/理/拣/发货'}, {'code': 240303, 'name': '配送员'}, {'code': 290205, 'name': '送餐员'},
             {'code': 240304, 'name': '快递员'}, {'code': 300630, 'name': '搬运工/装卸工'}, {'code': 300602, 'name': '叉车工'},
             {'code': 240118, 'name': '配送站长'}, {'code': 240301, 'name': '货运司机'}, {'code': 150208, 'name': '商务司机'},
             {'code': 240305, 'name': '网约车司机'}, {'code': 240306, 'name': '代驾司机'}, {'code': 240307, 'name': '驾校教练'},
             {'code': 240308, 'name': '客运司机'}, {'code': 100311, 'name': '无人机飞手'}, {'code': 240204, 'name': '仓库管理员'},
             {'code': 240205, 'name': '仓库文员'}, {'code': 240201, 'name': '仓库主管/经理'}, {'code': 240101, 'name': '供应链专员'},
             {'code': 240102, 'name': '供应链经理'}, {'code': 240401, 'name': '供应链总监'},
             {'code': 300107, 'name': '生产计划/物料管理(PMC)'}, {'code': 250103, 'name': '采购专员/助理'},
             {'code': 250102, 'name': '采购经理/主管'}, {'code': 250105, 'name': '采购工程师'}, {'code': 250104, 'name': '买手'},
             {'code': 250108, 'name': '供应商质量工程师'}, {'code': 250111, 'name': '商品专员/助理'}, {'code': 140312, 'name': '商品经理'},
             {'code': 250101, 'name': '采购总监'}, {'code': 250109, 'name': '招标专员'}, {'code': 250110, 'name': '投标专员'},
             {'code': 250203, 'name': '外贸业务员'}, {'code': 250201, 'name': '外贸经理'}, {'code': 250205, 'name': '海外销售'},
             {'code': 250204, 'name': '贸易跟单'}, {'code': 240117, 'name': '单证员'}, {'code': 240114, 'name': '报关/报检员'},
             {'code': 250301, 'name': '其他采购/贸易类职位'}, {'code': 300801, 'name': '电池工程师'},
             {'code': 300802, 'name': '电机工程师'}, {'code': 230102, 'name': '车身/造型设计'}, {'code': 230106, 'name': '汽车电子工程师'},
             {'code': 101308, 'name': '自动驾驶系统工程师'}, {'code': 300803, 'name': '线束设计'},
             {'code': 230110, 'name': '内外饰设计工程师'}, {'code': 230105, 'name': '动力系统工程师'},
             {'code': 230103, 'name': '底盘工程师'}, {'code': 230107, 'name': '汽车零部件设计'}, {'code': 230101, 'name': '汽车设计'},
             {'code': 230109, 'name': '汽车质量工程师'}, {'code': 230210, 'name': '总装工程师'}, {'code': 230108, 'name': '汽车项目管理'},
             {'code': 230111, 'name': '总布置工程师'}, {'code': 230204, 'name': '汽车维修'}, {'code': 230205, 'name': '汽车美容'},
             {'code': 230213, 'name': '洗车工'}, {'code': 230209, 'name': '汽车改装'}, {'code': 230203, 'name': '汽车服务顾问'},
             {'code': 230208, 'name': '4S店店长/维修站长'}, {'code': 230207, 'name': '二手车评估师'},
             {'code': 230206, 'name': '汽车查勘定损'}, {'code': 230214, 'name': '加油员'}, {'code': 230201, 'name': '汽车销售'},
             {'code': 230202, 'name': '汽车配件销售'}, {'code': 210201, 'name': '护士'}, {'code': 210503, 'name': '导医'},
             {'code': 210202, 'name': '护士长'}, {'code': 290111, 'name': '护工'}, {'code': 210309, 'name': '外科医生'},
             {'code': 210306, 'name': '内科医生'}, {'code': 210313, 'name': '皮肤科医生'}, {'code': 210311, 'name': '妇产科医生'},
             {'code': 210310, 'name': '儿科医生'}, {'code': 210312, 'name': '眼科医生'}, {'code': 210303, 'name': '精神心理科医生'},
             {'code': 210402, 'name': '整形医生'}, {'code': 210307, 'name': '全科医生'}, {'code': 210314, 'name': '耳鼻喉科医生'},
             {'code': 210111, 'name': '医学检验'}, {'code': 210113, 'name': '放射科医生'}, {'code': 210114, 'name': '超声科医生'},
             {'code': 210315, 'name': '麻醉科医生'}, {'code': 210316, 'name': '病理科医生'}, {'code': 210112, 'name': '医生助理'},
             {'code': 210302, 'name': '中医'}, {'code': 210304, 'name': '口腔科医生'}, {'code': 210308, 'name': '幼儿园保健医'},
             {'code': 210104, 'name': '药剂师'}, {'code': 210109, 'name': '验光师'}, {'code': 210317, 'name': '医务管理'},
             {'code': 210103, 'name': '其他医生职位'}, {'code': 210403, 'name': '理疗师'}, {'code': 210401, 'name': '营养师/健康管理师'},
             {'code': 210404, 'name': '针灸推拿'}, {'code': 210305, 'name': '康复治疗师'}, {'code': 290118, 'name': '产后康复师'},
             {'code': 260112, 'name': '心理咨询师'}, {'code': 210412, 'name': '按摩师'}, {'code': 210411, 'name': '足疗师'},
             {'code': 210415, 'name': '采耳师'}, {'code': 210803, 'name': '药店店员'}, {'code': 210802, 'name': '执业药师/驻店药师'},
             {'code': 210801, 'name': '药店店长'}, {'code': 210115, 'name': '生物学研究人员'}, {'code': 210108, 'name': '医药研发'},
             {'code': 210128, 'name': '生物信息工程师'}, {'code': 210117, 'name': '药品生产'}, {'code': 210116, 'name': '药品注册'},
             {'code': 210123, 'name': '医药项目经理'}, {'code': 210124, 'name': '细胞培养员'}, {'code': 210130, 'name': '药理毒理研究员'},
             {'code': 210126, 'name': '药物合成'}, {'code': 210129, 'name': '制剂研发'}, {'code': 210125, 'name': '药物分析'},
             {'code': 210127, 'name': '医疗产品技术支持'}, {'code': 210119, 'name': '临床协调员'}, {'code': 211002, 'name': '临床监查员'},
             {'code': 211001, 'name': '临床项目经理'}, {'code': 210120, 'name': '临床数据分析'},
             {'code': 210118, 'name': '临床医学经理/专员'}, {'code': 210501, 'name': '临床医学总监'},
             {'code': 210122, 'name': '医疗器械生产/质量管理'}, {'code': 210105, 'name': '医疗器械研发'},
             {'code': 210121, 'name': '医疗器械注册'}, {'code': 210901, 'name': '试剂研发'}, {'code': 210502, 'name': '医药代表'},
             {'code': 210506, 'name': '医疗器械销售'}, {'code': 210505, 'name': '医美咨询'}, {'code': 210504, 'name': '健康顾问'},
             {'code': 210507, 'name': '口腔咨询师'}, {'code': 210101, 'name': '医学编辑'}, {'code': 210701, 'name': '其他医疗健康职位'},
             {'code': 180402, 'name': '柜员'}, {'code': 180404, 'name': '银行大堂经理'}, {'code': 180403, 'name': '客户经理'},
             {'code': 180406, 'name': '信贷专员'}, {'code': 180106, 'name': '证券交易员'}, {'code': 180802, 'name': '卖方分析师'},
             {'code': 180803, 'name': '买方分析师'}, {'code': 180806, 'name': '投资银行业务'}, {'code': 180805, 'name': '基金经理'},
             {'code': 180402, 'name': '柜员'}, {'code': 180807, 'name': '量化研究员'}, {'code': 150307, 'name': '风控'},
             {'code': 180204, 'name': '合规稽查'}, {'code': 180203, 'name': '资信评估'}, {'code': 180304, 'name': '清算'},
             {'code': 180104, 'name': '资产评估'}, {'code': 180501, 'name': '金融产品经理'}, {'code': 180503, 'name': '催收员'},
             {'code': 180101, 'name': '投资经理'}, {'code': 180118, 'name': '投资助理'}, {'code': 180103, 'name': '行业研究'},
             {'code': 180115, 'name': '融资'}, {'code': 180117, 'name': '投后管理'}, {'code': 180116, 'name': '并购'},
             {'code': 180112, 'name': '投资总监/VP'}, {'code': 180120, 'name': '投资者关系/证券事务代表'},
             {'code': 180111, 'name': '其他投融资职位'}, {'code': 180703, 'name': '保险理赔'}, {'code': 180702, 'name': '保险精算师'},
             {'code': 180506, 'name': '理财顾问'}, {'code': 180701, 'name': '保险顾问'}, {'code': 180401, 'name': '信用卡销售'},
             {'code': 180801, 'name': '证券经纪人'}, {'code': 180601, 'name': '其他金融职位'}, {'code': 100601, 'name': '项目经理/主管'},
             {'code': 100603, 'name': '项目专员/助理'}, {'code': 220212, 'name': '建筑施工项目经理'},
             {'code': 220222, 'name': '装修项目经理'}, {'code': 220102, 'name': '房地产项目管理'}, {'code': 101012, 'name': '通信项目经理'},
             {'code': 100817, 'name': '硬件项目经理'}, {'code': 260106, 'name': '咨询项目管理'},
             {'code': 240109, 'name': '物流/仓储项目经理'}, {'code': 170212, 'name': '广告/会展项目经理'},
             {'code': 230108, 'name': '汽车项目管理'}, {'code': 300407, 'name': '化工项目经理'}, {'code': 211001, 'name': '临床项目经理'},
             {'code': 210123, 'name': '医药项目经理'}, {'code': 260111, 'name': '知识产权/专利/商标代理人'},
             {'code': 140804, 'name': '项目申报专员'}, {'code': 260106, 'name': '咨询项目管理'}, {'code': 260101, 'name': '企业管理咨询'},
             {'code': 260107, 'name': '战略咨询'}, {'code': 150303, 'name': '财务顾问'}, {'code': 260104, 'name': 'IT咨询顾问'},
             {'code': 260402, 'name': '咨询经理'}, {'code': 260105, 'name': '人力资源咨询顾问'}, {'code': 260108, 'name': '猎头顾问'},
             {'code': 260401, 'name': '咨询总监'}, {'code': 260109, 'name': '市场调研'}, {'code': 100511, 'name': '数据分析师'},
             {'code': 260112, 'name': '心理咨询师'}, {'code': 260113, 'name': '婚恋咨询师'}, {'code': 260114, 'name': '工程咨询'},
             {'code': 260110, 'name': '其他咨询顾问'}, {'code': 260301, 'name': '英语翻译'}, {'code': 260302, 'name': '日语翻译'},
             {'code': 260303, 'name': '韩语/朝鲜语翻译'}, {'code': 260306, 'name': '俄语翻译'}, {'code': 260307, 'name': '西班牙语翻译'},
             {'code': 260305, 'name': '德语翻译'}, {'code': 260304, 'name': '法语翻译'}, {'code': 260308, 'name': '其他语种翻译'},
             {'code': 260204, 'name': '律师助理'}, {'code': 260203, 'name': '知识产权律师'}, {'code': 260201, 'name': '律师'},
             {'code': 260501, 'name': '其他咨询/翻译类职位'}, {'code': 301002, 'name': '光伏系统工程师'},
             {'code': 301003, 'name': '风电/光伏运维工程师'}, {'code': 301004, 'name': '水利工程师'},
             {'code': 301001, 'name': '地质工程师'}, {'code': 300903, 'name': 'EHS工程师'}, {'code': 300905, 'name': '环境采样/检测员'},
             {'code': 300902, 'name': '环评工程师'}, {'code': 300901, 'name': '环保工程师'}, {'code': 300904, 'name': '碳排放管理师'},
             {'code': 400101, 'name': '农业/林业技术员'}, {'code': 400201, 'name': '饲养员'}, {'code': 400202, 'name': '养殖技术员'},
             {'code': 400203, 'name': '畜牧兽医'}, {'code': 150407, 'name': '总裁/总经理/CEO'},
             {'code': 150408, 'name': '副总裁/副总经理/VP'}, {'code': 150411, 'name': '总助/CEO助理/董事长助理'},
             {'code': 150410, 'name': '区域负责人(辖多个分公司)'}, {'code': 150409, 'name': '分公司/代表处负责人'},
             {'code': 150414, 'name': '董事会秘书'}, {'code': 150413, 'name': '联合创始人'}, {'code': 150499, 'name': '高级管理职位'},
             {'code': 200101, 'name': '其他职位'}]
