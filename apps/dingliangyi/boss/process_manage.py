import subprocess
import time
import os
from pathlib import Path
from loguru import logger
import sys


def start_proxy_server(port=8766, host="localhost", workspace_root=None):
    try:
        workspace_root = workspace_root or Path.cwd().parent.parent.parent
        script_path = workspace_root / "apps/dingliangyi/boss/proxy_server.py"

        command = [
            "python",
            str(script_path),
            "--port", str(port),
            "--host", host
        ]

        logger.info(f"启动代理服务器进程: {' '.join(command)}")

        # 启动独立进程
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True,
            cwd=str(workspace_root)  # 设置工作目录
        )

        # 等待一小段时间确保进程启动
        time.sleep(1.0)

        # 检查进程是否还在运行
        if process.poll() is None:
            result = {
                "process": process,
                "pid": process.pid,
                "command": " ".join(command),
                "status": "running",
                "port": port,
                "host": host,
                "script_path": str(script_path)
            }
            logger.info(f"代理服务器进程已启动，PID: {process.pid}, 端口: {port}")
            return result
        else:
            # 进程已经退出，获取错误信息
            stdout, stderr = process.communicate()
            error_msg = f"进程启动失败: {stderr.strip() if stderr else stdout.strip()}"
            logger.error(error_msg)
            return {
                "process": None,
                "pid": None,
                "command": " ".join(command),
                "status": "failed",
                "port": port,
                "host": host,
                "error": error_msg
            }

    except Exception as e:
        error_msg = f"启动代理服务器进程时出错: {e}"
        logger.error(error_msg)
        return {
            "process": None,
            "pid": None,
            "command": "",
            "status": "error",
            "port": port,
            "host": host,
            "error": error_msg
        }


def stop_proxy_server(server_info):
    try:
        if not server_info or not isinstance(server_info, dict):
            logger.warning("没有有效的服务器信息")
            return False

        process = server_info.get("process")
        pid = server_info.get("pid")

        if not process:
            logger.warning("没有有效的进程对象")
            return False

        if process.poll() is not None:
            logger.info(f"进程 {pid} 已经停止")
            return True

        logger.info(f"正在停止代理服务器进程 {pid}")

        # 尝试优雅地终止进程
        process.terminate()

        # 等待进程结束
        try:
            process.wait(timeout=5)
            logger.info(f"进程 {pid} 已成功停止")
            return True
        except subprocess.TimeoutExpired:
            # 如果优雅终止失败，强制杀死进程
            logger.warning(f"进程 {pid} 未响应终止信号，强制杀死")
            process.kill()
            process.wait()
            logger.info(f"进程 {pid} 已被强制停止")
            return True

    except Exception as e:
        logger.error(f"停止代理服务器进程时出错: {e}")
        return False


if __name__ == "__main__":
    server_info = start_proxy_server(port=8766)
    print(f"服务器信息: {server_info}")

    if server_info["status"] == "running":
        print("服务器启动成功！")

        # 等待几秒
        time.sleep(3)

        # 停止服务器
        if stop_proxy_server(server_info):
            print("服务器停止成功！")
        else:
            print("服务器停止失败！")
    else:
        print(f"服务器启动失败: {server_info.get('error', '未知错误')}")
