import requests
import json
import random
from apps.dingliangyi.nine.main import main
import time
from loguru import logger


def getTraceId():
    timestamp = int(time.time() * 1000)
    hex_str = hex(timestamp)[2:]
    last_six = hex_str[-6:]
    chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
    random_part = ""
    for _ in range(10):
        random_part += chars[int(random.random() * 62)]
    combined = last_six + random_part
    return "F-" + combined


headers = {
    "referer": "https://www.zhipin.com/web/user/safe/verify-slider?callbackUrl=https%3A%2F%2Fwww.zhipin.com%2Fweb%2Fgeek%2Fjobs",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "traceId": getTraceId(),
}


def gt_challenge(session):
    cookies = {
        # "ab_guid": "4b12005a-8052-4404-aaf9-637cecbb6d8d",
        # "__c": "1752141971",
        # "__g": "-",
        # "__a": "40185376.1752141971..175214197*******.1"
    }
    url = "https://www.zhipin.com/wapi/zpsecureflow/captcha/gettype"
    response = session.get(url, headers=headers)
    return response.json()['zpData']['startCaptcha']


def verify_nine(ip, res):
    session = requests.Session()
    session.proxies = {'http': ip, 'https': ip}

    validate, challenge = main(json.loads(res))
    count = 0
    while not validate or not challenge:
        count += 1
        if count > 3:
            logger.error("Failed to get validate and challenge after 3 attempts.")
            return
        res = gt_challenge(session)
        validate, challenge = main(json.loads(res))

    headers.update({
        "Host": "www.zhipin.com",
        "Zp-Captcha-Validate": validate,
        "Zp-Captcha-Seccode": f"{validate}|jordan",
        "Zp-Captcha-Challenge": challenge,
        "Zp-Captcha-Type": "1",
        "Origin": "https://www.zhipin.com",
    })
    cookies = {
        # "__g": "-",
        # "ab_guid": "b59a7e79-3dc7-4d4d-bafc-b7c2bbd85cb8",
        # "__zp_stoken__": "771dfOTTDlsKywpvCtDsvBAkJBwA7IDA0Kh44OSAyNzw5NDw5Pjk0NBcwKWdvwrVAWMOFZzciOTQ1Nzk7NDw4Ejk4wro3Ni59b8K1QFjDhRHCvsKwBDjCvAbEgcKwCcKKwrIEZcK0KinDgcK0NT06MFXCucO9wrgLwrTDtcKzF8K0IsK4PTLDscKwBCI2EQ1XXDYwRUJeAEJhRVRaTgJKQk0iMDg%2FNzrCvsO1KTkGBgcPBAYGBw8ECQkEAwgJCQQMBwAABQ0CMTnCm8K9wofCu8OhwpnDq8SUwpPDgcO0WsKcTcKlwrHCuELCgELCi0vCp0zCq2zCi2HCpF3CncKzwpjCv8K2wrPColRpUsKnwr9kXUfCtUVvdlNpBghYCwwENgg9fMOA",
        # "__c": "1752202980",
        # "__l": "l=%2Fwww.zhipin.com%2Fweb%2Fuser%2Fsafe%2Fverify-slider%3FcallbackUrl%3Dhttps%253A%252F%252Fwww.zhipin.com%252Fweb%252Fgeek%252Fjobs&r=&g=&s=3&friend_source=0&s=3&friend_source=0",
        # "__a": "91507654.1752202980..1752202980.*******"
    }
    url = "https://www.zhipin.com/wapi/zpsecureflow/captcha/validate"
    response = session.post(url, headers=headers, cookies=cookies)
    logger.success(response.json())
    return True

if __name__ == '__main__':
    pass
