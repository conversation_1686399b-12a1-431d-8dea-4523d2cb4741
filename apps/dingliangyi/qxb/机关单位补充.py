from gslib.id_center import id_center_query, EntityType
from dao.company import CompanyDao, CompanyGraphDao
from tqdm import tqdm
from loguru import logger

from dao.cods.cods2 import CodsEntity, CodsDao as CodsDao2
from apps.octopus.utils.entry_manager import EntryManager
from libs.env import ConstantProps
from clients.mysql_client import MySQLClient

mysql_client = MySQLClient(**ConstantProps.PROPS_GS_OUTER_RW)
sql = f'select * from prism.company where name = %s'
company_dao = CompanyDao()
entry_manager = EntryManager()
cods_dao2 = CodsDao2()


def dispatch(entry_word, inst_name):
    return entry_manager.inst_immediate(
        entry_name='credit',
        entry_word=entry_word,
        inst_name=inst_name,
        reason='cods_11',
        ignore_latest_search_empty=False
    )


def main():
    # with open(r"C:\Users\<USER>\Downloads\Execute and watch.csv", 'r', encoding='utf-8') as f:
    #     list_ = f.readlines()
    #
    # file = open(r'机关单位补充.txt', 'w', encoding='utf-8')
    #
    # for i in tqdm(list_[1:]):
    #     a = i.split(',')
    #     # company = company_dao.get(a[5], 'name')
    #     company = cods_dao2.get(a[3], 'unified_social_credit_code')
    #     if not company and '撤销' not in a[5]:
    #         logger.info(f'{a[3]} -- {a[5]} -- {a[21]}')
    #         file.write(f'{a[3]},{a[5]},{a[21]}\n')

    # with open(r'机关单位补充.txt', 'r', encoding='utf-8') as f:
    #     list_ = f.readlines()
    # file = open(r'机关单位补充2.txt', 'w', encoding='utf-8')
    #
    # for i in tqdm(list_):
    #     a = i.split(',')
    #     pre = id_center_query(credit_no=a[0])
    #     if pre[1] == 0:
    #         logger.info(i)
    #         file.write(i)

    with open(r'机关单位补充.txt', 'r', encoding='utf-8') as f:
        list_ = f.readlines()
    for i in tqdm(list_):
        a = i.split(',')
        success = dispatch(a[0], 'cods')
        logger.success(f'{a[0]} -- {success}')

    # company = company_dao.get('晋城市城区住房和城乡建设管理局', 'name')
    # print(company)
    # pre = id_center_query(credit_no='11140502767136579M')
    # print(pre)


if __name__ == '__main__':
    main()
