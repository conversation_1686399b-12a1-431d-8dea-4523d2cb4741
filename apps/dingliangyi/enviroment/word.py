from resx.redis_types import Redis
from resx.config import *
import json
import time
from apps.dingliangyi.enviroment.main import EnvironmentPenalty
import requests
import re


class EnvironmentalPenalties_LaunchWord(EnvironmentPenalty):

    def __init__(self):
        self.redis = Redis(**CFG_REDIS_ZHUAN, db=0)
        super().__init__()

    def get_redis_data(self):
        while True:
            result = self.redis.zpopmin('environmental_8m_crawler_queue', 1)
            if result:
                result = result[0][0].decode("utf-8")
                yield json.loads(result)
            else:
                time.sleep(5)

    def add_redis_data(self, data, score=1):
        if not isinstance(data, str):
            data = json.dumps(data, ensure_ascii=False)
        self.redis.zadd('environmental_8m_feedback_queue', {data: score})

    @staticmethod
    def get_lvwang_api(companyName):
        if not (companyName and isinstance(companyName, str)):
            return
        base_url = "http://10.39.223.80:8089/api/tyc/v1/lvwang/punish/company?enterprise_name=%s" % companyName
        for _ in range(3):
            try:
                res = requests.get(base_url, timeout=5)
                return res.json()
            except:
                continue
        return

    def handler_once_8m_data(self, data_by_8m):
        associate_key_dict = {}
        companyName = data_by_8m.get("companyName")
        for data in data_by_8m.get("dataContent"):
            punish_number = (data.get("punish_number") or "").strip()
            associate_key = re.sub('[^A-Za-z0-9\u4e00-\u9fa5]', '', punish_number)
            associate_key_dict[associate_key] = [False, punish_number]

        if not (companyName and associate_key_dict):
            return 0

        lvwang_data = self.get_lvwang_api(companyName)
        for data in lvwang_data['data']['data']:
            punish_number = (data.get('punish_paper') or "").strip()
            associate_key = re.sub('[^A-Za-z0-9\u4e00-\u9fa5]', '', punish_number)
            associate_key_dict.setdefault(associate_key, [True, punish_number])
            associate_key_dict[associate_key][0] = True
            self.parse(data, False)

        for associate_key, value in associate_key_dict.items():
            is_exist, punish_number = value
            if not is_exist:
                self.save_msv(
                    companyName,
                    punish_number,
                    {
                        "punish_time": "",
                        "area": "",
                        "punish_information": "create by environmental_penalties_crawl_launch_word.py",
                        "punish_paper": punish_number,
                        "pollution": "",
                        "punish_record_id": 0,
                        "punisher": "",
                        "update_time": "",
                        "money": "",
                        "attachment": None,
                        "punish_cat": "",
                        "name": companyName,
                        "ctime": "",
                        "source_info": "",
                        "status": "异常"
                    }
                )
                print("self_product:", companyName, punish_number)

        return 1

    def main(self):
        for data_by_8m in self.get_redis_data():
            if self.handler_once_8m_data(data_by_8m):
                self.add_redis_data(data_by_8m, score=1)
            else:
                self.add_redis_data(data_by_8m, score=9)
            time.sleep(1)  # 绿网方的qps为1


if __name__ == '__main__':
    env_penalties = EnvironmentalPenalties_LaunchWord()
    # env_penalties.main()

    data_ = {"companyName": "陕西双众建筑科技有限公司", "workOrderNo": "C202503251904408827273457674",
             "gid": 2311745791, "dataContent": [
            {"taskId": "3d3f9863bef483f7085f24412a0998a0_1742881420541", "gid": 2311745791,
             "echoTitle": "2019-01-21/黄环罚（2019）01号", "_index": 0, "publish_time": "2019-01-21", "id": 1591062,
             "punish_number": "黄环罚（2019）01号"}], "creditCode": "91610132MA6TX0PE86", "base": "snx"}
    env_penalties.handler_once_8m_data(data_)
