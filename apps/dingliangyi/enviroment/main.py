import datetime
import json
import re
from hashlib import md5
import time

import dateparser
import requests
from loguru import logger
from parse_anything_client import <PERSON><PERSON>r<PERSON>lient
from resx.mysql_dao import MySQLDao
from resx.obs_client import OBSClient
import schedule
from resx.redis_types import Redis
from resx.config import *


class EnvironmentPenalty:

    def __init__(self):
        self.obs = OBSClient(bucket_name='tyc-data-d0')
        self.multi_update_where_cols = ("punish_name", "associate_key", "source_name")
        self.parser_text = ParserClient(phone="***********")
        self.msv_dao = MySQLDao(**CFG_MYSQL_ZX_RDS113, db_tb_name='prism1.multi_source_fusion_environmental_punish',
                                primary_index_fields=(["punish_name", "associate_key", "source_name"], []))
        self.table = 'data_business_risk.environmental_penalties'
        self.dao = MySQLDao(**CFG_MYSQL_ZX_RDS109, db_tb_name=self.table, primary_index_fields=(['punished_name', 'punish_number'], []))
        self.redis = Redis(**CFG_REDIS_ZHUAN, db=3)
        self.pattern = '[^A-Za-z0-9\u4e00-\u9fa5]'

    def save_msv(self, punished_name, punish_number, data):
        try:
            associate_key = re.sub(self.pattern, '', punish_number)
            md5_str = md5(json.dumps(data, ensure_ascii=False).encode('utf-8')).hexdigest()
            obs_file_path = f"environmental_punish/{punished_name}/{associate_key}/{md5_str}.json"
            source_name = "绿网"
            if self.obs.obs_client.headObject('tyc-data-d0', obs_file_path):
                is_change = False
            else:
                self.obs.obs_client.putContent('tyc-data-d0', obs_file_path, json.dumps(data, ensure_ascii=False))
                is_change = True
            if self.msv_dao.get(punish_name=punished_name, associate_key=associate_key, source_name=source_name):
                item = {
                    "punish_name": punished_name,
                    "associate_key": associate_key,
                    "source_name": source_name,
                    "crawl_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "is_deleted": 1 if data.get('status') == '异常' else 0
                }
                if is_change:
                    item["obs_link"] = obs_file_path
                update_sql = f"""update prism1.multi_source_fusion_environmental_punish 
                        set {",".join([f"{k}=%({k})s" for k in item if k not in self.multi_update_where_cols])} 
                        where {" and ".join([f"{k}=%({k})s" for k in self.multi_update_where_cols])}"""
                self.msv_dao.execute(update_sql)
                logger.info(f"Multi-source fusion update {item.get('punish_name')} {item.get('associate_key')} {item.get('source_name')} succ")
            else:
                item = {
                    "punish_name": punished_name,
                    "punish_number": punish_number,
                    "associate_key": associate_key,
                    "source_name": source_name,
                    "url": data.get("source_info"),
                    "obs_link": obs_file_path,
                    "crawl_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "is_deleted": 1 if data.get('status') == '异常' else 0
                }
                self.msv_dao.save(item)
        except Exception as e:
            logger.error(f"Error saving multi-source fusion data: {e} - {data}")

    @staticmethod
    def clean_name(name):
        return name.strip().replace(' ', '').replace('(', '（').replace(')', '）')

    @staticmethod
    def request(page):
        for _ in range(10):
            try:
                url = "http://************:8089/api/tyc/v1/lvwang/punish"
                params = {
                    "page": page,
                    "access-token": "tyc:434e511e1e0f0fb370aaece7764548f2",
                    "time": str(datetime.date.today())
                }
                response = requests.get(url, params=params, timeout=5)
                return response.json()
            except Exception as e:
                print(f"Error occurred: {e}")
                continue

    def parse(self, data, ai=True):
        publish_time = dateparser.parse(data.get('punish_time') or "")
        status = data.get('status') or ''
        punish_record_id = str(data.get('punish_record_id', ''))
        punished_name = self.clean_name(data.get('name', ''))
        punish_number = data.get('punish_paper', '').strip()

        if ai and self.redis.hexists('environmental_penalty_ai_parse', punish_record_id):
            logger.info('已处理过: %s %s %s', punished_name, punish_number, punish_record_id)
            return
        else:
            self.redis.hset('environmental_penalty_ai_parse', punish_record_id, '1')

        if not punished_name or not punish_number:
            logger.info(f"数据不完整: {punished_name} {punish_number} {punish_record_id}")
            return
        if len(punish_number) < 6 or 'X' in punish_number:
            logger.info(f"处罚号不合法: {punished_name} {punish_number} {punish_record_id}")
            return

        punish_money = data.get('punish_money') or data.get('money', 0) or 0
        punish_content = data.get('punish_cat', '') if not punish_money else f"罚款:{punish_money}万元"

        text = data.get('information') or data.get('punish_information', '')
        text_result = self.parser_text.parse_data(parser_id="19411eb6-6d6b-4f53-bdff-2b43fb82e73f", source=text)

        info = {
            'punished_name': punished_name,
            'punish_number': punish_number,
            'punish_department': data.get('punisher', '').strip(),
            'publish_time': publish_time,

            'punish_basis': text_result['result']['punish_basis'],
            'lawbreaking': text_result['result']['lawbreaking'],
            'punish_reason': text_result['result']['punish_reason'],
            'punish_content': text_result['result']['punish_content'] or punish_content,

            # 'execution': data.get('', ''),
            'source': data.get('source_info', ''),
            'source_flag': 'lvwang',
            'punish_record_id': punish_record_id,
            'fines_amount': text_result['result']['fines_amount'] or int(punish_money) * 10000,
            'deleted': 1 if status == '异常' else 0
        }

        sql = f"select * from {self.table} where punished_name=%s and punish_number like %s"
        pre = self.dao.select(sql, args=(punished_name, re.sub(self.pattern, '%', punish_number)))
        if not pre:
            self.dao.save(info)
        else:
            info2 = {k: v for k, v in info.items() if k not in ['punished_name', 'punish_number'] and v}
            set_field = ', '.join([f"{k}=%s" for k in info2])
            sql2 = f'update {self.table} set {set_field} where id = {pre["id"]}'
            self.dao.execute(sql2, args=tuple(info2.values()))
            logger.info(f"Update {punished_name} {punish_number} succ")

    def from_api(self):
        total_page, page = 0, 1
        while not total_page or page <= total_page:
            datas = self.request(page)
            for data in datas['data']['data']:
                try:
                    self.parse(data)
                except Exception as e:
                    logger.error(f"Error parsing data: {data} - {e}")
            total_page = datas['data']['total_pages']
            page += 1


if __name__ == '__main__':
    environment_penalty = EnvironmentPenalty()
    environment_penalty.from_api()

    schedule.every(12).hours.do(environment_penalty.parse)
    while True:
        schedule.run_pending()
        time.sleep(1)
