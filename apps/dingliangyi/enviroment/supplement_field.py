import csv
import time
from loguru import logger
import datetime

from word import EnvironmentalPenalties_LaunchWord

env = EnvironmentalPenalties_LaunchWord()
file_path = r"C:\Users\<USER>\Downloads\env.csv"
call_count = 0
limit = 5500
today = datetime.date.today()

with open(file_path, newline='', encoding='utf-8') as csvfile:
    reader = csv.reader(csvfile)
    for row in reader:
        # 检查日期是否变化，重置计数器
        if datetime.date.today() != today:
            today = datetime.date.today()
            call_count = 0
        if call_count >= limit:
            logger.info("已达到今日调用上限，暂停处理。")
            time.sleep(60 * 60)
            break
        name = row[0]
        info = env.get_lvwang_api(name)
        for data in info['data']['data']:
            if call_count >= limit:
                logger.info("已达到今日调用上限，暂停处理。")
                break
            env.parse(data, False)
            call_count += 1
        logger.info(f"Processed {name}")
        time.sleep(1)
