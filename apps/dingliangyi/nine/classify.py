import time

from ultralytics import YOLO
import os
from PIL import Image
import io
from loguru import logger

from crop import crop_grid_image


class PredictByClassify:

    def __init__(self):
        self.model = YOLO(os.path.join(os.path.dirname(__file__), 'yolo_jy3_nine_classify.onnx'), task='classify')

    def predict(self, image: bytes):
        images = crop_grid_image(image)

        pic_classify = []
        for idx, image in enumerate(images):
            a = time.time()
            results = self.model.predict(source=image, imgsz=64, verbose=False)
            predicted_class = results[0].names[results[0].probs.top1]
            logger.info(f'timing: {round(time.time() - a, 3)} - {idx + 1}: {predicted_class}')
            if (idx + 1) % 3 == 0:
                logger.info('-' * 100)
            pic_classify.append(predicted_class)

        result = []
        for idx, i in enumerate(pic_classify[:-1]):
            if pic_classify[-1] == i:
                result.append(idx + 1)

        return result

    @staticmethod
    def crop_grid_image(image: bytes, tile_dim=112, line_w=4):
        # 打开原始图片
        img = Image.open(io.BytesIO(image))
        img_width, img_height = img.size
        img_height = img_height - 40

        tile_count = 0
        images: list[Image.Image] = []
        # 循环遍历 3x3 网格
        for row in range(3):
            for col in range(3):
                tile_count += 1

                # 计算当前小图的左上角坐标
                # x 坐标 = 列号 * (小图宽度 + 分割线宽度)
                left = col * (tile_dim + line_w)
                # y 坐标 = 行号 * (小图高度 + 分割线宽度)
                top = row * (tile_dim + line_w)

                # 计算当前小图的右下角坐标
                # x 坐标 = 左上角 x + 小图宽度
                right = left + tile_dim
                # y 坐标 = 左上角 y + 小图高度
                bottom = top + tile_dim

                # 定义裁剪区域 (left, top, right, bottom)
                # Pillow 的 crop 方法需要一个四元组
                box = (left, top, right, bottom)

                tile = img.crop(box)
                images.append(tile)

        img_temp = img.crop((0, img_height, 45, img_height + 40))
        images.append(img_temp)
        return images


if __name__ == '__main__':
    model = PredictByClassify()
    for j in os.listdir('./error_pic'):
        with open(f'./error_pic/{j}', 'rb') as f:
            image_ = f.read()
        _ = model.predict(image_)
        logger.success(_)
