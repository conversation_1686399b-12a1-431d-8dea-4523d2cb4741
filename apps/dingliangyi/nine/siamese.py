import time
from PIL import Image
import onnxruntime
import numpy as np
import io
import os
from loguru import logger

from crop import crop_grid_image


class SiamOnnx:
    def __init__(self, providers=None):
        if not providers:
            providers = ['CPUExecutionProvider']
        self.sess = onnxruntime.InferenceSession(os.path.join(os.path.dirname(__file__), 'jy3_nine_siamese.onnx'), providers=providers)
        self.loadSize = 512
        self.input_shape = [105, 105]

    def predict(self, image: bytes):
        images = crop_grid_image(image)

        verify_result = []
        for idx, image in enumerate(images[:-1]):
            image1 = image
            image2 = images[-1]
            a = time.time()
            large_img = self.predict_siamese(image1, image2)
            logger.info(f'timing: {round(time.time() - a, 3)} - {idx + 1}: {round(large_img, 3)}')
            if (idx + 1) % 3 == 0:
                logger.info('-' * 100)
            if 0.5 < large_img < 1:
                verify_result.append(idx + 1)

        return verify_result

    @staticmethod
    def sigmoid(x):
        return 1 / (1 + np.exp(-x))

    @staticmethod
    def convert(file):
        # 图片转换为矩阵
        if isinstance(file, np.ndarray):
            img = Image.fromarray(file)
        elif isinstance(file, bytes):
            img = Image.open(io.BytesIO(file))
        elif isinstance(file, Image.Image):
            img = file
        else:
            img = Image.open(file)
        return img

    def open_image(self, file, input_shape, nc=3):
        out = self.convert(file)
        # 改变大小 并保证其不失真
        out = out.convert('RGB')
        h, w = input_shape
        out = out.resize((w, h), 1)
        if nc == 1:
            out = out.convert('L')
        return out

    def set_img(self, lines):
        image = self.open_image(lines, self.input_shape, 3)
        image = np.array(image).astype(np.float32) / 255.0
        photo = np.expand_dims(np.transpose(image, (2, 0, 1)), 0)
        return photo

    def predict_siamese(self, image1, image2):
        photo_1 = self.set_img(image1)
        photo_2 = self.set_img(image2)
        out = self.sess.run(None, {"x1": photo_1, "x2": photo_2})
        out = out[0]
        out = self.sigmoid(out)
        out = out[0][0]
        return out


if __name__ == '__main__':
    model = SiamOnnx()
    for j in os.listdir('./error_pic'):
        with open(f'./error_pic/{j}', 'rb') as f:
            image_ = f.read()
        _ = model.predict(image_)
        logger.success(_)
