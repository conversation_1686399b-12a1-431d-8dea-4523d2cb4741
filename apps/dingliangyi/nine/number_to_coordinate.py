import math


def number_to_coordinates(number):
    """
    将九宫格编号 (1-9) 转换为以 (1,1) 为左上角起点的 (x, y) 坐标。

    九宫格编号规则:
    1 2 3
    4 5 6
    7 8 9

    坐标系规则:
    左上角为 (1,1)
    x 轴向右增长 (列)
    y 轴向下增长 (行)
    对应关系：
    1 -> (1,1)   2 -> (2,1)   3 -> (3,1)
    4 -> (1,2)   5 -> (2,2)   6 -> (3,2)
    7 -> (1,3)   8 -> (2,3)   9 -> (3,3)

    Args:
      number (int): 1 到 9 之间的整数编号。

    Returns:
      tuple: 包含 (x, y) 坐标的元组。
             如果输入无效，则返回 None。
    """
    if not isinstance(number, int) or number < 1 or number > 9:
        print(f"错误：输入必须是 1 到 9 之间的整数。你输入的是: {number}")
        return None

    # 将编号调整为从 0 开始 (0 到 8)
    zero_based_number = number - 1

    # 计算从 0 开始的 x 坐标 (列索引: 0, 1, 2)
    x_zero_based = zero_based_number % 3
    # 计算从 0 开始的 y 坐标 (行索引: 0, 1, 2)
    y_zero_based = zero_based_number // 3

    # 将 0-based 坐标转换为 1-based 坐标
    x = x_zero_based + 1
    y = y_zero_based + 1

    return x, y


if __name__ == '__main__':
    _ = number_to_coordinates(3)
    print(_)
