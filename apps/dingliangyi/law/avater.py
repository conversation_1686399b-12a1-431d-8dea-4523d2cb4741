import time
import oss2
from obs import ObsClient
from public_utils_configs.util.TycSecureUtil import TycSecureUtil
import requests
import urllib3
import hashlib
from tqdm import tqdm
from loguru import logger

from resx.tools import BoundedExecutor
from resx.config import *
from resx.mysql_dao import MySQLDao

logger.add(sink=r'./logs/avatar_{time:YYYY_MM_DD}.log', rotation='1 GB', retention='2 days', encoding='utf8', level='INFO', enqueue=True)
urllib3.disable_warnings()

sec = TycSecureUtil(app_name='hk_report', env='prod', ak='infra-secure-obs-KBV5xtP3raLoWfxrlZCB1g==', sk='infra-secure-obs-INXEdn0u736Bv/g8v0LCYw==')
res = sec.decrypt_obs()
# print('打印结果ak:' + res[0] + ',ck:' + res[1])
sec2 = TycSecureUtil(app_name='pygs-work-parent', env='prod', ak='infra-secure-obs-e2APLgPcftGkWVXxa5Owhw==', sk='infra-secure-obs-iqafGsbB4QPMgWSOlXRTUw==')
res2 = sec2.decrypt_obs()
# print('打印结果ak:' + res2[0] + ',ck:' + res2[1])

proxies = {"http": "http://10.99.138.95:30636/",
           "https": "http://10.99.138.95:30636/"}
auth = oss2.Auth(res[0], res[1])
bucket_oss = oss2.Bucket(auth, 'http://oss-cn-beijing.aliyuncs.com', 'jindi-oss-wangsu')
bucket_obs = ObsClient(access_key_id=res2[0], secret_access_key=res2[1], server='http://obs.cn-north-4.myhuaweicloud.com')
dao_lawyer = MySQLDao(db_tb_name='data_judicial_risk.lawyer', **CFG_MYSQL_ZX_RDS108)
executor = BoundedExecutor(50)


def generate_lawyer_img_name(pic_url):
    m = hashlib.md5()
    m.update(pic_url)
    return m.hexdigest() + '.png'


def main(url):
    for _ in range(5):
        try:
            response = requests.get(url, proxies=proxies, timeout=5, verify=False)
            if response.status_code != 200:
                continue
            if '数据异常' in response.text:
                dao_lawyer.execute('update data_judicial_risk.lawyer set pic_url = "" where id = %s', args=(lawyer['id'],))
                logger.warning(f'数据异常:{url}')
                break
            remote_url = 'lawTeam/lawyerLogo/' + generate_lawyer_img_name(response.content)
            upload_reso = bucket_oss.put_object(remote_url, response.content)
            upload_res = bucket_obs.putContent('splider-test', remote_url, response.content)
            if upload_res and upload_res.status == 200:
                dao_lawyer.execute(f'update data_judicial_risk.lawyer set oss_path=%s where id=%s', args=(remote_url, lawyer['id']))
                logger.success(f'图片更新成功:{url}')
            break
        except:
            continue


while True:
    lawyers = list(dao_lawyer.select_many("select * from data_judicial_risk.lawyer where oss_path = '' and pic_url <> '' limit 1000"))
    bar = tqdm(total=len(lawyers))
    for lawyer in tqdm(lawyers):
        url = lawyer['pic_url']
        if not url:
            continue
        if not url.startswith('http'):
            url = "https://www.12348.gov.cn/imagetype/" + url
        future = executor.submit(main, url)
        future.add_done_callback(lambda x: bar.update(1))
    time.sleep(5)
