# from resx.log import setup_logger
#
# logger = setup_logger(logger_path='./logs/update_law.log', backup_count=10, name=__name__)

from urllib.parse import quote
from bs4 import BeautifulSoup
import re
from tqdm import tqdm
from loguru import logger
import time

from apps.dingliangyi.law.acla import Acla
from resx.tools import BoundedExecutor
from resx.config import *
from resx.mysql_dao import MySQLDao
import schedule

executor = BoundedExecutor(50)
dao_lawyer = MySQLDao(db_tb_name='data_judicial_risk.lawyer', **CFG_MYSQL_ZX_RDS108)
logger.add(sink=r'./logs/update_law_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8', enqueue=True)


class AclaLawyer(Acla):

    def get_detail_url(self, name):
        url = self.url_lawyer + quote(name)
        html = self.add_cookies_params_request(url)
        soup = BeautifulSoup(html, 'lxml')
        a = soup.select_one('.search-result-list a')
        if not a:
            logger.info('搜索为空')
            return
        url_encrypted = re.search("\('(.*?)' ,", a.get('onclick')).group(1)
        url = self.decrypt_url(html, url_encrypted, 'lawyer')
        return url

    def add_edu(self, license):
        url = self.get_detail_url(license)
        sql = f'update data_judicial_risk.lawyer set educational_background=%s,pic_url=%s,gender=%s,practice_area=%s where license=%s'
        if not url:
            dao_lawyer.execute(f'update data_judicial_risk.lawyer set educational_background=%s where license=%s', args=('-', license))
            return
        info = self.parse_map_lawyer(url)
        dao_lawyer.execute(sql, args=(info['educational_background'], info['pic_url'], info['gender'], info['practice_area'], license))
        logger.info(f'{license} 更新成功')


def main():
    limited = dao_lawyer.select("select count(*) from data_judicial_risk.lawyer where educational_background = ''")
    # limited = {'count(*)': 64815}
    acla = AclaLawyer()
    for i in range(0, limited['count(*)'], 1000):
        list_ = list(dao_lawyer.select_many(f"select * from data_judicial_risk.lawyer where educational_background = '' limit 1000 offset {i}"))
        bar = tqdm(total=len(list_))
        for item in list_:
            if item['educational_background']:
                continue
            # acla.add_edu(item['license'])
            future = executor.submit(acla.add_edu, item['license'])
            future.add_done_callback(lambda x: bar.update(1))


if __name__ == '__main__':
    logger.info('start')
    while True:
        try:
            main()
        except Exception as e:
            pass
        time.sleep(60)

    # schedule.every().day.at("07:00").do(main)
    # while True:
    #     schedule.run_pending()
    #     time.sleep(1)
