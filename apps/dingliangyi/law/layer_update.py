from resx.config import *
from resx.mysql_dao import MySQLDao
from biz_utils.id_center import id_center_query
from loguru import logger
from biz_utils.entity.company_graph import CompanyGraphDao
from biz_utils.entity.history_name import CompanyHistoryNameDao, GSXTHistoryNameDao

law_dao = MySQLDao(**CFG_MYSQL_ZX_RDS108, db_tb_name='data_judicial_risk.law_firm')
team_dao = MySQLDao(**CFG_MYSQL_ZX_RDS108, db_tb_name='data_judicial_risk.law_firm_team')
his_dao = MySQLDao(**CFG_MYSQL_ZX_RDS108, db_tb_name='data_judicial_risk.lawyer_history_law_firm')
lawyer_dao = MySQLDao(**CFG_MYSQL_ZX_RDS108, db_tb_name='data_judicial_risk.lawyer')
gid_dao = CompanyGraphDao()
com_his_dao = CompanyHistoryNameDao()
g_his_dao = GSXTHistoryNameDao()

lawyers = list(his_dao.select_many('select * from data_judicial_risk.lawyer_history_law_firm where law_firm_id = 0 and deleted = 0 limit 1000'))


def find(his_):
    # his_ = hiss[0]
    law_name = his_['name']
    # logger.info(f'正在处理 律所：{law_name}  律师：{lawyer_}')
    law = law_dao.get(lawFirmName=law_name)
    if not law:
        his_names = list(g_his_dao.get_many(history_name=law_name))
        if his_names:
            for his_name in his_names:
                cid = his_name.cid
                law = law_dao.get(company_id=cid)
                if law:
                    break
            if not law:
                logger.warning(f'未找到律所 {law_name} 律师：{lawyer_}')
                return
    if not law:
        gid_ = gid_dao.select(f"select * from prism.company_graph where graph_id = '{his_['graph_id']}' and deleted = 0")
        if gid_:
            cid = gid_['company_id']
            law = law_dao.get(company_id=cid)
            if not law:
                logger.warning(f'未找到律所 {law_name} 律师：{lawyer_}')
                return
        else:
            logger.warning(f'未找到律所 {law_name} 律师：{lawyer_}')
            return
    return law


for lawyer in lawyers:
    lawyer_id = lawyer['lawyer_id']
    lawyer_ = lawyer_dao.get(lawyer_id=lawyer_id)
    license = lawyer_['license']
    hiss = list(his_dao.select_many(f"select distinct name, graph_id from data_judicial_risk.law_firm_team where License = '{license}' and deleted <> 0"))
    if not hiss:
        logger.info('无历史')
        his_dao.execute('update data_judicial_risk.lawyer_history_law_firm set deleted = 1 where id = %s', (lawyer['id']))
        continue

    if len(hiss) == 1:
        law = find(hiss[0])
        if not law:
            continue

        # logger.info(f'正在处理 律所：{law["lawFirmName"]}  {law["creditCode"]} 律师：{lawyer_}')
        law_id = law['id']
        now = list(his_dao.select_many(f"select * from data_judicial_risk.law_firm_team where License = '{license}' and deleted = 0"))

        is_ = False
        for n in now:
            if n['name'] == law['lawFirmName']:
                logger.warning(f'历史与现在律所相同 {lawyer} {n}')
                his_dao.execute('update data_judicial_risk.lawyer_history_law_firm set deleted = 1 where id = %s', (lawyer['id']))
                is_ = True
        if is_:
            continue

        his_dao.execute('update data_judicial_risk.lawyer_history_law_firm set law_firm_id = %s where id = %s', (law_id, lawyer['id']))
    else:
        pass
        # print(lawyer)
        exists = list(
            his_dao.select_many(f"select * from data_judicial_risk.lawyer_history_law_firm where lawyer_id = '{lawyer['lawyer_id']}' and law_firm_id <> 0"))
        exists_names = []
        for e in exists:
            law_ = law_dao.get(id=e['law_firm_id'])
            exists_names.append(law_['lawFirmName'])

        n = 0
        c = 0
        for idx, i in enumerate(hiss):
            if n > 0:
                break

            law = find(hiss[idx])
            if not law:
                continue

            if i['name'] in exists_names:
                c += 1
                logger.info(f'历史律所已存在 {i["name"]} {exists_names}')
                continue
            his_dao.execute('update data_judicial_risk.lawyer_history_law_firm set law_firm_id = %s where id = %s', (law['id'], lawyer['id']))
            n += 1

        if c == len(hiss):
            logger.success('所有历史律所已存在')
            his_dao.execute('update data_judicial_risk.lawyer_history_law_firm set deleted = 1 where id = %s', (lawyer['id']))
