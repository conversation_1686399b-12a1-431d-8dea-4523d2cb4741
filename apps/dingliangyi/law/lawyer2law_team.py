from tqdm import tqdm
import schedule
import time

from resx.tools import BoundedExecutor
from resx.config import *
from resx.mysql_dao import MySQLDao

sql1 = "select count(*) from data_judicial_risk.law_firm_team where educational_background = '' and deleted = 0"
sql2 = "select count(*) from data_judicial_risk.law_firm_team where oss_path = '' and deleted = 0"

dao_lawyer = MySQLDao(db_tb_name='data_judicial_risk.lawyer', **CFG_MYSQL_ZX_RDS108)
dao_lawyer2 = MySQLDao(db_tb_name='data_judicial_risk.law_firm_team', **CFG_MYSQL_ZX_RDS108)

executor = BoundedExecutor(5)


def main1(id, license):
    pre = dao_lawyer.select('select * from data_judicial_risk.lawyer where license = %s', args=(license,))
    if not pre or not pre['educational_background']:
        return
    args = (pre['educational_background'], pre['oss_path'], pre['gender'], pre['practice_area'], id)
    dao_lawyer2.execute(f'update data_judicial_risk.law_firm_team set educational_background=%s,oss_path=%s,gender=%s,practice_area=%s where id=%s', args=args)


def rask1():
    limited = dao_lawyer2.select(sql1)
    for i in range(0, limited['count(*)'], 1000):
        list_ = list(
            dao_lawyer2.select_many(f"select * from data_judicial_risk.law_firm_team where educational_background = '' and deleted = 0 limit 1000 offset {i}"))
        bar = tqdm(total=len(list_))
        for item in list_:
            if item['educational_background']:
                continue
            # main1(item['id'], item['License'])
            future = executor.submit(main1, item['id'], item['License'])
            future.add_done_callback(lambda x: bar.update(1))


def main2(id, license):
    pre = dao_lawyer.select('select * from data_judicial_risk.lawyer where license = %s', args=(license,))
    if not pre or not pre['oss_path'] or pre['oss_path'] == '-':
        return
    args = (pre['oss_path'], id)
    dao_lawyer2.execute(f'update data_judicial_risk.law_firm_team set oss_path=%s where id=%s', args=args)


def rask2():
    limited = dao_lawyer2.select(sql2)
    for i in range(0, limited['count(*)'], 1000):
        list_ = list(dao_lawyer2.select_many(f"select * from data_judicial_risk.law_firm_team where oss_path = '' and deleted = 0 limit 1000 offset {i}"))
        bar = tqdm(total=len(list_))
        for item in list_:
            if item['oss_path']:
                continue
            # main2(item['id'], item['License'])
            future = executor.submit(main2, item['id'], item['License'])
            future.add_done_callback(lambda x: bar.update(1))


def main():
    rask1()
    rask2()


if __name__ == '__main__':
    main()

    schedule.every(3).days.at("07:00").do(main)
    while True:
        schedule.run_pending()
        time.sleep(1)
