from loguru import logger
import time
import json
import schedule
from tqdm import tqdm
import requests
from requests import Session
from requests.adapters import H<PERSON><PERSON>dapter
import random
import re
from hashlib import md5
import urllib3

from resx.mysql_dao import MySQLDao
from resx.config import *
from apps.dingliangyi.common2 import touci

urllib3.disable_warnings()
dao_law = MySQLDao(db_tb_name='data_judicial_risk.law_firm', **CFG_MYSQL_ZX_RDS108)
logger.add(sink=r'./logs/douci_{time:YYYY_MM_DD}.log', rotation='1 GB', encoding='utf8', level='INFO', enqueue=True)
headers = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json;charset=UTF-8',
    'Origin': 'http://www.12348.gov.cn',
    'Referer': 'http://www.12348.gov.cn/',
    # "Referer": "https://credit.acla.org.cn/credit/lawFirm?keyWords=313100003326104621&lawFirmType=none&lawyerNumber=none&partnerNumber=none&orgType=none&zone=none&_hasPunish=on&_hasHonor=on&picCaptchaVerification=&pageIndex=1&pageSize=10&sorter=",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
}


def get_new_session() -> Session:
    session = requests.session()
    session.proxies = {'http': 'http://10.99.138.95:30636', 'https': 'http://10.99.138.95:30636'}
    session.mount('http://', HTTPAdapter(max_retries=2))
    session.mount('https://', HTTPAdapter(max_retries=2))
    return session


def get_uuid():
    # crawlerCookie获取
    s = []
    hexDigits = "0123456789abcdef"
    for i in range(36):
        s.append(hexDigits[int(random.random() * 16)])
    s[14] = "4"
    s[19] = hexDigits[(int(re.sub('[a-zA-Z]', '0', s[19])) & 0x3) | 0x8]
    s[8] = s[13] = s[18] = s[23] = "-"
    uuid = "".join(s)
    return uuid


def get_number(v_key, guid, pageNum, pageSize):
    string = md5((v_key + guid + str(pageNum) + "." + pageSize).encode()).hexdigest()
    number = ord(string[10]) + ord(string[3]) + ord(string[1]) + ord(string[6]) + ord(string[8]) + ord(string[5])
    return number


def crawl_first(session: Session, xzqh='11', pageSize='10000'):
    link = "http://www.12348.gov.cn/lawerdeptlist/getlawerdeptlist"
    page = 1
    v_key = get_uuid()
    crawlerCookie = get_uuid()
    guid = get_uuid()
    data = {
        'pageSize': pageSize,
        'pageNum': page,
        'xzqh': xzqh,
        'yw': '',
        'pzslsj': 0,
        'nums': 0,
        'v_key': v_key,
        'guid': guid,
        'number': get_number(v_key, guid, page, str(pageSize)),
        'crawlerCookie': crawlerCookie,
    }
    session.cookies.set('crawlerCookie', crawlerCookie, domain='www.12348.gov.cn')
    for _ in range(5):
        try:
            res = session.post(link, json=data, headers=headers).json()
            return res['list']
        except:
            continue
    return []


def crawl_list(code) -> list:
    url = "https://credit.acla.org.cn/api/credit/MapDataService/list"
    data = {"pageSize": 4000, "pageIndex": 1, "code": code}
    for _ in range(5):
        try:
            session = get_new_session()
            res = session.post(url, json=data, headers=headers, verify=False).json()
            return res['items']
        except:
            continue
    return []


def main():
    code_dic = {'11': '北京', '12': '天津', '13': '河北', '14': '山西', '15': '内蒙古自治区', '21': '辽宁', '22': '吉林',
                '23': '黑龙江', '31': '上海', '32': '江苏', '33': '浙江', '34': '安徽', '35': '福建', '36': '江西', '37': '山东',
                '41': '河南', '42': '湖北', '43': '湖南', '44': '广东', '45': '广西壮族自治区', '46': '海南', '50': '重庆',
                '51': '四川', '52': '贵州', '53': '云南', '54': '西藏自治区', '61': '陕西', '62': '甘肃', '63': '青海',
                '64': '宁夏回族自治区', '65': '新疆维吾尔自治区', '66': '新疆生产建设兵团'}
    for k, v, in code_dic.items():
        list_ = crawl_first(get_new_session(), xzqh=k)
        for item in tqdm(list_, desc=k):
            name = item['lsswsmc']
            # if not dao_law.get(lawFirmName=name):
            logger.info(name)
            touci(name, 'law_firm_gj')

    province = [{'code': '110000', 'name': '北京市'}, {'code': '120000', 'name': '天津市'}, {'code': '130000', 'name': '河北省'},
                {'code': '140000', 'name': '山西省'}, {'code': '150000', 'name': '内蒙古自治区'}, {'code': '210000', 'name': '辽宁省'},
                {'code': '220000', 'name': '吉林省'}, {'code': '230000', 'name': '黑龙江省'}, {'code': '310000', 'name': '上海市'},
                {'code': '320000', 'name': '江苏省'}, {'code': '330000', 'name': '浙江省'}, {'code': '340000', 'name': '安徽省'},
                {'code': '350000', 'name': '福建省'}, {'code': '360000', 'name': '江西省'}, {'code': '370000', 'name': '山东省'},
                {'code': '410000', 'name': '河南省'}, {'code': '420000', 'name': '湖北省'}, {'code': '430000', 'name': '湖南省'},
                {'code': '440000', 'name': '广东省'}, {'code': '450000', 'name': '广西壮族自治区'}, {'code': '460000', 'name': '海南省'},
                {'code': '500000', 'name': '重庆市'}, {'code': '510000', 'name': '四川省'}, {'code': '520000', 'name': '贵州省'},
                {'code': '530000', 'name': '云南省'}, {'code': '540000', 'name': '西藏自治区'}, {'code': '610000', 'name': '陕西省'},
                {'code': '620000', 'name': '甘肃省'}, {'code': '630000', 'name': '青海省'}, {'code': '640000', 'name': '宁夏回族自治区'},
                {'code': '650000', 'name': '新疆维吾尔自治区'}]
    for i in province:
        list_ = crawl_list(i['code'])
        for item in tqdm(list_, desc=i['name']):
            name = item['name']
            # if not dao_law.get(lawFirmName=name):
            logger.info(name)
            touci(name, 'law_firm_gj')


if __name__ == '__main__':
    list_ = crawl_list('430000')
    print(list_)
    list_ = crawl_first(get_new_session(), xzqh='43')
    print(list_)

    # main()
    # schedule.every(3).day.at("07:00").do(main)
    # while True:
    #     schedule.run_pending()
    #     time.sleep(1)
