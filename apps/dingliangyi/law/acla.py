# -*- coding:utf-8 -*-
import random
import re
import time
import requests
from loguru import logger
import traceback
from requests.adapters import HTTPAdapter
import schedule
from tqdm import tqdm
import execjs
from urllib.parse import urlparse
import string
from fontTools.ttLib import TTFont
from io import BytesIO
from bs4 import BeautifulSoup
import os
from urllib.parse import quote
from requests import Session, Response
from typing import Union
from urllib3 import disable_warnings

parent_path = os.path.dirname(os.path.abspath(__file__))
disable_warnings()


class Acla:
    def __init__(self):
        self.url = 'https://credit.acla.org.cn/credit/lawFirm?picCaptchaVerification=true&keyWords='
        self.url_lawyer = 'https://credit.acla.org.cn/credit/lawyer?picCaptchaVerification=true&keyWords='
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
        }
        self.timeout = 5
        self.province = [{'code': '110000', 'name': '北京市'}, {'code': '120000', 'name': '天津市'}, {'code': '130000', 'name': '河北省'},
                         {'code': '140000', 'name': '山西省'}, {'code': '150000', 'name': '内蒙古自治区'}, {'code': '210000', 'name': '辽宁省'},
                         {'code': '220000', 'name': '吉林省'}, {'code': '230000', 'name': '黑龙江省'}, {'code': '310000', 'name': '上海市'},
                         {'code': '320000', 'name': '江苏省'}, {'code': '330000', 'name': '浙江省'}, {'code': '340000', 'name': '安徽省'},
                         {'code': '350000', 'name': '福建省'}, {'code': '360000', 'name': '江西省'}, {'code': '370000', 'name': '山东省'},
                         {'code': '410000', 'name': '河南省'}, {'code': '420000', 'name': '湖北省'}, {'code': '430000', 'name': '湖南省'},
                         {'code': '440000', 'name': '广东省'}, {'code': '450000', 'name': '广西壮族自治区'}, {'code': '460000', 'name': '海南省'},
                         {'code': '500000', 'name': '重庆市'}, {'code': '510000', 'name': '四川省'}, {'code': '520000', 'name': '贵州省'},
                         {'code': '530000', 'name': '云南省'}, {'code': '540000', 'name': '西藏自治区'}, {'code': '610000', 'name': '陕西省'},
                         {'code': '620000', 'name': '甘肃省'}, {'code': '630000', 'name': '青海省'}, {'code': '640000', 'name': '宁夏回族自治区'},
                         {'code': '650000', 'name': '新疆维吾尔自治区'}]
        self.font_list = ['王', '李', '张', '师', '律', '事', '所', '务', '刘', '陈', '杨', '文', '东', '华', '晓', '海', '林', '明', '黄', '赵', '周', '吴',
                          '伟', '江', '建', '丽', '军', '志', '徐', '孙', '国', '金', '平', '北', '红', '朱', '广', '云', '龙', '杰', '马', '玉', '高', '春',
                          '胡', '郭', '小', '强', '宇', '新', '艳', '辉', '永', '京', '峰', '敏', '南', '鹏', '山', '何', '苏', '郑', '涛', '燕', '俊', '婷',
                          '宁', '成', '勇', '玲', '飞', '天', '芳', '静', '梅', '佳', '慧', '波', '罗', '娟', '阳', '斌', '庆', '德', '雪', '超', '荣', '梁',
                          '安', '君', '霞', '亚', '西', '丹', '英', '洪', '宋', '谢', '光', '许', '刚', '韩', '立', '唐', '方', '萍', '河', '程', '宏', '亮',
                          '田', '曹', '冯', '福', '利', '瑞', '肖', '青', '川', '振', '娜', '清', '忠', '叶', '兴', '浩', '邓', '正', '董', '民', '生', '磊',
                          '武', '于', '曾', '丁', '彭', '旭', '潘', '莉', '学', '秀', '颖', '袁', '祥', '思', '杜', '凯', '沈', '蒋', '琳', '鑫', '锋', '上',
                          '兰', '中', '泽', '蔡', '余', '洁', '贵', '魏', '健', '松', '博', '夏', '任', '欣', '凤', '良', '吕', '卫', '雷', '智', '胜', '湖',
                          '一', '世', '子', '万', '嘉', '元', '晶', '崔', '姚', '宝', '倩', '梦', '莹', '星', '大', '姜', '四', '洋', '康', '石', '卢', '雨',
                          '彬', '汪', '市', '钟', '秋', '范', '孟', '兵', '美', '晨', '雅', '州', '陆', '吉', '剑', '家', '远', '维', '坤', '贾', '长', '谭',
                          '向', '琴', '媛', '浙', '楠', '达', '白', '力', '秦', '月', '昌', '彦', '毅', '付', '桂', '恒', '爱', '和', '义', '冰', '惠', '翔',
                          '冬', '群', '侯', '廖', '邹', '全', '薛', '盛', '黎', '琪', '章', '少', '雯', '乐', '继', '尹', '熊', '珍', '蒙', '邱', '源', '景',
                          '威', '淑', '贺', '朝', '凌', '璐', '怡', '雄', '辽', '琦', '锦', '鸿', '仁', '进', '常', '戴', '古', '树', '铭', '段', '顾', '津',
                          '宗', '珊', '培', '琼', '闫', '启', '顺', '钱', '凡', '信', '哲', '富', '重', '帅', '友', '卓', '耀', '科', '克', '银', '史', '陶',
                          '奇', '毛', '豪', '双', '诚', '蓉', '韦', '贤', '邵', '尚', '齐', '昊', '柳', '郝', '森', '法', '严', '然', '瑶', '徽', '丰', '连',
                          '欢', '栋', '传', '蕾', '芬', '权', '会', '尔', '迪', '岩', '岳', '睿', '婧', '苗', '书', '保', '泉', '开', '才', '龚', '炜', '扬',
                          '路', '茂', '先', '申', '黑', '政', '帆', '聪', '施', '晖', '虹', '鲁', '航', '孔', '菲', '乔', '汉', '悦', '发', '道', '甘', '汤',
                          '欧', '娇', '虎', '葛', '瑜', '合', '易', '喜', '敬', '艺', '花', '舒', '兆', '温', '牛', '露', '锐', '樊', '延', '润', '阿', '泰',
                          '跃', '彩', '翠', '薇', '玮', '诗', '邢', '来', '纪', '勤', '香', '萌', '如', '颜', '靖', '绍', '业', '代', '恩', '茜', '内', '茹',
                          '妮', '园', '陕', '艾', '莲', '素', '妍', '庄', '钰', '木', '疆', '轩', '运', '益', '圣', '莎', '同', '可', '辰', '希', '俞', '倪',
                          '鸣', '殷', '赖', '涵', '巍', '伦', '昕', '通', '提', '越', '升', '根', '佩', '傅', '治', '城', '灵', '晋', '翟', '承', '男', '霖',
                          '莫', '水', '冠', '士', '聂', '彤', '心', '楚', '依', '应', '有', '坚', '伍', '增', '巧', '洲', '腾', '斯', '冉', '震', '童', '仲',
                          '晴', '礼', '真', '若', '关', '耿', '旺', '曦', '菊', '邦', '其', '善', '怀', '潇', '为', '都', '之', '柏', '日', '覃', '柯', '时',
                          '衡', '占', '焦', '曼', '鼎', '朋', '璇', '庞', '曲', '湘', '奎', '炳', '婉', '左', '祝', '彪', '理', '原', '盈', '祖', '季', '铁',
                          '肃', '三', '奕', '沙', '骏', '逸', '风', '联', '捷', '芝', '包', '灿', '馨', '廷', '尧', '钦', '乾', '仕', '煜', '珠', '守', '行',
                          '焕', '格', '爽', '崇', '羽', '毕', '卿', '蕊', '韬', '竹', '孝', '加', '婕', '芸', '皓', '言', '笑', '滨', '昆', '昭', '雁', '谷',
                          '本', '梓', '显', '碧', '宪', '纯', '定', '芹', '自', '单', '菁', '珂', '仪', '辛', '盼', '蓝', '满', '瑾', '登', '迎', '硕', '宜',
                          '征', '裴', '钢', '甜', '姣', '太', '育', '霍', '圆', '容', '蔚', '紫', '柱', '姗', '昱', '众', '麦', '畅', '存', '游', '展', '伊',
                          '烨', '钧', '商', '禹', '普', '勋', '熙', '舟', '相', '臣', '琛', '弘', '晟', '桥', '修', '隆', '詹', '谦', '玥', '人', '司', '拉',
                          '喻', '柴', '岚', '标', '令', '管', '宾', '靳', '阮', '微', '赛', '涂', '杭', '米', '贞', '均', '鲍', '能', '深', '劲', '韵', '鹤',
                          '前', '厚', '淼', '驰', '钊', '锡', '百', '照', '汇', '公', '娅', '沛', '翁', '蓓', '晗', '枫', '麟', '知', '臻', '环', '骆', '裕',
                          '瀚', '瑛', '济', '滕', '年', '诺', '渊', '轶', '创', '闻', '斐', '庭', '祁', '解', '贝', '亭', '品', '基', '尤', '典', '寒', '姝',
                          '宽', '久', '买', '饶', '伯', '功', '堂', '意', '房', '印', '祺', '丛', '符', '名', '儒', '娴', '炎', '影', '党', '穆', '霄', '岭',
                          '项', '献', '喆', '九', '干', '卜', '蒲', '实', '攀', '牟', '懿', '汝', '猛', '经', '珺', '战', '望', '晔', '镇', '郁', '娥', '筱',
                          '桐', '冲', '绪', '作', '融', '苑', '映', '泓', '举', '池', '热', '晏', '乃', '榕', '亦', '宣', '果', '窦', '仙', '甫', '非', '寿',
                          '翰', '声', '宫', '侠', '瀛', '布', '缪', '冀', '初', '里', '卉', '图', '营', '啸', '娄', '屈', '费', '寅', '曙', '焱', '旗', '楼',
                          '姬', '官', '多', '褚', '铮', '夫', '孜', '泳', '岑', '峻', '虞', '潮', '边', '秉', '桑', '玺', '哈', '化', '祎', '婵', '策', '特',
                          '巴', '戈', '财', '车', '禄', '峥', '致', '领', '恺', '简', '从', '野', '地', '壮', '毓', '起', '咏', '戚', '渝', '媚', '朗', '竞',
                          '魁', '仇', '琨', '翼', '拓', '沁', '农', '召', '闵', '席', '以', '妹', '郎', '观', '珏', '圳', '臧', '舜', '溪', '佟', '迟', '誉',
                          '念', '旻', '幸', '栾', '邬', '允', '卞', '挺', '区', '乌', '瞿', '纬', '冷', '浪', '赫', '桦', '琰', '妙', '桃', '音', '娣', '吾',
                          '唯', '聚', '佑', '再', '澜', '阎', '宸', '尼', '予', '滢', '煌', '芮', '添', '革', '骁', '好', '杉', '岗', '赟', '萱', '昂', '港',
                          '伶', '鹰', '庚', '衍', '歌', '俐', '隋', '朴', '刁', '巨', '得', '苟', '荆', '井', '甲', '封', '枝', '璟', '绮', '必', '殿', '匡',
                          '语', '炯', '昀', '二', '颂', '优', '寇', '苹', '尊', '查', '临', '现', '含', '麒', '奥', '繁', '努', '侧', '干', '口', '佐', '遥',
                          '璞', '垚', '端', '敖', '杏', '鞠', '谈', '豫', '赞', '雍', '禾', '廉', '旋', '桢', '储', '霜', '首', '茵', '栗', '习', '韶', '漫',
                          '靓', '楷', '滔', '奚', '居', '纳', '浦', '甄', '在', '敦', '淳', '丘', '争', '鉴', '留', '五', '吐', '阁', '巫', '见', '闯', '慕',
                          '巩', '至', '键', '盟', '佘', '邝', '那', '度', '迅', '祯', '胥', '旦', '灏', '门', '芦', '效', '淮', '熠', '球', '纲', '步', '训',
                          '隽', '嵩', '放', '旸', '鸽', '萧', '淇', '澄', '俭', '烈', '玄', '妤', '慈', '选', '不', '湛', '班', '麻', '分', '铃', '镜', '屹',
                          '恬', '蕴', '蜀', '索', '洛', '火', '闽', '芷', '玛', '嵘', '墨', '骥', '库', '谋', '侃', '塔', '筠', '计', '述', '台', '呼', '霏',
                          '奉', '鹿', '罡', '稳', '笛', '鲜', '丙', '玫', '堃', '帮', '黔', '蔺', '棋', '涌', '烽', '研', '拥', '支', '沐', '劳', '卡', '社',
                          '岛', '练', '湛', '穗', '粤']
        with open(f'{parent_path}/js_script/refer__1711.js', 'r', encoding='utf-8') as f:
            self.js_compile_1711 = execjs.compile(f.read())
        with open(f'{parent_path}/js_script/key.js', 'r', encoding='utf-8') as f:
            self.js_code_key = f.read()
        with open(f'{parent_path}/js_script/crypto.js', 'r', encoding='utf-8') as f:
            self.js_compile_crypto = execjs.compile(f.read())

    def crawl_list(self, code) -> list:
        url = "https://credit.acla.org.cn/api/credit/MapDataService/list"
        data = {"pageSize": 4000, "pageIndex": 1, "code": code}
        res = self.request(self.get_new_session(), 'POST', url, json=data, tojson=True, name='list')
        return res['items']

    def crawl_detail(self, name):
        url = self.url + quote(name)
        html = self.add_cookies_params_request(url)
        soup = BeautifulSoup(html, 'lxml')
        url_encrypted = re.search("\('(.*?)',", soup.select_one('.search-result-list a').get('onclick')).group(1)
        url = self.decrypt_url(html, url_encrypted, 'lawFirm')
        html = self.add_cookies_params_request(url)
        return html

    def add_cookies_params_request(self, url) -> str:
        url = self.create_url(url)
        for i in range(10):
            session = self.get_new_session()
            html = self.request(session, 'GET', url, name='add_cookies_params-1')
            if '请进行验证' in html:
                logger.warning(f'触发阿里滑块风控-{i + 1}')
                continue
            self.update_cookie(session, html)
            html = self.request(session, 'GET', url, name='add_cookies_params-2')
            if '验证码' in html:
                logger.warning(f'cookie失效-{i + 1}')
                continue
            time.sleep(0.3)
            return html
        raise Exception('10次触发阿里滑块风控')

    def create_url(self, url):
        parsed_url = urlparse(url)
        url = self.js_compile_1711.call('get_url', url, parsed_url.path, '?' + parsed_url.query if parsed_url.query else '')
        # logger.info(url)
        return url

    def update_cookie(self, session, html):
        I = 'var I = [' + re.search(r'=\[(.*?)];\(function\(', html).group(1) + '];\n'
        js_compile = execjs.compile(I + self.js_code_key)
        key = js_compile.call('get_key')
        UM_UNDEFINED = self.js_compile_crypto.call('DES_Encrypt', self.generate_random_string(), key)
        session.cookies.set('UM_UNDEFINED', UM_UNDEFINED)

    def decrypt_url(self, html, url, url_type):
        I = 'var I = [' + re.search(r'=\[(.*?)];\(function\(', html).group(1) + '];\n'
        js_compile = execjs.compile(I + self.js_code_key)
        key = js_compile.call('get_key')
        url = f'https://credit.acla.org.cn/credit/{url_type}/' + self.js_compile_crypto.call('DES_Decrypt', url, key)
        # logger.info(url)
        return url

    def parse(self, html, selector, type_='lawFirm'):
        replace_success = False
        for _ in range(3):
            url_ttf = ''
            try:
                url_ttf = re.search(r'https://static.homolo.net/credit/prototype/static/font/(.*?)/font.ttf', html)
                if not url_ttf:
                    raise Exception('字体url为空')
                url_ttf = url_ttf.group(1)
                ttf_path = f'{parent_path}/cache_font/{url_ttf}.ttf'
                if os.path.exists(ttf_path):
                    with open(ttf_path, 'rb') as f:
                        map_dict = self.map_font(f.read())
                else:
                    url = f'https://static.homolo.net/credit/prototype/static/font/{url_ttf}/font.ttf'
                    response = self.request(requests.session(), 'GET', url, toRaw=True, name=f'font-{url}')
                    self.save_font(response.content, url_ttf.split('/')[-2])
                    map_dict = self.map_font(response.content)
                for key, value in map_dict.items():
                    html = html.replace(key, str(value))
                replace_success = True
                break
            except Exception as e:
                logger.error(f'error: {e} {_ + 1} {url_ttf}')

        if not replace_success:
            raise Exception('字体替换失败')

        soup = BeautifulSoup(html, 'lxml')
        lis = soup.select(selector)
        info = {}
        for li in lis:
            list_: list[str] = list(li.stripped_strings)
            info[list_[0].replace('\u2003', '')] = ''.join(list_[1:])
        name = ''.join(list(soup.select_one('div.person-name-inner.pull-left').stripped_strings))
        code = soup.select_one('div.person-desc-inner.pull-left').text
        info['name'] = name
        info['code'] = code

        if type_ == 'lawyer':
            image_url = re.search(r"avatar-photo/(.*?)\?thumb=150x200", html)
            if image_url:
                pic_url = 'https://credit.acla.org.cn/avatar-photo/' + image_url.group(1) + '?thumb=150x200'
                info['pic_url'] = pic_url

        urls = []
        lawyer_names = []
        lawyers = {}
        if type_ == 'lawFirm':
            lis = soup.select('div.card-content-main li.common-name-item.pull-left a')
            spans = soup.select('div.card-content-main li.common-name-item.pull-left span.inner-name')
            for span in spans:
                name = ''.join(span.stripped_strings)
                lawyer_names.append(name)
            for li in tqdm(lis, desc='lawyer-decrypt_url'):
                url_encrypted = re.search("\('(.*?)',", li.get('onclick')).group(1)
                urls.append(self.decrypt_url(html, url_encrypted, 'lawyer'))
            lawyers = dict(zip(lawyer_names, urls))
            logger.info(f'{name}-{code}-{lawyers}')
        logger.info(f'{name}-{code}-{info}')
        return info, lawyers

    def parse_map_lawyer(self, url):
        html = self.add_cookies_params_request(url)
        lawyer, _ = self.parse(html, 'div.detail-content.clearfix li', 'lawyer')
        lawyer: dict
        lawyer_info = {
            'name': lawyer.get('执业机构：'),
            'human_name': lawyer.get('name'),
            'license': lawyer.get('code'),
            'pic_url': lawyer.get('pic_url'),
            'educational_background': lawyer.get('学历学位：'),
            'gender': lawyer.get('性别：'),
            'practice_area': lawyer.get('执业类别：')
        }
        return lawyer_info

    @staticmethod
    def generate_random_string(length=32):
        return ''.join(random.choices(string.hexdigits.lower(), k=length))

    @staticmethod
    def save_font(content: bytes, name):
        with open(f'{parent_path}/cache_font/{name}.ttf', 'wb') as f:
            f.write(content)

    def map_font(self, content: bytes):
        font = TTFont(BytesIO(content))
        code_list = font.getGlyphOrder()[3:]
        code_list = [i.replace('uni', '&#x') + ';' for i in code_list]
        if len(code_list) == 1083:
            return dict(zip(code_list, self.font_list))
        if len(code_list) == 1093:
            number = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
            number.extend(self.font_list)
            return dict(zip(code_list, number))

    def request(self, session: Session, method: str, url: str, params: dict = None, data: Union[dict, str] = None, json: dict = None,
                path: str = None, name: str = '', tojson=False, toRaw=False, isDetail=False) -> Union[dict, str, Response, None]:
        for i in range(20):
            response = None
            try:
                a = time.time()
                request_params = {'method': method, 'url': url, 'data': data, 'headers': self.headers,
                                  'verify': False, 'timeout': self.timeout, 'params': params, 'json': json}
                response = session.request(**request_params)

                if response.status_code != 200:
                    logger.warning(f'{name}-{i + 1} --> {response.status_code}')
                    del session.cookies['proxyBase']
                    continue
                # logger.success(f'{name} --> {response.status_code} --> time: {time.time() - a}')

                if toRaw:
                    return response
                if isDetail:
                    a = re.sub(r'[\n\r\t]', '', response.text)
                    logger.info(f'{name} --> {a}')
                if tojson:
                    return response.json()
                return response.text
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'{name} --> continue{i} exception: {e}')
                del session.cookies['proxyBase']
            except Exception as e:
                status = response.status_code if response else "空"
                text = response.text if response else "空"
                logger.warning(f'{name} --> continue{i} 状态码：{status} res: {text} exception: {e}')
                del session.cookies['proxyBase']

    @staticmethod
    def get_new_session() -> Session:
        session = requests.session()
        session.proxies = {'http': 'http://10.99.138.95:30636', 'https': 'http://10.99.138.95:30636'}
        session.mount('http://', HTTPAdapter(max_retries=2))
        session.mount('https://', HTTPAdapter(max_retries=2))
        return session


if __name__ == '__main__':
    pass
