#!/usr/bin/env python3
"""
socks_tunnel.py (updated)
------------------------

* Dynamic SOCKS5 proxy via **asyncssh** (supports -D)
* Accepts in‑memory PEM key (like your `CFG_TUNNEL['ssh_pkey']`)
* Auto‑reconnect, keep‑alive, clean shutdown
"""

import asyncio
import logging
import signal
import sys
import textwrap
from pathlib import Path
from typing import Optional, Union

import asyncssh

# ──────────────────────────────────────────────────────────────
# Configuration – fill in with your CFG_TUNNEL values
# ──────────────────────────────────────────────────────────────
CFG_TUNNEL = {
    "ssh_address_or_host": ("*************", 22),
    "ssh_username": "work",
    # 直接把 PEM 字符串贴在这里；换行保持原样
    "ssh_pkey": textwrap.dedent(
        """******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
    ),
}

# Local SOCKS5 listen port
LOCAL_PORT = 1080
KEEPALIVE = 15  # seconds

# ──────────────────────────────────────────────────────────────
# Logging
# ──────────────────────────────────────────────────────────────
LOG_FILE = Path.home() / ".local/var/log/socks_tunnel.log"
LOG_FILE.parent.mkdir(parents=True, exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler(LOG_FILE, encoding="utf-8"),
        logging.StreamHandler(sys.stdout),
    ],
)


# ──────────────────────────────────────────────────────────────
# Tunnel class
# ──────────────────────────────────────────────────────────────
class SocksTunnel:
    def __init__(
            self,
            host: str,
            port: int,
            username: str,
            pkey_pem: Union[str, Path],
            local_port: int = 1080,
            keepalive: int = 15,
    ) -> None:
        self.host = host
        self.port = port
        self.username = username
        self.pkey_pem = pkey_pem
        self.local_port = local_port
        self.keepalive = keepalive
        self._conn: Optional[asyncssh.SSHClientConnection] = None
        self._listener: Optional[asyncssh.SOCKSListener] = None

    async def _connect(self):
        # Accept both raw PEM string/path **or** Paramiko key objects
        import paramiko, io
        if isinstance(self.pkey_pem, paramiko.PKey):
            buf = io.StringIO()
            self.pkey_pem.write_private_key(buf)
            pem_data = buf.getvalue()
        else:
            pem_data = self.pkey_pem  # str/Path
        key_obj = asyncssh.import_private_key(pem_data)
        self._conn = await asyncssh.connect(
            host=self.host,
            port=self.port,
            username=self.username,
            client_keys=[key_obj],
            keepalive_interval=self.keepalive,
            keepalive_count_max=3,
            known_hosts=None,  # Disable host key checking
        )
        self._listener = await self._conn.forward_socks("127.0.0.1", self.local_port)
        logging.info(
            "SOCKS5 ready ➜ socks5://127.0.0.1:%d (tunnel %s@%s:%d)",
            self._listener.get_port(),
            self.username,
            self.host,
            self.port,
        )

    async def _serve(self):
        try:
            await self._listener.wait_closed()
        finally:
            await self._cleanup()

    async def _cleanup(self):
        if self._listener:
            self._listener.close()
            self._listener = None
        if self._conn:
            self._conn.close()
            await self._conn.wait_closed()
            self._conn = None

    async def serve_forever(self):
        backoff = 5
        while True:
            try:
                await self._connect()
                await self._serve()
                backoff = 5  # reset after clean close
            except (asyncio.CancelledError, KeyboardInterrupt):
                raise
            except Exception as exc:
                logging.warning("Tunnel error: %s – retrying in %ds", exc, backoff)
                await asyncio.sleep(backoff)
                backoff = min(backoff * 2, 120)


# ──────────────────────────────────────────────────────────────
# Entrypoint
# ──────────────────────────────────────────────────────────────
async def amain():
    tunnel = SocksTunnel(
        host=CFG_TUNNEL["ssh_address_or_host"][0],
        port=CFG_TUNNEL["ssh_address_or_host"][1],
        username=CFG_TUNNEL["ssh_username"],
        pkey_pem=CFG_TUNNEL["ssh_pkey"],
        local_port=LOCAL_PORT,
        keepalive=KEEPALIVE,
    )
    await tunnel.serve_forever()


def main():
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    def _shutdown(_sig, _frm):
        for task in asyncio.all_tasks(loop):
            task.cancel()

    signal.signal(signal.SIGINT, _shutdown)
    signal.signal(signal.SIGTERM, _shutdown)

    try:
        loop.run_until_complete(amain())
    except KeyboardInterrupt:
        pass
    finally:
        loop.run_until_complete(loop.shutdown_asyncgens())
        loop.close()
        logging.info("Bye!")


if __name__ == "__main__":
    main()
