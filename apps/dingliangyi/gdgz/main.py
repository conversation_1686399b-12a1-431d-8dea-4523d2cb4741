import time

from resx.redis_types import Redis
from resx.config import *
from resx.obs_client import OBSClient
import requests
from loguru import logger
from PIL import Image
from io import BytesIO
from urllib3 import disable_warnings
from tqdm import tqdm
from resx.tools import BoundedExecutor

disable_warnings()
redis = Redis(**CFG_REDIS_GS, db=1)
obs = OBSClient(bucket_name='jindi-oss-gsxt')
executor = BoundedExecutor(max_workers=2)

headers = {
    "User-Agent": 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) '
                  'Chrome/114.0.0.0 Safari/537.36'
}

logger.add(sink='./temp.log')


def get_pic(keyword: str):
    url = "https://air.scjgj.gz.gov.cn/cancelEasy/commonquery/getBusinessLicenceImg"
    params = {"uniscid": keyword}
    for _ in range(10):
        try:
            proxies = {'http': 'http://10.99.138.95:30636', 'https': 'http://10.99.138.95:30636'}
            a = time.time()
            response = requests.get(url, headers=headers, params=params, timeout=20, verify=False, proxies=proxies)
            if response.status_code != 200:
                continue
            if response.content == b'':
                redis.sadd('gdgz_no_licence', keyword)
                return
            logger.info(f'{keyword} : {response.status_code} {time.time() - a}')
            image = Image.open(BytesIO(response.content))
            image_data = BytesIO()
            image.save(image_data, format='PNG')
            obs.put(f'gdgz_licence/{keyword}.png', image_data.getvalue())
        except Exception as e:
            logger.error(f'请求失败: {_ + 1} {e}')
            continue
    redis.sadd('gdgz_fail_licence', keyword)


bar = tqdm(total=redis.zcard('gz_e_licence'), desc='获取营业执照图片')
for i in range(redis.zcard('gz_e_licence')):
    key = redis.zpopmin('gz_e_licence')
    logger.info(key)
    future = executor.submit(get_pic, key[0][0])
    future.add_done_callback(lambda x: bar.update(1))

# for i in ['91440106MA59CAP224', '91440101MA9WGCPQ40', '91440184591543372B', '92440101MA5A8YDE96', '92440101L43718867A', '91440101MA59EGAB8Q',
#           '92440101MA5A16AW2A', '91440101MA59F9T3XH', '92440101MA5A7AQ57W', '92440101MA5A7NUL9A', '92440101MA59J3006C', '91440101340132675X']:
#     executor.submit(get_pic, i)
