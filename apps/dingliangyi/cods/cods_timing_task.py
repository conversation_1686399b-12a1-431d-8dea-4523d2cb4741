import schedule
from resx.config import *
from resx.mysql_dao import MySQLDao
import time


def main():
    dao = MySQLDao(db_tb_name='prism1.cods_account', **CFG_MYSQL_ZX_RDS113)
    dao.execute("update  prism1.cods_account set search = 0, detail = 0, is_login = 0 where state = 0")


if __name__ == '__main__':
    schedule.every().day.at("00:00").do(main)
    while True:
        schedule.run_pending()
        time.sleep(1)
