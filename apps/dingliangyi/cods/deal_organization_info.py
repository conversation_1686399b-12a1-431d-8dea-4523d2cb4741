import json

from clients.kafka_client import KafkaConsumerClient
# from PIL import Image
# from io import BytesIO
# from requests import Session, Response
from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao
from dao.organization_info import OrganizationInfo, OrganizationInfoDao
from datetime import datetime, date, timedelta
from gslib.id_center import id_center_query

organization_info_dao = OrganizationInfoDao()


def dump_func():
    topic_name = 'qx_monitor_v1'
    consumer = KafkaConsumerClient(kafka_topic=topic_name, group_id='tyc_saq_deal_organization_info',
                                   **ConstantProps.PROPS_GS_KAFKA_ONLINE)
    for sid, s in enumerate(consumer.read()):
        try:
            data = json.loads(s)
            database = data['database']
            database: str
            table = data['table']
            table: str
            type = data['type']
            if database.startswith('db_enterprise_') and table.startswith('t_enterprise_') and (
                    type == 'UPDATE' or type == 'INSERT'):
                data_detail = data['data'][0]
                if 'credit_no' in data_detail and data_detail['credit_no']:
                    credit_no = data_detail['credit_no']
                    credit_no: str
                    if credit_no.startswith('11') and len(credit_no) == 18:
                        print('获取到的数据:' + s)
                        before_data = organization_info_dao.get_one_by_credit_code(credit_no)
                        # before_data:OrganizationInfo
                        # 更新
                        if before_data and before_data['id']:
                            row_update_time = data_detail['row_update_time']
                            timestamp = datetime.strptime(row_update_time, '%Y-%m-%d %H:%M:%S').timestamp()
                            us_time = datetime.strptime(str(before_data['update_time']), '%Y-%m-%d %H:%M:%S').timestamp()
                            if timestamp > us_time:
                                org_name = before_data['org_name']
                                if data_detail['name'] and data_detail['name'] != '':
                                    org_name = data_detail['name']
                                registration_authority = before_data['registration_authority']
                                if data_detail['belong_org'] and data_detail['belong_org'] != '':
                                    registration_authority = data_detail['belong_org']
                                legal_person = before_data['legal_person']
                                legal_person_id = before_data['legal_person_id']
                                if data_detail['oper_name'] and data_detail['oper_name'] != '':
                                    oper_name = data_detail['oper_name']
                                    if not legal_person or legal_person != oper_name:
                                        print('========' + s)
                                        id_center_result = id_center_query(oper_name)
                                        legal_person = oper_name
                                        entity_type, legal_person_id = id_center_result
                                registration_date = before_data['registration_date']
                                if data_detail['start_date'] and data_detail['start_date'] != '' and data_detail['start_date'] != '0000-00-00':
                                    registration_date = data_detail['start_date']
                                if not registration_date:
                                    registration_date = '0000-00-00'
                                expiry_date = before_data['expiry_date']
                                if (data_detail['term_start'] and data_detail['term_start'] != '') or (data_detail['term_end'] and data_detail['term_end'] != ''):
                                    expiry_date = data_detail['term_start'] + '至' + data_detail['term_end']
                                registered_capital = before_data['registered_capital']
                                if data_detail['regist_capi'] and data_detail['regist_capi'] != '':
                                    registered_capital = data_detail['regist_capi']
                                reg_status = before_data['reg_status']
                                if data_detail['status'] and data_detail['status'] != '':
                                    reg_status = data_detail['status']
                                # address = before_data.address
                                # if data_detail['']
                                business_scope = before_data['business_scope']
                                if data_detail['scope'] and data_detail['scope'] != '':
                                    business_scope = data_detail['scope']
                                org_source = 'qx_monitor_v1'
                                organization_info_dao.update_by_id(before_data['id'], org_name, registration_authority, legal_person, legal_person_id, registration_date, expiry_date, registered_capital, reg_status, business_scope, org_source)
                        else:
                            org_name = ''
                            if data_detail['name'] and data_detail['name'] != '':
                                org_name = data_detail['name']
                            registration_authority = ''
                            if data_detail['belong_org'] and data_detail['belong_org'] != '':
                                registration_authority = data_detail['belong_org']
                            legal_person = ''
                            legal_person_id = 0
                            if data_detail['oper_name'] and data_detail['oper_name'] != '':
                                oper_name = data_detail['oper_name']
                                id_center_result = id_center_query(oper_name)
                                entity_type, legal_person_id = id_center_result
                            registration_date = '0000-00-00'
                            if data_detail['start_date'] and data_detail['start_date'] != '':
                                registration_date = data_detail['start_date']
                            expiry_date = ''
                            if (data_detail['term_start'] and data_detail['term_start'] != '') or (
                                    data_detail['term_end'] and data_detail['term_end'] != ''):
                                expiry_date = data_detail['term_start'] + '至' + data_detail['term_end']
                            registered_capital = ''
                            if data_detail['regist_capi'] and data_detail['regist_capi'] != '':
                                registered_capital = data_detail['regist_capi']
                            reg_status = ''
                            if data_detail['status'] and data_detail['status'] != '':
                                reg_status = data_detail['status']
                            # address = before_data.address
                            # if data_detail['']
                            business_scope = ''
                            if data_detail['scope'] and data_detail['scope'] != '':
                                business_scope = data_detail['scope']
                            org_source = 'qx_monitor_v1'
                            # 新增
                            organization_info_dao.insert_by(credit_no,org_name, registration_authority,
                                                               legal_person, legal_person_id, registration_date,
                                                               expiry_date, registered_capital, reg_status,
                                                               business_scope, org_source)

        except Exception as e:
            print('执行异常:' + s)
            print(e)
            continue
    consumer.close()


if __name__ == '__main__':
    dump_func()
