import requests
from loguru import logger
from tqdm import tqdm

from dao.cods.cods2 import CodsDao
from apps.octopus.utils.entry_manager import EntryManager

logger.add(sink=r'./logs/shanghai_11_13_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8', enqueue=True)

entry_manager = EntryManager()
headers = {
    "Origin": "https://www.sydjsh.cn",
    "Referer": "https://www.sydjsh.cn/",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
}
cods_dao = CodsDao()
keywords = ['上海', '黄浦', '徐汇', '长宁', '静安', '普陀', '虹口', '杨浦', '闵行', '宝山', '嘉定', '浦东新', '金山', '松江', '青浦', '奉贤', '崇明']


def dispatch(entry_word, inst_name):
    return entry_manager.inst_immediate(
        entry_name='credit',
        entry_word=entry_word,
        inst_name=inst_name,
        reason='shanghai_11_13',
        ignore_latest_search_empty=False
    )


def process():
    for j in range(1, 100):
        url = "https://www.sydjsh.cn/shbb/website/searchForJgqt"
        data = {"page": {"pageSize": 500, "pageIndex": j}, "type": "", "keyword": "十字会", "enCodeNum": ""}
        response = requests.post(url, headers=headers, json=data)
        codeNum = response.json()['codeNum']
        data['enCodeNum'] = codeNum
        response = requests.post(url, headers=headers, json=data)
        print(response.json()['jgqtList']['data'])
        if response.json()['jgqtList']['data']:
            yield response.json()['jgqtList']['data']
        else:
            return []


for ii in process():
    for i in tqdm(ii):
        us_credit_code = i['xydm']
        info = cods_dao.get(value=us_credit_code, field='unified_social_credit_code')
        if not info:
            pass_ = False
            a = dispatch(us_credit_code, 'cods')
            logger.info(f'cods: {us_credit_code} {a}')
