import urllib3
import re
from fontTools.ttLib import TTFont
import string
from io import BytesIO
from bs4 import BeautifulSoup
from loguru import logger

from apps.dingliangyi.cods.map_dict import map_dict2
from clients.mysql_client import MySQLClient
from clients.redis._redis import Redis

urllib3.disable_warnings()

logger.add(sink=r'./logs/holder_empty_detail_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8', enqueue=True)


class CodsCrawler:

    def __init__(self, keyword):
        self.map_dict = {}

    def main(self):
        html = ''
        url = re.search(r"url\('/css/woff/(\d+).woff2'\)", html).group(1)
        url = f"https://ss.cods.org.cn/css/woff/{url}.woff2"
        response = self.request('GET', url, name='woff2', toraw=True)
        font = TTFont(BytesIO(response))
        list_data = self.parse(html, font)
        if list_data.get(self.keyword):
            return list_data.get(self.keyword)

    def parse(self, html, font):
        digits_and_uppercase = string.digits + string.ascii_uppercase
        map_list = font.getBestCmap()
        for index, (key, value) in enumerate(list(map_list.items())[36:]):
            self.map_dict[self.hex_to_char(hex(key))] = digits_and_uppercase[index]

        soup = BeautifulSoup(html, 'lxml')
        divs = soup.find_all('div', class_='each has-img')
        data = {}
        for div in divs:
            h3 = div.find_all('h3')[0]
            us_credit_code = div.find_all('p', string=lambda x: x and len(x) == 18)[0]
            history = div.select('div.alert p')
            old_name = ''.join(history[0].stripped_strings)[5:] if history else ''
            name = self.map_str(''.join(h3.stripped_strings), 2)
            data[name] = self.map_str(us_credit_code.text)
            data[old_name] = self.map_str(us_credit_code.text)
        return data

    @staticmethod
    def hex_to_char(hex_string):
        hex_value = int(hex_string, 16)
        character = chr(hex_value)
        return character

    def map_str(self, s, type_=1):
        if type_ == 1:
            return ''.join([self.map_dict.get(i, '') for i in s])
        return ''.join([map_dict2.get(i, i) for i in s])


def main():
    redis_client = Redis(host='redis-2b0f46e5-545a-4c21-a4a4-053daff1ee7b.cn-north-4.dcs.myhuaweicloud.com', password="rdsnhgot0akp17Xq", db=0)
    mysql_client = MySQLClient(**{
        'host': '0035c980965348f3b3ace8e7d92d2d0bin01.internal.cn-north-4.mysql.rds.myhuaweicloud.com',
        'user': 'jdtest_d_gong_ddl',
        'password': 'CoIfwlop8nnNJzAAOA'
    })
    sql = "select * from prism.holder_empty_detail where date(update_time) = date(now())"
    holder_empties = list(mysql_client.select_many(sql))

    for holder_empty in holder_empties:
        keyword = holder_empty['holder_name']
        if re.search(r'(公司|有限合伙|撤销|虚假责任人|（董事）|HK|香港|[A-Z\s]+（[\u4e00-\u9fa5]{2,3}）)', keyword) or len(keyword) <= 5 or len(keyword) >= 50:
            logger.info(f'pass-->{keyword}')
            continue

        cods_crawler = CodsCrawler(keyword)
        try:
            us_credit_code = cods_crawler.main()
        except Exception as e:
            holder_empties.append(holder_empty)
            continue

        if not us_credit_code:
            continue

        if re.search(r'^(51|52|53)', us_credit_code):
            dispatch(us_credit_code, 'china_npo')
        if re.search(r'^(53)', us_credit_code):
            dispatch(us_credit_code, 'foundation')
        if re.search(r'^(11|13|19|32|33|34|35|39|41|49|54|55|59|61|62|69|71|72|79|G1|N1|N2|N3|N9|Y1)', us_credit_code):
            dispatch(us_credit_code, 'cods')
        if re.search(r'^(80|81|89)', us_credit_code):
            dispatch(us_credit_code, 'acftu')
        if re.search(r'^(12)', us_credit_code):
            redis_client.lpush('gov_unit2', us_credit_code)


if __name__ == '__main__':
    pass
