import requests
import asyncio
import aiohttp
import sys

if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

url = "https://mp.weixin.qq.com/cgi-bin/newscanproductinfo"
params = {
    "action": "GetPage",
    "barcode_type": "EAN13",
    "barcode": "6974744860197"
}
proxies = {
    'http': 'http://10.99.138.95:30636',
    'https': 'http://10.99.138.95:30636'
}

# response = requests.get(url, params=params, proxies=proxies, verify=False)
# print(response.json())
headers = {
    # "Origin": "https://www.gds.org.cn",
    # "Referer": "https://www.gds.org.cn/",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                  "Chrome/119.0.0.0 Safari/537.36",
}


async def mian():
    connector = aiohttp.TCPConnector(ssl=False)
    timeout = aiohttp.ClientTimeout(total=3)
    proxy = proxies['http']
    async with aiohttp.ClientSession(connector=connector) as session:
        async with session.get(url, params=params, proxy=proxy, timeout=timeout, headers=headers) as response:
            print(await response.json())


if __name__ == '__main__':
    asyncio.run(mian())
