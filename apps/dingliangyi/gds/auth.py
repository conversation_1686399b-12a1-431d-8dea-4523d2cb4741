import requests

headers = {'Authorization': 'Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 'Origin': 'https://www.gds.org.cn', 'Referer': 'https://www.gds.org.cn/', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36'}
url = "https://bff.gds.org.cn/gds/carding-api/Cards/GetUserIsAuth"
response = requests.get(url, headers=headers)

print(response.text)
print(response)
