import requests
from loguru import logger
from urllib.parse import quote
import time
from datetime import datetime, timedelta
import pytz
import urllib3

urllib3.disable_warnings()

headers = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    # "$Cookie": "ASP.NET_SessionId=gwnpyiu40ebheqx2h5ebuwuy; accessToken_v4=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; refreshToken_v4=0FB836CC4831C7C4D3DE231CF55AD6A5FCDD6996D89D72F8DABE39DF86397D31; tokenExpire_v4=Fri%20Aug%2002%202024%2013:22:38%20GMT+0800%20(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4); accessToken=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; accessToken=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; userInfo={\"UserName\":null,\"BrandOwnerId\":0,\"BrandOwnerName\":null,\"GcpCode\":null,\"UserCardNo\":\"\",\"IsPaid\":false,\"CompanyNameEN\":null,\"CompanyAddressCN\":null,\"Contact\":null,\"ContactTelNo\":null,\"GcpLicenseHolderType\":null,\"LegalRepresentative\":null,\"UnifiedSocialCreditCode\":null}; userInfo={\"UserName\":null,\"BrandOwnerId\":0,\"BrandOwnerName\":null,\"GcpCode\":null,\"UserCardNo\":\"\",\"IsPaid\":false,\"CompanyNameEN\":null,\"CompanyAddressCN\":null,\"Contact\":null,\"ContactTelNo\":null,\"GcpLicenseHolderType\":null,\"LegalRepresentative\":null,\"UnifiedSocialCreditCode\":null}; userRole_v4=Mine; userInfo_v4={%22UserName%22:%22shu123213%22%2C%22Email%22:null%2C%22Phone%22:%2215616431767%22%2C%22CardNo%22:%22%22}; firmInfo_v4={%22FirmId%22:-100%2C%22FirmName%22:null%2C%22Address%22:null%2C%22BranchCode%22:-100%2C%22BranchName%22:null%2C%22IsLogout%22:0%2C%22IsSystemMember%22:false%2C%22IsLateForLicense%22:false%2C%22GLN%22:null}; expires_in_v4=5849; refreshtime_v4=Fri%20Aug%2002%202024%2013:42:38%20GMT+0800%20(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)",
    "Pragma": "no-cache",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "same-origin",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "sec-ch-ua": "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}


def index(tokens, user_info, expires_at):
    expires_in = expires_at - int(time.time())
    tokenExpire_v4, refreshtime_v4 = get_now_date(expires_in)

    url = "https://www.gds.org.cn/"
    res = requests.get(url, headers=headers, verify=False, cookies={
        "accessToken": tokens['accessToken'],
        "accessToken_v4": tokens['accessToken'],
        'expires_in_v4': str(expires_in),
        "firmInfo_v4": '{%22FirmId%22:-100%2C%22FirmName%22:null%2C%22Address%22:null%2C%22BranchCode%22:-100%2C%22BranchName%22:null%2C%22IsLogout%22:0%2C'
                       '%22IsSystemMember%22:false%2C%22IsLateForLicense%22:false%2C%22GLN%22:null}',
        "refreshToken_v4": tokens['refresh_token'],
        'refreshtime_v4': quote(refreshtime_v4),
        'tokenExpire_v4': quote(tokenExpire_v4),
        'userInfo': user_info.encode('utf-8').decode('latin-1'),
        'userInfo_v4': quote(user_info),
        'userRole_v4': 'Mine'
    })
    logger.info(f'index: {res.status_code}')


def get_now_date(expires_in):
    dt = datetime.now()
    dt1 = dt + timedelta(seconds=expires_in)
    dt2 = dt + timedelta(minutes=20)
    china_tz = pytz.timezone('Asia/Shanghai')
    dt1 = china_tz.localize(dt1)
    dt2 = china_tz.localize(dt2)
    date_str1 = dt1.strftime('%a %b %d %Y %H:%M:%S GMT%z (%Z)')
    date_str2 = dt2.strftime('%a %b %d %Y %H:%M:%S GMT%z (%Z)')

    return date_str1.replace('(CST)', '(中国标准时间)'), date_str2.replace('(CST)', '(中国标准时间)')


def main():
    a = {
        'accessToken': "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "refresh_token": "E8D40960EEC598A1E03C51ABFE671E2BE4FDCB37BB6FA1151BC92484CDB48F1F"
    }
    b = '{"UserName":null,"BrandOwnerId":0,"BrandOwnerName":null,"GcpCode":null,"UserCardNo":"暂无信息","IsPaid":false,"CompanyNameEN":null,"CompanyAddressCN":null,"Contact":null,"ContactTelNo":null,"GcpLicenseHolderType":null,"LegalRepresentative":null,"UnifiedSocialCreditCode":null}'
    expires_at = **********
    index(a, b, expires_at)


if __name__ == '__main__':
    main()
