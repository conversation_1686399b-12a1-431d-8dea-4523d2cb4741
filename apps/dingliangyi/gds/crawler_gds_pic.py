import time

from dao.gds import ProductDao, ProductEntity
import requests
from loguru import logger
from clients.obs_client import OBSClient
import urllib3
from libs.concurrent import BoundedExecutor
from concurrent.futures import Future
import asyncio
import aiohttp
import tempfile

urllib3.disable_warnings()
logger.add(sink=r'./logs/gds_pic_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8', enqueue=True)

headers = {
    "Referer": "https://www.gds.org.cn/",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 "
                  "Safari/537.36 Edg/120.0.0.0"
}

obs = OBSClient(bucket_name='jindi-oss-gsxt')
dao = ProductDao(batch_size=200)
executor = BoundedExecutor(max_workers=200)


def get_pic(url, barcode, idx):
    for _ in range(5):
        try:
            res = requests.get(url, headers=headers, verify=False, proxies={
                'http': 'http://10.99.138.95:30636',
                'https': 'http://10.99.138.95:30636'
            }, timeout=15)
            if res.status_code != 200:
                raise Exception('pic failed')
            path = f'gds_img/{barcode}/product_{idx}.jpg'
            obs.put(path, res.content)
            return path

        except Exception as e:
            logger.warning(f'继续')
            continue

    try:
        res = requests.get(url, headers=headers, verify=False, timeout=3)
        if res.status_code != 200:
            raise Exception('pic failed')
        path = f'gds_img/{barcode}/product_{idx}.jpg'
        obs.put(path, res.content)
        logger.warning('hhh')
        return path
    except Exception as e:
        pass

    return 'failed'


def deal_item(item: ProductEntity):
    if item.product_img_paths:
        logger.warning(f'爬过')
        return

    ProductImageUrls = item.json_data['ProductImageUrls']
    logger.info(f"{ProductImageUrls}")
    barcode = item.barcode

    urls = []
    for idx, i in enumerate(ProductImageUrls):
        url = 'https://oss.gds.org.cn' + i['Url']
        path = get_pic(url, barcode, idx)
        if path != 'failed':
            logger.success(f'成功 {path}')
            urls.append(path)

    item.product_img_paths = ' '.join(urls) if urls else '-'
    dao.save_by_cmp(item, fields=['product_id'])


def main():
    while True:
        products = dao.get_many('', 'product_img_paths')
        products = list(products)
        logger.info(f'待爬取 {len(products)}')
        for product in products:
            deal_item(product)
        time.sleep(60 * 60)


def main1():
    products_ = dao.get_many(value=705458, field='firm_id')
    for item in products_:
        deal_item(item)


if __name__ == '__main__':
    main()
