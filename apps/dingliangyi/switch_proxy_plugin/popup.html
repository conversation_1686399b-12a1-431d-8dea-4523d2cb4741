<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Switch Proxy Plugin</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Switch Proxy Plugin</h2>
        </div>
        
        <div class="status-section">
            <div class="status-item">
                <label>连接状态:</label>
                <span id="connection-status" class="status-value">检查中...</span>
                <div id="connection-indicator" class="indicator"></div>
            </div>
        </div>
        
        <div class="proxy-section">
            <div class="section-title">当前代理设置</div>
            <div id="current-proxy" class="proxy-info">
                <div class="no-proxy">未设置代理</div>
            </div>
        </div>
        
        <div class="controls-section">
            <button id="refresh-btn" class="btn btn-secondary">刷新状态</button>
            <button id="clear-proxy-btn" class="btn btn-warning">清除代理</button>
            <button id="reconnect-btn" class="btn btn-primary">重新连接</button>
        </div>
        
        <div class="info-section">
            <div class="info-text">
                <p>📡 此插件通过WebSocket与Python脚本通信</p>
                <p>🔧 Python服务器地址: ws://localhost:8765</p>
                <p>⚡ 安装插件后自动连接，运行Python脚本即可切换代理</p>
            </div>
        </div>
        
        <div class="footer">
            <div class="version">v1.0</div>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
