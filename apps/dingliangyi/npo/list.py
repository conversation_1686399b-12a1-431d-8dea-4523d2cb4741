# -*- coding: utf-8 -*-
import random
import requests
from requests import Session
from typing import Union
from loguru import logger
from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA
import base64
import cv2
import numpy as np
import urllib3
from apps.octopus.utils.entry_manager import EntryManager
from libs.env import ConstantProps
from clients.mysql_client import MySQLClient
from tqdm import tqdm
# from dao.npo.npo import NpoD<PERSON>, Npo
import schedule
import time
from apps.dingliangyi.npo.keyword_ import two, three, address
from resx.mysql_dao import MySQLDao
from resx.config import *
import re
from apps.dingliangyi.common2 import touci

urllib3.disable_warnings()
headers = {
    "Origin": "https://xxgs.chinanpo.mca.gov.cn",
    "Referer": "https://xxgs.chinanpo.mca.gov.cn/gsxt/newDetails?b=eyJpZCI6IjUxMTEwMDAwTUowMTAwOTMyNSJ9",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* "
                  "Safari/537.36",
}
timeout = 60
# entry_manager = EntryManager()
mysql_client = MySQLClient(**ConstantProps.PROPS_GS_OUTER_RW)
sql = f'select * from prism.company where name = %s'
npodao = MySQLDao(**CFG_MYSQL_GS_OUTER, db_tb_name='prism.npo')
logger.add(sink=r'./logs/npo_list_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8', enqueue=True)


def request(session: Session, method: str, url: str, params: dict = None, data: dict = None,
            json: dict = None, path: str = None, name: str = '', tojson=False) -> Union[dict, str]:
    for i in range(10):
        response = None
        try:
            response = session.request(**{
                'method': method,
                'url': url,
                'data': data,
                'headers': headers,
                'verify': False,
                'timeout': timeout,
                'params': params,
                'json': json
            })

            if response.status_code != 200:
                logger.warning(f'{name} --> {response.status_code}')
                del session.cookies['proxyBase']
                continue

            logger.success(f'{name} --> {response.status_code}')
            # a = response.text.replace("\n", "").replace('\r', '').replace('\t', '')
            # logger.info(f'{name} --> {a}')

            if 'slideCaptcha' in name and response.json().get('msg', '') == 'error':
                session.cookies.clear()
                continue

            if tojson:
                return response.json()
            return response.text

        except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
            logger.warning(f'{name} --> continue{i} exception: {e}')
            del session.cookies['proxyBase']
            continue
        except Exception as e:
            status = response.status_code if response else "空"
            text = response.text if response else "空"
            logger.warning(f'{name} --> continue{i} 状态码：{status} res: {text} exception: {e}')
            del session.cookies['proxyBase']
            continue


def RSA_Encrypt(text: str):
    public_key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCCsYUGHMhjSzdMqn9JzPfKs9JbxXTPtHofTv7reV0HrEz4brnE6ZJpNn5s934KO3L4QDF7ELHysIiounhhpF1bewW9jKdcpZA5M1CkGHKcwpLA2liaqOlt/0Mf3ui9jxR9AHxUMFVGfJ6Q4+cEmDBUAEOXlxqk4ZjGpubwGNk9XQIDAQAB"
    public_key_str = f'-----BEGIN PUBLIC KEY-----\n{public_key}\n-----END PUBLIC KEY-----\n'
    key = RSA.import_key(public_key_str)
    cipher = PKCS1_v1_5.new(key)
    encrypt_text = base64.b64encode(cipher.encrypt(str(text).encode())).decode()
    return encrypt_text


def Captcha(base64_str: str):
    base64_data = base64.b64decode(base64_str)
    # 将二进制数据转换成numpy数组
    np_data = np.frombuffer(base64_data, np.uint8)
    # 读取图片并进行解码
    img = cv2.imdecode(np_data, cv2.IMREAD_COLOR)
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    # 设置提取白色的阈值范围
    lower_white = np.array([0, 0, 255])  # 白色的最低阈值
    upper_white = np.array([180, 30, 255])  # 白色的最高阈值
    # 根据阈值范围创建掩膜
    mask = cv2.inRange(hsv, lower_white, upper_white)
    # 将掩膜应用于原始图像
    result = cv2.bitwise_and(img, img, mask=mask)
    # 将其他颜色置为黑色
    result[np.where((result != [255, 255, 255]).all(axis=2))] = [0, 0, 0]

    k = np.ones((10, 10), np.uint8)
    opening = cv2.morphologyEx(result, cv2.MORPH_OPEN, k)

    opening = cv2.Canny(opening, 100, 200)
    contours, _ = cv2.findContours(opening, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        cv2.rectangle(img, (x, y), (x + w, y + h), (0, 255, 0), 2)
        return x


def search(key):
    session = requests.session()
    for _ in range(3):
        url = 'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/slideCaptcha'
        image: dict = request(session, 'GET', url, tojson=True, name=f'{key}-slideCaptcha')
        move_x = Captcha(image['result']['c']['oriImage'])
        params = {
            'a': base64.b64encode(image['result']['a'].encode('utf-8')).decode(),
            'b': base64.b64encode(image['result']['b'].encode('utf-8')).decode(),
            'c': base64.b64encode(RSA_Encrypt(str(move_x)).encode('utf-8')).decode(),
        }
        url = 'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/slide_captcha_check'
        res = request(session, 'GET', url, params=params, tojson=True, name=f'{key}-slide_captcha_check')
        if res['msg'] != 'success':
            continue
        else:
            break

    data = {
        'pageNo': 1, 'pageSize': 200000, 'paramsValue': key, 'ssfw': '1', 'aaae0127': '1', 'xyzk': '', 'aaae0129': '', 'aaae0105': '',
        'aaae0123': '2', 'aaae0114': '', 'aae15having': '', 'aaae0145': '', 'aaae0110': '', 'aaae0137': '', 'aaae0149': '', 'aaae0136': '', 'aaae0139': '',
    }
    data.update(params)
    url = 'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/biz/ma/shzzgsxt/a/gridQuery.html'
    all_list = []
    for i in [1]: # 1, 2, 3,
        try:
            data.update({'aaae0123': str(i)})
            res: dict = request(session, 'POST', url, json=data, tojson=True, name=f'{key}-gridQuery')
            temp_list = res['result']['data']
            logger.info(f'{key} {i} --> add {len(temp_list)}')
            all_list.extend(temp_list)
        except Exception as e:
            logger.error(f'{key} {i} --> {e}')
            continue
    logger.info(re.sub(r'[\s\t\n]', '', str(all_list)))
    return all_list


# def dispatch(entry_word, inst_name):
#     return entry_manager.inst_immediate(
#         entry_name='credit',
#         entry_word=entry_word,
#         inst_name=inst_name,
#         reason='npo_list',
#         ignore_latest_search_empty=False
#     )


def main(word):
    for i in word:
        if len(i) > 2:
            i = i.replace('县', '').replace('区', '').replace('市', '')
        list_ = search(i)
        print(len(list_))
        for npo in tqdm(list_):
            for k, v in npo.items():
                npo[k] = re.sub(r'[\s\t\n]', '', str(v))
            try:
                # info_pre = mysql_client.select(sql, args=(npo['aaae0103'],))
                info_pre = npodao.get(unified_social_credit_code=npo["aaae0102"])
                if not info_pre:
                    success = touci(npo["aaae0102"], 'china_npo')
                    logger.info(f'new npo:  {success} --> {npo["aaae0123"]} - {npo["aaae0102"]} - {npo["aaae0103"]}')
            except:
                pass


if __name__ == '__main__':
    # main(['联合'])
    # a = search('联合')

    main(two)
    main(three)
    main(address)

    schedule.every().monday.run(main, two)
    schedule.every().tuesday.run(main, three)
    schedule.every().wednesday.run(main, address)

    while True:
        schedule.run_pending()
        time.sleep(1)
