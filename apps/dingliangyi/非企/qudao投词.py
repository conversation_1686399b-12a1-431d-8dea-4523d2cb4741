import json
from clients.kafka_client import KafkaProducerClient, KafkaConsumerClient
from apps.octopus.utils.entry_manager import EntryManager
from libs.env import ConstantProps
import re
from dao.cods.cods2 import CodsEntity, CodsDao
from clients.redis._redis import Redis
from loguru import logger

entry_manager = EntryManager()
consumer = KafkaConsumerClient(kafka_topic='channel_data_kk_nacao_entity', group_id='qudao', bootstrap_servers='************:19092')
cods_dao = CodsDao()
redis_client = Redis(host='redis-2b0f46e5-545a-4c21-a4a4-053daff1ee7b.cn-north-4.dcs.myhuaweicloud.com', password="rdsnhgot0akp17Xq", db=0)
logger.add(sink=r'./logs/qudao投词_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8', enqueue=True)


# http://************:9002/#/topic-data?clusterId=60743bf3-b246-4bb9-9d1a-bd48e0dd60b8&topic=channel_data_kk_nacao_entity

def dispatch(entry_word, inst_name):
    return entry_manager.inst_immediate(
        entry_name='credit',
        entry_word=entry_word,
        inst_name=inst_name,
        reason='channel_kk_kafka',
        ignore_latest_search_empty=False
    )


print('start')
for i in consumer.read():
    try:
        i = json.loads(i)
        us_credit_code = i.get('credit_code')
        if not us_credit_code:
            continue
        pre = cods_dao.get(us_credit_code, 'unified_social_credit_code')
        if not pre:
            result = ''
            if re.search(r'^(51|52|53)', us_credit_code):
                result = dispatch(us_credit_code, 'china_npo')
            if re.search(r'^(53)', us_credit_code):
                result = dispatch(us_credit_code, 'foundation')
            if re.search(r'^(11|12|13|19|21|29|32|33|34|35|39|41|49|51|52|53|54|55|59|61|62|69|71|72|79|80|81|89|A1|A9|G1|J1|N1|N2|N3|N9|Q1|Q2|Q3|Q9|Y1)',
                         us_credit_code):
                result = dispatch(us_credit_code, 'cods')
            if re.search(r'^(80|81|89)', us_credit_code):
                result = dispatch(us_credit_code, 'acftu')
            if re.search(r'^(12)', us_credit_code):
                redis_client.lpush('gov_unit2', us_credit_code)
            logger.success(f'{us_credit_code} result:{result}')
    except Exception as e:
        pass
