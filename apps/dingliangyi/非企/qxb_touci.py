from typing import Optional, List
import argparse
from datetime import datetime, <PERSON><PERSON><PERSON>
from pydantic import Field
from loguru import logger
import re
from tqdm import tqdm
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from gslib.credit_code import credit_code_valid
from gslib.id_center import id_center_query
from gslib.gs_enum import EntityType
from clients.redis.redis_hash import RedisHash
from dao.qxb.enterprise import EnterpriseDao, Enterprise
from apps.octopus.utils.entry_manager import EntryManager
from clients.redis._redis import Redis
from dao.npo.npo import NpoDao
from dao.cods.cods2 import CodsDao
from dao.foundation.foudation import FoundationDao
from clients.mysql_client import MySQLClient

entry_manager = EntryManager()
logger.add(sink=r'./logs/qxb_touci_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8', enqueue=True)
redis_client = Redis(host='redis-2b0f46e5-545a-4c21-a4a4-053daff1ee7b.cn-north-4.dcs.myhuaweicloud.com', password="rdsnhgot0akp17Xq", db=0)
mysql_111 = MySQLClient(**{
    'host': '886a213ba5de451eb4add2e0d1fb2ef6in01.internal.cn-north-4.mysql.rds.myhuaweicloud.com',
    'user': 'jdhw_d_zhuan_dml',
    'password': 'kmbpZRTr1pooyB9',
    'table': 'data_experience_situation.gov_unit'
})
cods_dao = CodsDao()
foundation_dao = FoundationDao()
npo_dao = NpoDao()


class QxbCodeEid(BaseEntity):
    id: int
    credit_code: str
    eid: str
    cid: Optional[int] = Field(default=None)
    ts: Optional[datetime] = Field(default=None)


enterprise_dao = EnterpriseDao()


def dispatch(entry_word, inst_name):
    return entry_manager.inst_immediate(
        entry_name='credit',
        entry_word=entry_word,
        inst_name=inst_name,
        reason='qxb_touci',
        ignore_latest_search_empty=False
    )


def process(items):
    logger.info(f'process total of {len(items)}')
    for item in tqdm(items):
        item: Enterprise
        eid, us_credit_code = item.eid, item.credit_code
        if not credit_code_valid(us_credit_code):
            continue

        pass_ = True
        if re.search(r'^(51|52|53)', us_credit_code):
            info = npo_dao.get(value=us_credit_code, field='unified_social_credit_code')
            if not info:
                pass_ = False
                dispatch(us_credit_code, 'china_npo')
                logger.info(f'china_npo: {us_credit_code}')

        if re.search(r'^(53)', us_credit_code):
            info = foundation_dao.get(value=us_credit_code, field='creditCode')
            if not info:
                pass_ = False
                dispatch(us_credit_code, 'foundation')
                logger.info(f'foundation: {us_credit_code}')

        if re.search(r'^(11|13|19|21|29|32|33|34|35|39|41|49|54|55|59|61|62|69|71|72|79|80|81|89|A1|A9|G1|J1|N1|N2|N3|N9|Q1|Q2|Q3|Q9|Y1)',
                     us_credit_code):
            info = cods_dao.get(value=us_credit_code, field='unified_social_credit_code')
            if not info:
                pass_ = False
                dispatch(us_credit_code, 'cods')
                logger.info(f'cods: {us_credit_code}')

        # if re.search(r'^(80|81|89)', us_credit_code):
        #     info = cods_dao.get(value=us_credit_code, field='unified_social_credit_code')
        #     if not info:
        #         pass_ = False
        #         dispatch(us_credit_code, 'acftu')
        #         logger.info(f'acftu: {us_credit_code}')

        if re.search(r'^(12)', us_credit_code):
            info = mysql_111.select(fields=['us_credit_code'], args=(us_credit_code,))
            if not info:
                pass_ = False
                dispatch(us_credit_code, 'institution_gj')
                logger.info(f'institution_gj: {us_credit_code}')

        # if pass_:
        #     logger.info(f'pass: {us_credit_code}')


def main(args):
    logger.info(f'args={args}')
    enterprise_offsets = RedisHash(
        db=2,
        name='no_enterprise_offset',
        **ConstantProps.PROPS_GS_REDIS_ONLINE,
    )

    enterprise_dao.sharding_scan(
        process_fn=process,
        process_workers=args.process_num,
        scan_workers=args.dump_num,
        part_num=args.idx_num,
        redis_offset=enterprise_offsets,
        init_offset=datetime.now() - timedelta(days=args.start_days),
        infinite_wait_secs=600,
        scan_key='row_update_time',
    )


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='非企')
    ap.add_argument('--dump-num', type=int, default=100, help='线程数')
    ap.add_argument('--start-days', type=int, default=360, help='row-update-time开始时间')
    ap.add_argument('--idx-num', type=int, default=256, help='只计算0-N的分片')
    ap.add_argument('--process-num', type=int, default=100, help='线程数')
    main(ap.parse_args())
