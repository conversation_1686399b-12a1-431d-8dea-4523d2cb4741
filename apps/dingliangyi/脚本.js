// ==UserScript==
// @name         浙江政务服务网-页面HTML缓存
// @namespace    https://gswsdj.zjzwfw.gov.cn/
// @version      0.2
// @description  上传当前浙江政务服务网档案打印页面的HTML到本地服务器
// <AUTHOR> Name
// @match        https://gswsdj.zjzwfw.gov.cn/daprint.do*
// @icon         https://gswsdj.zjzwfw.gov.cn/favicon.ico
// @grant        GM_xmlhttpRequest
// @connect      *************
// @run-at       document-end
// ==/UserScript==

(function () {
    'use strict';

    let uploadCount = 0;
    const serverUrl = 'http://*************:8020/gs_chche/gszj_chche_api/'; // FastAPI 服务器地址

    // 创建上传按钮
    const uploadButton = document.createElement("button");
    uploadButton.id = "uploadCurrentHtmlButton";
    uploadButton.innerHTML = "上传HTML (0)";
    uploadButton.style.position = "fixed";
    uploadButton.style.bottom = "20px";
    uploadButton.style.right = "20px";
    uploadButton.style.zIndex = "9999";
    uploadButton.style.padding = "10px 15px";
    uploadButton.style.backgroundColor = "#007bff";
    uploadButton.style.color = "white";
    uploadButton.style.border = "none";
    uploadButton.style.borderRadius = "5px";
    uploadButton.style.cursor = "pointer";
    uploadButton.style.boxShadow = "0 2px 5px rgba(0,0,0,0.2)";

    // 按钮点击事件
    uploadButton.onclick = function () {
        const currentHtml = document.documentElement.outerHTML;
        const pageUrl = window.location.href;

        console.log("准备上传HTML，长度:", currentHtml.length);
        console.log("页面URL:", pageUrl);

        GM_xmlhttpRequest({
            method: "POST",
            url: serverUrl,
            headers: {
                "Content-Type": "application/x-www-form-urlencoded" // 或者 application/json 如果你发送JSON
            },
            data: "response_url=" + encodeURIComponent(pageUrl) + "&&&&&&&&form_data=null&&&&&&&&body=" + encodeURIComponent(currentHtml),
            onload: function (response) {
                if (response.status === 200 || response.status === 201) {
                    uploadCount++;
                    uploadButton.innerHTML = `上传HTML (${uploadCount})`;
                    alert("HTML上传成功！服务器响应: " + response.responseText);
                    console.log("上传成功", response);
                } else {
                    alert(`HTML上传失败！状态: ${response.status}, 响应: ${response.responseText}`);
                    console.error("上传失败", response);
                }
            },
            onerror: function (response) {
                alert("HTML上传发生网络错误！详情请查看控制台。");
                console.error("上传错误", response);
            },
            ontimeout: function (response) {
                alert("HTML上传超时！");
                console.error("上传超时", response);
            }
        });
    };

    // 将按钮添加到页面
    document.body.appendChild(uploadButton);

    console.log("浙江政务服务网HTML缓存脚本已加载。");

})();