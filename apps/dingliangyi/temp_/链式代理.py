import requests
from urllib3 import disable_warnings
import socks  # 来自 PySocks
import socket

# 设置默认 SOCKS5 代理（所有 socket 连接都会通过这个）
socks.set_default_proxy(socks.SOCKS5, "127.0.0.1", 1080)
socket.socket = socks.socksocket  # 猴子补丁：强制所有 socket 通过 SOCKS5

disable_warnings()  # Disable SSL warnings for self-signed certificates

headers = {
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0",
}
url = "https://www.baidu.com/"
proxies = {
    'http': 'http://10.99.138.95:30636',
    'https': 'http://10.99.138.95:30636'
}
response = requests.get(url, headers=headers, proxies=proxies, verify=False, timeout=5)
print(response.cookies.get_dict())
print(response)
