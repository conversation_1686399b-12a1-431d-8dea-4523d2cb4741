from resx.redis_types import Redis
from resx.config import *
from resx.mysql_dao import MySQLDao

redis = Redis(**CFG_REDIS_GS, db=9)

for i in range(1, 11):
    pass
    # redis.hset('eventlog_spider:crawler_threads_set_test', f'hk1#{str(i)}', '')
    # redis.hset('eventlog_spider:crawler_threads_set_online', f'law_firm_gj#{str(i)}', '')
    # redis.hset('eventlog_spider:crawler_threads_set_yufa', f'law_firm_gj#{str(i)}', '')

for i in range(1, 26):
    pass
    # redis.hdel('eventlog_spider:spider_threads_set.yufa', f'tj#{str(i)}')
    # redis.hdel('eventlog_spider:crawler_threads_set_online', f'law_firm_gj#{str(i)}')

# redis = Redis(**CFG_REDIS_GS, db=9)
# redis.zremrangebyscore("cods", 0, 0)

# 1. 找出所有分值为 0 的成员
# members_zero = redis.zrangebyscore('law_firm_gj', 0, 0)
# if not members_zero:
#     print("没有分值为 0 的成员")
# else:
#     # 2. 在管道中批量把它们的分值改为 1
#     pipe = redis.pipeline()
#     for member in members_zero:
#         # 直接用 zadd 覆盖旧分值
#         pipe.zadd('law_firm_gj', {member: 1})
#         # 如果你想只更新已存在的成员，可以加上 xx=True：
#         # pipe.zadd('myset', {member: 1}, xx=True)
#     pipe.execute()
#
#     print(f"已将 {len(members_zero)} 个成员的分值从 0 更新为 1：", members_zero)

dao = MySQLDao(**CFG_MYSQL_GS_OUTER,db_tb_name='prism.company_hk')
print(dao.get(br_num=72637881))