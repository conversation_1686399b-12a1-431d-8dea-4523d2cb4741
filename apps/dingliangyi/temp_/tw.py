import requests
from bs4 import BeautifulSoup
from ss1 import *

headers = {
    "Origin": "https://findbiz.nat.gov.tw",
    "Referer": "https://findbiz.nat.gov.tw/fts/query/QueryList/queryList.do",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
}

url = "https://findbiz.nat.gov.tw/fts/query/QueryList/queryList.do"
data = {
    # "errorMsg": "",
    "validatorOpen": "N",
    "rlPermit": "0",
    # "userResp": "",
    "curPage": "0",
    "fhl": "zh_TW",
    "qryCond": "84071859",
    "infoType": "D",
    "qryType": "cmpyType",
    "cmpyType": "true",
    # "brCmpyType": "",
    # "busmType": "",
    # "factType": "",
    # "lmtdType": "",
    "isAlive": "all",
    # "busiItemMain": "",
    # "busiItemSub": ""
}
response = requests.post(url, headers=headers, data=data, proxies={
    'http': 'socks5h://127.0.0.1:6666',
    'https': 'socks5h://127.0.0.1:6666',
})

soup = BeautifulSoup(response.text, 'lxml')
div = soup.select_one('div#vParagraph')
a = div.select('a')
for i in a[:1]:
    print('https://findbiz.nat.gov.tw/' + i.get('href').replace('\r\n', ''))
