import requests

# 定义 URL 和请求体
url = "http://schedule-gsdata.jindidata.com/schedule/realtime"
headers = {
    "Content-Type": "application/json"
}
for i in ('84894989',):
    payload = {
        "crawler_id": "luLJkXO6",
        "schedule_info": {
            "barcode": i
        },
        "weight": 1
    }

    # 发起请求
    response = requests.post(url, json=payload, headers=headers)

    # 打印响应
    print("Status Code:", response.status_code)
    print("Response Body:", response.json())
