import requests
import re
from bs4 import BeautifulSoup
import urllib3

urllib3.disable_warnings()

headers = {
    "Referer": "https://findbiz.nat.gov.tw/fts/query/QueryList/queryList.do",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
}

url = "https://findbiz.nat.gov.tw/fts/query/QueryCmpyDetail/queryCmpyDetail.do"
params = {
    "objectId": "SEM4NDg5NDk4OQ==",
    "banNo": "84894989",
    "disj": "920F94DB3F26C7267D07565D17FE2992",
    "fhl": "zh_TW"
}
response = requests.get(url, headers=headers, params=params, verify=False, proxies={
    'http': 'http://127.0.0.1:7890',
    'https': 'http://127.0.0.1:7890',
})


def parse_table(table):
    headers = [th.get_text(strip=True) for th in table.thead.find_all('th')]
    result = []

    for tr in table.tbody.find_all('tr'):
        tds = tr.find_all('td')
        temp = {}
        if len(tds) == len(headers):
            for header, td in zip(headers, tds):
                for sp in td.find_all('span'):
                    sp.decompose()
                text = td.get_text(strip=True)
                temp[header] = text
        result.append(temp)

    return result


soup = BeautifulSoup(response.text, "lxml")
table = soup.select('table.table.table-striped')
trs = table[0].select('tbody > tr')

result = {}
for tr in trs:
    tds = tr.find_all('td')
    if len(tds) < 2:
        continue
    key = tds[0].get_text(strip=True)
    cell = tds[1]
    for sp in cell.find_all('span'):
        sp.decompose()
    value = cell.get_text(separator='', strip=True).replace('\xa0', '')
    result[key] = value
print(result)
map_dict = {
    '統一編號': 'code',
    "公司名稱": 'name',
    "登記現況": 'status',
    '資本總額(元)': 'reg_capital',
    '实收资本额(元)': 'reg_capital_rel',
    '代表人姓名': 'legal_name',
    '公司所在地': 'address',
    '登記機關': 'reg_institute',
    '核准設立日期': 'approved_time',
    '最後核准變更日期': 'approved_lasttime',
    '所營事業資料': 'scope',
}


print(parse_table(table[2]))
map_dict2 = {
    '职称': 'position',
    '姓名': 'name',
    '所代表法人': 'link_legal_name',
    '持有股份数(股)': 'equity_num',
}

print(parse_table(table[3]))
map_dict4 = {
    '姓名': 'name',
    '到職日期': 'end_date',
}

print(parse_table(table[5]))
map_dict3 = {
    '登記編號': 'f_reg_no',
    '工廠名稱': '',
    '登記現況': '',
    '工廠登記核准日期': 'f_reg_approved_time',
    '最後核准變更日期': '',
}
