import asyncio, threading, time
import requests, urllib3
import pproxy

urllib3.disable_warnings()

remote = pproxy.Connection(
    'ss://aes-128-gcm:94c2598d-a942-4386-817a-767c37974232'
    '@120.233.248.101:16005'
)


async def start_local_proxy(remote):
    # 2. 本地监听 SOCKS5
    server = pproxy.Server('socks5://127.0.0.1:6666')
    args = {
        'rserver': [remote],  # 一组远端 Connection 列表
        'verbose': print  # 可选：日志回调
    }
    handler = await server.start_server(args)  # 异步启动代理服务
    try:
        await asyncio.Event().wait()  # 挂起当前协程
    finally:
        handler.close()


# 在后台线程中运行 asyncio 事件循环
threading.Thread(
    target=lambda: asyncio.run(start_local_proxy(remote)),
    daemon=True
).start()
print('建立ss代理')
time.sleep(1)  # 等待本地端口就绪

# 3. 发起通过本地 SOCKS5 的请求
proxies = {
    'http': 'socks5h://127.0.0.1:6666',
    'https': 'socks5h://127.0.0.1:6666',
}
# response = requests.get(
#     'https://findbiz.nat.gov.tw/fts/query/QueryCmpyDetail/queryCmpyDetail.do',
#     headers={
#         "Referer": "https://findbiz.nat.gov.tw/fts/query/QueryList/queryList.do",
#         "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"
#     },
#     params={
#         "objectId": "SEM4NDA3MTg1OQ==",
#         "banNo": "84071859",
#         "disj": "13FC27A616D13E5334C3E39C58AF47DC",
#         "fhl": "zh_TW"
#     },
#     verify=False,
#     proxies=proxies,
#     timeout=10
# )
# print(response.text)
