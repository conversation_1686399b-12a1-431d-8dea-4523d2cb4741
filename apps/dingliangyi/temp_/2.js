// ==UserScript==
// @name         通用抓取插件-页面HTML+JSON拦截缓存
// @version      1.0
// @description  上传当前页面的HTML和拦截的JSON响应到服务器
// <AUTHOR>
// @match        https://*.gov.cn/*
// @grant        GM_xmlhttpRequest
// @connect      *************
// @run-at       document-start
// @noframes
// ==/UserScript==

;(function () {
    "use strict"

    // 确保document.body存在后再添加元素
    function appendContainer() {
        if (document.body) {
            document.body.appendChild(container)
        } else {
            // 如果body还不存在，等待DOMContentLoaded事件
            document.addEventListener("DOMContentLoaded", () => {
                document.body.appendChild(container)
            })
        }
    }

    // 在页面 <head> 中注入样式
    const style = document.createElement("style")
    style.textContent = `
    .toast-container {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
    }
    .toast {
      min-width: 200px;
      margin-bottom: 10px;
      padding: 10px 15px;
      background-color: rgba(0,0,0,0.7);
      color: #fff;
      border-radius: 4px;
      opacity: 0;
      transform: translateY(-20px);
      transition: opacity 0.3s, transform 0.3s;
    }
    .toast.show {
      opacity: 1;
      transform: translateY(0);
    }
    `
    document.head.appendChild(style)

    // 创建并插入容器
    const container = document.createElement("div")
    container.className = "toast-container"
    // document.body.appendChild(container);
    appendContainer()

    // 定义全局 showToast 函数
    window.showToast = function (message, duration = 500) {
        const toast = document.createElement("div")
        toast.className = "toast"
        toast.textContent = message
        container.appendChild(toast)
        // 触发入场动画
        console.log("显示提示:", message)
        requestAnimationFrame(() => toast.classList.add("show"))
        // 定时移除
        setTimeout(() => {
            toast.classList.remove("show")
            // 安全移除元素的函数
            const safeRemove = () => {
                if (toast.parentNode === container) {
                    container.removeChild(toast);
                }
            };
            // 监听过渡结束事件，但只执行一次
            toast.addEventListener('transitionend', safeRemove, {once: true});
            // 备用移除机制，防止 transitionend 不触发
            setTimeout(safeRemove, duration);
        }, duration)
    }

    if (window.top !== window.self) {
        return
    }

    let uploadCount = 0
    const serverUrl = "http://*************:8020/gs_chche/gszj_chche_api/"
    let datas = []

    const interceptConfig = {
        exactUrls: [
            "/outer/entEnt/fddbr.do",
            "/outer/entEnt/detail.do",
            "/outer/entEnt/tag.do",
            // "/outer/entEnt/biangeng.do",
            // "/outer/entEnt/bgdetail.do"
        ],
        regexPatterns: []
    }

    function shouldIntercept(url) {
        for (const exactUrl of interceptConfig.exactUrls) {
            if (url.includes(exactUrl)) {
                console.log("匹配到完整URL:", exactUrl)
                return true
            }
        }

        for (const pattern of interceptConfig.regexPatterns) {
            if (pattern.test(url)) {
                return true
            }
        }
        return false
    }

    // 拦截XMLHttpRequest
    ;(function (send) {
        XMLHttpRequest.prototype.send = function (requestData) {
            let rData = requestData
            this.addEventListener(
                "readystatechange",
                function () {
                    if (this.responseText.length > 0 && this.status === 200) {
                        // debuggee
                        if (!shouldIntercept(this.responseURL)) {
                            return
                        }
                        datas.push(JSON.parse(this.response)["data"][0]["data"])
                        showToast(`拦截到数据: ${this.responseURL.split("?")[0]}`, 1500)
                        console.log("拦截到数据:", this.responseURL, JSON.parse(this.response)["data"][0]["data"])
                        uploadCount++
                    }
                },
                false
            )
            send.apply(this, arguments)
        }
    })(XMLHttpRequest.prototype.send)

    function createButton() {
        // 创建上传按钮
        const uploadButton = document.createElement("button")
        uploadButton.className = "uploadDataButton"
        uploadButton.innerHTML = `上传数据`
        Object.assign(uploadButton.style, {
            position: "fixed",
            bottom: "20px",
            right: "20px",
            zIndex: "9999",
            padding: "10px 15px",
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer",
            boxShadow: "0 2px 5px rgba(0,0,0,0.2)"
        })

        // 按钮点击事件
        uploadButton.onclick = function () {
            const currentHtml = document.documentElement.outerHTML
            const pageUrl = window.location.href

            let payload = {
                html: currentHtml,
                info: datas
            }

            GM_xmlhttpRequest({
                method: "POST",
                url: serverUrl,
                data: JSON.stringify(payload),
                headers: {
                    "Content-Type": "application/json" // 关键修复：添加此头
                },
                onload: function (response) {
                    if (response.status === 200 || response.status === 201) {
                        const message = `数据上传成功！\n- HTML: 1个页面\n- JSON响应: ${uploadCount}个`
                        alert(message)
                        console.log("上传成功", response)
                    } else {
                        alert(`数据上传失败！状态: ${response.status}, 响应: ${response.responseText}`)
                        console.error("上传失败", response)
                    }
                },
                onerror: function (response) {
                    alert("数据上传发生网络错误！详情请查看控制台。")
                    console.error("上传错误", response)
                },
                ontimeout: function (response) {
                    alert("数据上传超时！")
                    console.error("上传超时", response)
                }
            })

            datas = [] // 清空已上传数据
        }

        document.body.appendChild(uploadButton)
        window.uploadButton = uploadButton
    }

    // 创建上传按钮（等待DOM加载）
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", createButton)
    } else {
        createButton()
    }
})()
