from datetime import datetime, date
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
import json
from pydantic import Field, conint
from typing import Dict, TypeVar, Optional, Generator

EntityType = TypeVar('EntityType', BaseEntity, Dict)


class NoticeEntity(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=0)
    cid: conint(strict=True, ge=0) = Field(default=0)
    name: str = Field(default='')
    certificate_code: str = Field(default='')
    notice_id: str = Field(default='')
    notice_type: int = Field(default=0)
    notice_date: date = Field(default=datetime.strptime('0001-01-01', '%Y-%m-%d').date())
    notice_title: str = Field(default='')
    notice_bureau: str = Field(default='')
    province: str = Field(default='')
    city: str = Field(default='')
    other_info: dict
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    def __init__(self, **kwargs):
        other_info = kwargs.get('other_info', None)
        if other_info is None:
            kwargs['other_info'] = {}
        elif isinstance(other_info, str):
            kwargs['other_info'] = json.loads(other_info)
        super().__init__(**kwargs)


class NoticeDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_TEST.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.gsxt_notice_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', NoticeEntity)
        super().__init__(**kwargs)

    def get_(self, fields: list, value) -> Optional[EntityType]:
        sets = ', '.join([f'{field} = %s' for field in fields])
        sql = f'select * from {self.db_tb_name} where {sets} limit 1'
        d = self.mysql_client.select(sql, args=value)
        return self._to_entity(d, raw=False)

    def get_by_date(self, notice_type: str, date_: str) -> Generator:
        sql = f"select * from {self.db_tb_name} where notice_type = %s and date(update_time) = %s limit {self.batch_size}"
        for d in self.mysql_client.select_many(sql, args=(notice_type, date_)):
            yield self._to_entity(d, raw=False)

    def get_by_condition(self, conditio: str):
        sql = f'select * from {self.db_tb_name} where {conditio} limit {self.batch_size}'
        for d in self.mysql_client.select_many(sql):
            yield self._to_entity(d, raw=False)

    def update(self, entity: EntityType, condition_fields: list = ('id',), dict_fields='other_info'):
        entity_dict = entity.to_dict()
        entity_dict[dict_fields] = json.dumps(entity_dict[dict_fields], ensure_ascii=False)
        condition_values = [entity_dict.pop(i) for i in condition_fields]
        keys = entity_dict.keys()
        values = list(entity_dict.values())
        sets = ', '.join([f'{field} = %s' for field in keys])
        conditions = 'and '.join([f'{field} = %s' for field in condition_fields])
        sql = f'update {self.db_tb_name} set {sets} where {conditions} limit 1'
        return self.mysql_client.execute(sql, args=values + condition_values)

    def get_by_fuzzy(self, like_name, notice_type):
        sql = f'select * from {self.db_tb_name} where name like %s and notice_type = {notice_type} limit {self.batch_size}'
        for d in self.mysql_client.select_many(sql, args=('%' + like_name + '%',)):
            yield self._to_entity(d, raw=False)
