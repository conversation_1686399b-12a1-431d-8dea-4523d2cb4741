import requests
from loguru import logger
import urllib3
import schedule
import json
import copy

logger.add(sink=r'./logs/gs_notice_change_{time:YYYY_MM_DD}.log', rotation='00:00', retention='10 days', encoding='utf8', enqueue=True)
urllib3.disable_warnings()
proxies = {
    'http': 'http://************:30636',
    'https': 'http://************:30636'
}

headers = {
    "Host": "app.gsxt.gov.cn",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* "
                  "Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows "
                  "WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555",
    "Referer": "https://servicewechat.com/wx5b0ed3b8c0499950/14/page-frame.html",
}

data_ = ''
names_ = []


def get_simple_notice_list(type: str = '10', start: str = '1', areaid: str = "310000", regOrg: str = "310118"):
    url = "https://app.gsxt.gov.cn/gsxt/affiche-query-area-info-paperall.html"
    data = {
        "noticeType": type,
        "areaid": areaid,
        "noticeTitle": "",
        "regOrg": regOrg,
        "start": start,
        "sourceType": "W"
    }
    while True:
        try:
            response = requests.post(url, headers=headers, data=data, verify=False, proxies=proxies, timeout=5)
            if 'data' not in response.json():
                continue
            break
        except Exception as e:
            pass

    names = []
    for i in response.json()['data']:
        names.append(f"{i['noticeTitle']}---{i['noticeDate']}")
    logger.info(f'temp {start} : {sorted(names)}')
    return names


def main():
    global data_
    global names_
    names = []
    for i in range(1, 6):
        names.extend(get_simple_notice_list(start=str(i)))
    logger.info(f'names_去重长度:{len(set(names_))} | names去重长度:{len(set(names))} | '
                f'差值names_-names : {list(set(names_) - set(names))} | 差值names-names_ : {list(set(names) - set(names_))}')
    names_ = copy.deepcopy(names)

    temp = json.dumps(sorted(names), ensure_ascii=False)
    logger.info(f"all Data: {temp}")
    logger.info('-' * 30)
    if data_ != temp:
        logger.warning(f"Data changed: {data_} -> {temp}")
        logger.info('-' * 30)
    data_ = json.dumps(sorted(names), ensure_ascii=False)


if __name__ == '__main__':
    main()
    main()

    # schedule.every(30).minutes.do(main)
    # while True:
    #     schedule.run_pending()
