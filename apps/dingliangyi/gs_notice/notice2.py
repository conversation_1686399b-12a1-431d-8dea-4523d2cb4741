import asyncio
import copy
import json
import re
from collections import OrderedDict
from datetime import datetime, timedelta
import aiohttp
import requests
import urllib3
from loguru import logger
import schedule
import time

from libs.concurrent import BoundedExecutor
from libs.env import get_stack_info
from noticeCity import importance_cities, provinces
from noticeDao import NoticeEntity, NoticeDao

logger.add(sink=r'./logs/gs_notice2_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8', enqueue=True)
urllib3.disable_warnings()
# if sys.platform == 'win32':
#     asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
# executor = BoundedExecutor(max_workers=200)
proxies = {
    'http': 'http://************:30636',
    'https': 'http://************:30636'
}
headers = {
    "Host": "app.gsxt.gov.cn",
    "Accept": "application/json",
    "xweb_xhr": "1",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/6.8.0(0x16080000) NetType/WIFI MiniProgramEnv/Mac MacWechat/WMPF MacWechat/3.8.10(0x13080a10) XWEB/1227",
    "Sec-Fetch-Site": "cross-site",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Dest": "empty",
    # "Referer": "https://servicewechat.com/wx5b0ed3b8c0499950/15/page-frame.html",
    "Accept-Language": "zh-CN,zh;q=0.9"
}

class Tools:
    @staticmethod
    def within_three_months(date_str):
        date = datetime.strptime(date_str[:10], '%Y-%m-%d')
        current_date = datetime.now()
        if date >= current_date:
            return True
        three_months_ago = current_date - timedelta(days=90)
        return three_months_ago <= date <= current_date

    @staticmethod
    def in_db(dao: NoticeDao, notice_id):
        return dao.get_(fields=['notice_id'], value=[notice_id])

    @staticmethod
    def unique(my_list, exclude_field: str = ''):
        unique_list = []
        # history = {}
        for d in my_list:
            filtered_items = tuple((k, v) for k, v in sorted(d.items()) if k != exclude_field)
            unique_list.append(filtered_items)
            # history.update({d["uniscid"]: d[exclude_field]})
        unique_list = list(OrderedDict.fromkeys(unique_list))
        data = []
        for t in unique_list:
            d = dict(t)
            # d.update({exclude_field: history[d['uniscid']]})
            data.append(d)
        return data


class RegProcess:

    def __init__(self):
        self.special_strs = ['《', '未经工商行', '小经营店', '无名称经营主体', '，', '无照', '、', '执行', '违法行', '施行', '进行', '活动行',
                             '案行', '的行', '执照', '进行', '侵权', '当事人', '登记注册二处', '监管局行', '*', '无名氏', '关于拟', '涉嫌', '吊销', '未经许可']
        self.compile_reg = re.compile(r"""
            (.*?管理局(（.*?）)?)?
            (.*?监督管理(（.*?）)?)?
            (.*?分局)?
            (移出)?
            (关于)?
            (将)?
            (个体工商户)?
            (对)?
            (.*?[(（])?
            (撤销)?
            (?P<result>.*(?P<if>公司|[（(]个体工商户[)）]|[（(]普通合伙[)）]|[（(]有限合伙[)）]|[（(]个人独资[)）]|
            商行|超市|酒坊|中心|综合诊所|酒家|庄|部|院|吧|场|档|馆|行|店|厂|种植园|气站|\d{15}|[\dA-Z]{18}|合作社|
            农家乐|采购处|游戏厅|会所|工作室|租赁站|电子商务|经销处|工程队|施工队|月饼屋|门市(部)?|服装间|美妆城)?)
            (?(if)|(恢复正常|经营异常|(企业)?移出|
            (?P<a>涉嫌)(?(a)|(无证|未经|列入|未按规定|未办理))|
            (?P<d>\(经营者.*?\))(?(d)|标记)
            ))
            """, re.X)

    def exclude_special_str(self, text):
        for special_str in self.special_strs:
            if special_str in text:
                return ''

        if ('（' in text) and ('）' not in text):
            return ''

        if re.match(r'.*市场$', text):
            return ''

        return text

    @staticmethod
    def remove_str_by_reg(text):
        reg = [r'的列入$', r'的$', r'([(（]经营者(：)?.{2,3}[)）])?(标记)?$', r'涉嫌$', r'\.$', r'企业$', r'列入$', r'从事$', r'[(（].{2,3}[）)]$']
        for r in reg:
            text = re.sub(r, '', text)
        if re.search(r'(个体工商户|有限合伙|普通合伙|个人独资)', text):
            return text
        return re.sub(r'[)）]$', '', text)

    @staticmethod
    def pre_replace(text):
        str_ = ['\n', '行为', '以市场', '行政', '进行', '生产加工场', '经营场所', r'[(（][\u4e00-\u9fa5]{2,3}[）)]', '小经营店', '履行', '执行']
        for s in str_:
            text = re.sub(s, '', text)
        return text

    def main(self, text):
        reg = self.compile_reg.search(self.pre_replace(text))
        reg = reg.group('result') if reg else ""

        reg = self.remove_str_by_reg(reg)
        reg = self.exclude_special_str(reg)

        if 0 < len(reg) <= 4:
            reg = ''

        return reg.strip()


def get_simple_notice_list(type: str = '10', start: str = '1', areaid: str = "100000", regOrg: str = ""):
    url = "https://app.gsxt.gov.cn/gsxt/affiche-query-area-info-paperall.html"
    data = {
        "noticeType": type,
        "areaid": areaid,
        "noticeTitle": "",
        # "regOrg": regOrg,
        "regOrg": '',
        "start": start,
        "sourceType": "W"
    }
    response = requests.post(url, headers=headers, data=data, verify=False, timeout=5, proxies=proxies)
    logger.info(response.json())
    return response.json()


async def get_notice_list_aio(sem, type: str = '10', start: str = '1', areaid: str = "100000", regOrg: str = ""):
    url = "https://app.gsxt.gov.cn/gsxt/affiche-query-area-info-paperall.html"
    data = {
        "noticeType": type,
        "areaid": areaid,
        "noticeTitle": "",
        "regOrg": regOrg,
        "start": start,
        "sourceType": "W"
    }

    for _ in range(20):
        try:
            connector = aiohttp.TCPConnector(ssl=False)
            timeout = aiohttp.ClientTimeout(total=5)
            proxy = proxies['http']
            async with aiohttp.ClientSession(connector=connector) as session:
                async with sem:
                    res = await session.post(url, data=data, headers=headers, timeout=timeout, proxy=proxy)
                if res.status != 200:
                    continue
                notice_data: list = json.loads(await res.text())['data']
                for _ in notice_data:
                    _.update({'province_id': areaid, 'city_id': regOrg})
                logger.info(notice_data)
                return notice_data
        except Exception as e:
            logger.error(f'continue {e.__class__.__name__}:{e} {_ + 1}')
            continue
    raise Exception('放弃')


async def order_rask(sem, notice_type, province_id, city_id, dao):
    result = []

    async def temp():
        temps = []
        for i in range(1, 6):
            data = await get_notice_list_aio(sem, notice_type, str(i), province_id, city_id)
            if not data:
                logger.warning(f'empty type:{notice_type}-{province_id}-{city_id}-页:{i}')
                break
            temps.extend(data)
            # todo 只做地区级增量处理
            if city_id:
                if data and Tools.in_db(dao, data[-1]['noticeId']):
                    logger.warning(f'exist type:{notice_type}-{province_id}-{city_id}-页:{i}')
                    break
        return temps

    a = await temp()
    as_ = []
    result.extend(a)
    for j in a:
        as_.append(j.get('noticeDate', ''))
    if len(as_) > 40 and len(set(as_)) == 1:
        for _ in range(2):
            result.extend(await temp())

    return Tools.unique(result)


async def main_list(dao, executor):
    sem = asyncio.Semaphore(60)
    notice_type = ['11', '12', '13', '10', '20', '30', '51', '52', '53', '54', '55', '61', '62', '18', '21', '22', '19', '32', '17']
    # notice_type = ['17']
    notice_type_ = ['31', '14', '15', '16', '1001', '1002', '1004', '55', '33', '4']
    params_compose = [[str(provinces[province]), str(city_id)] for province, importance_cities_ in importance_cities.items() for city_id in
                      importance_cities_.values()]
    for type_ in notice_type:
        params_compose_copy = copy.deepcopy(params_compose)
        while params_compose_copy:
            params_compose_ = params_compose_copy[:40]
            params_compose_copy = params_compose_copy[40:]
            tasks = [asyncio.create_task(order_rask(sem, type_, province_id, city_id, dao), name=f'{type_}-{province_id}-{city_id}')
                     for province_id, city_id in params_compose_]
            done, _ = await asyncio.wait(tasks, return_when=asyncio.ALL_COMPLETED)
            for task in done:
                if task.exception():
                    logger.error(f'{task.get_name()} --> {task.exception()}')
                elif task.result():
                    executor.submit(write_db, dao, task.result())


def write_db(dao, datas):
    for item in datas:
        if not item or not item.get('noticeDate', ''):
            continue
        try:
            name = replace_null(item.get('noticeTitle', ''))
            certificate_code = ''
            notice_type = item.get('noticeType', 0)
            if notice_type in ['18', '11', '12', '13', '21', '22', '19']:
                name = RegProcess().main(name)
                certificate_code = re.search(r'([0-9A-Z]{18}|\d{15})', replace_null(item.get('noticeContent', '')))
                certificate_code = certificate_code.group(1) if certificate_code else ''
            # if notice_type in ['10', '20', '30', '51', '52', '53', '54', '55', '61', '62']:
            #     name = item.get('noticeTitle', '') if item.get('noticeTitle', '') else ''
            if notice_type == '32':
                name = name[:-4]
            # if notice_type == '33':
            #     name = item.get('noticeTitle', '')[item.get('noticeTitle', '').find('_'):]
            if notice_type == '17':
                name = name[:-8]

            entity = NoticeEntity.from_dict({
                'name': name,
                'certificate_code': certificate_code,
                'notice_id': replace_null(item.get('noticeId', '')),
                'notice_type': notice_type,
                'notice_date': datetime.strptime(replace_null_date(item.get('noticeDate', '0001-01-01')), '%Y-%m-%d').date(),
                'notice_title': replace_null(item.get('noticeTitle', '')),
                'notice_bureau': replace_null(item.get('judAuth_CN', '')),
                'province': item['province_id'],
                'city': item['city_id'],
                'other_info': item,
            })
        except Exception as e:
            logger.error(f'{item} --> {get_stack_info()}')
            continue
        logger.info('save')
        dao.save_by_cmp(entity, fields=['notice_id'], ignore_fields=['id', 'create_time'])


def replace_null(value):
    return value if value else ''


def replace_null_date(value):
    return value if value else '0001-01-01'


def main():
    try:
        executor = BoundedExecutor(max_workers=200, cache_factor=2)
        dao = NoticeDao()
        asyncio.run(main_list(dao, executor))
        executor.shutdown()
        logger.success('finish')
    except:
        pass


if __name__ == '__main__':
    main()

    schedule.every().day.at("00:00").do(main)
    schedule.every().day.at("09:00").do(main)
    schedule.every().day.at("19:00").do(main)
    while True:
        schedule.run_pending()
        time.sleep(1)

    # get_simple_notice_list(areaid='110101', regOrg='110101005')
