import oss2
import os
from public_utils_configs.util.TycSecureUtil import TycSecureUtil

sec = TycSecureUtil(app_name='hk_report', env='prod', ak='infra-secure-obs-KBV5xtP3raLoWfxrlZCB1g==', sk='infra-secure-obs-INXEdn0u736Bv/g8v0LCYw==')
res = sec.decrypt_obs()
print('打印结果ak:' + res[0] + ',ck:' + res[1])


class DevelopmentConfig:
    """
    Development configurations
    """
    config = {
        # 'OSS_KEY_ID': 'LTAI5t5gWjAwyXVnZC6FoKya',
        'OSS_KEY_ID': res[0],
        # 'OSS_KEY_SECRET': '******************************',
        'OSS_KEY_SECRET': res[1],
        'OSS_END_POINT': 'http://oss-cn-beijing.aliyuncs.com',
    }


OSSDeveProConf = DevelopmentConfig.config


class OssDB:
    def __init__(self, oss_bucket_name):
        self.access_key_id = OSSDeveProConf["OSS_KEY_ID"]
        self.access_key_secret = OSSDeveProConf["OSS_KEY_SECRET"]
        self.oss_end_point = OSSDeveProConf["OSS_END_POINT"]
        self.oss_bucket_name = oss_bucket_name
        self.auth = oss2.Auth(self.access_key_id, self.access_key_secret)
        self.bucket = oss2.Bucket(self.auth, self.oss_end_point, self.oss_bucket_name)

    def get_file_content_by_name_from_oss(self, key):
        '''获取响应内容'''
        res = self.get_file_res_by_name_from_oss(key)
        content = res.read()
        # if res.client_crc != res.server_crc:
        # logger.debug("The CRC checksum between client and server is inconsistent!")
        if content:
            return content
            # return res.decode('utf-8')
        return None

    def get_file_res_by_name_from_oss(self, key):
        '''获取 响应对象'''
        if key is None or len(key) == 0:
            return None
        if not self.does_object_exist(key):
            return ""
        # 'company/bj/a71d526b6a0ce47d6b3bc398da939c0d/success.txt'
        res = self.bucket.get_object(key)
        return res

    def put_content_to_oss(self, key, content, headers=None):
        '''上传一个普通文件 —— 上传内容到oss'''
        result = self.bucket.put_object(key, content, headers)
        if result:
            # logger.debug("oss_put_status:{}".format(result.status))
            # logger.debug("oss_request_id:{}".format(result.request_id))
            # logger.debug("oss_etag".format(result.etag))
            # return result.status
            return 'https://' + self.oss_bucket_name + '.oss-cn-beijing.aliyuncs.com/' + key
        return None

    def put_file_to_oss(self, key, filename, headers=None):
        '''上传一个本地文件到OSS的普通文件 —— 上传内容到oss'''
        result = self.bucket.put_object_from_file(key, filename, headers)
        if result:
            # logger.debug("oss_put_status:{}".format(result.status))
            # logger.debug("oss_request_id:{}".format(result.request_id))
            # logger.debug("oss_etag".format(result.etag))
            # return result.status
            # 返回文件地址
            return 'https://' + self.oss_bucket_name + '.oss-cn-beijing.aliyuncs.com/' + key
        return None

    def scran_put_file_to_oss(self, file_dir, base_key):
        '''
        迭代上传目录下所有文件至oss
        :param file_dir: 本地文件目录
        :param base_key: 上传至oss的基础路径
        :return:
        '''
        for root, dirs, files in os.walk(file_dir):
            for file in files:
                file_path = os.path.join(root, file)
                key = os.path.join(base_key, os.path.relpath(file_path, file_dir))
                # status = self.put_file_to_oss(key, file_path)
                static_data_iter = {'key': key, 'file_path': file_path}
                yield static_data_iter

    def does_object_exist(self, file_path):
        flag = False
        exist = self.bucket.object_exists(file_path)
        if exist:
            flag = True
        return flag

    # file_name 为需要保留的文件名
    def delete_file_on_oss(self, path, file_name):
        keys_obj = oss2.ObjectIterator(self.bucket, prefix=path)
        keys = []
        for obj in keys_obj:
            # logger.debug("file:{}".format(obj.key))
            keys.append(obj.key)
        # 没有文件需要删除
        if not keys or len(keys) == 0:
            return True
        # 只剩下指定不删除的文件，直接返回
        has_special_file = file_name and file_name != '' and self.does_object_exist(path + file_name)
        if len(keys) == 1 and has_special_file:
            return True
        # 若需要排除某个特定的文件，或文件数量多于1000个（理论上每次最多获取100条，不会超过1000条），则一个一个的删除
        # 否则，批量删除
        if has_special_file or len(keys) >= 1000:
            for key in keys:
                if has_special_file and key != (path + file_name):
                    self.bucket.delete_object(key)
        else:
            key_list = []
            for key in keys:
                key_list.append(key)
            result = self.bucket.batch_delete_objects(key_list)
            print("delete_keys", "\n".join(result.deleted_keys))
        # 判断是否已删除完成，若可能没有删除完，则递归删除
        # 这里100，表明可能本次获取没有获取所有文件，需要获取剩余的keys并删除
        # 采用递归删除的方式
        if len(keys) >= 100:
            return self.delete_file_on_oss(path, file_name)
        return True

    # 删除某个文件
    def delete_object_from_oss(self, key):
        if key is None or len(key) == 0:
            return True
        self.bucket.delete_object(key)
        return False

    # 通过传入的路径得到其下的文件list
    def get_all_keys_from_oss(self, path):
        if path is None or len(path) == 0:
            return None
        result_list = []
        list_object = oss2.ObjectIterator(self.bucket, prefix=path)
        for item in list_object:
            result_list.append(item.key)
        return result_list

    # 拷贝文件
    def copy_object(self, source_key, destination_key):
        self.bucket.copy_object(self.oss_bucket_name, source_key, destination_key)

    # 拷贝操作-文件夹
    def copy_folder(self, source_folder, destination_folder):
        keys = self.get_all_keys_from_oss(source_folder)
        if keys is None:
            return
        if len(keys) == 0:
            return
        for key in keys:
            destination_key = key.replace(source_folder, destination_folder)
            self.copy_object(key, destination_key)


if __name__ == "__main__":
    oss_db = OssDB('jindi-oss-companyinfo')
    a = oss_db.put_file_to_oss('weekly_report_picture/新增企业一周汇总.png', '新增企业一周汇总.png')
