import requests
from bs4 import BeautifulSoup
import csv
import io
from tqdm import tqdm
from apps.dingliangyi.common import dispatch
from loguru import logger
import schedule
import time

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
}


def main():
    url = "https://data.gov.hk/sc-data/dataset/hk-cr-crdata-list-newly-registered-companies-2526"
    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.text, "lxml")
    lis = soup.select('li.dataset-details__list-item[data-format="CSV"]')
    urls = []
    for li in lis:
        print(f'{li.get_text(strip=True)}: {li["data-url"]}')
        urls.append(li["data-url"])

    for url in urls[-2:]:
        response = requests.get(url, headers=headers)
        response.encoding = 'utf-8'
        reader = csv.reader(io.StringIO(response.text))
        list_ = list(reader)
        bar = tqdm(total=len(list_))
        for row in list_:
            # print(row)
            if row[5]:
                success = dispatch(row[3], 'hk', 'name_change', 'brno')
                logger.info(f'{row[3]}: {success}')
            bar.update(1)


# 每天定时执行
schedule.every().day.at("10:00").do(main)

while True:
    schedule.run_pending()
    time.sleep(1)
