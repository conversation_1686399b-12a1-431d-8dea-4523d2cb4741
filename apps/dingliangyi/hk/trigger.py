import time
# from loguru import logger
from resx.log import setup_logger

logger = setup_logger(logger_path='./logs/hk_trigger.log', backup_count=10, name=__name__)

from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.spider.crawler import CrawlerHK2 as Crawler_
from apps.spider.parser import <PERSON>rserH<PERSON> as Parser_
from apps.spider.utils.conf_manager import SpiderConfManager
from dao.hk.hk_company_payed_require_report import HKCompanyPayedRequireReport, HKCompanyPayedRequireReportDao
from dao.company import CompanyDao, Company, CompanyGraph, CompanyGraphDao
from dao.company_hk import CompanyHk
from libs.feishu import send_feishu_message_attachments as feishu
from resx.mysql_dao import MySQLDao
from resx.config import *

dao = HKCompanyPayedRequireReportDao()
company_graph_dao = CompanyGraphDao()
company_hk_dao = MySQLDao(db_tb_name='prism.company_hk', **CFG_MYSQL_GS_OUTER, primary_index_fields=(['br_num'], []), entity_class=CompanyHk,
                          ignore_fields=['id', 'company_id', 'updatetime'])
company_dao = CompanyDao()

# logger.add(sink=r'./logs/hk_trigger_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8', enqueue=True, level='INFO')
chat_id = 'oc_c1a20434dc7c3ca4c6fc279ddfaeb879'
manager = SpiderConfManager(json_conf_name='hk.json', reload_conf_interval_sec=0)
spider_conf = manager.get_exact_one()
crawler = Crawler_(spider_name=spider_conf.spider_name, crawler_conf=spider_conf.crawler)
parser = Parser_(spider_name=spider_conf.spider_name, parser_conf=spider_conf.parser)

logger.info('start')

while True:
    current_time = time.localtime()
    is_workday = current_time.tm_wday < 5  # 0-4 represents Monday to Friday
    is_work_hours = 9 <= current_time.tm_hour < 19 or (current_time.tm_hour == 9 and current_time.tm_min >= 30)
    # is_work_hours = True

    if not (is_workday and is_work_hours):
        logger.info("Outside working hours (Mon-Fri 9:30-19:00). Sleeping for 5 minutes...")
        time.sleep(300)  # Sleep for 5 minutes
        continue

    for task in dao.get_tasks():
        task: HKCompanyPayedRequireReport
        cg: CompanyGraph = company_graph_dao.get(value=task.graph_id, field='graph_id')
        if not cg:
            logger.warning(f'no cg for {task.graph_id} {task}')
            continue
        c: Company = company_dao.get(cg.cid)
        if not c:
            logger.warning(f'no cg for {cg} {task}')
            continue
        company_hk: CompanyHk = company_hk_dao.get(company_id=c.cid)
        logger.info(f'brno={company_hk.br_num}')

        # todo 判断企业类型

        word = str(company_hk.br_num)
        eventlog: Eventlog = Eventlog.from_dict({
            "event_id": f"octopvs_entry-brno-{word}-hk-1703756336", "code": EventlogCode.UNFILLED.value,
            "selector": {"send_ts": 63836777, "receive_ts": -1, "reason": 'hk_payed_report', "clue": False, "entry_name": "brno",
                         "inst_name": "hk", "word": word, "info": {}, "try_id": 0, "meta": {}, "weight": 960},
            "spider": {"spider_data": {
                # "page_ts": 1736160981
            }}})
        crawler.crawl(eventlog)
        parser.parse(eventlog)
        logger.info(f'OUTPUT eventlog={eventlog.to_json()}')
        if eventlog.code == 0:
            dao.success_task(task.graph_id)
        else:
            feishu(chat_id=chat_id, ats=['丁良益'], text=f'{word} 抓取失败')
            dao.set_task(task.graph_id, 4)
            logger.warning(f'fail task {task} {eventlog}')
        print('-' * 30)

    logger.info("Sleeping for 1 minute...")
    time.sleep(10)  # Sleep for 1 minute before next iteration
