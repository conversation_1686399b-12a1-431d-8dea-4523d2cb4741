from resx.config import *
from resx.mysql_dao import MySQLDao
from biz_utils.entity.company_graph import CompanyGraphDao

company_hk_dao = MySQLDao(db_tb_name='prism.company_hk', **CFG_MYSQL_GS_OUTER)
g_dao = CompanyGraphDao()


def get_gid(cn, en):
    print(cn, en)
    a = None
    if cn:
        a = company_hk_dao.get(name_cn=cn)
    if not a and en:
        a = company_hk_dao.get(name_en=en)
    if a:
        b = g_dao.get(company_id=a['company_id'])
        return b.graph_id
    return 0


if __name__ == '__main__':
    dao = MySQLDao(db_tb_name='prism.hk_annual_shareholders', **CFG_MYSQL_GS_OUTER)
    list_ = list(dao.select_many("select * from prism.hk_annual_shareholders where name_en <> '' or name_cn <>''"))
    for i in list_:
        # print(i)
        gid = get_gid(i['name_cn'], i['name_en'])
        if gid:
            print(gid)
            dao.execute('update prism.hk_annual_shareholders set inv_id = %s where id = %s', args=(gid, i['id']))
