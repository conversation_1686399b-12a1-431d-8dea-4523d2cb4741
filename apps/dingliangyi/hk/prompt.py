import json
import requests
from loguru import logger

text1 = '''
    # 角色
    你是一个信息整理专家，擅长从图片中抽取需要的信息。
    # 任务
    根据输入的图片，抽取图片中json格式所需字段对应的值，并严格遵守任务要求，生成json数据。
    # 任务要求
    1.日期格式为YYYY-MM-DD
    json格式:{"return_date":“Date to which this Return is Made Up”或“Date of this Return”}
    '''

text2 = '''
    # 角色
    你是一个信息整理专家，擅长从图片中抽取需要的信息。
    # 任务
    根据输入的图片，抽取出‘股本’信息，json格式所需字段对应的值，并严格遵守任务要求，生成json数据。
    # 任务要求
    1. 提取issued_total_number、issued_total_amount、issued_total_amount_paid字段值时，只保留数字部分，例如去除逗号、HKD（单位）等其他符号，除了小数点。
    2. 表格的一行代表一个列表元素，如果无数据则返回空列表。
    3.不用提取一条数据中有 “N/A” 的数据
    json格式:
        {
            "hk_annual_capital": [
                {
                  "shares_class": "Class of Shares",
                  "currency": "Currency",
                  "issued_total_number": "Total Number",
                  "issued_total_amount": "Total Amount",
                  "issued_total_amount_paid": "Total Amount Paid up or Regarded as Paid up"
                }
              ]
        }
    '''

# Name（shareholder_name）字段必须识别出所有文字包括繁体中文、英文、简体中文。非常重要！！！

text3_ = '''
    # 角色
    你是一个信息整理专家，擅长从图片中抽取需要的信息。
    # 任务
    根据输入的图片，抽取出标题为‘(非)上市公司的成員詳情’的信息，json格式所需字段对应的值，并严格遵守任务要求，生成json数据。
    # 图片特殊情况
    1. ‘No.of Shares Held’代表了‘Current Holding’字段值
    # 任务要求
    1. Name（shareholder_name）字段必须识别出所有文字包括繁体中文、英文、简体中文。非常重要！！！
    2. 表示数值的字段值时，只保留数字部分，例如去除逗号等其他符号，除了小数点。
    3. Remarks如果该字段没有对应的值则用“-”代替
    4. 数据中不能含有换行符
    5. 大小写需要与原图片相同
    6. 如果无数据则返回空列表。
    json格式:
        {
            "total_number":"Total Number of Issued Shares in this Class"',
            "hk_annual_shareholders": [
            {
              "shareholder_name": "Name",
              "address": "Address",
              "current_holding": "Current Holding",
              "remarks": "Remarks",
              "shares_class": "Class of Shares"
            }
          ]
        }
    '''

text3 = '''
    # 角色
    你是一个信息整理专家，擅长从图片中提取需要的信息。
    # 任务
    输入是一张包含表格的图片，包含简体中文、繁体中文、英文，从图片中提取各字段符合要求的信息。
    # 特别注意
    1.注意表格中行和列的对应关系，需要一一对应，不要串行、串列。
    2.输出格式和内容要与原文一致，例如原文用大写输出也需要大写，例如原文中一个字段的信息同时用多种语言描述，需要保持原文全部提取，此外不能包含换行符。
    3.‘No.of Shares Held’代表了‘Current Holding’字段值。
    4.不能把简体中文转为繁体中文，或者繁体中文转为简体中文。英文保持原大小写。
    5.表示数值的字段值时，例如total_number、current_holding,只保留数字部分，例如去除逗号等其他符号，除了小数点。
    6.如果一行数据中有 "Nil"，则表示无效数据，需要抛弃
    #严格输出以下json格式：
    {
    "total_number":"{{指“Total Number of Issued Shares in this Class”所指的数值；}}",
    "hk_annual_shareholders": [
    {
      "shareholder_name": "{{指表格中“Name”所指的信息；}}",
      "address": "{{当前shareholder_name所在行，对应的“地址/Address”列的内容；}}",
      "current_holding": "{{指当前shareholder_name所在行，对应的“現時持有量/Current holding”列的内容；}}",
      "remarks": "{{指当前shareholder_name所在行，对应的“備註/Remarks”列的内容；如图片中对应的位置是的“——”和空白，仅输出“-”}}",
      "shares_class": "{{指当前shareholder_name所在行，对应的“股份類別/Class of Shares”列的内容；}}"
    }
     /*如果不止一个shareholder_name，继续输出，否则忽略。*/
      ]
    }
    '''


def doubao_api(image_url, text):
    model = "ep-20241113111622-zz5kr"
    temperature = 0.7
    top_p = 0.9
    url = 'http://************:18900/doubao'
    data = json.dumps({
        'model': model,
        'messages': [{'role': 'user', 'content': [{"type": "text", "text": text}, {"type": "image_url", "image_url": {"url": image_url}}]}],
        'stream': False,
        'parameters': {'temperature': temperature, 'top_p': top_p}})
    for _ in range(20):
        try:
            res = requests.post(url, data=data, timeout=60 * 5)
            logger.info(res.text)
            info = json.loads(res.json()['choices'][0]['message']['content'])
            logger.info(f"json: {info}")
            return info
        except Exception as e:
            logger.error(f"请求失败 {e} {_ + 1}")
            continue


if __name__ == '__main__':
    doubao_api('https://jindi-oss-companyinfo.oss-cn-beijing.aliyuncs.com/channel/12590984_1.png', text1)
