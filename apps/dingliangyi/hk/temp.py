import json

import lark_oapi as lark
from resx.redis_types import Redis
from flask import Flask
from lark_oapi.adapter.flask import *
from lark_oapi.api.im.v1 import *

redis_conn = Redis(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com',
                               port=6379, password='3lvadmpiSj61ge',
                               db=2)
app = Flask(__name__)


def do_p2_im_message_receive_v1(data: P2ImMessageReceiveV1) -> None:
    print('===========')
    try:
        json_res = lark.JSON.marshal(data)
        json_ret = json.loads(json_res)
        content = json_ret['event']['message']['content']
        json_obj = json.loads(content)
        text = json_obj['text']
        if text:
            redis_conn.setex('test_img_result_redis_key', 300000, text)
    except:
        print('except')
    print(lark.JSON.marshal(data))


def do_customized_event(data: lark.CustomizedEvent) -> None:
    print('===========')
    # try:
    #     json_res = lark.JSON.marshal(data)
    #     json_ret = json.loads(json_res)
    #     content = json_ret['event']['message']['content']
    #     json_obj = json.loads(content)
    #     text = json_obj['text']
    #     if text:
    #         redis_conn.setex('test_img_result_redis_key', 300000, text)
    # except:
    #     print('except')
    # print(lark.JSON.marshal(data))


handler = lark.EventDispatcherHandler.builder('ZzH8E4C7pLXZyENfQGGx0cwRsAOsAooD', 'XUrd3un0txMYwlBzEo51VebTMlXerueW',
                                              lark.LogLevel.DEBUG) \
    .register_p2_im_message_receive_v1(do_p2_im_message_receive_v1) \
    .register_p1_customized_event("message", do_customized_event) \
    .build()


@app.route("/event", methods=["POST"])
def event():
    resp = handler.do(parse_req())
    return parse_resp(resp)


@app.route("/get/img/result", methods=["GET"])
def get_result():
    res = {
        'code': 200,
        'data': {'img_code': ''},
        'message': '成功'
    }
    redis_key = 'test_img_result_redis_key'
    result = redis_conn.get(redis_key)
    if result:
        res['data']['img_code'] = str(result)
        redis_conn.delete(redis_key)
    return json.dumps(res)


if __name__ == "__main__":
    app.run('0.0.0.0',port=10021)