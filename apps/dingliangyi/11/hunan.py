import requests
import re
import json
from dao.cods.cods2 import CodsEntity, CodsDao as CodsDao2
from apps.octopus.utils.entry_manager import EntryManager
from tqdm import tqdm

entry_manager = EntryManager()
cods_dao2 = CodsDao2()


def dispatch(entry_word, inst_name):
    return entry_manager.inst_immediate(
        entry_name='credit',
        entry_word=entry_word,
        inst_name=inst_name,
        reason='cods_11',
        ignore_latest_search_empty=False
    )


headers = {
    "Referer": "http://www.hunanbb.gov.cn/hunanbb/xhtml/tydm_index.html",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36 Edg/128.0.0.0"
}
url = "http://www.hunanbb.gov.cn/sbb/front/organ/page.do"
params = {"page": "1", "rows": "300", "jsonpCallback": "builtTable", "jgmc": "", "unify_code": ""}
response = requests.get(url, headers=headers, params=params, verify=False)
# print(response.text)
info = re.search(r'builtTable\((.*?)\)', response.text).group(1)
data = json.loads(info)
print(data)

for item in data['rows']:
    company = cods_dao2.get(item['unify_code'], 'unified_social_credit_code')
    if not company:
        success = dispatch(item['unify_code'], 'cods')
        print(f'{item["unify_code"]} -- {success}')
