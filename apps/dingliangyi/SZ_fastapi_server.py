from fastapi import FastAP<PERSON>, Form, HTTPException, Request, Body
import uvicorn
from pydantic import BaseModel
import json
from typing import List, Dict, Optional, Union
# from loguru import logger
import re
import time
from resx.log import setup_logger

from apps.gs_spider.utils.msv_enum import MSVSource
from libs.env import get_env_prop
from clients.kafka_client import KafkaProducerClient
from entity.eventlog import Eventlog, SpiderCode
from libs.dt import to_date
from apps.gs_spider.parser.parser_gsxt import GSXTParserTask, GSXTParser, MSVBaseInfo, MSVPartnership, MSVInvestor, \
    MSVStaff, MSVChange

fusion_producer_client = KafkaProducerClient(**get_env_prop(f'kafka.tyc.gs'), kafka_topic='gsxt.data_fusion')
logger = setup_logger(logger_path='./logs/sz_plugins_crawler.log', backup_count=10, name=__name__)
app = FastAPI()


class UploadData(BaseModel):
    html: str  # 字符串类型
    info: Union[List, Dict]  # JSON 类型（用 dict 表示对象，或 list 如果是数组）


@app.post("/gs_chche/gszj_chche_api/")
async def receive_data(data: UploadData = Body(...)):
    infos = data.info
    word: dict = infos[0][0]['unifsocicrediden']
    ts = int(time.time())
    eventlog = Eventlog.from_dict(
        {
            "event_id": f"octopus_entry-company-{word}-gdsz-{ts}",
            "is_clue": False,
            "spider_code": -1,
            "crawlerType": 1,
            "crawlerCode": -1,
            "parserCode": -1,
            "fusionCode": -1,
            "selector": {
                "send_ts": ts,
                "receive_ts": -1,
                "reason": 'local_update',
                "item_name": 'company',
                "inst_name": 'gdsz',
                "word": "",
                "info": {
                    "keyword": word,
                },
                "try_id": 0,
                "meta": None,
                "weight": 999,
            },
            "spider": {
                "receive_ts": -1,
                "send_ts": -1,
                "item_insert": False,
                "ab_info": {}
            },
            "crawler": {},
            "parser": {},
            "fusion": {},
            "dims": {},
            "channel": {}
        }
    )
    task = GSXTParserTask(eventlog, {})
    for info in infos:
        logger.info(f"Received info: {info}")

    if len(infos) < 4:
        logger.error(f"Received data length is not 5, received: {len(infos)}")
        raise HTTPException(status_code=400, detail="请正常点击")

    base_info: dict = infos[0][0]
    if base_info['opetype'] in ['HHQY', 'WZHH']:
        lereps = [i['persname'] for i in infos[1]]
        base_info['lerep'] = ','.join(lereps)
    scope_info: dict = infos[2][0]
    business_scope = f'一般经营项目是：{scope_info.get("cbuitem", "")}，许可经营项目是：{scope_info.get("pabuitem", "")}'

    msv_base_info_data = dict(
        name=base_info['entname'],
        legal_person_name=re.sub(r'([(（]委派代表.*?[）)])$', '', base_info['lerep']),
        reg_number=base_info.get('regno', None),
        company_org_type=base_info.get('enttype', None),
        reg_location=base_info.get('dom', None),
        estiblish_time=to_date(base_info['estdate']),
        approved_time=to_date(base_info['apprdate']),
        from_time=to_date(base_info['estdate']),
        business_scope=business_scope,
        reg_status=base_info.get('entstatus', None),
        reg_capital=str(base_info['regcap']) + "万" + base_info.get('currency', '人民币') if (
                'regcap' in base_info) else None,
        credit_code=base_info.get('unifsocicrediden', None),
    )
    msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)

    msv_partnerships = None
    company_org_type = msv_base_info.company_org_type
    legal_person_name = msv_base_info.legal_person_name
    if company_org_type and '合伙' in company_org_type and legal_person_name:
        msv_partnerships = [MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split(',')]
    task.set_base(msv_base_info, msv_partnerships)

    msv_investors = [MSVInvestor.from_dict(
        {
            'investor_name': x['inv'],
            'investor_type': 0,
            'capital': [{
                'amomon': f"{x['subconam']}万元",
                'time': '',
                'paymet': ''
            }] if ('subconam' in x) else [],
            'detail': {
                'amomon': x.get('subconam', None),
                '合伙人/股东属性': x.get('invatt', None),
                '合伙人类别': x.get('responway', None),
                '股东类别': x.get('invtype', None),
                'shareholder_type': x.get('responway', '') + x.get('invtype', '')
            }
        }) for x in infos[3]]
    task.set_investors(msv_investors)

    if len(infos) == 5:
        msv_staff = [MSVStaff.from_dict({
            'staff_name': i['name'],
            'staff_position': i['post']
        }) for i in infos[4] if i != {} and 'post' in i]
        task.set_staffs(msv_staff)

    # msv_change = [MSVChange.from_dict(
    #     {
    #         'change_item': row['valueNew'],
    #         'change_time': to_date(row['altdate']),
    #         'content_before': row.get('altbe', ''),
    #         'content_after': row.get('altaf', '')
    #     }
    # ) for row in json.loads(pages['变更信息.txt'])]
    # task.set_changes(msv_change)

    task.msv_save(MSVSource.GDSZ)

    eventlog.spider_code = SpiderCode.SUCCESS
    eventlog.spider.send_ts = int(time.time())
    eventlog_str = eventlog.to_json()
    ret = fusion_producer_client.write(eventlog_str)
    logger.info(f"Fusion data sent: {ret}")


if __name__ == "__main__":
    uvicorn.run(
        app,
        host="0.0.0.0",  # 监听所有网络接口
        port=8020,
        log_level="info"
    )
