import dateparser
from pydantic import Field, conint
from typing import Optional, Union
from datetime import datetime, date
from resx.base_model import BaseModel
import re


# Pesticide Info Model
class PesticideInfo(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    company_id: conint(strict=True, ge=0) = Field(default=0)
    registration_number: str = Field(default='')
    pesticide_name: str = Field(default='')
    first_approval_date: Optional[Union[date, str]] = Field(default=None)
    valid_until: Optional[Union[date, str]] = Field(default=None)
    toxicity: str = Field(default='')
    dosage_form: str = Field(default='')
    certificate_holder: str = Field(default='')
    pesticide_category: str = Field(default='')
    active_ingredient_content: str = Field(default='')
    remarks: Optional[str] = Field(default='')
    toxicity_symbol: Optional[str] = Field(default='')
    active_ingredient_details: Optional[str] = Field(default='')
    usage_technical_requirements: Optional[str] = Field(default='')
    product_performance: Optional[str] = Field(default='')
    precautions: Optional[str] = Field(default='')
    poisoning_first_aid: Optional[str] = Field(default='')
    storage_and_transportation: Optional[str] = Field(default='')
    quality_guarantee_period: str = Field(default='')
    approval_date: str = Field(default='')
    reapproval_date: str = Field(default='')
    create_time: datetime = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    update_time: datetime = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    deleted: conint(strict=True, ge=0) = Field(default=0)

    def __init__(self, init=False, **data_dict):
        if init:
            field_mapping = {
                "登记证号": "registration_number",
                "农药登记证号": "registration_number",
                "首次批准日期": "first_approval_date",
                "有效期至": "valid_until",
                "农药名称": "pesticide_name",
                "毒性": "toxicity",
                "剂型": "dosage_form",
                "登记证持有人": "certificate_holder",
                "农药类别": "pesticide_category",
                "总有效成分含量": "active_ingredient_content",
                "备注": "remarks",
                "毒性及其标识": "toxicity_symbol",
                "有效成分及其含量": "active_ingredient_details",
                "使用技术要求:": "usage_technical_requirements",
                "产品性能:": "product_performance",
                "注意事项": "precautions",
                "中毒急救措施": "poisoning_first_aid",
                "储存和运输方法": "storage_and_transportation",
                "质量保证期": "quality_guarantee_period",
                "核准日期": "approval_date",
                "重新核准日期": "reapproval_date"
            }
            mapped_data = {}
            for cn_key, en_key in field_mapping.items():
                if cn_key in data_dict:
                    mapped_data[en_key] = re.sub(r'[\s ]', '', data_dict[cn_key].strip())
            super().__init__(**mapped_data)
        else:
            super().__init__(**data_dict)


# Pesticide Active Ingredient Model
class PesticideActiveIngredient(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    pesticide_info_id: conint(strict=True, ge=0) = Field(default=0)
    active_ingredient: str = Field(default='')
    active_ingredient_en: str = Field(default='')
    active_ingredient_content: str = Field(default='')
    create_time: datetime = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    update_time: datetime = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    deleted: conint(strict=True, ge=0) = Field(default=0)

    def __init__(self, init=False, **data_dict):
        if init:
            field_mapping = {
                "有效成分": "active_ingredient",
                "有效成分英文名": "active_ingredient_en",
                "有效成分含量": "active_ingredient_content"
            }
            mapped_data = {}
            for cn_key, en_key in field_mapping.items():
                if cn_key in data_dict:
                    mapped_data[en_key] = data_dict[cn_key].strip()
            super().__init__(**mapped_data)
        else:
            super().__init__(**data_dict)


# Pesticide Dosage Info Model
class PesticideDosageInfo(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    pesticide_info_id: conint(strict=True, ge=0) = Field(default=0)
    crop_or_place: str = Field(default='')
    target: str = Field(default='')
    dosage: str = Field(default='')
    application_method: str = Field(default='')
    create_time: datetime = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    update_time: datetime = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    deleted: conint(strict=True, ge=0) = Field(default=0)

    def __init__(self, init=False, **data_dict):
        if init:
            field_mapping = {
                "作物/场所": "crop_or_place",
                "防治对象": "target",
                "用药量（制剂量/亩）": "dosage",
                "施用方法": "application_method"
            }

            mapped_data = {}
            for cn_key, en_key in field_mapping.items():
                if cn_key in data_dict:
                    mapped_data[en_key] = data_dict[cn_key].strip()
            super().__init__(**mapped_data)
        else:
            super().__init__(**data_dict)


# Pesticide Production Approval Model
class PesticideProductionApproval(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    company_id: conint(strict=True, ge=0) = Field(default=0)
    company_name: str = Field(default='')
    product_name: str = Field(default='')
    product_category: str = Field(default='')
    production_type: str = Field(default='')
    execution_standard: str = Field(default='')
    production_approval_no: str = Field(default='')
    approval_date: Optional[Union[date, str]] = Field(default=None)
    valid_until: Optional[Union[date, str]] = Field(default=None)
    create_time: datetime = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    update_time: datetime = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    deleted: conint(strict=True, ge=0) = Field(default=0)

    def __init__(self, init=False, **data_dict):
        if init:
            field_mapping = {
                "企业名称": "company_name",
                "产品名称": "product_name",
                "产品类别": "product_category",
                "生产类型": "production_type",
                "执行标准": "execution_standard",
                "批准证号": "production_approval_no",
                "生产批件号": "production_approval_no",
                "批准日期": "approval_date",
                "有效期": "valid_until"
            }
            mapped_data = {}
            for cn_key, en_key in field_mapping.items():
                if cn_key in data_dict:
                    mapped_data[en_key] = data_dict[cn_key].strip() if isinstance(data_dict[cn_key], str) else data_dict[cn_key]
            super().__init__(**mapped_data)
        else:
            super().__init__(**data_dict)


# 农药生产许可（Pesticide Production Aptitude）模型
class PesticideProductionAptitude(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    company_id: conint(strict=True, ge=0) = Field(default=0)
    approval_number: str = Field(default='')
    enterprise_name: str = Field(default='')
    social_credit_code: str = Field(default='')
    legal_representative: str = Field(default='')
    residence: str = Field(default='')
    production_address: str = Field(default='')
    production_scope: Optional[str] = Field(default='')
    first_approval_date: Optional[Union[date, str]] = Field(default=None)
    valid_until: Optional[Union[date, str]] = Field(default=None)
    create_time: datetime = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    update_time: datetime = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    deleted: conint(strict=True, ge=0) = Field(default=0)

    def __init__(self, init=False, **data_dict):
        if init:
            field_mapping = {
                "编号": "approval_number",
                "生产企业名称": "enterprise_name",
                "统一社会信用代码": "social_credit_code",
                "法定代表人（负责人）": "legal_representative",
                "住所": "residence",
                "生产地址": "production_address",
                "生产范围": "production_scope",
                "首次批准日期": "first_approval_date",
                "有效期至": "valid_until"
            }
            mapped_data = {}
            for cn_key, en_key in field_mapping.items():
                if cn_key in data_dict:
                    if '期' in cn_key:
                        mapped_data[en_key] = dateparser.parse(data_dict[cn_key])
                    else:
                        mapped_data[en_key] = data_dict[cn_key].strip()
            super().__init__(**mapped_data)
        else:
            super().__init__(**data_dict)
