import requests
from bs4 import BeautifulSoup, Tag
from loguru import logger
import re
import json
from resx.mysql_dao import MySQLDao
from resx.config import *
from urllib3 import disable_warnings
# from dateutil import parser as dateparser
from resx.tools import BoundedExecutor
from tqdm import tqdm
from concurrent.futures import wait

disable_warnings()
from pesticide_model import PesticideInfo, PesticideActiveIngredient, PesticideDosageInfo, PesticideProductionAptitude

pesticide_dao = MySQLDao(db_tb_name='data_business_risk.pesticide_info', **CFG_MYSQL_ZX_RDS109, primary_index_fields=(['registration_number'], []),
                         entity_class=PesticideInfo, ignore_fields=['id', 'company_id', 'update_time'])
active_ingredient_dao = MySQLDao(db_tb_name='data_business_risk.pesticide_active_ingredient', **CFG_MYSQL_ZX_RDS109,
                                 primary_index_fields=(['pesticide_info_id'], ['active_ingredient']),
                                 entity_class=PesticideActiveIngredient, ignore_fields=['id', 'update_time'])
dosage_info_dao = MySQLDao(db_tb_name='data_business_risk.pesticide_dosage_info', **CFG_MYSQL_ZX_RDS109,
                           primary_index_fields=(['pesticide_info_id'], ['crop_or_place', 'target']),
                           entity_class=PesticideDosageInfo, ignore_fields=['id', 'update_time'])
aptitude_dao = MySQLDao(db_tb_name='data_business_risk.pesticide_production_aptitude', **CFG_MYSQL_ZX_RDS109,
                        primary_index_fields=(['social_credit_code'], ['approval_number']),
                        entity_class=PesticideProductionAptitude, ignore_fields=['id', 'update_time'])

headers = {
    "Origin": "https://www.icama.cn",
    "Referer": "https://www.icama.cn/BasicdataSystem/pesticideRegistration/queryselect.do",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0",
}
executor = BoundedExecutor(20)


def get_pesticide_register(pageNo='1', pageSize='1000'):
    url = "https://www.icama.cn/BasicdataSystem/pesticideRegistration/queryselect.do"
    data = {
        "pageNo": pageNo, "pageSize": pageSize, "djzh": "", "nymc": "", "cjmc": "", "sf": "", "nylb": "", "zhl": "", "jx": "", "zwmc": "",
        "fzdx": "", "syff": "", "dx": "", "yxcf": "", "yxcf_en": "", "yxcfhl": "", "yxcf2": "", "yxcf2_en": "", "yxcf2hl": "",
        "yxcf3": "", "yxcf3_en": "", "yxcf3hl": "", "yxqs_start": "", "yxqs_end": "", "yxjz_start": "", "yxjz_end": "", "accOrfuzzy": "2"
    }
    response = requests.post(url, headers=headers, data=data)
    soup = BeautifulSoup(response.text, 'lxml')
    table = soup.find('table', id='tab')
    return parse_table(table)


def get_pesticide_aptitude(pageNo="1", pageSize="20"):
    url = "https://www.icama.cn/DataCenter/manufacturer/query"
    data = {"pageNum": pageNo, "pageSize": pageSize, "productionLicenseCode": "", "enterpriseName": "", "creditCode": "",
            "provinceCode": "", "productScope": "", "productAddress": "", "startDate": "", "endDate": "", "status": "-1"}
    for _ in range(10):
        response = requests.post(url, headers=headers, data=data, proxies={
            'http': 'http://************:30636',
            'https': 'http://************:30636'
        }, verify=False)
        soup = BeautifulSoup(response.text, 'lxml')
        table = soup.find('table', class_='data_table')
        list_ = parse_table(table, type_='aptitude')
        if list_:
            return list_


def parse_table(table, type_=''):
    if not table:
        return []
    urls = []
    for row in table.find_all("tr")[1:]:
        onclick = row.find("a")["onclick"]
        id_ = re.search(r"\('(.+)'\)", onclick).group(1)
        url = f'https://www.icama.cn/BasicdataSystem/pesticideRegistration/viewpd.do?id={id_}'
        if type_ == 'aptitude':
            url = f'https://www.icama.cn/DataCenter/manufacturer/view?id={id_}'
        urls.append(url)
    return urls


class BabelInfo:
    @staticmethod
    def parse_usage_table(table_tag):
        """
        解析“使用范围和使用方法”子表格
        """
        rows = table_tag.find_all("tr")
        if len(rows) < 2:
            return []

        # 第一行是表头
        headers = [th.get_text(strip=True) for th in rows[0].find_all(["td", "th"])]
        data_rows = rows[1:]
        result = []

        for row in data_rows:
            cols = row.find_all(["td", "th"])
            row_data = {}
            for i, col in enumerate(cols):
                col_name = headers[i] if i < len(headers) else f"col{i}"
                row_data[col_name] = col.get_text(strip=True)
            result.append(row_data)

        return result

    def main(self, url):
        response = requests.get(url, headers=headers)
        soup = BeautifulSoup(response.text, "lxml")

        # 找到外层表格( class="kuang" )
        outer_table = soup.find("table", class_="kuang")
        if not outer_table:
            return {}

        # 找到嵌套的内层表格
        inner_table = outer_table.find("table")
        if not inner_table:
            return {}

        info = {}
        rows = inner_table.find_all("tr")
        for row in rows:
            td = row.find("td")
            if not td:
                continue

            # 尝试找到带 class="style4" 的元素(可能是 span 或 div)
            label_tag = td.find(["span", "div"], class_="style4")
            if not label_tag:
                continue

            # 取出标签文本，去掉末尾的冒号
            label_text = label_tag.get_text(strip=True).rstrip("：")

            if "使用范围和使用方法" in label_text:
                usage_table = td.find("table", {"border": "1"})
                if usage_table:
                    usage_data = self.parse_usage_table(usage_table)
                    info[label_text] = usage_data
                else:
                    value_text = td.get_text(strip=True)
                    value_text = value_text.replace(label_text, "", 1).lstrip("：")
                    info[label_text] = value_text
                continue

            if "毒性及其标识" in label_text:
                img_tag = td.find("img")
                if img_tag and img_tag.get("src"):
                    info[label_text] = 'https://www.icama.cn' + img_tag["src"]
                else:
                    value_text = td.get_text(strip=True).replace(label_text, "", 1).lstrip("：")
                    info[label_text] = value_text
                continue

            full_text = td.get_text(strip=True)
            value = full_text.replace(label_text, "", 1).lstrip("：").strip()
            info[label_text] = value

        return info


class PesticideRegisterDate:

    @staticmethod
    def get_table_title(table: Tag):
        first_tr = table.find('tr')
        return first_tr.get_text(strip=True) if first_tr else ""

    @staticmethod
    def parse_pesticide_info(table: Tag):
        result = {}
        rows = table.find_all('tr')
        for row in rows[1:]:
            cell_texts = [td.get_text(strip=True) for td in row.find_all('td')]
            i = 0
            while i < len(cell_texts):
                label = cell_texts[i].rstrip("：")
                value = ""
                if i + 1 < len(cell_texts):
                    value = cell_texts[i + 1]
                result[label] = value
                i += 2
        return result

    @staticmethod
    def parse_component_info(table: Tag):
        rows = table.find_all('tr')
        if len(rows) < 2:
            return []
        header_cells = [td.get_text(strip=True) for td in rows[1].find_all('td')]
        data_rows = rows[2:]

        result_list = []
        for row in data_rows:
            cells = row.find_all('td')
            row_data = {}
            for i, td in enumerate(cells):
                col_name = header_cells[i] if i < len(header_cells) else f"列{i}"
                row_data[col_name] = td.get_text(strip=True)
            result_list.append(row_data)
        return result_list

    @staticmethod
    def parse_dosage_info(table: Tag):
        rows = table.find_all('tr')
        if len(rows) < 2:
            return []
        headers = [td.get_text(strip=True) for td in rows[1].find_all('td')]
        data_rows = rows[2:]

        result_list = []
        for row in data_rows:
            cells = row.find_all('td')
            row_data = {}
            for i, td in enumerate(cells):
                col_name = headers[i] if i < len(headers) else f"列{i}"
                row_data[col_name] = td.get_text(strip=True)
            result_list.append(row_data)
        return result_list

    def main(self, url):
        response = requests.post(url, headers=headers)
        pdno = re.search(r'tagview.do\?pdno=(.+)"', response.text).group(1)
        pdno = f'https://www.icama.cn/BasicdataSystem/pesticideRegistration/tagview.do?pdno={pdno}'
        logger.info(f"标签信息: {pdno}")
        babel_info = BabelInfo().main(pdno)

        soup = BeautifulSoup(response.text, 'lxml')
        tables = soup.find_all('table', id='reg')
        pesticide_info = {}
        component_info_list, dosage_info_list = [], []
        for table in tables:
            title = self.get_table_title(table)
            if "农药登记证信息" in title:
                pesticide_info = self.parse_pesticide_info(table)
                pesticide_info.update(babel_info)
            elif "有效成分信息" in title:
                component_info_list = self.parse_component_info(table)
            elif "制剂用药量信息" in title:
                dosage_info_list = self.parse_dosage_info(table)

        return pesticide_info, component_info_list, dosage_info_list


def main():
    def __(url):
        a, b, c = PesticideRegisterDate().main(url)
        pesticide_dao.save(PesticideInfo(init=True, **a))
        pesticide_info_id = pesticide_dao.get(registration_number=a['登记证号']).id
        for item in b:
            temp: PesticideActiveIngredient = PesticideActiveIngredient(init=True, **item)
            temp.pesticide_info_id = pesticide_info_id
            active_ingredient_dao.save(temp)
        for item in c:
            temp: PesticideDosageInfo = PesticideDosageInfo(init=True, **item)
            temp.pesticide_info_id = pesticide_info_id
            dosage_info_dao.save(temp)

    page = 1
    while True:
        list_ = get_pesticide_register(pageNo=str(page), pageSize='100')
        if not list_ or page > 500:
            break
        bar = tqdm(total=len(list_), desc=f'获取登记信息-{page}')
        for url in list_:
            # __(url)
            future = executor.submit(__, url)
            future.add_done_callback(lambda x: bar.update(1))
        logger.success(f'page: {page}')
        page += 1


def parse_aptitude(url):
    for _ in range(10):
        try:
            response = requests.get(url, headers=headers, proxies={
                'http': 'http://************:30636',
                'https': 'http://************:30636'
            }, verify=False, timeout=10)
        except:
            continue
    soup = BeautifulSoup(response.text, 'lxml')
    trs = soup.find_all('tr')
    temp = {}
    for tr in trs:
        key, value = list(tr.stripped_strings)
        temp[key[:-1]] = value
    # logger.info(temp)
    return temp


def main2():
    def __(url):
        _ = parse_aptitude(url)
        aptitude = PesticideProductionAptitude(init=True, **_)
        if not aptitude.social_credit_code or not aptitude.approval_number:
            return
        aptitude_dao.save(aptitude)

    page = 16
    while True:
        urls = get_pesticide_aptitude(pageNo=str(page), pageSize='100')
        if not urls or page > 20:
            break
        bar = tqdm(total=len(urls), desc=f'获取生产许可证-{page}')
        list_ = []
        for url in urls:
            # _ = parse_aptitude(url)
            # aptitude_dao.save(PesticideProductionAptitude(**_))
            future = executor.submit(__, url)
            future.add_done_callback(lambda x: bar.update(1))
            list_.append(future)
        wait(list_)
        logger.info(f'page: {page}')
        page += 1


if __name__ == '__main__':
    main()
    # main2()
