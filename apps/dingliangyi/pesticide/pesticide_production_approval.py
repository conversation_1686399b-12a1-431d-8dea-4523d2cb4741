import requests
from bs4 import BeautifulSoup
from docx import Document
import re
from loguru import logger
from io import BytesIO
import tempfile
import os
import subprocess
import time
import json
from urllib3 import disable_warnings

from resx.config import *
from resx.mysql_dao import MySQLDao

disable_warnings()
headers = {
    "Accept": "application/json, text/javascript, */*; q=0.01",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded",
    "DNT": "1",
    "Pragma": "no-cache",
    "Referer": "https://www.miit.gov.cn/search/index.html?websiteid=110000000000000&pg=&p=&tpl=&category=&jsflIndexSeleted=&q=%E7%94%9F%E4%BA%A7%E6%89%B9%E5%87%86%E8%AF%81%E4%B9%A6%E5%90%8D%E5%8D%95",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "X-Requested-With": "XMLHttpRequest",
    "sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}


def get_url(page):
    url = "https://www.miit.gov.cn/search-front-server/api/search/info"
    params = {
        "websiteid": "110000000000000",
        "scope": "basic",
        "q": "产品生产批准证书名单",
        "pg": "10",
        "cateid": "47",
        "pos": "title_text,titlepy,infocontent,filenumbername,keyword,contentdescribe",
        "pq": "",
        "oq": "",
        "eq": "",
        "begin": "",
        "end": "",
        "dateField": "deploytime",
        "selectFields": "title,content,deploytime,_index,url,cdate,infoextends,infocontentattribute,keyword,contentdescribe,sectitle,picpath,columnname,themename,publishgroupname,publishtime,metaid,bexxgk,columnid,infocontenthashcode",
        "group": "distinct",
        "highlightConfigs": "\\[\\{\"field\":\"infocontent\",\"numberOfFragments\":2,\"fragmentOffset\":0,\"fragmentSize\":110,\"noMatchSize\":110\\}\\]",
        "highlightFields": "title_text,infocontent,webid",
        "level": "6",
        "sortFields": "\\[\\{\"name\":\"extend1\",\"type\":\"desc\"\\},\\{\"name\":\"jsearch_score\",\"type\":\"desc\"\\}\\]",
        "hidCol": "fbafd13557fa453d9c59432567d2b150",
        "p": page
    }
    response = requests.get(url, headers=headers, params=params)
    list_ = response.json()['data']['searchResult']['dataResults']
    urls = []
    for i in list_:
        for j in i['groupData']:
            url: str = j['data']['url']
            if url.startswith('http'):
                urls.append(url)
            else:
                urls.append('https://www.miit.gov.cn' + url)
    logger.info(f'len: {len(urls)} urls: {urls}')
    return urls


def get_doc_url(url):
    # logger.info('start ' + url)
    response = requests.get(url, headers=headers, timeout=5, verify=False)
    soup = BeautifulSoup(response.text, 'lxml')
    as_ = soup.find_all('a')
    url_dict = {}
    for a in as_:
        title = a.get_text(strip=True)
        if '产品生产批准证书名单' not in title:
            continue
        try:
            url = 'https://www.miit.gov.cn' + a['href']
            title = re.sub(r'(附件\d?：|.xls|拟)', '', a.get_text(strip=True))
            logger.info(f'{title}: {url}')
            url_dict[title] = url
        except Exception:
            logger.warning(f'{title}: {url}')
    return url_dict


if __name__ == '__main__':
    url_dicts = {}

    for p in range(1, 44):
        for url_ in get_url(str(p)):
            url_dicts.update(get_doc_url(url_))
            time.sleep(0.4)

    logger.info(json.dumps(url_dicts, ensure_ascii=False, indent=4))
