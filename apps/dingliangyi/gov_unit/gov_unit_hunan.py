import random
import ddddocr
import schedule
import time
from datetime import datetime
from loguru import logger
import traceback
from bs4 import BeautifulSoup
import re
from tqdm import tqdm
import json
from requests import Response, Session

from apps.dingliangyi.gov_unit.gov_unit_base import GovUnitSpider
from apps.dingliangyi.gov_unit.gov_search import GovUint
from apps.dingliangyi.gov_unit.save import save


class GovUnitHunanSpider(GovUnitSpider):
    def __init__(self):
        super().__init__()

    def get_list(self):
        url = "http://www.hunanbb.gov.cn/sbb/front/institutions/page.do"
        params = {"page": "1", "rows": "500", "jsonpCallback": "builtTable", "sydwmc": "", "unify_code": ""}
        all_list = []
        for i in range(5, 9):
            params['page'] = str(i)
            res: str = self.request(self.new_session(), 'GET', url, params=params, name=f'get_list-{i}')
            res = re.sub('[\n\s\r\t]', '', res)
            data = json.loads(re.search(r'rows":(.*),"total', res).group(1))
            all_list.extend(data)
        return all_list

    def get_change_list(self):
        url = "http://www.hunanbb.gov.cn/sbb/front/exchange/page.do"
        for i in range(1, 24):
            params = {"page": str(i), "rows": "10", "jsonpCallback": "builtTable", "sydwmc": "", "ywlx": "设立登记", "unify_code": ""}
            res: str = self.request(self.new_session(), 'GET', url, params=params, name='get_change_list')
            res = re.sub('[\n\s\r\t]', '', res)
            data = json.loads(re.search(r'rows":(.*),"total', res).group(1))
            yield from data

    def parse(self, data):
        if not data.get('zsyxqsrq', 0) or not data.get('zsyxqsrq', 0):
            validity = '证书已公告废止'
        else:
            validity = f"自{self.timestamp_to_date(data.get('zsyxqsrq', 0))}至{self.timestamp_to_date(data.get('zsyxjsrq', 0))}"

        mapped_data = {
            "名称": data.get('sydwmc', ''),
            "统一社会信用代码": data.get('unify_code', ''),
            "宗旨和业务范围": data.get('zzhywfw', ''),
            "住所": data.get('address', ''),
            "法定代表人": data.get('fddbr', ''),
            "经费来源": data.get('jfly', ''),
            "开办资金": data.get('kbzj'),
            "举办单位": data.get('jbdwmc', ''),
            "有效期": validity,
            "单位状态": data.get('dwzt', ''),
            "登记管理机关": '湖南省事业单位登记管理局',
            # "reg_time": data.get('办理时间', ''),
            "source": 'http://www.hunanbb.gov.cn/hunanbb/bbxxgk/gkgs/djfwgs/index.html',
        }
        return mapped_data

    def main_schedule(self):
        list_ = self.get_list()
        list_2 = self.unique(list_)
        logger.info(f'总共{len(list_)} -> {len(list_2)}条数据')
        for item in tqdm(list_2):
            try:
                if not item.get('unify_code') and not item.get('sydwmc'):
                    continue
                if not item.get('unify_code') and item.get('sydwmc'):
                    keyword = item['sydwmc']
                else:
                    continue
                # is_exist = self.check_in_db(item['unify_code'], item['sydwmc'])
                # if self.is_increment and is_exist:
                #     break
                # if not is_exist:
                #     data = self.parse(item)
                #     self.to_kafka({'gov_unit': data})
                #     logger.info(f'新增或变更数据: {data}')
                self.push_to_redis([keyword])
            except Exception as e:
                logger.error(f"{item} {e}")
                traceback.print_exc()

    def main_schedule2(self):
        list_ = self.get_change_list()
        for item in list_:
            keyword = item.get('unify_code', '')
            if self.check_in_db(keyword):
                continue
            try:
                info = GovUint().main(Session(), keyword)
            except Exception as e:
                continue
            save(info['gov_unit'])

    def main(self):
        self.main_schedule2()

        # logger.info('Start schedule')
        # schedule.every().day.at("10:00").do(self.main_schedule2)
        # while True:
        #     schedule.run_pending()
        #     time.sleep(1)


if __name__ == '__main__':
    spider = GovUnitHunanSpider()
    spider.main()
