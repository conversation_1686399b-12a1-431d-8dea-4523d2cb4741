import json
import requests
import re
import time
from district import D<PERSON><PERSON>IC<PERSON>, ERROR2RIGHT, ERROR2RIGHT2
from loguru import logger
from tqdm import tqdm
from resx.config import *
from resx.mysql_dao import MySQLDao
from apps.dingliangyi.common2 import touci

logger.add(sink=r'./logs/new_touci_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8', level='INFO', enqueue=True)

headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0"}
proxies = {'http': 'http://10.99.138.95:30636',
           'https': 'http://10.99.138.95:30636'}
url = "http://search.gjsy.gov.cn/bjtz/queryFrame"
mysql_client = MySQLDao(**CFG_MYSQL_ZX_RDS111, db_tb_name='data_experience_situation.gov_unit')


def exist_in_db(us_credit_code):
    sql = f"select * from data_experience_situation.gov_unit where us_credit_code = '{us_credit_code}' limit 1"
    return bool(mysql_client.select(sql))


def task(c):
    logger.info(f'start task: {c}')
    for _ in range(3):
        try:
            data = {"ts": str(int(time.time() * 1000)), "c": c, "t": "18", "sn": "1", "mc": "20", "at": "0", "atRadio": "0"}
            response = requests.post(url, headers=headers, data=data, verify=False, proxies=proxies, timeout=3)
            if response.status_code != 200 or 'Please try again!' in response.text:
                continue
            creditCode = re.findall(r'd\.push\("(12[\dA-Z]{16})"\);', response.text)
            return creditCode
        except Exception as e:
            logger.error(e)
            continue


for district, address in DISTRICTS:
    if str((district, address)) in ERROR2RIGHT:
        district = eval(ERROR2RIGHT[str((district, address))])[0]
    if address in ERROR2RIGHT2:
        district = ERROR2RIGHT2[address]
    creditCodes = task(district)
    for i in creditCodes:
        if exist_in_db(i):
            logger.info(f'Already exists: {i}')
            continue
        touci(i, 'institution_gj')
