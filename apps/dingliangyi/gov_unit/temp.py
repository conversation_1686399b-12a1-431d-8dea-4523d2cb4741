import requests
import time

headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    "DNT": "1",
    "Pragma": "no-cache",
    "Referer": "https://djgl.jssbb.gov.cn:8096/sydjweb/home",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
    "X-Requested-With": "XMLHttpRequest",
    "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}
url = "https://djgl.jssbb.gov.cn:8096/sydjweb/api/admin/service/sydw/sydwproclaiminfo/uiQuerySydwProclaimSlDataNew"
params = {
    "certiFlag": "1",
    "groupRadio": "xjFlag",
    "districtCode": "320000",
    "checkValue": "0",
    "pageNo": "1",
    "pageSize": "100",
    "totalRows": "22647"
}
response = requests.get(url, headers=headers, params=params)

a = time.time()
print(response.text)
print(time.time() - a)