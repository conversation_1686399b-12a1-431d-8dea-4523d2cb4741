import random
import ddddocr
import schedule
import time
from datetime import datetime
from loguru import logger
import traceback
from bs4 import BeautifulSoup
import re
from tqdm import tqdm

from apps.dingliangyi.gov_unit.gov_unit_base import GovUnitSpider
from apps.dingliangyi.gov_unit.gov_search import GovUint
from apps.dingliangyi.gov_unit.save import save


class GovUnitShandongSpider(GovUnitSpider):
    def __init__(self):
        super().__init__()
        self.host = 'http://sydwjg.sdbb.gov.cn'

    def get_list(self):
        url = "http://sydwjg.sdbb.gov.cn/website/permissionNoticeAction\u0021xkggList.action"
        params = {"permissionNotice.yw_type": "SL", "permissionNotice.unit_name": "", "permissionNotice.xydm": "", "cpage": "1"}
        for i in range(1, 2133):
            params['cpage'] = str(i)
            res: str = self.request(self.new_session(), 'GET', url, params=params, name=f'get_list-{i}')
            if not res:
                return
            if self.parse_list(res):
                yield from self.parse_list(res)

    def get_detail(self, path):
        url = f"{self.host}{path}"
        res: str = self.request(self.new_session(), 'GET', url, name='get_detail')
        soup = BeautifulSoup(res, 'lxml')
        trs = soup.select('tr')
        key, value = [], []
        for tr in trs:
            list_ = list(tr.stripped_strings)
            key.append(list_[0])
            value.append(list_[1])
        data = dict(zip(key, value))
        data['source'] = url
        return self.parse(data)

    def parse(self, data: dict):
        end_date = datetime.strptime(data.get('有效期结束时间', ''), '%Y-%m-%d')
        status = '正常'
        if end_date < datetime.now():
            status = '该单位已注销'
        if '已注销' in data.get('名称', ''):
            status = '该单位已注销'
        mapped_data = {
            "名称": data.get('名称', '').replace('（已注销）', ''),
            "统一社会信用代码": data.get('统一社会信用代码', ''),
            "宗旨和业务范围": data.get('宗旨和业务范围', ''),
            "住所": data.get('住所', ''),
            "法定代表人": data.get('法定代表人', ''),
            "经费来源": data.get('经费来源', ''),
            "开办资金": re.sub(r'[（）]', '', data.get('开办资金')) if data.get('开办资金') else '',
            "举办单位": data.get('举办单位', ''),
            "有效期": f"自{self.datatime_format(data.get('有效期开始时间', ''))}至{self.datatime_format(data.get('有效期结束时间', ''))}",
            "单位状态": status,
            "登记管理机关": data.get('登记机关', ''),
            "reg_time": data.get('办理时间', ''),
            "source": data.get('source', ''),
        }
        return mapped_data

    @staticmethod
    def parse_list(html: str):
        soup = BeautifulSoup(html, 'lxml')
        trs = soup.select('tr')
        all_list = []
        for tr in trs[1:]:
            try:
                list_ = list(tr.stripped_strings)
                path = re.search("Window\('(.*?)',", tr.select('a')[0]['onclick']).group(1)
                list_.insert(0, path)
                all_list.append(list_)
            except Exception as e:
                continue
        return all_list

    def main_schedule(self):
        list_ = self.get_list()
        for item in list_:
            try:
                is_exist = self.check_in_db(item[2])
                if self.is_increment and is_exist:
                    break
                if not is_exist:
                    data = self.get_detail(item[0])
                    if data['单位状态'] == '该单位已注销':
                        data2 = self.search_master(item[2])
                        data = data2['gov_unit'] if data2 else data
                    logger.info(f'新增或变更数据: {data}')
                    save(data)
            except Exception as e:
                logger.error(f"{item} {e}")
                traceback.print_exc()

    def main(self):
        self.main_schedule()

        # logger.info('Start schedule')
        # schedule.every().day.at("10:00").do(self.main_schedule)
        # while True:
        #     schedule.run_pending()
        #     time.sleep(1)


if __name__ == '__main__':
    # 1.3k
    spider = GovUnitShandongSpider()
    spider.main()
