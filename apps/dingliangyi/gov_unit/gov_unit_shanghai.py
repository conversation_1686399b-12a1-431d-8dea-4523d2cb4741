import requests
import json
import schedule
import time
from loguru import logger
from requests import Session
from tqdm import tqdm

from apps.dingliangyi.gov_unit.gov_unit_base import GovUnitSpider
from apps.dingliangyi.gov_unit.gov_search import GovUint
from apps.dingliangyi.gov_unit.save import save


class GovUnitShanghaiSpider(GovUnitSpider):
    def __init__(self):
        super().__init__()
        self.headers = {
            "Origin": "https://www.sydjsh.cn",
            "Referer": "https://www.sydjsh.cn/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36",
        }
        self.timeout = 60 * 2

    def search_or_detail(self, keyword='', type_='searchForUnit', unit_id=''):
        # searchForUnit searchForJgqt searchForUnitDetail
        for i in range(1, 10):
            try:
                url = f"https://www.sydjsh.cn/shbb/website/{type_}"
                data = {"page": {"pageSize": 500, "pageIndex": i}, "type": "", "keyword": keyword, "enCodeNum": ""}
                if type_ == 'searchForUnitDetail':
                    del data['page']
                    del data['keyword']
                    data['unit_id'] = unit_id

                res = self.request(self.new_session(), "POST", url, json=data, name=f"search-{keyword}-{i}", tojson=True, long_proxy=True)
                data['enCodeNum'] = res['codeNum']
                res: dict = self.request(self.new_session(), "POST", url, json=data, name=f"search-{keyword}-{i}", tojson=True, long_proxy=True)
                if 'codeNum' in res:
                    continue
                if res['unitList']['data']:
                    yield res
                else:
                    return []
            except Exception as e:
                continue

    def get_announcements(self) -> list[dict]:
        url = "https://www.sydjsh.cn/shbb/website/djgg"
        data = {"page": {"pageSize": 157, "pageIndex": 1}, "yw_type": "SL", "type": "", "vl": "item"}
        res: dict = self.request(self.new_session(), "POST", url, json=data, name="get_announcements", tojson=True)
        return res['ggList']['data']

    def get_companies(self, gonggao_id) -> list[dict]:
        url = "https://www.sydjsh.cn/shbb/website/slgg"
        data = {"gonggao_id": gonggao_id, "type": "", "isold": "0"}
        res: dict = self.request(self.new_session(), "POST", url, json=data, name="get_companies", tojson=True)
        return res['sldjList']

    @staticmethod
    def parse(data: dict):
        mapped_data = {
            "名称": data.get('mc', ''),
            "统一社会信用代码": data.get('xydm', ''),
            "宗旨和业务范围": data.get('zzyw', ''),
            "住所": data.get('txdz', ''),
            "法定代表人": data.get('frName', ''),
            "经费来源": data.get('jfxs', ''),
            "开办资金": f"{data['kbzj']}万元" if data.get('kbzj') else '',
            "举办单位": data.get('zgbm', ''),
            "有效期": "",
            "单位状态": "正常",
            "登记管理机关": "上海市事业单位登记管理局",
            "source": data.get('source', 'sh')
        }
        return mapped_data

    def main_schedule(self):
        list_ = self.get_announcements()
        for item in list_:
            for company in tqdm(self.get_companies(item['id'])):
                try:
                    data = self.parse(company)
                    is_exist = self.check_in_db(data['统一社会信用代码'])
                    if self.is_increment and is_exist:
                        break
                    if not is_exist:
                        info = {}
                        try:
                            info = GovUint().main(Session(), data['统一社会信用代码'])
                        except Exception as e:
                            logger.error(f"{e} {item}")
                        data = info['gov_unit'] if info else data
                        save(data)
                        self.sum += 1
                        logger.info(f'新增数据: {data}')
                    else:
                        logger.info(f'已存在数据: {data}')
                except Exception as e:
                    self.custom_traceback(e)

    def main_schedule2(self):
        # '上海',
        keywords = ['黄浦', '徐汇', '长宁', '静安', '普陀', '虹口', '杨浦', '闵行', '宝山', '嘉定', '浦东新', '金山', '松江', '青浦', '奉贤', '崇明']
        for keyword in keywords:
            for j in self.search_or_detail(keyword):
                for item in j['unitList']['data']:
                    try:
                        is_exist = self.check_in_db(item['xydm'])
                        if self.is_increment and is_exist:
                            break
                        if not is_exist:
                            info = {}
                            try:
                                info = GovUint().main(Session(), item['xydm'])
                            except Exception as e:
                                logger.error(f"{e} {item}")
                            data = info if info else {
                                'gov_unit': self.parse(self.search_or_detail(type_='searchForUnitDetail', unit_id=item['unitId'])['detail'])}
                            self.to_kafka(data)
                            self.sum += 1
                            logger.info(f'新增数据: {data}')
                        else:
                            logger.info(f'已存在数据: {item}')
                    except Exception as e:
                        self.custom_traceback(e)

    def main(self):
        # self.main_schedule2()
        self.main_schedule()
        logger.success(f'新增{self.sum}条数据')

        # schedule.every(21).days.at('10:00').do(self.main_schedule)
        # while True:
        #     schedule.run_pending()
        #     time.sleep(1)


if __name__ == '__main__':
    spider = GovUnitShanghaiSpider()
    spider.main()
