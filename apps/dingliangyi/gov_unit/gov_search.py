# -*- coding: utf-8 -*-
import re
import time
from typing import Union
import ddddocr
import requests
from bs4 import BeautifulSoup
from loguru import logger
from requests import Session, Response
import traceback
import sys


# logger.remove()
# logger.add(sink=r'./logs/gov_unit_search_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8', level='INFO', enqueue=True)
# logger.add(sys.stderr, level="INFO")


class MyException(Exception):

    def __init__(self, message):
        self.message = message
        super().__init__(self.message)


class GovUint:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
        }
        self.proxies = {
            'http': 'http://10.99.138.95:30636',
            'https': 'http://10.99.138.95:30636'
        }
        self.timeout = 5
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        self.search_url = 'http://search.gjsy.gov.cn/wsss/query'

    def main(self, session: Session, keyword):
        html = self.search(session, keyword)
        base_info = self.parse(keyword, html)
        token = re.findall(r'a1.push\("/annual/random/(.*?)"\);', html)
        changes = re.findall(r'a1.push\("/bulletin/random/(.*?)"\);', html)
        report_info = {}
        change_info = []
        if token:
            report_html, report_url = self.report_change(session, token[0], 'annual')
            report_info = self.parse_report(report_html, report_url)
        if changes:
            for change in changes:
                change_html, _ = self.report_change(session, change, 'bulletin')
                change_info.extend(self.parse_change(base_info.get('统一社会信用代码', ''), change_html))

        return {
            'gov_unit': base_info,
            'gov_unit_report': report_info,
            'gov_unit_change': change_info
        }

    def search(self, session: Session, keyword):
        for _ in range(10):
            url = f"http://search.gjsy.gov.cn/randomcode/refresh/{int(time.time() * 1000)}"
            response = self.request(session, 'GET', url, name='captcha1', raw=True)

            captcha = self.ocr.classification(response.content)
            data = {"ts": "", "c": "100000", "t": "2", "s": keyword, "random": captcha}
            text = self.request(session, 'POST', self.search_url, data=data, name='search')
            if '您所查询的单位不存在' in text:
                raise MyException('搜索为空')
            if re.search(r'(验证码不匹配|验证码输入错误)', text):
                continue
            return text
        raise MyException('验证码连续失败')

    def report_change(self, session: Session, token: str, type_: str):
        """
        report: annual
        change: bulletin
        """
        for _ in range(3):
            url = f"http://search.gjsy.gov.cn/{type_}/random/{token}"
            url2 = f"http://search.gjsy.gov.cn/randomcode/refresh/{int(time.time() * 1000)}"
            url3 = f"http://search.gjsy.gov.cn/{type_}/{'report' if type_ == 'annual' else 'detail'}/{token}"

            self.request(session, 'GET', url, name='report-first', raw=True)
            response = self.request(session, 'GET', url2, name='captcha2', raw=True)

            captcha = self.ocr.classification(response.content)
            text = self.request(session, 'POST', url3, data={"random": captcha}, name='report')
            if re.search(r'(验证码不匹配|验证码输入错误)', text):
                continue
            return text, url
        raise MyException('验证码连续失败')

    @staticmethod
    def parse(us_credit_code, html):
        try:
            soup = BeautifulSoup(html, 'lxml')
            trs = soup.select('table[width="98%"]')[0].find_all('tr')
            keys, values = [], []
            for tr in trs:
                list_ = list(tr.stripped_strings)
                if list_ and len(list_) == 2:
                    keys.append(list_[0])
                    values.append(re.sub(r'[\n\s\t\r]', '', list_[1]))
            data = dict(zip(keys, values))
            logger.success(data)
            return data
        except Exception as e:
            logger.error(f'基本信息解析失败 {us_credit_code} {traceback.format_exc()}')
            # redis_client.rpush('gov_unit_parse_base_error', us_credit_code)
            raise MyException('基本信息解析失败')

    @staticmethod
    def parse_report(html, report_url):
        us_credit_code = ''
        try:
            soup = BeautifulSoup(html, 'lxml')
            us_credit_code_and_name = soup.find(string=lambda text: text and re.search(r'[A-Z0-9]{18}(-|- | -)[\u4e00-\u9fa5]+', text)).split('-')
            us_credit_code, name = us_credit_code_and_name[0].strip(), us_credit_code_and_name[1].strip()
            pub_time = soup.find(string=lambda text: text and re.search(r'时间：\d{4}年\d{2}月\d{2}日', text)).strip().replace('时间：', '')
            year = soup.select('span[lang="EN-US"][style="font-size: 15pt;"]')[1].text

            trs = soup.select('table.MsoNormalTable tbody')[2].find_all('tr')
            keys, values = [], []
            for idx, tr in enumerate(trs[1:-1]):
                tds = [''.join(td.stripped_strings) for td in tr.find_all('td')]
                if idx < 6 or idx > 9:
                    keys.append(tds[0])
                    values.append(tds[1])
                if idx == 7:
                    keys.extend(['资产损益情况 净资产合计（所有者权益合计）' + td for td in tds])
                if idx == 8:
                    values.extend([float(td) for td in tds])
                if idx == 9:
                    keys.extend([tds[0], tds[2]])
                    values.extend([tds[1], tds[3]])
            data = dict(zip(keys, values))
            data.update({'us_credit_code': us_credit_code, 'name': name, 'pub_time': pub_time, 'year': year, 'source': report_url})
            logger.info(data)
            return data
        except Exception as e:
            logger.error(f'报告解析失败 {us_credit_code} {traceback.format_exc()}')
            # redis_client.rpush('gov_unit_parse_report_error', us_credit_code)
            return {}

    @staticmethod
    def parse_change(us_credit_code, html):
        try:
            changes = []
            soup = BeautifulSoup(html, 'lxml')
            change_time = soup.find(string=lambda text: text and re.search(r'时间：\d{4}年\d{2}月\d{2}日', text)).strip().replace('时间：', '')
            change_items = [str(i).replace('的变更', '') for i in soup.find_all(string=re.compile(r'^(.*?)的变更$'))]
            after_befores = [str(j).replace('）', '').split('（原：') for j in soup.find_all(string=re.compile(r'^(.*?)（原：(.*?)）$'))]
            for idx, change_item in enumerate(change_items):
                change_after, change_before = after_befores[idx]
                changes.append({'change_item': change_item, 'change_before': change_before, 'change_after': change_after, 'change_time': change_time})
            return changes
        except Exception as e:
            logger.error(f'变更解析失败 {us_credit_code} {traceback.format_exc()}')
            # redis_client.rpush('gov_unit_parse_change_error', us_credit_code)
            return []

    def request(self, session: Session, method: str, url: str, headers: dict = None, params: dict = None, data: Union[dict, str] = None,
                json: dict = None, path: str = None, name: str = '', tojson=False, raw=False) -> Union[dict, str, Response]:
        for i in range(15):
            response = None
            try:
                a = time.time()
                response = session.request(**{'method': method, 'url': url, 'data': data, 'headers': headers if headers else self.headers,
                                              'verify': False, 'timeout': self.timeout, 'params': params, 'json': json, 'proxies': self.proxies})
                # response.encoding = 'utf-8'

                status = response.status_code
                if status != 200 or 'Please try again!' in response.text:
                    logger.warning(f'{name}-{i + 1} --> {status}')
                    del session.cookies['proxyBase']
                    continue
                if raw:
                    return response

                logger.success(f'{name} --> {response.status_code}-->time: {time.time() - a}')
                a = response.text.replace("\n", "").replace('\r', '').replace('\t', '')
                # logger.info(f'{name} --> {a}')

                if tojson:
                    return response.json()
                return response.text
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'continue {name}-{i + 1} exception: {e}')
                del session.cookies['proxyBase']
                continue
            except Exception as e:
                status = response.status_code if not response else "空"
                text = response.text if not response else "空"
                logger.warning(f'continue{i} 状态码：{status} res: {text} exception: {traceback.format_exc()}')
                del session.cookies['proxyBase']
                continue

        raise MyException('接口连续失败')


if __name__ == '__main__':
    pass
