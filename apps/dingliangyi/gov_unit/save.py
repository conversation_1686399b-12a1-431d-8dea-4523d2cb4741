from resx.config import *
from resx.mysql_dao import MySQLDao
from pydantic import Field, conint
from typing import Optional
from datetime import datetime
from resx.base_model import BaseModel


class GovUnit(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    base: str = Field(default='')
    company_id: Optional[conint(strict=True, ge=0)] = Field(default=0)
    name: str = Field(default='')
    old_cert: Optional[str] = Field(default=None)
    us_credit_code: str = Field(default='')
    scope: str = Field(default='')
    address: str = Field(default='')
    legal_person: str = Field(default='')
    expend_source: str = Field(default='')
    reg_capital: str = Field(default='')
    reg_unit_name: str = Field(default='')
    hold_unit: str = Field(default='')
    reg_unit: str = Field(default='')
    valid_time: str = Field(default='')
    reg_time: Optional[str] = Field(default=None)
    reg_status: str = Field(default='')
    reg_unit_name_second: str = Field(default='')
    reg_unit_name_third: str = Field(default='')
    reg_unit_name_other: str = Field(default='')
    name_second: str = Field(default='')
    name_third: str = Field(default='')
    name_other: str = Field(default='')
    source: str = Field(default='http://search.gjsy.gov.cn/wsss/query')
    create_time: Optional[datetime] = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    update_time: Optional[datetime] = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    craw_num: int = Field(default=1)
    is_deleted: int = Field(default=0)

    # model_config = ConfigDict(populate_by_name=True, json_encoders={datetime: lambda dt: dt.strftime('%Y-%m-%d %H:%M:%S')})

    def __init__(self, init=False, **data):
        if init:
            field_mapping = {
                '名称': 'name',
                '统一社会信用代码': 'us_credit_code',
                '宗旨和业务范围': 'scope',
                '住所': 'address',
                '法定代表人': 'legal_person',
                '经费来源': 'expend_source',
                '开办资金': 'reg_capital',
                '举办单位': 'reg_unit_name',
                '登记管理机关': 'hold_unit',
                '有效期': 'valid_time',
                '单位状态': 'reg_status'
            }
            mapped_data = {}
            for key, value in data.items():
                mapped_key = field_mapping.get(key, key)
                mapped_data[mapped_key] = value
            super().__init__(**mapped_data)
        else:
            super().__init__(**data)

        # self.base = self.get_base_by_credit_code(self.us_credit_code)
        # self.expend_source = self.replace_bracket(self.expend_source)
        # self.name = self.replace_bracket(self.name)

    class Config:
        populate_by_name = True
        json_encoders = {datetime: lambda dt: dt.strftime('%Y-%m-%d %H:%M:%S')}


dao = MySQLDao(**CFG_MYSQL_ZX_RDS111, db_tb_name='data_experience_situation.gov_unit', ignore_fields=['id', 'company_id', 'create_time'], entity_class=GovUnit,
               primary_index_fields=(['us_credit_code'], []))


def save(data: dict):
    dao.save(GovUnit(init=True, **data))
