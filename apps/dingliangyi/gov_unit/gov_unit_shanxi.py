import random
import ddddocr
import schedule
import time
from loguru import logger
import traceback
from bs4 import BeautifulSoup
import re
from tqdm import tqdm
from requests import Response, Session

from df_spiders.gov_unit_base import GovUnitSpider
from search_gov_unit.gov_search import GovUint


class GovUnitShanxiSpider(GovUnitSpider):
    def __init__(self):
        super().__init__()
        self.host = 'http://djgl.sxbb.gov.cn'

    def get_list(self):
        for i in range(1869, 1, -1):
            url = f"http://djgl.sxbb.gov.cn/djgl/search/sydwByPage/{i}-14"
            data = {"keywords": "", "djglj.id": "", "djglj.regionCode": "", "djglj.level": "", "flag": "djgl"}
            res: str = self.request(self.new_session(), 'POST', url, data=data, name=f'get_all_list-{i}')
            if '暂无数据' in res:
                break
            yield self.parse_list(res)

    def search(self, path):
        url = f"{self.host}{path}"
        res: str = self.request(self.new_session(), 'GET', url, name=f'search-{path}')
        soup = BeautifulSoup(res, 'lxml')
        trs = soup.select('tr')
        data = {}
        for tr in trs:
            list_ = list(tr.stripped_strings)
            if len(list_) == 2:
                data[list_[0]] = list_[1]
        data['source'] = url
        return self.parse(data)

    @staticmethod
    def parse(data):
        mapped_data = {
            "名称": data.get('单位名称', ''),
            "统一社会信用代码": data.get('统一社会信用代码', ''),
            "宗旨和业务范围": data.get('宗旨和业务范围', ''),
            "住所": data.get('单位住所', ''),
            "法定代表人": data.get('法定代表人', ''),
            "经费来源": data.get('经费来源', ''),
            "开办资金": f"{data['开办资金'][1:]}万元" if data.get('开办资金') else '',
            "举办单位": data.get('举办单位', ''),
            "登记管理机关": data.get('登记管理机关', ''),
            "source": data.get('source', '')
        }
        return mapped_data

    @staticmethod
    def parse_list(html):
        soup = BeautifulSoup(html, 'lxml')
        lis = soup.select('li a')
        list_ = []
        for li in lis:
            a = [li.text.strip(), li['href']]
            list_.append(a)
        return list_

    def main_schedule(self):
        generator = self.get_list()
        for idx, list_ in enumerate(generator):
            logger.info(f'总共{len(list_)}条数据')
            for item in tqdm(list_, desc=f'第{idx}页'):
                try:
                    is_exist = self.check_in_db2(name=item[0])
                    if self.is_increment and is_exist:
                        break
                    if not is_exist:
                        data = self.search(item[1])
                        is_exist = self.check_in_db2(data["统一社会信用代码"], data['名称'])
                        if not is_exist:
                            info = {}
                            try:
                                info = GovUint().main(Session(), data['统一社会信用代码'])
                            except Exception as e:
                                logger.error(f"{item} {e}")
                            data = info if info else {'gov_unit': data}
                            self.to_kafka(data)
                            self.sum += 1
                            logger.info(f'新增或变更数据: {data}')
                        else:
                            logger.info(f'已存在数据: {data}')
                except Exception as e:
                    self.custom_traceback(e)

    def main(self):
        self.main_schedule()
        logger.success(f'新增{self.sum}条数据')

        logger.info('Start schedule')
        schedule.every().day.at("10:00").do(self.main_schedule)
        while True:
            schedule.run_pending()
            time.sleep(1)


if __name__ == '__main__':
    # 新增 351 + 71
    spider = GovUnitShanxiSpider()
    spider.main()
