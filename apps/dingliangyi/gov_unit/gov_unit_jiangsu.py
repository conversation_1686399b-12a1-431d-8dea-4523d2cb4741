import random
import ddddocr
from loguru import logger
import traceback

from apps.dingliangyi.gov_unit.gov_unit_base import GovUnitSpider
from apps.dingliangyi.gov_unit.save import save


class GovUnitJiangsuSpider(GovUnitSpider):
    def __init__(self):
        super().__init__()
        self.headers = {
            # "Origin": "https://www.jssbb.gov.cn",
            # "Referer": "https://www.jssbb.gov.cn/sydjweb/sydwInfoDetail?unifyCode=12321012MB1W14286C",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36",
        }
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        self.timeout = 60

    def get_list(self):
        for i in range(1, 100):
            url = "https://djgl.jssbb.gov.cn:8096/sydjweb/api/admin/service/sydw/sydwproclaiminfo/uiQuerySydwProclaimSlDataNew"
            params = {
                "certiFlag": "1",
                "groupRadio": "xjFlag",
                "districtCode": "320000",
                "checkValue": "0",
                "pageNo": str(i),
                "pageSize": "100",
                "totalRows": "22647"
            }
            session = self.new_session()
            session.proxies = self.get_long_proxy()
            res: dict = self.request(session, "GET", url, params=params, name=f"get_list{i}", tojson=True)
            if res['data']['objList']:
                yield from res['data']['objList']
            else:
                return []

    def search_by_usci(self, keyword) -> dict:
        session = self.new_session()
        for _ in range(20):
            url = "https://djgl.jssbb.gov.cn:8096/api/admin/service/sbsp/internetuserinfo/getSearchCodeImage"
            params = {"_i": f"{random.uniform(0, 1):.16f}"}
            res = self.request(session, "GET", url, params=params, name="get_search_code_image", toRaw=True)
            verify = self.ocr.classification(res.content)
            url = f"https://djgl.jssbb.gov.cn:8096/sydjweb/api/admin/service/sbsp/internetuserinfo/checkSearchCodeImage/{verify}"
            res = self.request(session, 'POST', url, name='verify', tojson=True)
            if not res['data']:
                continue
            url = f"https://djgl.jssbb.gov.cn:8096/sydjweb/api/admin/service/sydw/sydwinfo/getSearchSydwInfo/{keyword}/{verify}"
            res = self.request(session, 'GET', url, name='search', tojson=True)
            if not res['data']:
                continue
            return res['data']

    @staticmethod
    def parse(data: dict):
        mapped_data = {
            "名称": data.get('sydwNameFirst', ''),
            "统一社会信用代码": data.get('unifyCode', ''),
            "宗旨和业务范围": data.get('tenetOperation', ''),
            "住所": data.get('address', ''),
            "法定代表人": data.get('fddbrName', ''),
            "经费来源": data.get('assetsSourceText', ''),
            "开办资金": f"{data.get('assetsSum')}万元" if data.get('assetsSum') else '',
            "举办单位": data.get('jbdwName', ''),
            "有效期": f"自{data.get('certiDateFrom', '')}至{data.get('certiDateTo', '')}",
            "单位状态": data.get('sydwStateText', ''),
            "登记管理机关": data.get('groupName', ''),
            "source": f"https://www.jssbb.gov.cn/sydjweb/sydwInfoDetail?unifyCode={data.get('unifyCode', '')}"
        }
        return mapped_data

    def main_schedule(self, is_increment=False):
        list_ = self.get_list()
        # for i in list_:
        for item in list_:
            try:
                if not item.get('unifyCode') or not item.get('sydwNameFirst'):
                    continue
                is_exist = self.check_in_db(item['unifyCode'])
                if is_increment and is_exist:
                    break
                if not is_exist:
                    data = self.search_by_usci(item['unifyCode'])
                    if not data:
                        continue
                    data = self.parse(data)
                    save(data)
                    logger.info(f'新增数据: {data}')
            except Exception as e:
                logger.error(f'{item.get("unifyCode")} - {item.get("sydwNameFirst")} - {traceback.format_exc()}')

    def main(self):
        self.main_schedule(is_increment=False)

        # schedule.every().day.at("10:00").do(self.main_schedule, is_increment=True)
        # while True:
        #     schedule.run_pending()
        #     time.sleep(1)


if __name__ == '__main__':
    spider = GovUnitJiangsuSpider()
    spider.main()
