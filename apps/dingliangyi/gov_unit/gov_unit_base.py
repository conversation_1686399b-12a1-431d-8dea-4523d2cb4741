from abc import <PERSON><PERSON><PERSON>, abstractmethod, <PERSON>
from typing import Union
from datetime import datetime
from requests import Session, Response
from loguru import logger
from requests.adapters import HTT<PERSON>dapter
import time
import re
from resx.tools import BoundedExecutor
import requests
import urllib3
import random
from collections import OrderedDict
import traceback
from resx.mysql_dao import MySQLDao
from resx.config import *
from apps.dingliangyi.gov_unit.gov_search import GovUint

urllib3.disable_warnings()


class GovUnitSpider(metaclass=ABCMeta):  # class Abstract(ABC):

    def __init__(self):
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 "
                          "Safari/537.36",
        }
        logger.add(sink=r'./logs/' + f'{self.__class__.__name__}' + '_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8',
                   enqueue=True)
        self.timeout = 3
        self.session = requests.session()
        self.session.proxies = {
            'http': 'http://10.99.138.95:30636',
            'https': 'http://10.99.138.95:30636'
        }
        self.session.mount('http://', HTTPAdapter(max_retries=1))
        self.session.mount('https://', HTTPAdapter(max_retries=1))
        self.mysql_111 = MySQLDao(**CFG_MYSQL_ZX_RDS111, db_tb_name='data_experience_situation.gov_unit')
        self.is_increment = False
        self.sum = 0
        self.first_page = False

    def check_in_db(self, us_credit_code=None, name=None) -> bool:
        """
        False name、us_credit_code变化 新增
        """
        if name:
            if self.mysql_111.get(name=name, is_deleted=0):
                return True
        if self.mysql_111.get(us_credit_code=us_credit_code, is_deleted=0):
            return True
        return False

    @staticmethod
    def new_session():
        session = requests.session()
        session.proxies = {
            'http': 'http://10.99.138.95:30636',
            'https': 'http://10.99.138.95:30636'
        }
        session.mount('http://', HTTPAdapter(max_retries=1))
        session.mount('https://', HTTPAdapter(max_retries=1))
        return session

    @staticmethod
    def get_long_proxy():
        res = requests.get('http://10.99.192.206:8015/long-proxy')
        proxy = random.choice(res.text.split('\r\n'))
        return {
            'http': f'http://{proxy}',
            'https': f'http://{proxy}'
        }

    @staticmethod
    def datatime_format(date_str: str):
        if not date_str:
            return ''
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        formatted_date = date_obj.strftime("%Y年%m月%d日")
        return formatted_date

    @staticmethod
    def replace_bracket(name, cn_to_en=False):
        if not name:
            return ''
        if cn_to_en:
            return name.replace('（', '(').replace('）', ')')
        return name.replace('(', '（').replace(')', '）')

    @staticmethod
    def search_master(keyword):
        try:
            return GovUint().main(Session(), keyword)
        except Exception as e:
            logger.warning(f'搜索主体失败 {keyword} {e}')
            return {}

    @staticmethod
    def timestamp_to_date(timestamp: int):
        timestamp = timestamp / 1000
        return datetime.fromtimestamp(timestamp).strftime("%Y年%m月%d日")

    @staticmethod
    def unique(my_list, exclude_field: str = ''):
        unique_list = []
        # history = {}
        for d in my_list:
            filtered_items = tuple((k, v) for k, v in sorted(d.items()) if k != exclude_field)
            unique_list.append(filtered_items)
            # history.update({d["uniscid"]: d[exclude_field]})
        unique_list = list(OrderedDict.fromkeys(unique_list))
        data = []
        for t in unique_list:
            d = dict(t)
            # d.update({exclude_field: history[d['uniscid']]})
            data.append(d)
        return data

    @staticmethod
    def custom_traceback(e):
        exc_type = type(e)
        exc_value = e
        tb = e.__traceback__
        """
        自定义打印 traceback，仅显示用户代码的报错信息
        """
        # 提取 traceback 信息
        tb_list = traceback.extract_tb(tb)
        user_tb = [frame for frame in tb_list if 'pygs-work-parent' in frame.filename]
        error = ''
        if user_tb:
            print("Traceback (most recent call last):")
            # 格式化并打印用户代码的 traceback 信息
            print("".join(traceback.format_list(user_tb)))
            print(f"{exc_type.__name__}: {exc_value}")
            error += "".join(traceback.format_list(user_tb)) + f"{exc_type.__name__}: {exc_value}"
        else:
            print(f"{exc_type.__name__}: {exc_value}")
            error += f"{exc_type.__name__}: {exc_value}"
        return error

    def request(self, session: Session, method: str, url: str, params: dict = None, data: dict = None,
                json: dict = None, path: str = None, name: str = '', tojson=False, toRaw=False, long_proxy=False) -> Union[dict, str, Response]:
        for i in range(20):
            response = None
            try:
                a = time.time()
                request_params = {'method': method, 'url': url, 'data': data, 'headers': self.headers,
                                  'verify': False, 'timeout': self.timeout, 'params': params, 'json': json}
                if long_proxy:
                    request_params['proxies'] = self.get_long_proxy()
                    request_params['timeout'] = 60 * 2
                response = session.request(**request_params)
                response.encoding = 'utf-8'

                if response.status_code != 200:
                    logger.warning(f'{name} --> {response.status_code}')
                    del session.cookies['proxyBase']
                    continue
                logger.success(f'{name} --> {response.status_code} --> time: {time.time() - a}')

                if toRaw:
                    return response

                if name not in ['get_establish_list', 'get_report_list']:
                    a = re.sub(r'[\n\r\t]', '', response.text)
                    logger.info(f'{name} --> {a}')
                if tojson:
                    return response.json()
                return response.text
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'{name} --> continue{i} exception: {e}')
                del session.cookies['proxyBase']
                continue
            except Exception as e:
                status = response.status_code if not response else "空"
                text = response.text if not response else "空"
                logger.warning(f'{name} --> continue{i} 状态码：{status} res: {text} exception: {e}')
                del session.cookies['proxyBase']
                continue
