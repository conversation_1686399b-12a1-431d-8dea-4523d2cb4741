import schedule
import time
from loguru import logger
from bs4 import BeautifulSoup
import re
from tqdm import tqdm
from requests import Response, Session
from datetime import datetime

from apps.dingliangyi.gov_unit.gov_unit_base import GovUnitSpider
from apps.dingliangyi.gov_unit.gov_search import GovUint
from apps.dingliangyi.gov_unit.save import save


class GovUnitZhejiang(GovUnitSpider):
    def __init__(self, columnid_report, unitid_report, columnid_establish, unitid_establish):
        super().__init__()
        self.timeout = 5
        self.host = 'https://www.zjjgbz.gov.cn'
        self.headers = {
            # "Origin": "https://www.zjjgbz.gov.cn",
            # "Referer": "https://www.zjjgbz.gov.cn/col/col1229761078/index.html",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        }
        self.columnid_establish = columnid_establish
        self.unitid_establish = unitid_establish

        self.columnid_report = columnid_report
        self.unitid_report = unitid_report

    def get_establish_list(self) -> list:
        url = "https://www.zjjgbz.gov.cn/module/jpage/dataproxy.jsp"
        params = {"startrecord": "1", "endrecord": "60", "perpage": "20"}
        data = {"col": "1", "appid": "1", "webid": "3877", "path": "/", "columnid": self.columnid_establish, "sourceContentType": "1",
                "unitid": self.unitid_establish, "webname": "中共浙江省委机构编制委员会办公室", "permissiontype": "0"}
        res = self.request(self.new_session(), "POST", url, params=params, data=data, name='get_establish_list')
        soup = BeautifulSoup(res, 'lxml')
        as_ = soup.select('a')
        list_ = []
        for i in as_:
            a = i.parent.parent.find(string=lambda text: text and re.search(r'\d{4}-\d{2}-\d{2}', text))
            list_.append([i.get('href'), i.text.strip()[:-4], a.strip()])
        return list_

    def get_report_list(self, startrecord=0) -> list:
        url = "https://www.zjjgbz.gov.cn/module/jpage/dataproxy.jsp"
        page = 100
        params = {"startrecord": 1 + page * startrecord, "endrecord": 1 + page * startrecord + (page - 1), "perpage": page}
        data = {"col": "1", "appid": "1", "webid": "3877", "path": "/", "columnid": self.columnid_report, "sourceContentType": "1",
                "unitid": self.unitid_report, "webname": "中共浙江省委机构编制委员会办公室", "permissiontype": "0"}
        res = self.request(self.new_session(), "POST", url, params=params, data=data, name='get_report_list', long_proxy=True)
        soup = BeautifulSoup(res, 'lxml')
        as_ = soup.select('a')
        list_ = []
        for i in as_:
            a = i.parent.parent.find(string=lambda text: text and re.search(r'\d{4}-\d{2}-\d{2}', text))
            list_.append([i.get('href'), i.text.strip(), a.strip()])
        return list_

    def get_detail(self, path: str):
        url = f"{self.host}{path}"
        res = self.request(self.new_session(), "GET", url)
        soup = BeautifulSoup(res, 'lxml')
        trs = soup.select('tr')
        key, value = [], []
        for tr in trs[2:8]:
            strings = list(tr.stripped_strings)
            filtered_strings = [s for s in strings if s != '>']
            key.extend(filtered_strings[0::2])
            value.extend(filtered_strings[1::2])
        data = dict(zip(key, value))
        data['source'] = url
        return self.parse(data)

    def parse(self, data: dict):
        mapped_data = {
            "名称": data.get('单位名称', ''),
            "统一社会信用代码": data.get('统一社会信用代码', ''),
            "宗旨和业务范围": data.get('宗旨和业务范围', ''),
            "住所": data.get('住所', ''),
            "法定代表人": data.get('法定代表人', ''),
            "经费来源": data.get('经费来源', ''),
            "开办资金": f"{data['开办资金（万元）']}万元" if data.get('开办资金（万元）') else (data['开办资金'] if data.get('开办资金') else ''),
            "举办单位": data.get('举办单位', ''),
            # "有效期": f"自{self.datatime_format(date[0])}至{self.datatime_format(date[1])}" if date else '',
            "单位状态": '正常',
            # "登记管理机关": data.get('登记管理机关：', ''),
            "reg_time": data.get('设立时间', ''),
            "source": data.get('source', '')
        }
        return mapped_data

    def main_schedule(self):
        for j in range(20):
            list_ = self.get_report_list(j)
            logger.info(f'总共{len(list_)}条数据')
            for item in tqdm(list_):
                try:
                    if datetime.strptime(item[2], '%Y-%m-%d').year < 2024:
                        return
                    is_exist = self.check_in_db(name=item[1])
                    if self.is_increment and is_exist:
                        break
                    if not is_exist:
                        data = self.get_detail(item[0])
                        is_exist = self.check_in_db2(data["统一社会信用代码"], data['名称'])
                        if not is_exist:
                            info = {}
                            try:
                                info = GovUint().main(Session(), data['统一社会信用代码'])
                            except Exception as e:
                                self.custom_traceback(e)
                                logger.error(f"{item} {e}")
                            data = info if info else {'gov_unit': data}
                            self.to_kafka(data)
                            logger.info(f'新增或变更数据: {data}')
                        else:
                            logger.info(f'已存在数据: {data}')
                except Exception as e:
                    self.custom_traceback(e)

    def main_schedule2(self):
        list_ = self.get_establish_list()
        logger.info(f'总共{len(list_)}条数据')
        for item in tqdm(list_):
            try:
                if datetime.strptime(item[2], '%Y-%m-%d').year < 2024:
                    break
                is_exist = self.check_in_db(name=item[1])
                if self.is_increment and is_exist:
                    break
                if not is_exist:
                    data = self.get_detail(item[0])
                    is_exist = self.check_in_db(data["统一社会信用代码"])
                    if not is_exist:
                        info = {}
                        try:
                            info = GovUint().main(Session(), data['统一社会信用代码'])
                        except Exception as e:
                            self.custom_traceback(e)
                            logger.error(f"{item} {e}")
                        data = info['gov_unit'] if info else data
                        save(data)
                        logger.info(f'新增或变更数据: {data}')
                    else:
                        logger.info(f'已存在数据: {data}')
            except Exception as e:
                self.custom_traceback(e)

    def main(self):
        # self.main_schedule()
        self.main_schedule2()

        # schedule.every().monday.at("10:30").do(self.main_schedule2)


def main():
    spider1 = GovUnitZhejiang('1229760642', '8761080', '1229761049', '8705087')
    spider1.main()

    spider2 = GovUnitZhejiang('1229761098', '8759242', '1229761078', '8767799')
    spider2.main()

    spider3 = GovUnitZhejiang('1229761160', '8761554', '1229761140', '8770054')
    spider3.main()

    spider4 = GovUnitZhejiang('1229761252', '8762680', '1229761232', '8772511')
    spider4.main()

    spider5 = GovUnitZhejiang('1229761200', '8761712', '1229761180', '8772047')
    spider5.main()

    spider6 = GovUnitZhejiang('1229761290', '8763915', '1229761270', '8773241')
    spider6.main()

    spider7 = GovUnitZhejiang('1229761327', '8763922', '1229761307', '8773414')
    spider7.main()

    spider8 = GovUnitZhejiang('1229761514', '8763957', '1229761494', '8776984')
    spider8.main()

    spider9 = GovUnitZhejiang('1229761438', '8763943', '1229761418', '8774272')
    spider9.main()

    spider10 = GovUnitZhejiang('1229761364', '8763929', '1229761344', '8773728')
    spider10.main()

    spider11 = GovUnitZhejiang('1229761401', '8763936', '1229761381', '8774038')
    spider11.main()


if __name__ == '__main__':
    main()

    # logger.info('Start schedule')
    # schedule.every().monday.at("10:30").do(main)
    #
    # while True:
    #     schedule.run_pending()
    #     time.sleep(1)

    #            年报            登记
    # 浙江省 1229760642,8761080 1229761049,8705087
    # 杭州市 1229761098,8759242 1229761078,8767799
    # 温州市 1229761160,8761554 1229761140,8770054
    # 嘉兴市 1229761252,8762680 1229761232,8772511
    # 湖州 1229761200,8761712  1229761180,8772047
    # 金华市 1229761290,8763915 1229761270,8773241
    # 丽水市 1229761327,8763922 1229761307,8773414
    # 舟山市 1229761514,8763957 1229761494,8776984
    # 台州市 1229761514,8763957 1229761494,8776984
    # 绍兴市 1229761438,8763943 1229761418,8774272
    # 宁波市 1229761364,8763929 1229761344,8773728
    # 衢州市 1229761401,8763936 1229761381,8774038
