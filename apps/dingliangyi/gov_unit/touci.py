import json
import requests
from loguru import logger
from requests import Session
import time
from typing import Union
from random import shuffle
from bs4 import BeautifulSoup
import re
import aiohttp
import asyncio
import sys
from functools import partial
from concurrent.futures import Future
import traceback
import schedule

from district import DISTRICTS, ERROR2RIGHT, ERROR2RIGHT2
from libs.concurrent import BoundedExecutor
from clients.redis._redis import Redis
from clients.mysql_client import MySQLClient
from apps.dingliangyi.common2 import touci

if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
logger.remove()
logger.add(sink=r'./logs/gov_unit_word_{time:YYYY_MM_DD}.log', rotation='00:00', retention='1 days', encoding='utf8', level='INFO', enqueue=True)
logger.add(sys.stderr, level="INFO")
args = sys.argv


# todo 公告没有上海市（310000）、贵州省（520000）、      广东省（440000）、江苏省（320000）、浙江省（330000）、山东省（370000）、陕西省（610000）数据

class MyException(Exception):

    def __init__(self, message):
        self.message = message
        super().__init__(self.message)


class GovWord:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36',
        }
        self.proxies = {
            'http': 'http://************:30636',
            'https': 'http://************:30636'
        }
        self.timeout = 5
        self.redis_client = Redis(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com', password="3lvadmpiSj61ge", db=9)
        self.mysql_client = MySQLClient(**{
            'host': '886a213ba5de451eb4add2e0d1fb2ef6in01.internal.cn-north-4.mysql.rds.myhuaweicloud.com',
            'user': 'jdhw_d_zhuan_dml',
            'password': 'kmbpZRTr1pooyB9'
        })
        self.key = "gov_unit2"
        # self.key = "gov_unit_test"
        self.host = 'http://search.gjsy.gov.cn:9090'

    def main(self, province, if_increment=0):
        url = f'{self.host}/bulletin/all/{province}/%s'
        urls = []
        for i in range(1):
            page = 0
            end = True
            while end:
                page += 1
                html = self.request(Session(), 'GET', url % i, params={"page": str(page)}, name=f'province-{province}-page({i})')
                if not html:
                    break
                if page >= int(re.findall(r'共(\d+)页', html)[0]):
                    end = False
                soup = BeautifulSoup(html, 'lxml')
                as_ = soup.select('a.blue14')
                for a in as_:
                    if if_increment:
                        us_credit_codes_temp = self.detail([a['href']])
                        if self.exist_in_db(us_credit_codes_temp[0]):
                            logger.success(f'增量处理')
                            break
                    urls.append(a['href'])

        us_credit_codes = self.detail_by_aio(urls)
        logger.info(f'us_credit_codes: {us_credit_codes}')

        for us_credit_code in us_credit_codes:
            if not self.exist_in_db(us_credit_code):
                touci(us_credit_code, 'institution_gj')

    def detail(self, urls: list):
        us_credit_codes = []
        for url in urls:
            url = f'{self.host}{url}'
            html = self.request(Session(), 'GET', url, name=f'url-{url}')
            soup = BeautifulSoup(html, 'lxml')
            a = soup.find_all(string=lambda text: text and re.search(r'[A-Z0-9]{18}', text))
            for i in a:
                us_credit_code = re.findall(r'([A-Z0-9]{18})', i)
                us_credit_code and us_credit_codes.append(us_credit_code[0])
        return us_credit_codes

    def detail_by_aio(self, urls_: list):
        us_credit_codes = []

        async def main():
            urls = urls_
            while urls:
                urls__ = urls[:10]
                urls = urls[10:]
                tasks = [asyncio.create_task(self.search_retail(url), name=f"{url}") for url in urls__]
                done, _ = await asyncio.wait(tasks, return_when=asyncio.ALL_COMPLETED)
                for task in done:
                    if task.exception() is None:
                        soup = BeautifulSoup(task.result(), 'lxml')
                        a = soup.find_all(string=lambda text: text and re.search(r'[A-Z0-9]{18}', text))
                        for i in a:
                            us_credit_code = re.findall(r'([A-Z0-9]{18})', i)
                            us_credit_code and us_credit_codes.append(us_credit_code[0])
                    else:
                        url = task.get_name()
                        urls.append(url)
                        logger.warning(f"url：{url} failed 添加到数组 {task.exception()}")

        asyncio.run(main())
        return us_credit_codes

    async def search_retail(self, url):
        url = f'{self.host}{url}'
        for i in range(5):
            try:
                connector = aiohttp.TCPConnector(ssl=False)
                timeout = aiohttp.ClientTimeout(total=self.timeout)
                proxy = self.proxies['http']
                async with aiohttp.ClientSession(connector=connector) as session:
                    response = await session.get(url, headers=self.headers, timeout=timeout, proxy=proxy)
                    status = response.status
                    text = await response.text()
                    if status != 200 or 'Please try again!' in text:
                        logger.warning(f'continue {url}-{i + 1} --> {status}')
                        continue
                    logger.info(f'{url} --> {status}')
                    return text
            except Exception as e:
                logger.warning(f'continue {url}-{i + 1} exception: {e}')
        raise Exception('超过5次')

    def exist_in_db(self, us_credit_code):
        sql = f"select * from data_experience_situation.gov_unit where us_credit_code = '{us_credit_code}' limit 1"
        return bool(self.mysql_client.select(sql))

    def request(self, session: Session, method: str, url: str, headers: dict = None, params: dict = None, data: Union[dict, str] = None,
                json: dict = None, path: str = None, name: str = '', tojson=False) -> Union[dict, str]:

        for i in range(15):
            response = None
            try:
                a = time.time()
                response = session.request(
                    **{'method': method, 'url': url, 'data': data, 'headers': headers if headers else self.headers, 'verify': False, 'timeout': self.timeout,
                       'params': params, 'json': json, 'proxies': self.proxies})

                status = response.status_code
                if status != 200 or 'Please try again!' in response.text:
                    logger.warning(f'{name}-{i + 1} --> {status}')
                    del session.cookies['proxyBase']
                    continue

                # logger.success(f'{name} --> {response.status_code}-->time: {time.time() - a}')
                # a = response.text.replace("\n", "").replace('\r', '').replace('\t', '')
                # logger.info(f'{name} --> {a}')

                if tojson:
                    return response.json()
                return response.text
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'continue {name}-{i + 1} exception: {e}')
                del session.cookies['proxyBase']
                continue
            except Exception as e:
                status = response.status_code if not response else "空"
                text = response.text if not response else "空"
                logger.warning(f'continue{i} 状态码：{status} res: {text} exception: {e}')
                del session.cookies['proxyBase']
                continue

        # raise MyException('接口连续失败')
        return ''


def main():
    gov_word = GovWord()
    # gov_word.main('350213', if_increment=False)
    # todo 默认是不增量的

    executor = BoundedExecutor(20)
    shuffle(DISTRICTS)
    for district, address in DISTRICTS:
        if str((district, address)) in ERROR2RIGHT:
            district = eval(ERROR2RIGHT[str((district, address))])[0]
        if address in ERROR2RIGHT2:
            district = ERROR2RIGHT2[address]
        executor.submit(gov_word.main, district, 0)
    logger.success('公告列表爬取完成')


if __name__ == '__main__':
    main()

    schedule.every(3).day.at("07:00").do(main)
    while True:
        schedule.run_pending()
        time.sleep(1)
