import requests
from bs4 import BeautifulSoup
import dateparser
from gpt_api import extract_image_json

from resx.config import *
from resx.mysql_dao import MySQLDao

headers = {
    "Referer": "https://www.cac.gov.cn/",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
}
dao = MySQLDao(**CFG_MYSQL_ZX_RDS111, db_tb_name='data_experience_situation.app_info', primary_index_fields=(['app_name', 'put_time'], []),
               ignore_fields=['id', 'company_id', 'update_time'])

url = "https://search.cac.gov.cn/cms/cmsadmin/infopub/gjjs.jsp"
params = {
    "sort": "1", "pubpath": "portal", "webappcode": "A09", "templetid": "1563339473064626", "pubtype": "S",
    "advance": "true", "huopro": "app", "mustpro": "的通报", "notpro": "", "inpro": "", "searchdir%22": "",
    "startDate": "", "endDate": "", "searchfield": "topic"
}
response = requests.get(url, headers=headers, params=params)
# print(response.text)
soup = BeautifulSoup(response.text, 'lxml')
# lis = soup.select('ul.xpage-content.xpage-content-page li')
lis = [
    'www.cac.gov.cn/2021-12/14/c_1641080798914299.htm',
    'www.cac.gov.cn/2021-08/29/c_1631828989708393.htm',
    'www.cac.gov.cn/2021-06/11/c_1624994586637626.htm',
]
for i in lis:  #
    # print(i.select_one('a').get('href')[2:], i.get_text(strip=True))

    print(i)
    res = requests.get('http://' + i, headers=headers)
    soup_ = BeautifulSoup(res.text, 'lxml')
    put_time = soup_.select_one('#pubtime').get_text(strip=True)
    put_time_ = dateparser.parse(put_time).date().strftime('%Y-%m-%d')
    imgs = soup_.select('#BodyLabel img')
    images = [img.get('src')[2:] for img in imgs]
    images_ = []
    for img in images:
        for _ in range(3):
            try:
                res_img = requests.get('https://' + img, headers=headers)
                if res_img.status_code == 200:
                    images_.append(res_img.content)
                    break
            except:
                print(f"Failed to fetch image: {img}")

    for img in images_:
        info = extract_image_json([img])
        for j in info['apps']:
            b = {
                'app_name': j['app_name'],
                'developer': j['developer'],
                'source': j['source'],
                'version': j['version'],
                'issues': '、'.join(j['issues']),
                'put_time': put_time_,
            }
            dao.save(b)
