import json
import requests
from bs4 import BeautifulSoup
import os
import re
from loguru import logger
from dateutil import parser
from gpt_api import extract_image_json, establish_mapping_json
from tqdm import tqdm
from urllib.parse import urlparse

from resx.config import *
from resx.mysql_dao import MySQLDao
from app import parse_docx_tables

dao = MySQLDao(**CFG_MYSQL_ZX_RDS111, db_tb_name='data_experience_situation.app_info', primary_index_fields=(['app_name', 'put_time'], []),
               ignore_fields=['id', 'company_id', 'update_time'])

headers = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "cache-control": "no-cache",
    "dnt": "1",
    "pragma": "no-cache",
    "priority": "u=0, i",
    "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "none",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
}
cookies = {
    "rewardsn": "",
    "wxtokenkey": "777"
}

urls = [

]

urls_table = [

]


def parse_html_tables(table):
    # 提取所有行数据
    rows = []
    for row in table.find_all("tr"):
        cells = row.find_all(["th", "td"])
        row_data = [cell.get_text(strip=True) for cell in cells]
        if row_data:
            rows.append(row_data)

    if not rows:
        return

        # 判断是否存在表头：如果表中存在 th 标签，则认为第一行为表头
    headers = rows[0] if table.find("th") else None
    data_rows = rows[1:] if headers else rows

    # 确定预期列数
    expected_cols = len(headers) if headers else max(len(r) for r in data_rows)

    # 合并因解析错误而拆分的行
    merged_rows = []
    for row in data_rows:
        if len(row) == expected_cols:
            merged_rows.append(row)
        # 如果行的单元格不足，认为是上一行的补充
        elif len(row) < expected_cols and merged_rows:
            # 以空格将内容拼接到上一行最后一列
            merged_rows[-1][-1] += " " + " ".join(row)
        else:
            merged_rows.append(row)

    # 获取表头
    headers_ = merged_rows[0]
    # 获取数据行
    rows = merged_rows[1:]
    # 转换为字典列表
    result = [{headers_[i]: row[i] for i in range(len(headers_))} for row in rows]
    logger.info(result)

    return result, headers_


def deal_image(urls, gov=False):
    for url_ in tqdm(urls, desc='Processing URLs'):
        url = url_[1]
        try:
            response = requests.get(url, headers=headers, cookies=cookies)
            soup = BeautifulSoup(response.text, 'lxml')

            if '该内容已被发布者删除' in response.text:
                logger.info(f'该内容已被发布者删除: {url}')
                continue

            link = soup.find('a', string='阅读原文')
            if link:
                ori_url = re.search(r"var msg_source_url = '(.*?)';", response.text).group(1)
                logger.warning(f'Found "阅读原文" link: {url} -> {ori_url}')
                ori_urls.append(ori_url)
                continue

            if gov:
                publish_time_str = re.search(r'发布时间：(.*?)<', response.text).group(1)
            else:
                publish_time_str = re.search(r"var createTime = '(.*?)';", response.text).group(1)

            as_ = soup.find_all(name='a', href=lambda href: href and re.search(r'doc|docx$', href))
            if as_:
                for idx, a_tag in enumerate(as_):
                    docx_url = a_tag['href']
                    parsed_url = urlparse(url)
                    docx_url = docx_url if docx_url.startswith('http') else f"{parsed_url.scheme}://{parsed_url.netloc}" + docx_url
                    logger.info(f'正在解析第 {idx + 1} 个附件：{docx_url}')

                    doc_ = requests.get(docx_url, headers=headers).content
                    tables_ = parse_docx_tables(doc_, docx_url.endswith('doc'))

                    for table in tables_:
                        table.update({
                            'put_time': parser.parse(publish_time_str).strftime('%Y-%m-%d'),
                            'article_url': url
                        })
                        dao.save(table)

                continue

            imgs = soup.select('img.rich_pages.wxw-img')
            if not imgs:
                continue

            for img in imgs[:]:
                img_url = img.get('src') or img.get('data-src')
                h = img.get('data-backh') or 1000
                w = img.get('data-backw') or img.get('data-w') or 0
                type_ = img.get('data-type') or 'png'

                if type_ == 'gif' or 'gif' in img_url:
                    continue

                if img_url and (int(h) > 200 and int(w) > 400 and type_ in ['png', 'jpeg']):
                    img_response = requests.get(img_url, headers=headers)
                    info = extract_image_json([img_response.content])
                    for i in info['apps']:
                        b = {
                            'app_name': i['app_name'],
                            'developer': i['developer'],
                            'source': i['source'],
                            'version': i['version'],
                            'issues': '、'.join(i['issues']),
                            'put_time': parser.parse(publish_time_str).strftime('%Y-%m-%d'),
                            'article_url': url
                        }
                        dao.save(b)

                else:
                    logger.warning(f"{img_url} is not a valid image")

            # tables = soup.find_all('table')
            # if tables:
            #     deal_html([url_], gov)
            #     continue

        except:
            logger.error(url)


def deal_html(urls, gov=False):
    for url_ in tqdm(urls, desc='Processing URLs'):
        url = url_[1]
        logger.info(f'Processing URL: {url}')
        try:
            response = requests.get(url, headers=headers, cookies=cookies)
            soup = BeautifulSoup(response.text, 'lxml')

            if gov:
                publish_time_str = re.search(r'发布时间：(.*?)<', response.text).group(1)
            else:
                publish_time_str = re.search(r"var createTime = '(.*?)';", response.text).group(1)

            # 查找所有表格
            tables = soup.find_all('table')
            for i in tables[:]:
                info, headers_table = parse_html_tables(i)

                if not info:
                    logger.warning(f'No info in table: {url}')
                    continue

                mapping_dict = establish_mapping_json(headers_table)
                for c in info:
                    b = {
                        'app_name': c.get(mapping_dict['app_name']),
                        'developer': c.get(mapping_dict['developer']),
                        'source': c.get(mapping_dict.get('source', ''), ''),
                        'version': c.get(mapping_dict.get('version', ''), ''),
                        'issues': ', '.join(list(set(c[mapping_dict.get('issues', '')].split(', ')))) if c.get(mapping_dict.get('issues', '')) else '',
                        'put_time': parser.parse(publish_time_str).strftime('%Y-%m-%d'),
                        'article_url': url
                    }
                    dao.save(b)
        except Exception as e:
            logger.error(e)
            logger.error(url)


if __name__ == '__main__':
    ori_urls = []
    # deal_image(urls)
    # print(ori_urls)

    for ii in [
        # 'https://mp.weixin.qq.com/s/MBYr8VtM3XgThCOd2PZpsA',
    ]:
        deal_image([('', ii)], False)
