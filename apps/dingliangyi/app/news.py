import json

import requests
import re
import urllib3
from tqdm import tqdm

urllib3.disable_warnings()

from bs4 import BeautifulSoup
from dateutil import parser
from gpt_api import extract_image_json

from resx.config import *
from resx.mysql_dao import MySQLDao

dao = MySQLDao(**CFG_MYSQL_ZX_RDS111, db_tb_name='data_experience_situation.app_info', primary_index_fields=(['app_name', 'put_time'], []),
               ignore_fields=['id', 'company_id', 'update_time'])

a = [
    'https://www.app-maestro.com/fw/398',
    'https://www.app-maestro.com/fw/395',
    'https://www.app-maestro.com/fw/391',
    'https://www.app-maestro.com/fw/393',
    'https://www.app-maestro.com/fw/392',
    'https://www.app-maestro.com/fw/389',
    'https://www.app-maestro.com/fw/388',
    'https://www.app-maestro.com/fw/387', 'https://www.app-maestro.com/fw/386', 'https://www.app-maestro.com/fw/385', 'https://www.app-maestro.com/fw/383',
    'https://www.app-maestro.com/fw/381', 'https://www.app-maestro.com/fw/380', 'https://www.app-maestro.com/fw/378', 'https://www.app-maestro.com/fw/376',
    'https://www.app-maestro.com/fw/374',
    'https://www.app-maestro.com/fw/373', 'https://www.app-maestro.com/fw/367',
    'https://www.app-maestro.com/fw/375', 'https://www.app-maestro.com/fw/371', 'https://www.app-maestro.com/fw/370', 'https://www.app-maestro.com/fw/369',
    'https://www.app-maestro.com/fw/358', 'https://www.app-maestro.com/fw/361', 'https://www.app-maestro.com/fw/360', 'https://www.app-maestro.com/fw/359',
    'https://www.app-maestro.com/fw/355',
    'https://www.app-maestro.com/fw/345', 'https://www.app-maestro.com/fw/353', 'https://www.app-maestro.com/fw/352',
    'https://www.app-maestro.com/fw/351', 'https://www.app-maestro.com/fw/350', 'https://www.app-maestro.com/fw/349',
    'https://www.app-maestro.com/fw/348', 'https://www.app-maestro.com/fw/346', 'https://www.app-maestro.com/fw/344',
    'https://www.app-maestro.com/fw/339',
    'https://www.app-maestro.com/fw/343', 'https://www.app-maestro.com/fw/342', 'https://www.app-maestro.com/fw/337', 'https://www.app-maestro.com/fw/336',
    'https://www.app-maestro.com/fw/335', 'https://www.app-maestro.com/fw/334', 'https://www.app-maestro.com/fw/332',
    'https://www.app-maestro.com/fw/331', 'https://www.app-maestro.com/fw/330',
    'https://www.app-maestro.com/fw/329',
    'https://www.app-maestro.com/fw/328', 'https://www.app-maestro.com/fw/327', 'https://www.app-maestro.com/fw/326',
    'https://www.app-maestro.com/fw/325', 'https://www.app-maestro.com/fw/324', 'https://www.app-maestro.com/fw/322',
    'https://www.app-maestro.com/fw/321', 'https://www.app-maestro.com/fw/320', 'https://www.app-maestro.com/fw/318',
    'https://www.app-maestro.com/fw/317', 'https://www.app-maestro.com/fw/316', 'https://www.app-maestro.com/fw/314',
    'https://www.app-maestro.com/fw/313', 'https://www.app-maestro.com/fw/312', 'https://www.app-maestro.com/fw/311',
    'https://www.app-maestro.com/fw/310', 'https://www.app-maestro.com/fw/309', 'https://www.app-maestro.com/fw/308', 'https://www.app-maestro.com/fw/307',
    'https://www.app-maestro.com/fw/306', 'https://www.app-maestro.com/fw/305', 'https://www.app-maestro.com/fw/303', 'https://www.app-maestro.com/fw/301',
    'https://www.app-maestro.com/fw/300',
    'https://www.app-maestro.com/fw/296', 'https://www.app-maestro.com/fw/295', 'https://www.app-maestro.com/fw/299', 'https://www.app-maestro.com/fw/298',
    'https://www.app-maestro.com/fw/297', 'https://www.app-maestro.com/fw/294', 'https://www.app-maestro.com/fw/293',
    'https://www.app-maestro.com/fw/291', 'https://www.app-maestro.com/fw/290', 'https://www.app-maestro.com/fw/289', 'https://www.app-maestro.com/fw/288',
    'https://www.app-maestro.com/fw/287', 'https://www.app-maestro.com/fw/286', 'https://www.app-maestro.com/fw/285', 'https://www.app-maestro.com/fw/284',
    'https://www.app-maestro.com/fw/283',
    'https://www.app-maestro.com/fw/282', 'https://www.app-maestro.com/fw/281', 'https://www.app-maestro.com/fw/279', 'https://www.app-maestro.com/fw/278',
    'https://www.app-maestro.com/fw/277', 'https://www.app-maestro.com/fw/276', 'https://www.app-maestro.com/fw/273',
    'https://www.app-maestro.com/fw/271', 'https://www.app-maestro.com/fw/270', 'https://www.app-maestro.com/fw/269', 'https://www.app-maestro.com/fw/267',
    'https://www.app-maestro.com/fw/264', 'https://www.app-maestro.com/fw/263', 'https://www.app-maestro.com/fw/262',
    'https://www.app-maestro.com/fw/261', 'https://www.app-maestro.com/fw/260', 'https://www.app-maestro.com/fw/258', 'https://www.app-maestro.com/fw/257',
    'https://www.app-maestro.com/fw/256', 'https://www.app-maestro.com/fw/255', 'https://www.app-maestro.com/fw/253', 'https://www.app-maestro.com/fw/252',
    'https://www.app-maestro.com/fw/251',
    'https://www.app-maestro.com/fw/250', 'https://www.app-maestro.com/fw/249', 'https://www.app-maestro.com/fw/248', 'https://www.app-maestro.com/fw/246',
    'https://www.app-maestro.com/fw/244', 'https://www.app-maestro.com/fw/243', 'https://www.app-maestro.com/fw/242',
    'https://www.app-maestro.com/fw/239', 'https://www.app-maestro.com/fw/238', 'https://www.app-maestro.com/fw/237', 'https://www.app-maestro.com/fw/236',
    'https://www.app-maestro.com/fw/234', 'https://www.app-maestro.com/fw/233', 'https://www.app-maestro.com/fw/230', 'https://www.app-maestro.com/fw/229',
    'https://www.app-maestro.com/fw/228',
    'https://www.app-maestro.com/fw/227', 'https://www.app-maestro.com/fw/226', 'https://www.app-maestro.com/fw/225', 'https://www.app-maestro.com/fw/224',
    'https://www.app-maestro.com/fw/223', 'https://www.app-maestro.com/fw/218', 'https://www.app-maestro.com/fw/219', 'https://www.app-maestro.com/fw/217',
    'https://www.app-maestro.com/fw/216',
    'https://www.app-maestro.com/fw/213', 'https://www.app-maestro.com/fw/212', 'https://www.app-maestro.com/fw/211', 'https://www.app-maestro.com/fw/210',
    'https://www.app-maestro.com/fw/209', 'https://www.app-maestro.com/fw/208', 'https://www.app-maestro.com/fw/207',
    'https://www.app-maestro.com/fw/205', 'https://www.app-maestro.com/fw/204', 'https://www.app-maestro.com/fw/203', 'https://www.app-maestro.com/fw/202',
    'https://www.app-maestro.com/fw/201', 'https://www.app-maestro.com/fw/200', 'https://www.app-maestro.com/fw/199', 'https://www.app-maestro.com/fw/198',
    'https://www.app-maestro.com/fw/197',
    'https://www.app-maestro.com/fw/187', 'https://www.app-maestro.com/fw/188', 'https://www.app-maestro.com/fw/189', 'https://www.app-maestro.com/fw/190',
    'https://www.app-maestro.com/fw/191', 'https://www.app-maestro.com/fw/193', 'https://www.app-maestro.com/fw/194'
]

f = open('apps.txt', 'w', encoding='utf-8')

headers = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "cache-control": "no-cache",
    "dnt": "1",
    "pragma": "no-cache",
    "priority": "u=0, i",
    "referer": "https://www.app-maestro.com/news?orderway=desc&page=1",
    "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0"
}
cookies = {
    "PHPSESSID": "akjm98d81apg23elfhb3qsed9p"
}
for url in ['https://www.app-maestro.com/fw/311']:  # https://www.app-maestro.com/fw/311 https://www.app-maestro.com/fw/251 https://www.app-maestro.com/fw/218 https://www.app-maestro.com/fw/210
    try:
        response = requests.get(url, headers=headers, cookies=cookies)
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'lxml')
            title = soup.select_one('div.wow.fadeInUp.title').text
            content = soup.select_one('div.text').text
            if '山东' in title:
                continue

            date_time = re.findall(r'(\d{4}[年-]\d{1,2}[月-]\d{1,2})', content)
            if date_time:
                date_time_ = parser.parse(date_time[0].replace('年', '-').replace('月', '-'))
                date_time_ = date_time_.strftime('%Y-%m-%d')
            else:
                date_time_ = ''

            imgs = soup.select('div.text img')
            imgs_url = [i.get('src') for i in imgs]
            imgs_bytes = []
            for image_url in tqdm(imgs_url[:], desc='Downloading images'):
                print(image_url)
                res = requests.get(image_url, headers=headers, verify=False)
                imgs_bytes.append(res.content)

            info = extract_image_json(imgs_bytes[6:9])
            print(f"Successfully fetched {url}")
            print(date_time_)
            print(info)
            f.write(date_time_ + '\n')
            f.write(json.dumps(info, ensure_ascii=False) + '\n')
            print('-----------------------------------------------')

            # for i in info:
            #     b = {
            #         'app_name': i['app_name'],
            #         'developer': i['developer'],
            #         'source': i['source'],
            #         'version': i['version'],
            #         'issues': '、'.join(i['issues']),
            #         'put_time': date_time_,
            #     }
            #     dao.save(b)
        else:
            print(f"Failed to fetch {url}, status code: {response.status_code}")
    except Exception as e:
        print(f"Error fetching {url}: {e}")
