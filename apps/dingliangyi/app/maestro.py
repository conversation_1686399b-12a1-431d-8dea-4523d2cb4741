import requests
from bs4 import BeautifulSoup
import re

headers = {
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0"
}
cookies = {
    "PHPSESSID": "akjm98d81apg23elfhb3qsed9p"
}
url = "https://www.app-maestro.com/news"
params = {
    "orderway": "desc",
    "page": "23"
}
response = requests.get(url, headers=headers, cookies=cookies, params=params)
# print(response.text)
soup = BeautifulSoup(response.text, 'lxml')
lis = soup.select('ul.ul li')
print(len(lis))
a = []
for li in lis:
    div  = li.select('div.name.line1')
    url = li.select_one('a').get('href')
    title = div[0].text
    print(title)
    if re.search(r'((?<!山东省)通信管理局|(APP|App)通报|网信办|.*?的通报)',title):
        # print(title,url)
        a.append('https://www.app-maestro.com'+url)
print(a)
print(len(a))
print(len(a) == 9)