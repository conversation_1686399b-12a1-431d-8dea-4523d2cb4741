# -*- coding: utf-8 -*-
import requests
from bs4 import BeautifulSoup
from docx import Document
import re
from loguru import logger
from io import BytesIO
import tempfile
import os
import subprocess
import time
import pdfplumber
from dateparser import parse as date_parse

from resx.config import *
from resx.mysql_dao import MySQLDao
from apps.dingliangyi.temp_.ss1 import *
from apps.dingliangyi.app.gpt_api import extract_image_json, establish_mapping_json

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0",
}


def remove_None(list_):
    return [item.replace("\n", "") for item in list_ if item is not None]


def parse_pdf(pdf: bytes):
    row_list = []

    with pdfplumber.open(BytesIO(pdf)) as pdf:
        for page_number, page in enumerate(pdf.pages, start=1):
            # print(f"\n--- 第 {page_number} 页 ---\n")
            # 尝试提取表格
            tables = page.extract_tables()
            if tables:
                for table in tables:
                    for row in table:
                        row = remove_None(row)
                        row_list.append(row)

    merged_rows = []
    for row in row_list:
        if len(row) == 6:
            merged_rows.append(row)
        # 如果行的单元格不足，认为是上一行的补充
        elif len(row) < 6 and merged_rows:
            # 以空格将内容拼接到上一行最后一列
            merged_rows[-1][-1] += " " + " ".join(row)
        else:
            merged_rows.append(row)

    # 获取表头
    headers_ = merged_rows[0]
    # 获取数据行
    rows = merged_rows[1:]
    # 转换为字典列表
    result = [{headers_[i]: row[i] for i in range(len(headers_))} for row in rows]
    logger.info(result)

    return result


def convert_doc_to_docx_from_binary(doc_binary) -> BytesIO:
    with tempfile.NamedTemporaryFile(suffix='.doc', delete=False) as temp_doc:
        temp_doc.write(doc_binary)
        temp_doc_path = temp_doc.name

    temp_docx_path = os.path.splitext(temp_doc_path)[0] + '.docx'

    try:
        # Use full path to LibreOffice executable on Windows
        libreoffice_path = "C:/Program Files/LibreOffice/program/soffice.exe"  # Adjust path as needed

        cmd = [
            libreoffice_path,
            "--headless",
            "--convert-to", "docx",
            temp_doc_path,  # Input file (previously was using temp_docx_path incorrectly)
            "--outdir", os.path.dirname(temp_doc_path)
        ]

        subprocess.run(cmd, check=True)

        # Read the converted file
        with open(temp_docx_path, 'rb') as f:
            return BytesIO(f.read())
    finally:
        # Clean up temporary files
        if os.path.exists(temp_doc_path):
            os.unlink(temp_doc_path)
        if os.path.exists(temp_docx_path):
            os.unlink(temp_docx_path)


def parse_docx_tables(doc_content, doc_type):
    if doc_type:
        doc_io = convert_doc_to_docx_from_binary(doc_content)
    else:
        doc_io = BytesIO(doc_content)

    doc = Document(doc_io)
    all_tables_data = []
    for table in doc.tables:
        table_data = []
        header = [cell.text.strip() for cell in table.rows[0].cells]

        for row in table.rows[1:]:
            row_cells = [cell.text.strip() for cell in row.cells]

            # 检查是否是合并单元格的延续行
            if table_data and len(row_cells) == len(header):
                # 检测前几列是否与上一行相同或为空（表示合并单元格）
                is_merged_row = False
                merge_threshold = len(header) - 1  # 除了最后一列外的所有列

                # 检查前几列是否为空或与上一行相同
                empty_or_same_count = 0
                for i in range(min(merge_threshold, len(row_cells) - 1)):
                    if (not row_cells[i] or
                            row_cells[i] == table_data[-1].get(header[i], '')):
                        empty_or_same_count += 1

                # 如果前几列大部分为空或相同，且最后一列有内容，则认为是合并行
                if (empty_or_same_count >= merge_threshold * 0.7 and
                        row_cells[-1] and
                        len(row_cells) > 1):
                    is_merged_row = True

                if is_merged_row:
                    # 将最后一列内容追加到上一行的最后一列
                    last_col_key = header[-1]
                    if last_col_key in table_data[-1]:
                        current_content = table_data[-1][last_col_key]
                        new_content = row_cells[-1]
                        # 使用合适的分隔符连接内容
                        separator = ', ' if current_content and new_content else ''
                        table_data[-1][last_col_key] = current_content + separator + new_content
                    continue

            # 正常行处理
            row_dict = dict(zip(header, row_cells))
            table_data.append(row_dict)

        all_tables_data.extend(table_data)

        # 获取所有原始key用于建立映射
        all_keys = set()
        for row in all_tables_data:
            all_keys.update(row.keys())

        # 建立映射关系
        mapping_dict = establish_mapping_json(list(all_keys))

        # 替换所有行的key
        mapped_tables_data = []
        for row in all_tables_data:
            mapped_row = {
                'app_name': row.get(mapping_dict['app_name']),
                'developer': row.get(mapping_dict['developer']),
                'source': row.get(mapping_dict.get('source', ''), ''),
                'version': row.get(mapping_dict.get('version', ''), ''),
                'issues': ', '.join(list(set(row[mapping_dict.get('issues', '')].split(', ')))) if row.get(mapping_dict.get('issues', '')) else '',
            }
            mapped_tables_data.append(mapped_row)
        return mapped_tables_data


def parse_html_tables(table):
    # 提取所有行数据
    rows = []
    for row in table.find_all("tr"):
        cells = row.find_all(["th", "td"])
        row_data = [cell.get_text(strip=True) for cell in cells]
        if row_data:
            rows.append(row_data)

    if not rows:
        return

        # 判断是否存在表头：如果表中存在 th 标签，则认为第一行为表头
    headers = rows[0] if table.find("th") else None
    data_rows = rows[1:] if headers else rows

    # 确定预期列数
    expected_cols = len(headers) if headers else max(len(r) for r in data_rows)

    # 合并因解析错误而拆分的行
    merged_rows = []
    for row in data_rows:
        if len(row) == expected_cols:
            merged_rows.append(row)
        # 如果行的单元格不足，认为是上一行的补充
        elif len(row) < expected_cols and merged_rows:
            # 以空格将内容拼接到上一行最后一列
            merged_rows[-1][-1] += " " + " ".join(row)
        else:
            merged_rows.append(row)

    # 获取表头
    headers_ = merged_rows[0]
    # 获取数据行
    rows = merged_rows[1:]
    # 转换为字典列表
    result = [{headers_[i]: row[i] for i in range(len(headers_))} for row in rows]
    logger.info(result)

    return result


def get_url():
    url = "https://www.miit.gov.cn/search-front-server/api/search/info"
    params = {
        "websiteid": "110000000000000",
        "scope": "basic",
        "q": "APP名单的通报",  # APP名单的通报 APP通报 APP（SDK）通报
        "pg": "10",
        "cateid": "47",
        "pos": "title_text,titlepy",
        "pq": "",
        "oq": "",
        "eq": "",
        "begin": "",
        "end": "",
        "dateField": "deploytime",
        "selectFields": "title,content,deploytime,_index,url,cdate,infoextends,infocontentattribute,keyword,contentdescribe,sectitle,picpath,columnname,themename,publishgroupname,publishtime,metaid,bexxgk,columnid,infocontenthashcode",
        "group": "distinct",
        "highlightConfigs": "\\[\\{\"field\":\"infocontent\",\"numberOfFragments\":2,\"fragmentOffset\":0,\"fragmentSize\":110,\"noMatchSize\":110\\}\\]",
        "highlightFields": "title_text,infocontent,webid",
        "level": "6",
        "sortFields": "\\[\\{\"name\":\"extend1\",\"type\":\"desc\"\\},\\{\"name\":\"deploytime\",\"type\":\"desc\"\\}\\]",
        "hidCol": "fbafd13557fa453d9c59432567d2b150",
        "p": "1"
    }
    response = requests.get(url, headers=headers, params=params)
    list_ = response.json()['data']['searchResult']['dataResults']
    urls = []
    for i in list_:
        url: str = i['groupData'][0]['data']['url']
        if url.startswith('http'):
            urls.append(url)
        else:
            urls.append('https://www.miit.gov.cn' + url)
    return urls


def parse(url):
    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.text, 'lxml')

    title = soup.select_one('h1#con_title').text.strip()
    logger.info(title)
    put_time = re.search(r'\d{4}-\d{2}-\d{2}', soup.select('#con_time')[0].text).group()
    if title in titles:
        logger.warning(f'已存在标题：{title} URL：{url}')
        return []
    else:
        titles.append(title + put_time)

    tables = soup.find_all('table')
    as_ = soup.find_all(name='a', href=lambda href: href and re.search(r'doc|docx$', href))
    pdf = soup.select('iframe')
    images = soup.select('#con_con img')
    result: list[dict] = []
    if_ = False

    # if images:
    #     for img in images:
    #         url_img = img.get('src')
    #         img_response = requests.get(url_img, headers=headers)
    #         info = extract_image_json([img_response.content])
    #         for i in info['apps']:
    #             b = {
    #                 'app_name': i['app_name'],
    #                 'developer': i['developer'],
    #                 'source': i['source'],
    #                 'version': i['version'],
    #                 'issues': '、'.join(i['issues']),
    #                 'put_time': date_parse(put_time).strftime('%Y-%m-%d'),
    #                 'article_url': url
    #             }
    #             dao.save(b)
    #     return []

    # if pdf:
    #     try:
    #         pdf = pdf[0]['fileurl']
    #         pdf_ = requests.get('https://www.miit.gov.cn' + pdf, headers=headers).content
    #         temp_ = parse_pdf(pdf_)
    #         result.extend(temp_)
    #     except:
    #         logger.error('https://www.miit.gov.cn' + pdf)

    for idx, url in enumerate(as_):
        url = as_[idx]['href']
        url = url if url.startswith('http') else 'https://www.miit.gov.cn' + url
        logger.info(f'正在解析第 {idx + 1} 个附件：{url}')

        doc_ = requests.get(url, headers=headers).content
        tables_ = parse_docx_tables(doc_, url.endswith('doc'))
        result.extend(tables_)

    # for idx, table in enumerate(tables):
    #     temp = parse_html_tables(table)
    #     result.extend(temp)

    for i in result:
        i['发布时间'] = put_time
    if not result:
        logger.warning(f'无解析内容 URL：{url}')
    logger.info(result)
    return result


if __name__ == '__main__':
    dao = MySQLDao(**CFG_MYSQL_ZX_RDS111, db_tb_name='data_experience_situation.app_info', primary_index_fields=(['app_name', 'put_time'], []),
                   ignore_fields=['id', 'company_id', 'update_time'])
    # aa = get_url()
    titles = []
    aa = [

    ]
    logger.info(len(aa))

    for a in aa[:]:
        logger.info(a)
        bb = parse(a)
        for b in bb:
            c = {}
            for k, v in b.items():
                c[k.replace('\n', '')] = v
            b = {
                'app_name': c.get('应用名称', '') or c.get('软件名称', ''),
                'developer': c.get('应用开发者', '') or c.get('企业名称', ''),
                'source': c.get('应用来源', ''),
                'version': c.get('应用版本', '') or c.get('下架版本', '') or c.get('复测应用版本', '') or c.get('版本号', ''),
                'issues': ', '.join(list(set(c['所涉问题'].split(', ')))) if c.get('所涉问题') else '',
                'put_time': c.get('发布时间', ''),
            }

            if_ = False
            for k, v in b.items():
                if k in ['app_name', 'developer', 'put_time'] and not v:
                    logger.warning(a)
                    if_ = True
                    break
            if if_:
                logger.warning(f'数据不完整 {b}，跳过 URL：{a}')
                continue

            dao.save(b)

    print(titles)
