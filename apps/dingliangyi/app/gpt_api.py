from openai import OpenAI
import base64
from textwrap import dedent
from pydantic import BaseModel, Field
import time
from loguru import logger
from typing import List
import os
import httpx

from apps.dingliangyi.temp_.ss1 import *

client = OpenAI(
    # api_key="AIzaSyDspfi9nOhss4BkkOQGzoR5l4F0EBYQhAE",
    api_key="AIzaSyCV31D8I6qwU5Eopz3wip5vsju3G0wO2Fg",
    base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
    # api_key="sk-hFkzyJWLf0i8Kf2XezlpX14POTC4TxnPxIWMHKYrXbQOawAq",
    # base_url="https://api.oaibest.com/v1",
    # api_key="sk-TaZ7lJp0Q79y98Dp75E7F3E3808e45B9A0822a0dCc4dDe3f",
    # base_url="https://api.ocoolai.com/v1",
    # http_client=httpx.Client(proxy='http://127.0.0.1:7890', verify=False)  # type: ignore
    http_client=httpx.Client(proxy='socks5h://127.0.0.1:6666', verify=False)  # type: ignore
)

prompt = '''
<Role>你是一位图片表格结构化信息提取专家</Role>
<Task>
1. 读取并理解图片中的表格内容，并将其转换为结构化的JSON格式。
</Task>
<Requirements>
## 严格按照给定的格式，返回JSON数据
## 如果某个字段没有数据，请返回空字符串
## 如果没有指定格式的数据，返回 {"apps":[]}
</Requirements>
<Format>
例子：
    {
      "apps": [{
            "app_name": "黑光图库",
            "developer": "潍坊黑光企业管理咨询有限公司",
            "source": "vivo 应用商店",
            "version": "2.2.3",
            "issues": [
                "违规收集个人信息",
                "APP强制、频繁、过度索取权限"
            ]
        }]
    }
</Format>
'''

prompt2 = '''
任务：
从两个列表中，根据名称含义或表达相似建立映射字典，无法映射则默认为空字符串
格式：
严格按照给定结构化格式返回
列表：
['app_name', 'developer', 'source', 'version', 'issues']
'''


class App(BaseModel):
    """
    违规软件的基本信息
    """
    app_name: str = Field(..., description='应用名称')
    developer: str = Field(..., description='开发者或公司名称')
    source: str = Field(..., description='应用来源')
    version: str = Field(..., description='应用版本号')
    issues: list[str] = Field(..., description='应用存在的多个主要问题或违规行为列表')


class Apps(BaseModel):
    """
    违规软件列表
    """
    apps: list[App] = Field(..., description='违规软件列表')


def encode_image(binary_data):
    return base64.b64encode(binary_data).decode("utf-8")


class Mapping(BaseModel):
    app_name: str = Field(..., description='应用名称')
    developer: str = Field(..., description='开发者或公司名称')
    source: str = Field(default='', description='应用来源')
    version: str = Field(default='', description='应用版本号')
    issues: str = Field(default='', description='应用存在的主要问题或违规行为')


def establish_mapping_json(keys: list[str]):
    a = time.time()
    completion = client.beta.chat.completions.parse(
        model='gemini-2.5-flash',
        # model='gpt-4.1-2025-04-14',
        messages=[  # type: ignore
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": dedent(prompt2 + str(f'{keys}'))},
                ]
            }
        ],
        temperature=0.1,
        top_p=0.9,
        response_format=Mapping
    )
    _ = completion.choices[0].message.parsed
    logger.info(f"timing: {time.time() - a} result: {_.model_dump()}")
    return _.model_dump()


def extract_image_json(binary_datas: list[bytes]):
    images = [
        {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/png;base64,{encode_image(data)}"
            }
        }
        for data in binary_datas
    ]
    a = time.time()

    completion = client.beta.chat.completions.parse(
        # gemini-2.5-flash  gpt-4.1-2025-04-14 gpt-4o-2024-11-20
        model='gemini-2.5-flash',
        messages=[  # type: ignore
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": dedent(prompt)},
                    *images
                ]
            }
        ],
        temperature=0.1,
        top_p=0.9,
        response_format=Apps
    )

    _ = completion.choices[0].message.parsed
    logger.info(f"timing: {time.time() - a} result: {_.model_dump()}")
    return _.model_dump()


if __name__ == '__main__':
    # models = client.models.list()
    # for model in models:
    #     print(model.id)

    establish_mapping_json(['序号', 'APP名称', '版本', '应用开发运营单位'])
