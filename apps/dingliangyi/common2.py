import requests
from loguru import logger
import pandas as pd
from resx.mysql_dao import MySQLDao
from resx.config import *

logger.add(sink=r'./logs/cods_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8', enqueue=True)


# hk  law_firm_gj foundation cods china_npo institution_gj
def touci(keyword, type):
    data = {
        "keyword": keyword,
        "reason": "customer",
        # "type": "hk"
        "type": type,
        'score': 0
    }
    a = requests.post('http://schedule-gsdata.jindidata.com/schedule/fq', json=data, headers={
        "content-type": "application/json",
    })
    logger.info(f'{a.text}  {keyword}')


if __name__ == '__main__':
    pass # '12330282419688270D','52330326758061990P'
    touci('31410000MD0309910Y', 'law_firm_gj')

    # for i in ('N2330411745078873T','N2330824MF2223982B','N2330110YA37205419','N2330624MF797219XQ','N2330502MA2B383E2W','N2330681780478143A','N2330483569398877X','N2330481661709287M','N2331083MF113200XW','N2330603661736576F','N2330522MF0763520B','N233068179437939XT','N2330482573997792H','N2330503051342721B','N2330110YA37205419','N2330483556184706F','N2330726MA29PD0T8P','N2330624MF51648508','N2330522054248845B','N2330522051302770Y','N2330624MF8349447J','N2330502MA29KPQG29','52330102586548080K','N2331023MF0509514F','N2330326581680197A','N2330726MF0769295G','N2330624MF1620230C','71331004777208273G','N23307823077214275','N2331081MF08480308','N2330723MF40033504','N2330110730330095M','N2331102095657143F','52330327725236707G','N2330185MF0744231R','N2330624MF2281090Q','N2330483563313123D','N2330683MF06388697','N23303820568803504','N23303827818054178','N2330683MF06351233','N2330782MF1246621T','N2331102094725466J','N2330723MF2329568F','N2330683MF06384553','N2330782307685809Q','523307007324275872','52330212MJ9015849G','N2330523693880433N','N2330683MF0904418U','N2330624MF4770066J','N2330784MF01443676','N2330624MF2011090D','N2330683329955654D','N2330110727211714G'):
    #     touci(i, 'cods')

    # dao = MySQLDao(**CFG_MYSQL_ZX_RDS108, db_tb_name='data_judicial_risk.law_firm')
    # list_ = list(dao.select_many("select * from data_judicial_risk.law_firm where update_time < '2025-06-16' and update_time > '2025-06-01' and company_id > 0"))
    # for i in list_[:]:
    #     touci(i['creditCode'],'law_firm_gj')

    # temp = []
    # df = pd.read_csv(r"C:\Users\<USER>\Downloads\prism_organization_info.csv", encoding='utf-8')
    # for index, row in df.iterrows():
    #     list_ = row.tolist()
    #     if index > 95000:
    #         touci(list_[5], 'cods')
    #         temp.append(list_[5])
    #     # if index >= 100000:
    #     #     break
    # logger.info(temp)

