from abc import <PERSON><PERSON><PERSON>, abstractmethod, <PERSON>
from typing import Union
from datetime import datetime
from requests import Session, Response
from loguru import logger
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter
import time
import re
from libs.concurrent import BoundedExecutor
import requests
import urllib3
import random
from collections import OrderedDict
import traceback

from clients.mysql_client import MySQLClient
from clients.redis._redis import Redis
from dao.npo.npo import <PERSON><PERSON><PERSON><PERSON>, Npo
from apps.spider.parser.parser_NPO import <PERSON><PERSON><PERSON><PERSON><PERSON>
from apps.octopus.utils.entry_manager import EntryManager

urllib3.disable_warnings()
entry_manager = EntryManager()


class MyException(Exception):

    def __init__(self, message):
        self.message = message
        super().__init__(self.message)


class NpoSpider(metaclass=ABCMeta):  # class Abstract(ABC):

    def __init__(self):
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* "
                          "Safari/537.36",
        }
        logger.add(sink=r'./logs/' + f'{self.__class__.__name__}' + '_{time:YYYY_MM_DD}.log', rotation='00:00', retention='2 days', encoding='utf8',
                   enqueue=True)
        self.timeout = 3
        self.executor = BoundedExecutor(200)
        self.mysql_111 = MySQLClient(**{
            'host': '886a213ba5de451eb4add2e0d1fb2ef6in01.internal.cn-north-4.mysql.rds.myhuaweicloud.com',
            'user': 'jdhw_d_zhuan_dml',
            'password': 'kmbpZRTr1pooyB9'
        })
        self.npodao = NpoDao()
        self.is_increment = False
        self.sum = 0
        self.first_page = False

    def check_in_db(self, us_credit_code=None, name=None) -> bool:
        # name = self.replace_bracket(name)
        result_by_credit = self.npodao.get(us_credit_code, 'unified_social_credit_code') if us_credit_code else True
        # result_by_name = self.npodao.get(name, 'name') if name else True
        # if not result_by_credit or not result_by_name:
        if not result_by_credit:
            return False
        return True

    def save_db(self, data: dict):
        self.npodao.save_by_cmp(Npo.from_dict(data), fields=['unified_social_credit_code'],
                                ignore_fields=['organization_code', 'website', 'phone', 'contact_people', 'registration_number',
                                               'industry_category', 'post_code', 'source', 'property1', 'property2', 'crawl_time'])

    @staticmethod
    def new_session():
        session = requests.session()
        session.proxies = {
            'http': 'http://10.99.138.95:30636',
            'https': 'http://10.99.138.95:30636'
        }
        session.mount('http://', HTTPAdapter(max_retries=1))
        session.mount('https://', HTTPAdapter(max_retries=1))
        return session

    @staticmethod
    def get_long_proxy():
        res = requests.get('http://10.99.192.206:8015/long-proxy')
        proxy = random.choice(res.text.split('\r\n'))
        return {
            'http': f'http://{proxy}',
            'https': f'http://{proxy}'
        }

    @staticmethod
    def datatime_format(date_str: str):
        if not date_str:
            return ''
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        formatted_date = date_obj.strftime("%Y年%m月%d日")
        return formatted_date

    @staticmethod
    def replace_bracket(name, cn_to_en=False):
        if not name:
            return ''
        if cn_to_en:
            return name.replace('（', '(').replace('）', ')')
        return name.replace('(', '（').replace(')', '）')

    @staticmethod
    def timestamp_to_date(timestamp: int):
        timestamp = timestamp / 1000
        return datetime.fromtimestamp(timestamp).strftime("%Y年%m月%d日")

    @staticmethod
    def unique(my_list, exclude_field: str = ''):
        unique_list = []
        # history = {}
        for d in my_list:
            filtered_items = tuple((k, v) for k, v in sorted(d.items()) if k != exclude_field)
            unique_list.append(filtered_items)
            # history.update({d["uniscid"]: d[exclude_field]})
        unique_list = list(OrderedDict.fromkeys(unique_list))
        data = []
        for t in unique_list:
            d = dict(t)
            # d.update({exclude_field: history[d['uniscid']]})
            data.append(d)
        return data

    @staticmethod
    def to_(filed):
        return filed if filed else ''

    @staticmethod
    def legal(code):
        # 统一代码由十八位的阿拉伯数字或大写英文字母（不使用I、O、Z、S、V）组成
        valid_char = '0123456789ABCDEFGHJKLMNPQRTUWXY'
        invlid_char = 'IOZSV'
        modulus = 31
        if code is None or code.isspace():
            return False
        # 位数校验 18 位
        if len(code) != 18:
            return False
        codeu = code.upper()
        # 含有不使用大写英文字母
        for i in invlid_char:
            if i in codeu:
                return False
        # 不在有效字符范围内
        for i in code:
            if not (i in valid_char):
                return False
        # 前两位登记管理部门代码和机构类别代码
        d_o_code = ['11', '12', '13', '19', '21', '29', '31', '32', '33', '34', '35', '39',
                    '41', '49', '51', '52', '53', '59', '61', '62', '69', '71', '72', '79',
                    '81', '89', '91', '92', '93', 'A1', 'A9', 'N1', 'N2', 'N3', 'N9', 'Y1']
        if not (code[0:2] in d_o_code):
            return False
        # 3-8位登记管理机关行政区划码先不检测了
        # 9-17位主体标识码（组织机构代码）先不拿出来校验了
        # 第18位校验码，这部分是关键
        # 第i位置对应的加权因子
        wi = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28]
        # 第i位置上的字符对应值
        corres_value = {'0': 0, '1': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, 'A': 10,
                        'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16, 'H': 17, 'J': 18, 'K': 19, 'L': 20,
                        'M': 21, 'N': 22, 'P': 23, 'Q': 24, 'R': 25, 'T': 26, 'U': 27, 'W': 28, 'X': 29, 'Y': 30}
        # 前17位字符位置序号i相对应的各个位置上的字符值
        ci = []
        for v in code[0:17]:
            j = corres_value.get(v)
            ci.append(j)
        # 计算与字符位置序号i相对应的乘积
        ciwi = []
        for i in range(len(ci)):
            ciwi.append(ci[i] * wi[i])
        # 计算级数之和
        ciwi_sum = sum(ciwi)
        # 级数之和求余,求出校验码字符值
        c18 = modulus - ciwi_sum % modulus
        # 查出校验码字符
        if c18 == modulus:
            c18 = 0
        check_code = None
        for k, v in corres_value.items():
            if v == c18:
                check_code = k
                break
        if check_code == code[17]:
            return True
        else:
            return False

    @staticmethod
    def dispatch(entry_word, inst_name='china_npo', reason='npo_list'):
        if not entry_word:
            return
        return entry_manager.inst_immediate(
            entry_name='credit',
            entry_word=entry_word,
            inst_name=inst_name,
            reason=reason,
            ignore_latest_search_empty=False
        )

    @staticmethod
    def getBaseByAreaCode(code):
        if code:
            code_len = len(code)
            if code_len != 6 and code_len != 2 and code_len != 18:
                return
            if code_len == 18:
                code = code[2:4]
            data = {
                '10': 'gj', '11': 'bj', '12': 'tj', '13': 'heb', '14': 'sx', '15': 'nmg', '21': 'ln', '22': 'jl',
                '23': 'hlj', '31': 'sh', '32': 'js', '33': 'zj', '34': 'ah', '35': 'fj', '36': 'jx', '37': 'sd',
                '41': 'hen', '42': 'hub', '43': 'hun', '44': 'gd', '45': 'gx', '46': 'han', '50': 'cq', '51': 'sc', '52': 'gz',
                '53': 'yn', '54': 'xz', '61': 'snx', '62': 'gs', '63': 'qh', '64': 'nx', '65': 'xj', '71': 'tw', '81': 'hk', '82': 'mo'
            }
            return data.get(code)

    def request(self, session: Session, method: str, url: str, params: dict = None, data: dict = None,
                json: dict = None, path: str = None, name: str = '', tojson=False, toRaw=False, long_proxy=False) -> Union[dict, str, Response]:
        for i in range(10):
            response = None
            try:
                a = time.time()
                request_params = {'method': method, 'url': url, 'data': data, 'headers': self.headers,
                                  'verify': False, 'timeout': self.timeout, 'params': params, 'json': json}
                if long_proxy:
                    request_params['proxies'] = self.get_long_proxy()
                    request_params['timeout'] = 40
                response = session.request(**request_params)

                if response.status_code != 200:
                    logger.warning(f'{name} --> {response.status_code}')
                    del session.cookies['proxyBase']
                    continue
                logger.success(f'{name} --> {response.status_code} --> time: {time.time() - a}')

                if toRaw:
                    return response

                if name not in ['']:
                    a = re.sub(r'[\n\r\t]', '', response.text)
                    logger.info(f'{name} --> {a}')
                if tojson:
                    return response.json()
                return response.text
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'{name} --> continue{i} exception: {e}')
                del session.cookies['proxyBase']
                continue
            except Exception as e:
                status = response.status_code if  response else "空"
                text = response.text if response else "空"
                logger.warning(f'{name} --> continue{i} 状态码：{status} res: {text} exception: {e}')
                del session.cookies['proxyBase']
                continue

    @staticmethod
    def custom_traceback(e):
        exc_type = type(e)
        exc_value = e
        tb = e.__traceback__
        """
        自定义打印 traceback，仅显示用户代码的报错信息
        """
        # 提取 traceback 信息
        tb_list = traceback.extract_tb(tb)
        user_tb = [frame for frame in tb_list if 'pygs-work-parent' in frame.filename]
        error = ''
        if user_tb:
            print("Traceback (most recent call last):")
            # 格式化并打印用户代码的 traceback 信息
            print("".join(traceback.format_list(user_tb)))
            print(f"{exc_type.__name__}: {exc_value}")
            error += "".join(traceback.format_list(user_tb)) + f"{exc_type.__name__}: {exc_value}"
        else:
            print(f"{exc_type.__name__}: {exc_value}")
            error += f"{exc_type.__name__}: {exc_value}"
        return error

    @abstractmethod
    def main(self):
        pass


if __name__ == '__main__':
    pass
