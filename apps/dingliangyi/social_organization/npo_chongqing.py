import re
import schedule
from loguru import logger
from tqdm import tqdm
import time
from bs4 import BeautifulSoup
from datetime import datetime

from apps.dingliangyi.social_organization.npo_base import NpoSpider
from apps.dingliangyi.social_organization.npo_base import MyException


class ChongQingNpoSpider(NpoSpider):
    def __init__(self):
        super().__init__()
        self.headers = {
            "Referer": "https://mzj.cq12349.cn/bmcx/shzz_query.html",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0",
        }
        self.timeout = 5

    def get_list(self):
        url = "https://mzj.cq12349.cn/hgdp-association-apply-biz/somorganinfo/getSomOrganList"
        params = {"name": "", "sort": "", "canclesort": "", "sortfiled": "A.BUILD_DATE", "code": "",
                  "pageno": "1", "ssqx": "", "type": "", "state": "22", "orgtype": "0"}
        for i in range(1, 889):
            params['pageno'] = str(i)
            res: dict = self.request(self.new_session(), 'GET', url, params=params, tojson=True, name=f'get_list-{i}')
            if self.first_page or not res['jsonData'] or datetime.strptime(res['jsonData'][0]['clsj'], '%Y-%m-%d').year < 1949:
                logger.info(f'翻页到{i}页结束')
                return res['jsonData']
            yield res['jsonData']

    def get_retail(self, path) -> dict:
        url = f"https://mzj.cq12349.cn/hgdp-association-apply-biz/somorganinfo/getSomOrganDatieList/{path}"
        res = self.request(self.new_session(), 'GET', url, tojson=True, name='get_retail')
        return res['jsonData']

    def parse(self, data):
        org_detail = data.get('organizationDetailInfo', {})
        if not org_detail.get('shzzmc') or not org_detail.get('tyshxydm'):
            raise MyException('没有名称或统一社会信用代码')

        data_dict = {
            'name': org_detail.get('shzzmc', ''),  # 名称
            'base': self.getBaseByAreaCode(org_detail.get('tyshxydm', '')),  # 地区
            'unified_social_credit_code': org_detail.get('tyshxydm', ''),  # 统一社会信用代码
            'registration_authority': self.to_(org_detail.get('djjg', '')),  # 登记管理机关
            'business_unit': self.to_(org_detail.get('ywzgdw')),  # 业务主管单位
            'legal_person': self.to_(org_detail.get('fddbr')),  # 法人
            'registration_date': self.to_(org_detail.get('djrq')),  # 成立登记日期
            'expiry_date': f'{data["certInfoList"][0]["yxqq"]} 至 {data["certInfoList"][0]["yxqz"]}',  # 有效期
            'registered_capital': f"{self.to_(org_detail.get('zczj'))}万元",  # 注册资金
            'status': '正常' if data['certInfoList'][0]['state'] == 1 else '',  # 登记状态
            "types": self.to_(org_detail.get('lx')),  # 社会组织类型
            "address": self.to_(org_detail.get('zs')),  # 住所
            "business_scope": self.to_(org_detail.get('ywfw')),  # 业务范围
            "property": 'new_country_npo',
            'is_charity': 0,  # 是否慈善组织
            'has_raise_funds_qualification': 0,  # 是否有募捐资格
            'source': 'https://mzj.cq12349.cn/bmcx/shzz_query.html'
        }
        return data_dict

    def main_schedule(self):
        generator = self.get_list()
        for idx, list_ in enumerate(generator):
            logger.info(f'总共{len(list_)}条数据')
            for item in tqdm(list_, desc=f'第{idx}页'):
                try:
                    is_exist = self.check_in_db(item['unifiedcode'], item['shzzmc'])
                    if not is_exist:
                        data = self.get_retail(item['sorgid'])
                        self.sum += 1
                        mapped_data = self.parse(data)
                        self.save_db(mapped_data)
                        logger.info(f'新增数据：{mapped_data}')
                except Exception as e:
                    self.custom_traceback(e)

    def main(self):
        self.main_schedule()
        logger.success(f'新增{self.sum}条数据')

        logger.info('Start schedule')
        schedule.every().day.at("10:00").do(self.main_schedule)
        while True:
            schedule.run_pending()
            time.sleep(1)


if __name__ == '__main__':
    # 65
    spider = ChongQingNpoSpider()
    spider.main()
