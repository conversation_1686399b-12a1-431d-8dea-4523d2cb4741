import re
import schedule
from loguru import logger
import traceback
from tqdm import tqdm
import time
from datetime import datetime

from apps.dingliangyi.social_organization.npo_base import NpoSpider
from apps.dingliangyi.social_organization.npo_base import MyException


class ShangDongNpoSpider(NpoSpider):
    def __init__(self):
        super().__init__()

    def get_list(self):
        url = "http://120.221.154.252:8088/socialorg/socExternal/querySorgList.action"
        data = {"CN_NAME": "", "CREDIT_CODE": "", "SORG_TYPE": "", "STATUS": "22", "LEGAL_PEOPLE": "", "BORG_NAME": "", "page": "1", "rows": "3000"}
        for i in range(1, 23):
            data['page'] = str(i)
            res: dict = self.request(self.new_session(), 'POST', url, data=data, name=f'get_list-{i}', tojson=True)
            if self.first_page or not res['rows'] or datetime.strptime(res['rows'][0]['REG_DATE'], '%Y-%m-%d').year < 1949:
                logger.info(f'翻页到{i}页结束')
                return res['rows']
            yield res['rows']

    def get_retail(self, sorgid) -> dict:
        url = "http://120.221.154.252:8088/socialorg/socExternal/querySorgListInfo.action"
        data = {"sorgid": sorgid}
        res = self.request(self.new_session(), 'POST', url, data=data, name='get_retail', tojson=True)
        return res['cert']

    def parse(self, data):
        if data.get('SORG_STATUS') == '22':
            status = '正常'
        elif data.get('SORG_STATUS') == '52':
            status = '撤销'
        else:
            status = '注销'

        if data.get('SORG_TYPE') == 'S':
            types = '社会团体'
        elif data.get('SORG_TYPE') == 'M':
            types = '民办非企业'
        elif data.get('SORG_TYPE') == 'J':
            types = '基金会'
        else:
            types = '其它'

        if not data.get('CN_NAME') or not data.get('CREDIT_CODE'):
            raise MyException('没有名称或统一社会信用代码')

        data_dict = {
            'name': data.get('CN_NAME', '').strip(),  # 名称
            'base': self.getBaseByAreaCode(data.get('CREDIT_CODE', '')).strip(),  # 地区
            'unified_social_credit_code': data.get('CREDIT_CODE', '').strip(),  # 统一社会信用代码
            'registration_authority': self.to_(data.get('SIGN_ORGAN', '')).strip(),  # 登记管理机关
            'business_unit': self.to_(data.get('BORG_NAME', '')).strip(),  # 业务主管单位
            'legal_person': self.to_(data.get('LEGAL_PEOPLE', '')).strip(),  # 法人
            'registration_date': data.get('REG_DATE'),  # 成立登记日期
            'expiry_date': f'{self.to_(data.get("SIGN_BEGIN_DATE"))} 至 {self.to_(data.get("SIGN_END_DATE"))}',  # 有效期
            'registered_capital': f'{data.get("REG_MON", "")}万元' if data.get("REG_MON", "") else "",  # 注册资金
            'status': status,  # 登记状态
            "types": types,  # 社会组织类型
            "address": self.to_(data.get('ADDRESS', '')).strip(),  # 住所
            "business_scope": re.sub(r'[\s\n\t\r]', '', self.to_(data.get('BUSINESS', ''))),  # 业务范围
            "property": 'new_country_npo',
            'source': 'http://mzt.shandong.gov.cn'
        }
        return data_dict

    def main_schedule(self):
        generator = self.get_list()
        for idx, list_ in enumerate(generator):
            logger.info(f'总共{len(list_)}条数据')
            for item in tqdm(list_, desc=f'第{idx}页'):
                try:
                    is_exist = self.check_in_db(item.get('CREDIT_CODE', ''), item.get('CN_NAME', ''))
                    if not is_exist:
                        data = self.get_retail(item['SORG_ID'])
                        if not data:
                            success = self.dispatch(item.get('CREDIT_CODE'))
                            logger.success(f'投词：{item.get("CREDIT_CODE")} {success}')
                            continue
                        self.sum += 1
                        mapped_data = self.parse(data)
                        self.save_db(mapped_data)
                        logger.info(f'新增数据：{mapped_data}')
                except Exception as e:
                    self.custom_traceback(e)

    def main(self):
        self.main_schedule()
        logger.success(f'新增{self.sum}条数据')

        logger.info('Start schedule')
        schedule.every().day.at("10:00").do(self.main_schedule)
        while True:
            schedule.run_pending()
            time.sleep(1)


if __name__ == '__main__':
    # 65
    spider = ShangDongNpoSpider()
    spider.main()
