import re
import schedule
from loguru import logger
from tqdm import tqdm
import time
from bs4 import BeautifulSoup
from datetime import datetime

from apps.dingliangyi.social_organization.npo_base import NpoSpider
from apps.dingliangyi.social_organization.npo_base import MyException


class GuangDongNpoSpider(NpoSpider):
    def __init__(self):
        super().__init__()
        self.headers = {
            "Referer": "https://main.gdnpo.gov.cn/home/<USER>/gssshzznewPy?MainCreID=&MainMc=&MainLb=&MainFddbr=&MainZt=0&MainClsj=&MainZgdwMc=&MainSiteID=&cp=2",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
        }
        self.timeout = 10

    def get_list(self):
        url = "https://main.gdnpo.gov.cn/home/<USER>/gssshzznewPy"
        params = {"MainCreID": "", "MainMc": "", "MainLb": "", "MainFddbr": "", "MainZt": "0",
                  "MainClsj": "", "MainZgdwMc": "", "MainSiteID": "", "cp": "1"}
        for i in range(342, 7084):
            params['cp'] = str(i)
            res = self.request(self.new_session(), 'GET', url, params=params, name=f'get_list-{i}')
            soup = BeautifulSoup(res, 'lxml')
            trs = soup.select("tr.talbetrtdHover")
            lis = []
            for tr in trs:
                list_ = list(tr.stripped_strings)
                url_ = re.search(r'id=(.*)\?gd', tr.select('a')[0].get('href')).group(1)
                list_.append(url_)
                lis.append(list_)
            if self.first_page or not lis or datetime.strptime(lis[0][-3], '%Y-%m-%d').year < 1949:
                logger.info(f'翻页到{i}页结束')
                return lis
            yield lis

    def get_retail(self, path) -> dict:
        url = f"https://www.gdnpo.gov.cn/home/<USER>/indexxxgsShzzPagenew/{path}?gd="
        res = self.request(self.new_session(), 'GET', url, name='get_retail')
        lis1 = (BeautifulSoup(res, 'lxml').select(".box_mod2 li"))
        lis2 = (BeautifulSoup(res, 'lxml').select(".box_mod1 li"))
        data = {}
        for li in lis1 + lis2:
            list_ = list(li.stripped_strings)
            data[list_[0]] = list_[1]
        return data

    def parse(self, data):
        if not data.get('社会组织名称：') or not data.get('统一社会信用代码：'):
            raise MyException('没有名称或统一社会信用代码')

        data_dict = {
            'name': data.get('社会组织名称：', '').strip(),  # 名称
            'base': self.getBaseByAreaCode(data.get('统一社会信用代码：', '')).strip(),  # 地区
            'unified_social_credit_code': data.get('统一社会信用代码：', '').strip(),  # 统一社会信用代码
            'registration_authority': self.to_(data.get('登记管理机关：', '')).strip(),  # 登记管理机关
            'business_unit': self.to_(data.get('业务主管单位：', '')).strip(),  # 业务主管单位
            'legal_person': self.to_(data.get('法定代表人：', '')).strip(),  # 法人
            'registration_date': re.sub(r'[年月]', data.get('成立登记日期：'), '-')[:-1],  # 成立登记日期
            'expiry_date': data.get('证书有效期：', ''),  # 有效期
            'registered_capital': self.to_(data.get('注册资金：')).replace('（', '').replace('）', ''),  # 注册资金
            'status': '正常',  # 登记状态
            "types": data.get('社会组织类型：', ''),  # 社会组织类型
            "address": self.to_(data.get('住 所：', '')).strip(),  # 住所
            "business_scope": re.sub(r'[\s\n\t\r]', '', self.to_(data.get('业 务 范 围：', ''))),  # 业务范围
            "property": 'new_country_npo',
            'is_charity': 1 if data.get('慈善组织：', '') == '是' else 0,  # 是否慈善组织
            'has_raise_funds_qualification': 1 if data.get('公开募捐：', '') == '是' else 0,  # 是否有募捐资格
            'source': 'http://mzt.shandong.gov.cn'
        }
        return data_dict

    def main_schedule(self):
        generator = self.get_list()
        for idx, list_ in enumerate(generator):
            logger.info(f'总共{len(list_)}条数据')
            for item in tqdm(list_, desc=f'第{idx}页'):
                try:
                    is_exist = self.check_in_db(item[2], item[3])
                    if not is_exist:
                        data = self.get_retail(item[-1])
                        self.sum += 1
                        mapped_data = self.parse(data)
                        self.save_db(mapped_data)
                        logger.info(f'新增数据：{mapped_data}')
                except Exception as e:
                    self.custom_traceback(e)

    def main(self):
        self.main_schedule()
        logger.success(f'新增{self.sum}条数据')

        logger.info('Start schedule')
        schedule.every().day.at("10:00").do(self.main_schedule)
        while True:
            schedule.run_pending()
            time.sleep(1)


if __name__ == '__main__':
    # 65
    spider = GuangDongNpoSpider()
    spider.main()
