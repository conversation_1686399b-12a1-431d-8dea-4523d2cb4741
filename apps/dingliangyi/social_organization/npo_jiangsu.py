import re
import schedule
from loguru import logger
import traceback
from tqdm import tqdm
import time
from datetime import datetime

from apps.dingliangyi.social_organization.npo_base import NpoSpider
from apps.dingliangyi.social_organization.npo_base import MyException


class ShangDongNpoSpider(NpoSpider):
    def __init__(self):
        super().__init__()
        self.headers = {
            "Referer": "https://shzzgs.jszhmz.cn/shzz/list.html",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0",
        }
        self.types = {
            'S': '社会团体',
            'M': '民办非企业单位',
            'J': '基金会'
        }
        self.timeout = 10

    def get_list(self, type_):
        url = "https://shzzgs.jszhmz.cn/api/web/shzz/grid"
        params = {"credit_status": "22", "page": "1", 'type': type_}
        for i in range(747, 4235):
            params['page'] = str(i)
            res: dict = self.request(self.new_session(), 'GET', url, params=params, tojson=True, name=f'get_list-{i}')
            if self.first_page or not res['data']['items'] or datetime.strptime(res['data']['items'][0]['established_time'][:10], '%Y-%m-%d').year < 1949:
                logger.info(f'翻页到{i}页结束')
                return res['data']['items']
            yield res['data']['items']

    def parse(self, data, type_):
        if not data.get('name') or not data.get('credit_code'):
            raise MyException('没有名称或统一社会信用代码')
        data_dict = {
            'name': data.get('name', '').strip(),  # 名称
            'base': self.getBaseByAreaCode(data.get('credit_code', '')).strip(),  # 地区
            'unified_social_credit_code': data.get('credit_code', '').strip(),  # 统一社会信用代码
            'registration_authority': self.to_(data.get('sign_unit', '')).strip(),  # 登记管理机关
            'business_unit': self.to_(data.get('competent_unit', '')).strip(),  # 业务主管单位
            'legal_person': self.to_(data.get('legal_person', '')).strip(),  # 法人
            'registration_date': self.to_(data.get('established_time'))[:10],  # 成立登记日期
            'expiry_date': f'{self.to_(data.get("cent_starttime"))[:10]} 至 {self.to_(data.get("cent_endtime"))[:10]}',  # 有效期
            'registered_capital': f'{data["registered_fund"]}万元' if data.get("registered_fund") else "",  # 注册资金
            'status': '正常',  # 登记状态
            "types": self.types[type_],  # 社会组织类型
            "address": self.to_(data.get('address')).strip(),  # 住所
            "business_scope": re.sub(r'[\s\n\t\r]', '', self.to_(data.get('business'))),  # 业务范围
            "property": 'new_country_npo',
            'source': 'https://shzzgs.jszhmz.cn/shzz/list.html'
        }
        return data_dict

    def main_schedule(self, type_):
        generator = self.get_list(type_)
        for idx, list_ in enumerate(generator):
            logger.info(f'总共{len(list_)}条数据')
            for item in tqdm(list_, desc=f'第{idx}页'):
                try:
                    info = self.parse(item, type_)
                    is_exist = self.check_in_db(info['unified_social_credit_code'], info['name'])
                    if not is_exist:
                        self.sum += 1
                        self.save_db(info)
                        logger.info(f'新增数据：{info}')
                except Exception as e:
                    self.custom_traceback(e)

    def main(self):
        self.main_schedule('S')
        self.main_schedule("M")
        self.main_schedule("J")
        logger.success(f'新增{self.sum}条数据')

        logger.info('Start schedule')
        schedule.every().day.at("10:00").do(self.main_schedule)
        while True:
            schedule.run_pending()
            time.sleep(1)


if __name__ == '__main__':
    # 65
    spider = ShangDongNpoSpider()
    spider.main()
