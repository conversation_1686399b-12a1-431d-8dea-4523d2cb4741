# encoding=utf8

import re
import json
import logging
from decimal import Decimal
from typing import List, Optional
from apps.equity_ratio_ng.msv import MSVInvestor, PayDetail
from libs.dt import to_date

logger = logging.getLogger(__name__)


def parse_capital(s: str):
    # capital = 1435.639200万人民币
    # amount = 14356392
    # currency = 人民币
    pats = [(r'([0-9]+.?[0-9]*)', True), (r'([0-9]+.?[0-9]*)万元', True), (r'([0-9]+.?[0-9]*)万人民币', True)]
    for pat, mul_by_w in pats:
        amount = re_group_amount(pat, s, mul_by_w)
        if amount is not None:
            return amount
    return None


def re_group_amount(pat, s, mul_by_w=True) -> Optional[int]:
    mo = re.fullmatch(pat, s)
    if not mo:
        return None
    v = Decimal(mo.group(1))
    if mul_by_w:
        v *= 10000
    return int(v)


# 先只用考虑 subscribe_detail
def update_investor_detail(investor: MSVInvestor):
    detail_json = json.loads(investor.detail)
    subscribe_amount_str = detail_json.get('subscribeAmount', None)
    if not isinstance(subscribe_amount_str, str):
        subscribe_amount = None  # 未知
    else:
        subscribe_amount = parse_capital(subscribe_amount_str)

    capital_json = json.loads(investor.capital)
    subscribe_detail: List[PayDetail] = []
    for subscribe_item in capital_json:
        # logger.info(f'subscribe_item={subscribe_item}')
        subscribe_item_amount = subscribe_item.get('amomon', None)
        day_str = subscribe_item.get('time', None)
        if isinstance(day_str, str) and re.fullmatch(r'(\d{4}-\d{2}-\d{2})', day_str):
            day = to_date(day_str)
        else:
            day = None
        pay_type = subscribe_item.get('paymet', None)
        if not isinstance(subscribe_item_amount, str):
            continue
        subscribe_detail_amount = parse_capital(subscribe_item_amount)
        if subscribe_detail_amount is not None:
            currency = '人民币'
        else:
            currency = None
        if subscribe_detail_amount is not None:
            subscribe_detail.append(PayDetail(day=day, amount=subscribe_detail_amount, pay_type=pay_type, currency=currency))

    if subscribe_amount is not None and subscribe_amount != sum(pay_detail.amount or 0 for pay_detail in subscribe_detail):
        subscribe_detail = [PayDetail(day=None, amount=subscribe_amount, pay_type=None, currency=None)]
    # logger.info(f'{investor.investor_name} subscribe_detail={subscribe_detail}')
    investor.subscribe_detail = subscribe_detail


def main():
    pass


if __name__ == '__main__':
    # from utils.log import logger
    main()
