# encoding=utf8

from datetime import datetime, date
from typing import List, Dict
from libs.log2 import setup_logger
from libs.dt import to_datetime
from apps.equity_ratio_ng.msv import msv_get_all, MSVInvestorVersion, PayDetail, MSVBaseInfoVersion
from apps.equity_ratio_ng.func import update_investor_detail, parse_capital

logger = setup_logger()


# return None if diffed, else the merge one
def msv_investors_version_same(last: MSVInvestorVersion, current: MSVInvestorVersion) -> bool:
    if current is None:
        # the very last one version
        return False

    same = True
    for investor_last in last.data:
        subscribe_last_total = sum(subscribe_item.amount for subscribe_item in investor_last.subscribe_detail)
        for investor_current in current.data:
            if investor_last.investor_id == investor_current.investor_id:
                subscribe_current_total = sum(subscribe_item.amount for subscribe_item in investor_current.subscribe_detail)
                if subscribe_current_total != subscribe_last_total:
                    if subscribe_current_total > 0 and subscribe_last_total > 0:
                        logger.info(f'diff at {investor_last.investor_name} {subscribe_last_total} -> {subscribe_current_total}')
                        same = False
                    elif subscribe_last_total > 0:
                        pass
                    else:
                        investor_last.subscribe_detail = investor_current.subscribe_detail
                break
        else:
            logger.info(f'diff leave {investor_last.investor_name} {subscribe_last_total}')
            pass  # last has new

    for investor_current in current.data:
        subscribe_current_total = sum(subscribe_item.amount for subscribe_item in investor_current.subscribe_detail)
        for investor_last in last.data:
            if investor_current.investor_id == investor_last.investor_id:
                break
        else:
            # current has new
            last.info.count += 1
            last.data.append(investor_current)
            logger.info(f'diff enter {investor_current.investor_name} {subscribe_current_total}')
    return same


def process(cid: int):
    msv_base_info = msv_get_all(cid=cid, table='company_base_info')
    # logger.info(f'msv_base_info={msv_base_info}')

    msv_investor = msv_get_all(cid=cid, table='company_investor')
    # logger.info(f'msv_investor={msv_investor}')

    if msv_investor is None:
        logger.info(f'error get msv_investor')
        return

    msv_gsxt_investor = msv_investor.get('gsxt_page', None)
    if msv_gsxt_investor is None:
        logger.info(f'msv_investor get no gsxt {msv_investor.keys()}')
        return

    approved_date_to_msv_first: Dict[date, (datetime, str)] = {}
    msv_xa_base_info = msv_base_info.get('XA', None)
    for base_info_version in msv_xa_base_info[::-1]:
        base_info_version: MSVBaseInfoVersion
        if base_info_version.info.count != 1:
            continue
        msv_first = to_datetime(base_info_version.info.msv_first)
        approved_date = base_info_version.data[0].approved_date
        if msv_first > to_datetime(approved_date):
            if approved_date not in approved_date_to_msv_first or approved_date_to_msv_first[approved_date][0] > msv_first:
                approved_date_to_msv_first[approved_date] = (msv_first, base_info_version.data[0].reg_capital)
        # logger.info(f'gsxt_base_info msv_first={msv_first} approved_date={approved_date}')

    msv_gsxt_base_info = msv_base_info.get('gsxt', None)
    for base_info_version in msv_gsxt_base_info[::-1]:
        base_info_version: MSVBaseInfoVersion
        if base_info_version.info.count != 1:
            continue
        msv_first = to_datetime(base_info_version.info.msv_first)
        approved_date = base_info_version.data[0].approved_date
        if msv_first > to_datetime(approved_date):
            if approved_date not in approved_date_to_msv_first or approved_date_to_msv_first[approved_date][0] > msv_first:
                approved_date_to_msv_first[approved_date] = (msv_first, base_info_version.data[0].reg_capital)
        # logger.info(f'gsxt_base_info msv_first={msv_first} approved_date={approved_date}')

    approved_date_list = sorted(approved_date_to_msv_first.items(), reverse=True)
    logger.info(f'approved_date_list={approved_date_list}')

    msv_investors_first_subscribe_dict: Dict[int, List[PayDetail]] = {}
    for msv_investors_version in msv_gsxt_investor[::1]:
        msv_investors_version: MSVInvestorVersion
        for msv_investor in msv_investors_version.data:
            update_investor_detail(msv_investor)
            if len(msv_investor.subscribe_detail) > 0:
                if msv_investor.investor_id not in msv_investors_first_subscribe_dict:
                    msv_investors_first_subscribe_dict[msv_investor.investor_id] = msv_investor.subscribe_detail

    msv_investors_last_subscribe_dict: Dict[int, List[PayDetail]] = {}
    approved_date_idx = 0
    for msv_investors_version in msv_gsxt_investor[::1]:
        msv_investors_version: MSVInvestorVersion
        if msv_investors_version.info.count == 0:  # 股东抓取失败的场景
            continue
        # logger.info(f'msv_investor_version={msv_investors_version.info.count} {msv_investors_version.info.msv_first}')

        for msv_investor in msv_investors_version.data:
            if len(msv_investor.subscribe_detail) == 0:
                if msv_investor.investor_id in msv_investors_last_subscribe_dict:
                    msv_investor.subscribe_detail = msv_investors_last_subscribe_dict[msv_investor.investor_id]
                elif msv_investor.investor_id in msv_investors_first_subscribe_dict:
                    msv_investor.subscribe_detail = msv_investors_first_subscribe_dict[msv_investor.investor_id]
                else:
                    logger.warning(f'no subscribe detail for {msv_investor.investor_name}')
            else:
                msv_investors_last_subscribe_dict[msv_investor.investor_id] = msv_investor.subscribe_detail

            # logger.info(f'msv_investor={msv_investor}')

        investor_digests, investor_subscribe_total = [], 0
        for investor in msv_investors_version.data:
            subscribe_total = sum(subscribe_item.amount for subscribe_item in investor.subscribe_detail)
            investor_subscribe_total += subscribe_total
            investor_digests.append(f'{investor.investor_name}:{subscribe_total}')
        while approved_date_idx < len(approved_date_list):
            msv_first = approved_date_list[approved_date_idx][1][0]
            if msv_investors_version.info.msv_first >= msv_first:
                break
            approved_date_idx += 1
        approved_date = approved_date_list[approved_date_idx][0]
        reg_capital = approved_date_list[approved_date_idx][1][1]
        reg_amount = parse_capital(reg_capital)
        if reg_amount == investor_subscribe_total:
            logger.info(f'{msv_investors_version.info.msv_first} {approved_date} {reg_amount} {investor_subscribe_total} {investor_digests}')


def main():
    # 3963535 天眼查
    # 227708197 企查查
    process(3963535)


if __name__ == '__main__':
    main()
