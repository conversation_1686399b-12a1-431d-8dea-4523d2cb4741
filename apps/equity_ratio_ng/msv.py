# encoding=utf8

from typing import Dict, Optional, List
from pydantic import Field
from datetime import date, datetime
import requests
import logging
from entity.deps.entity import BaseEntity
from libs.dt import to_datetime

logger = logging.getLogger(__name__)


class MSVItemData(BaseEntity):
    cid: int
    source: str
    msv_deleted: int


class MSVItemInfo(BaseEntity):
    count: int
    msv_first: datetime
    msv_last: datetime

    def __init__(self, **kwargs):
        if 'msv_first' in kwargs:
            kwargs['msv_first'] = to_datetime(kwargs['msv_first'])
        if 'msv_last' in kwargs:
            kwargs['msv_last'] = to_datetime(kwargs['msv_last'])
        super().__init__(**kwargs)


class MSVCompanyBase(MSVItemData):
    approved_date: date = Field(alias='approved_time')
    company_org_type: str = Field(default=None)
    establish_date: date = Field(alias='estiblish_time')
    name: str
    reg_capital: str = Field(default=None)
    reg_status: str
    reg_capital_amount: str = Field(default=None)
    reg_capital_currency: str = Field(default=None)


class PayDetail(BaseEntity):
    day: Optional[date] = Field(default=None)  # 缴费时间 未知
    amount: int  # 缴费数额  单位元
    pay_type: Optional[str] = Field(default=None)  # 未知 缴费方式 资金入股 还是 知识产权等
    currency: Optional[str] = Field(default=None)  # 未知 币种


class MSVInvestor(MSVItemData):
    capital: str = Field(default=None)
    capital_actl: str = Field(default=None)
    detail: str = Field(default=None)
    investor_id: int = Field(default=None)
    investor_name: str = Field(default=None)
    investor_type: int = Field(default=None)
    subscribe_detail: List[PayDetail] = Field(default=None)  # 解析后的认缴
    actual_detail: List[PayDetail] = Field(default=None)  # 解析后的实缴


class MSVInvestorVersion(BaseEntity):
    data: List[MSVInvestor]
    info: MSVItemInfo


class MSVBaseInfoVersion(BaseEntity):
    data: List[MSVCompanyBase]
    info: MSVItemInfo


def msv_get_all(cid: int, table) -> Optional[Dict[str, List]]:
    msv_params = {"table_name": table, "cid": cid, "human": True}
    try:
        response = requests.post(
            url='http://msv-gsdata.jindidata.com/read_list_version',
            headers={
                'Content-Type': 'application/json',
            },
            json=msv_params,
            timeout=5,
        )
    except requests.exceptions.ReadTimeout:
        logger.warning(f'get msv api timeout msv_params={msv_params}')
        return None

    if response.status_code != 200:
        logger.warning(f'bad response={response.status_code} {response.text} msv_params={msv_params}')
        return None

    js = response.json()
    data, errcode = js.get('data', None), js.get('errcode', None)
    if errcode != 0 or not isinstance(data, dict):
        logger.warning(f'bad response={response.status_code} {js} msv_params={msv_params}')
        return None

    ret = {}
    for source_t, source_list in data.items():
        if not isinstance(source_list, list):
            logger.warning(f'bad response={response.status_code} {source_list} msv_params={msv_params}')
            return None
        source_list_obj = []
        for version_dict in source_list:
            if not isinstance(version_dict, dict):
                logger.warning(f'bad response={response.status_code} {version_dict} msv_params={msv_params}')
                return None
            clazz = MSVBaseInfoVersion if table == 'company_base_info' else MSVInvestorVersion
            version_entity = clazz.from_dict(version_dict)
            source_list_obj.append(version_entity)
        source_t: str
        ret[source_t] = source_list_obj
    return ret


def main():
    ret = msv_get_all(cid=99334, table='company_base_info')
    logger.info(f'ret={ret}')


if __name__ == '__main__':
    from libs.log2 import setup_logger
    logger = setup_logger()
    main()
