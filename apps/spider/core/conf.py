
from typing import Any, Optional
from pydantic import Field
from entity.deps.entity import BaseEntity


class ClassArgsConf(BaseEntity):
    clazz: str
    obj: Optional[Any] = Field(default=None)
    args: dict = Field(default={})


class CrawlerConf(ClassArgsConf):
    page_bak_count: int = Field(default=1)
    crawler_threads: int = Field(default=1)


class ParserConf(ClassArgsConf):
    parser_threads: int = Field(default=1)


class SpiderConf(BaseEntity):
    spider_name: str  # same as the inst name in OCTOPUS
    crawler: CrawlerConf
    parser: ParserConf
