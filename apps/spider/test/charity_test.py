# encoding=utf8
from loguru import logger

from apps.octopus.core.eventlog import Eventlog
from apps.spider.crawler import CharityCrawler
from apps.spider.parser import CharityParser
from apps.spider.utils.conf_manager import SpiderConfManager

if __name__ == '__main__':
    def get_eventlog(word='53430100MJJ5209744'):
        return Eventlog.from_dict(
            {
                "event_id": "octopvs-credit-cods-81530821MC07490153-1709172352",
                "code": -1,
                "selector": {
                    "send_ts": 1709172352,
                    "receive_ts": -1,
                    "reason": "schedule",
                    "clue": False,
                    "entry_name": "credit",
                    "inst_name": "cods",
                    "word": word,
                    "info": {
                    },
                    "try_id": 0,
                    "meta": {},
                    "weight": 550
                },
                "spider": {
                    "receive_ts": -1,
                    "send_ts": -1,
                    "spider_data": {
                        # "page_ts": 1713501997
                    }
                }
            }
        )


    manager = SpiderConfManager(json_conf_name='charity.json', reload_conf_interval_sec=0)
    spider_conf = manager.get_exact_one()

    crawler = CharityCrawler(spider_name=spider_conf.spider_name, crawler_conf=spider_conf.crawler)
    parser = CharityParser(spider_name=spider_conf.spider_name, parser_conf=spider_conf.parser)

    eventlog = get_eventlog('53320800MJ7316795H')
    crawler.crawl(eventlog)
    parser.parse(eventlog)
