# encoding=utf8

from libs.dt import cur_ts_sec
from apps.spider.crawler import GSXTApp<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>_
from apps.spider.parser import GSXT<PERSON><PERSON>Parser as Parser_
from libs.log2 import setup_logger
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.spider.utils.conf_manager import SpiderConfManager


if __name__ == '__main__':
    # 本地抓取（更新/新增）一个公司
    logger = setup_logger()

    manager = SpiderConfManager(json_conf_name='gsxt_app.json', reload_conf_interval_sec=0)
    spider_conf = manager.get_exact_one()

    reason = 'schedule'
    entry_name, inst_name = 'credit', 'gsxt_app'
    word = '91110113663115984P'
    cid = 39

    eventlog = Eventlog.from_dict(
        {
            "event_id": f"octopvs_entry-{entry_name}-{word}-{inst_name}-{cur_ts_sec()}",
            "code": EventlogCode.UNFILLED.value,
            "selector": {
                "send_ts": 1703756336,
                "receive_ts": -1,
                "reason": reason,
                "clue": False,
                "entry_name": entry_name,
                "inst_name": inst_name,
                "word": word,
                "info": {
                    "cid": cid,
                },
                "try_id": 0,
                "meta": {},
                "weight": 960
            },
            "spider": {
                "receive_ts": -1,
                "send_ts": -1,
                "spider_data": {
                    # "page_ts": 1707232219
                }
            }
        }
    )

    crawler = Crawler_(spider_name=spider_conf.spider_name, crawler_conf=spider_conf.crawler)
    parser = Parser_(spider_name=spider_conf.spider_name, parser_conf=spider_conf.parser)
    crawler.crawl(eventlog)
    parser.parse(eventlog)
    logger.info(f'OUTPUT eventlog={eventlog.to_json()}')
