from apps.spider.crawler import <PERSON>rawlerHK<PERSON> as <PERSON>raw<PERSON>_
from apps.spider.parser import <PERSON><PERSON><PERSON><PERSON><PERSON> as Parse<PERSON>_
from apps.spider.utils.conf_manager import SpiderConfManager
from libs.log import setup_logger
from apps.octopus.core.eventlog import Eventlog, EventlogCode

if __name__ == '__main__':
    # 本地抓取（更新/新增）一个公司
    logger = setup_logger()
    manager = SpiderConfManager(json_conf_name='hk.json', reload_conf_interval_sec=0)
    spider_conf = manager.get_exact_one()

    reason = 'hk_payed_report'
    # reason = 'schedule'

    crawler = Crawler_(spider_name=spider_conf.spider_name, crawler_conf=spider_conf.crawler)
    parser = Parser_(spider_name=spider_conf.spider_name, parser_conf=spider_conf.parser)
    for i in ['30257623']:
        word = str(i)
        eventlog = Eventlog.from_dict({
            "event_id": f"octopvs_entry-brno-{word}-hk-1703756336", "code": EventlogCode.UNFILLED.value,
            "selector": {"send_ts": 63836777, "receive_ts": -1, "reason": reason, "clue": False, "entry_name": "brno",
                         "inst_name": "hk", "word": word, "info": {}, "try_id": 0, "meta": {}, "weight": 960},
            "spider": {"spider_data": {
                "page_ts": 1750297433
            }}})
        # crawler.crawl(eventlog)
        parser.parse(eventlog)
        logger.info(f'OUTPUT eventlog={eventlog.to_json()}')
