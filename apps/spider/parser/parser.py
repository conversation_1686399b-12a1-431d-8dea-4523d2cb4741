# encoding=utf8
import logging
from abc import abstractmethod
from threading import Lock, current_thread
from typing import Dict
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.spider.utils.obs_manager import OBSManager
from apps.octopus.core.eventlog_store import EventlogStore, EventlogStorePos
from apps.spider.core.conf import ParserConf
import traceback

logger = logging.getLogger(__name__)


class ParserTask(object):
    def __init__(self, eventlog: Eventlog, pages):
        self.eventlog: Eventlog = eventlog
        self.event_id = self.eventlog.event_id
        self.pages: Dict[str, str] = pages


class Parser(object):
    def __init__(self, spider_name: str, parser_conf: ParserConf, task_cls):
        self.spider_name = spider_name  # the spider's name
        self.task_cls = task_cls
        self.obs_manager = OBSManager()
        self.tasks: Dict[int, task_cls] = dict()
        self.tasks_lock = Lock()
        self.eventlog_store = EventlogStore(max_workers=2)

    def parse(self, eventlog: Eventlog) -> Eventlog:
        logger.info(f'==== BEGIN {eventlog}')

        # filter bad eventlog
        if eventlog.code != EventlogCode.UNFILLED:
            return eventlog

        page_ts = eventlog.spider.spider_data.get('page_ts', 0)
        if page_ts == 0:
            eventlog.code = EventlogCode.FAIL
            logger.warning(f'page_ts bad {eventlog.event_id}')
            return eventlog

        pages_obs_path = f'page/{eventlog.selector.inst_name}/{eventlog.selector.word}/{page_ts}'
        pages = self.obs_manager.download_pages(pages_obs_path)

        with self.tasks_lock:
            tid = current_thread().ident
            self.tasks[tid] = self.task_cls(eventlog, pages)
        self.do_parse()

        if eventlog.code == EventlogCode.UNFILLED:
            self.post_parse()

        # 最后设置成功
        if eventlog.code == EventlogCode.UNFILLED:
            eventlog.code = EventlogCode.SUCCESS
            # eventlog.crawler_code = 0
            # eventlog.parser_code = 0
        self.eventlog_store.save(eventlog, pos=EventlogStorePos.PARSED)

        return eventlog

    @abstractmethod
    def do_parse(self):
        pass

    def post_parse(self):
        # task: ParserTask = self.get_parser_task()
        # if eventlog.is_clue:
        # eventlog.spider.item_insert 这个需要 业务自己处理 parser_gsxt已处理
        # 设置 eventlog.selector.info['entry_word'] 以及 eventlog.selector.info['entry_*']
        pass

    def get_parser_task(self):
        with self.tasks_lock:
            tid = current_thread().ident
            if tid not in self.tasks:
                raise RuntimeError(f'not task tid={tid}')
            return self.tasks[tid]

    @staticmethod
    def custom_traceback(e, print_traceback=True):
        exc_type = type(e)
        exc_value = e
        tb = e.__traceback__
        """
        自定义打印 traceback，仅显示用户代码的报错信息
        """
        # 提取 traceback 信息
        tb_list = traceback.extract_tb(tb)
        user_tb = [frame for frame in tb_list if 'pygs-work-parent' in frame.filename]
        error = ''
        if user_tb:
            if print_traceback:
                print("Traceback (most recent call last):")
                # 格式化并打印用户代码的 traceback 信息
                print("".join(traceback.format_list(user_tb)))
                print(f"{exc_type.__name__}: {exc_value}")
            error += "".join(traceback.format_list(user_tb)) + f"{exc_type.__name__}: {exc_value}"
        else:
            if print_traceback:
                print(f"{exc_type.__name__}: {exc_value}")
            error += f"{exc_type.__name__}: {exc_value}"
        return error


class MyException(Exception):

    def __init__(self, message):
        self.message = message
        super().__init__(self.message)
