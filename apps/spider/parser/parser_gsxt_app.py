# encoding=utf8
from datetime import datetime, timedelta
import json
import logging
import re
from libs.dt import to_date
from apps.octopus.core.eventlog import EventlogCode
from apps.spider.parser.parser import Parser, ParserTask
from apps.spider.core.conf import ParserConf
from dao.company_supplement import CompanySupplementDao, CompanySupplement
from dao.octopus.item import ItemDao
from dao.company import CompanyDao, Company
from dao.reports.annual_report import AnnualReportDao
from gslib.schedule import immediately_gs

logger = logging.getLogger(__name__)


class GSXTAppParserTask(ParserTask):
    pass


class GSXTAppParser(Parser):
    def __init__(self, spider_name: str, parser_conf: ParserConf):
        self.company_sup_dao = CompanySupplementDao()
        self.octopus_item_dao = ItemDao(name='company', is_clue=False)
        self.company_dao = CompanyDao()
        self.report_dao = AnnualReportDao()
        super().__init__(spider_name=spider_name, task_cls=GSXTAppParserTask, parser_conf=parser_conf)

    def do_parse(self):
        task: GSXTAppParserTask = self.get_parser_task()
        eventlog = task.eventlog
        pages = task.pages

        base_content = pages.get('base.txt', None)
        if not base_content:
            logger.warning(f'no page base {task.event_id}')
            eventlog.code = EventlogCode.FAIL
            return

        base_content_d: dict = json.loads(base_content)
        base_info = base_content_d.get('result', {})
        if not base_info:
            logger.warning(f'empty detail page {task.event_id}')
            eventlog.code = EventlogCode.GIVE_UP
            return

        # reg_capital = None
        # if 'regCap' in base_info and 'regCapCur_CN' in base_info:
        #     reg_capital = '{}万{}'.format(base_info['regCap'], base_info['regCapCur_CN'])

        # msv_base_info_data = dict(
        #     name=base_info['entName'],
        #     legal_person_name=base_info['name'],
        #     reg_number=base_info.get('regNo', None),
        #     company_org_type=base_info.get('entType_CN', None),
        #     reg_location=base_info.get('dom', None),
        #     estiblish_time=to_date(base_info['estDate']),
        #     approved_time=to_date(base_info['apprDate']),
        #     from_time=base_info.get('opFrom', None),
        #     to_time=base_info.get('opTo', None),
        #     business_scope=base_info.get('opScope', '').strip(),
        #     reg_institute=base_info.get('regOrg_CN', None),
        #     reg_status=base_info.get('regState_CN', None),
        #     reg_capital=reg_capital,
        #     credit_code=base_info['uniscId'],
        # )

        name = base_info.get('entName', None) or base_info.get('traName', '')
        establish_date = to_date(base_info['estDate'])
        cid = eventlog.selector.info.get('cid', None)
        approved_date = to_date(base_info['apprDate'])

        if not isinstance(cid, int):
            logger.warning(f'no cid in eventlog {eventlog.event_id}')
            eventlog.code = EventlogCode.GIVE_UP
            return

        c: Company = self.company_dao.get(cid)
        if not c:
            logger.warning(f'no company for cid {cid} {eventlog.event_id}')
            eventlog.code = EventlogCode.GIVE_UP
            return

        if approved_date and approved_date > (datetime.now() - timedelta(days=30)).date():
            if c.approved_date and c.approved_date < approved_date and len(c.credit_code or '') == 18:
                immediately_gs(
                    credit_code=c.credit_code,
                    reason='app_chk_appr_change',
                    annual_report_enable=False,
                )
                logger.info(f'approved_date {c.approved_date}->{approved_date} {c.name} {c.credit_code}')

        company_sup: CompanySupplement = CompanySupplement.from_dict(dict(
            company_id=cid,
            name=name,
            establish_date=establish_date,
        ))
        if company_sup:
            # company_supplement.company_org_code
            pre_ent_type = base_content_d.get('preEntType', '')
            if re.fullmatch(r'\d{4}', pre_ent_type):
                company_sup.company_org_code = pre_ent_type
            # company_supplement.industry_code
            industry_code = base_info.get('industryPhy', '')
            if len(industry_code) == 1:
                company_sup.industry_code = industry_code
            self.company_sup_dao.save_by_cmp(
                item=company_sup,
                fields=['company_id', ],
                ignore_fields=['ent_type', 'id', 'establish_date'],
            )

        # report
        report_content = pages.get('report.txt', None)
        if report_content:
            report_info_list = json.loads(report_content)
            if not isinstance(report_info_list, list):
                logger.warning(f'not list {report_content}')
                report_info_list = []
            report_years = ''
            for report in report_info_list:
                report_year = report.get('anCheYear', None)
                if report_year:
                    report_years += f' {report_year}'
                if report_year == '2023':
                    report = self.report_dao.get_ex(values=[cid, 2023], fields=['company_id', 'report_year'])
                    if not report:
                        ret = self.octopus_item_dao.set_info(
                            word=str(cid),
                            do_update=False,
                            report_2023_check_ts=0,
                        )
                        logger.info(f'report_2023_check_ts cid={cid} ret={ret} {eventlog.event_id}')
            logger.info(f'report cid={cid} {report_years} {eventlog.event_id}')
