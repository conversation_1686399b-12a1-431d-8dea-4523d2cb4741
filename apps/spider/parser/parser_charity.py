import json
import logging
from libs.dt import to_date
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.spider.parser.parser import ParserTask, Parser
from apps.spider.core.conf import ParserConf
from datetime import datetime, date
from loguru import logger
from dao.charity.charity import <PERSON><PERSON>ao, CharityEntity
import re


class CharityParserTask(ParserTask):
    pass


class CharityParser(Parser):

    def __init__(self, spider_name: str, parser_conf: ParserConf):
        self.dao = CharityDao()
        super().__init__(spider_name=spider_name, task_cls=CharityParserTask, parser_conf=parser_conf)

    def do_parse(self):
        task: CharityParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        pages = task.pages

        info = pages.get('info.json', '')
        if not info:
            logger.warning(f'no info {eventlog}')
            return

        info = json.loads(info)
        logger.info(f'info: {info}')
        times = re.findall(r'[A-Za-z]{3} \d{1,2}, \d{4}', info.get('税前扣除资格有效期', ''))
        times_ = []
        for time in times:
            times_.append(datetime.strptime(time, "%b %d, %Y").date())

        charity_entity = CharityEntity.from_dict({
            "charity_name": info.get('组织名称', ''),
            "certificate_code": info.get('统一社会信用代码', ''),
            "establish_date": datetime.strptime(info['成立时间'], "%b %d, %Y").date() if info.get('成立时间', '') else date(1970, 1, 1),
            "department": info.get('登记管理机关', ''),
            "pretax_deduction_eligibility": 1 if info.get('是否取得公益性捐赠税前扣除资格', '') == '是' else 0,
            "pretax_deduction_eligibility_validity_from": str(times_[0]) if times_ else '1970-01-01',
            "pretax_deduction_eligibility_validity_to": str(times_[1]) if len(times_) > 1 else '1970-01-01',
            "public_raise_eligibility": 1 if info.get('是否具有公开募捐资格', '') == '是' else 0,
            "register_date": datetime.strptime(info['慈善组织认定登记日期'], "%b %d, %Y").date() if info.get('慈善组织认定登记日期', '') else date(1970, 1, 1),
            "address": info.get('组织住所', ''),
            "business_scope": info.get('业务范围', ''),
            "email": info.get("电子邮箱地址", ''),
            "fax": info.get('传真号', ''),
            "website": info.get('门户网站', ''),
            "weibo": info.get('官方微博', ''),
            "telephone": info.get('联系电话', ''),
            "wechat": info.get('官方微信公众号', ''),
            "network_platform": info.get('移动客户端等其他网络平台', ''),
            "legal": info.get('法定代表人', ''),
            "people": info.get('联系人', ''),
        })

        self.dao.save_by_cmp(charity_entity, fields=['certificate_code'], ignore_fields=['id', 'update_time'])
