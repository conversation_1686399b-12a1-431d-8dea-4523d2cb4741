# encoding=utf8

import logging
from typing import List, Tuple, Dict
from pydantic import Field
from entity.deps.entity import BaseEntity
from apps.gs_spider.utils.msv import msv_write
from apps.gs_spider.utils.msv_enum import MSVSource, MSVTableName

logger = logging.getLogger(__name__)


# 这里主要人员一定是自然人 农合社信息放股东
class MSVStaff(BaseEntity):
    staff_name: str
    staff_id: int = Field(default=0)  # cid
    staff_position: str = Field(default='未知')
    staff_other_info: str = Field(default='')


def msv_write_staff(items: List[MSVStaff], cid: int, source: MSVSource) -> Tuple[bool, Dict]:
    row_data = []
    for item in items:
        item_dict = item.to_dict()
        row_data.append(item_dict)

    return msv_write(
        table_name=MSVTableName.STAFF.value,
        cid=cid,
        source=source.value,
        row_data=row_data,
        list_mode=True,
    )


if __name__ == '__main__':
    from dao.company import TEST_COMPANY
    o1 = MSVStaff.from_dict(
        {
            'staff_name': '高管1',
            'staff_id': 101,
            'staff_position': '经理',
        }
    )

    o2 = MSVStaff.from_dict(
        {
            'staff_name': '高管2',
            'staff_id': 102,
        }
    )
    msv_write_staff(cid=TEST_COMPANY.cid, source=MSVSource.APP2, items=[o1, o2])
