# encoding=utf8

import logging
from typing import Optional, Tuple, Dict
from datetime import datetime, date
from pydantic import Field
from entity.deps.entity import BaseEntity
from gslib.gs_enum import EntityType
from apps.gs_spider.utils.msv import msv_write
from apps.gs_spider.utils.msv_enum import MSVSource, MSVTableName

logger = logging.getLogger(__name__)


class MSVBaseInfo(BaseEntity):
    name: str

    # 以下3字段为法人信息，可以为空 legal_person_type ~ 1，2，3
    legal_person_id: Optional[int] = Field(default=None)
    legal_person_name: Optional[str] = Field(default=None)
    legal_person_type: EntityType = Field(default=EntityType.UNSET)

    reg_number: Optional[str] = Field(default=None)
    company_org_type: Optional[str] = Field(default=None)
    reg_location: Optional[str] = Field(default=None)
    establish_date: Optional[date] = Field(default=None, alias='estiblish_time')
    approved_date: Optional[date] = Field(default=None, alias='approved_time')
    from_time: Optional[date] = Field(default=None)
    to_time: Optional[date] = Field(default=None)
    business_scope: Optional[str] = Field(default=None)
    reg_institute: Optional[str] = Field(default=None)
    reg_status: Optional[str] = Field(default=None)
    reg_capital: Optional[str] = Field(default=None)
    credit_code: Optional[str] = Field(default=None)


def msv_write_base_info(item: MSVBaseInfo, cid: int, source: MSVSource) -> Tuple[bool, Dict]:
    return msv_write(
        table_name=MSVTableName.BASE.value,
        cid=cid,
        source=source.value,
        row_data=[item.to_dict(), ]
    )


if __name__ == '__main__':
    from dao.company import TEST_COMPANY
    o = MSVBaseInfo.from_dict(
        {
            'name': 'xxx有限公司',
            'estiblish_time': datetime.now().date(),
            'to_time': datetime.now().date(),

        }
    )
    msv_write_base_info(cid=TEST_COMPANY.cid, source=MSVSource.APP2, item=o)
