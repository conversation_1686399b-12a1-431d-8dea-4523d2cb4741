# encoding=utf8
from datetime import datetime
import logging
from typing import Dict
from libs.dt import cur_ts_sec
from clients.obs_client import OBSClient

logger = logging.getLogger(__name__)


class OBSManager(object):

    def __init__(self, ):
        self.obs_client = OBSClient(bucket_name='jindi-oss-gsxt')

    def upload_pages(self, pages: Dict[str, str], bak_count: int, base_dir: str) -> int:
        #     if len(pages) > 0:  # 是否存储OBS 取决于本次是否抓取到数据
        if len(pages) == 0:
            logger.warning(f'empty pages base_dir={base_dir}')
            return 0
        ts = cur_ts_sec()
        page_dir_list = self.obs_client.list(base_dir, recursive=False)
        page_new_dir = f'{base_dir}/{ts}'
        if len(page_dir_list) >= bak_count:
            self.obs_client.delete(path=page_dir_list[0], recursive=True)
        for page_name, page_content in pages.items():
            self.obs_client.put(path=f'{page_new_dir}/{page_name}', data=page_content)
        return ts

    def upload_hk(self, pages: Dict[str, str], base_dir: str) -> bool:
        if len(pages) == 0:
            logger.warning(f'empty pages base_dir={base_dir}')
            return False

        if self.obs_client.exists_dir(base_dir):
            return False

        date = datetime.now().strftime('%Y_%m_%d')
        page_new_dir = f'{base_dir}/{date}'
        for page_name, page_content in pages.items():
            self.obs_client.put(path=f'{page_new_dir}/{page_name}', data=page_content)
        return True

    def download_pages(self, page_dir: str) -> Dict[str, str]:
        pages = dict()
        for page_dir_item in self.obs_client.list(page_dir):
            if page_dir_item.endswith('jpg'):
                continue
            page = self.obs_client.get(page_dir_item)
            page_name = page_dir_item.split('/')[-1]
            pages[page_name] = page
        return pages

    # def exists_pages(self, target_dir: str):
    #     ...
    #
    # def getObject(self, f):
    #     ...
    #
    # def find_list(self, base_dir):
    #     return self.obs_client.list(base_dir)


if __name__ == '__main__':
    from libs.log import setup_logger

    logger = setup_logger()
    o = OBSManager()
    o.download_pages(page_dir='page/app2/123/1699328304')
