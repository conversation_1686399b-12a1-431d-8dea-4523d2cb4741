# encoding=utf8

import os
import logging
import json
import threading
import time
from typing import Dict, Optional
from threading import Lock
from libs.env import get_stack_info
from apps.spider.core.constants import SPIDER_DIR
from apps.spider.crawler import *
from apps.spider.parser import *
from apps.spider.core.conf import SpiderConf

logger = logging.getLogger(__file__)


class SpiderConfManager(object):
    def __init__(self, json_conf_name=None, reload_conf_interval_sec: int = 0):
        """
        :param reload_conf_interval_sec:  重新加载配置时间间隔 0表示不重新加载
        :param json_conf_name:  指定配置文件名  None表示所有配置
        """
        self.reload_conf_interval_sec = reload_conf_interval_sec
        self.json_conf_name = json_conf_name
        self.lock = Lock()
        self.conf_text_dict: Dict[str, str] = {}  # json_conf_name -> text
        self.conf_dict: Dict[str, SpiderConf] = {}  # inst -> conf
        self.run_()
        if self.reload_conf_interval_sec > 0:
            t = threading.Thread(target=self.run, args=(), daemon=True)
            t.start()

    def run_(self):
        for file in os.listdir(f'{SPIDER_DIR}/conf'):
            if not file.endswith('.json') or (self.json_conf_name and self.json_conf_name != file):
                continue
            with open(f'{SPIDER_DIR}/conf/{file}', 'r') as fp:
                try:
                    conf_text = fp.read()
                    if file in self.conf_text_dict and self.conf_text_dict[file] == conf_text:
                        continue
                    conf = SpiderConf.from_dict(json.loads(conf_text))
                    crawler_class = globals()[conf.crawler.clazz]
                    crawler_: Crawler = crawler_class(conf.spider_name, conf.crawler)
                    conf.crawler.obj = crawler_
                    parser_class = globals()[conf.parser.clazz]
                    parser_: Parser = parser_class(conf.spider_name, conf.parser)
                    conf.parser.obj = parser_

                    with self.lock:
                        self.conf_text_dict[file] = conf_text
                        self.conf_dict[conf.spider_name] = conf
                        logger.info(f'successfully load {file} {conf.spider_name} {conf}')
                except Exception as e:
                    logger.warning(f'error load {file} {e} {get_stack_info()}')

    def run(self):
        while True:
            self.run_()
            time.sleep(self.reload_conf_interval_sec)

    def get_by_name(self, spider_name) -> Optional[SpiderConf]:
        with self.lock:
            return self.conf_dict.get(spider_name, None)

    def get_exact_one(self) -> Optional[SpiderConf]:
        with self.lock:
            if len(self.conf_dict) != 1:
                logger.warning(f'bad conf_dict size={len(self.conf_dict)}')
                return None
            return list(self.conf_dict.values())[0]


if __name__ == '__main__':
    from libs.log2 import setup_logger
    logger = setup_logger()
    manager = SpiderConfManager()
    logger.info(f"{manager.get_exact_one()}")
