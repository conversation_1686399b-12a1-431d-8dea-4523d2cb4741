# encoding=utf8

import enum


class MSVSource(enum.Enum):
    APP2 = 'app2'
    GSXT = 'gsxt'
    XA = 'XA'
    BJ = 'bjold'
    JS = 'jsold'
    GDSZ = 'gdsz'
    GZ = 'gzold'
    HLJ = 'hljold'
    HAN = 'hanold'
    GD = 'gdold'
    TJ = 'tjold'
    JX = 'jxold'
    FJXM = 'xm'
    GSXT_PAGE = 'gsxt_page'
    SH = 'shold'
    GDZH = 'gdzh'


class MSVTableName(enum.Enum):
    BASE = 'company_base_info'
    CHANGE = 'company_change_info'
    INVESTOR = 'company_investor'
    STAFF = 'company_staff'
    PARTNERSHIP = 'company_partnership_info'
