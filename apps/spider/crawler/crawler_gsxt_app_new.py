# encoding=utf8

import re
import json
import logging
import random
import requests
from typing import Optional
from urllib.parse import quote_plus
from requests import Response
from apps.spider.utils.req import request
from dao.company_clean_info import CompanyCleanInfoDao, CompanyCleanInfo
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.octopus.utils.conf_manager import OctopusConfManager
from apps.octopus.core.inst import Inst
from apps.octopus.entries.entry_dao import Entry, EntryDao
from apps.spider.crawler.crawler import Crawler, CrawlerTask
from apps.spider.core.conf import CrawlerConf
from libs.dt import cur_ts_sec

logger = logging.getLogger(__name__)


class GSXTAppCrawlerTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        self.proxy = None
        self.entry: Optional[Entry] = None
        self.inst: Optional[Inst] = None
        super().__init__(eventlog)


class GSXTAppCrawler(Crawler):
    CACHE_SECS = 86000  # 86400
    ALIAS_SEARCH_PAGES = 10  # 10
    USE_DEFAULT_PROXY = True

    def __init__(self, spider_name: str, crawler_conf: CrawlerConf):
        self.company_clean_info_dao = CompanyCleanInfoDao()
        conf_manager = OctopusConfManager(json_conf_name='gsxt_app.credit.json', reload_conf_interval_sec=0)
        self.entry_dao: EntryDao = conf_manager.get_exact_one().entry_dao.obj

        self.proxy_list, self.proxy_list_last_update_ts = [], 0

        super().__init__(spider_name=spider_name, crawler_conf=crawler_conf, task_cls=GSXTAppCrawlerTask)

    def get_task_proxy(self, ) -> Optional[dict]:
        if self.USE_DEFAULT_PROXY:
            return None
        with self.tasks_lock:
            if self.proxy_list_last_update_ts + 5 < cur_ts_sec():
                self.proxy_list_last_update_ts = cur_ts_sec()
                response = requests.get('http://*************:8015/tydaili2', timeout=3.0)
                if response.status_code == 200:
                    self.proxy_list.clear()
                    for s in response.text.split('\n'):
                        if len(s.strip()) > 10:
                            self.proxy_list.append(s.strip())
                    logger.info(f'reload proxy of size {len(self.proxy_list)}')
        proxy = random.choice(self.proxy_list)
        return {"https": f"http://{proxy}", "http": f"http://{proxy}"}

    # 有详情数据 则不走搜索 否则
    # 模糊搜索 或者 精确搜索 命中结果 则更新 detail_page_data
    # return pripid#node_num#ent_type or None or ''
    def do_request_search(self, task: GSXTAppCrawlerTask) -> Optional[str]:
        eventlog = task.eventlog
        if task.inst.inst_info.get('detail_ts', 0) + self.CACHE_SECS > cur_ts_sec():
            logger.info(f'{eventlog.event_id} use cache detail_data')
            eventlog.spider.spider_data['cache_detail_data'] = True
            return task.inst.inst_info['detail_data']
        # do search for alias
        cid = eventlog.selector.info.get('cid', None)
        if isinstance(cid, int):
            company_clean_info: CompanyCleanInfo = self.company_clean_info_dao.get(cid)
            if company_clean_info and isinstance(company_clean_info.alias, str) and len(company_clean_info.alias) < 3:
                logger.info(f'{eventlog.event_id} alias={company_clean_info.alias}')
                ret = self.request_search(keyword=company_clean_info.alias, filter_status=True, proxy=task.proxy)
                if ret is not None or ret != '':
                    return ret
        return self.request_search(keyword=eventlog.selector.word, proxy=task.proxy)

    def do_crawl(self):
        task: GSXTAppCrawlerTask = self.get_crawler_task()
        task.proxy = self.get_task_proxy()
        eventlog = task.eventlog

        # assert entry credit and get entry and inst
        if eventlog.selector.entry_name != 'credit':
            logger.warning(f'need entry_name=credit  {task.event_id}')
            eventlog.code = EventlogCode.GIVE_UP
            return
        task.entry = self.entry_dao.get_by_word(eventlog.selector.word)
        if not task.entry:
            logger.warning(f'fail to get entry entry_name=credit {task.event_id}')
            eventlog.code = EventlogCode.GIVE_UP
            return
        task.inst = task.entry.get_inst(inst=self.spider_name, init=True)

        search_result = self.do_request_search(task)
        if search_result is None:
            logger.warning(f'fail at request_search {task.event_id}')
            eventlog.code = EventlogCode.FAIL
            return
        if search_result == '':
            logger.warning(f'search empty {task.event_id}')
            eventlog.code = EventlogCode.SEARCH_EMPTY
            return

        prip_id, node_num, ent_type = search_result.split('#')
        base_content = self.request_base(pripid=prip_id, node_num=node_num, ent_type=ent_type, proxy=task.proxy)
        if not base_content:
            logger.warning(f'fail at request_base {task.event_id}')
            eventlog.code = EventlogCode.FAIL
            return
        task.pages['base.txt'] = base_content

    # return pripid#node_num#ent_type return '' if empty, None if errors
    def request_search(self, keyword: str, filter_status=False, cur_page=0, proxy=None) -> Optional[str]:
        if filter_status:
            # 当前假设filter_status为alias_search
            if cur_page >= self.ALIAS_SEARCH_PAGES:
                return None

        task = self.get_crawler_task()
        logger.info(f'{task.event_id} keyword={keyword} cur_page={cur_page}')
        response = request(
            url=f'https://app.gsxt.gov.cn/gsxt/corp-query-app-search-{cur_page+1}.html',
            response_validate_func=self.response_validate,
            post_data={
                "searchword": quote_plus(keyword),
                "conditions": json.dumps({
                    "excep_tab": "0",
                    "ill_tab": "0",
                    "area": "0",
                    "cStatus": "1" if filter_status else "0",
                    "xzxk": "0",  # 行政许可
                    "xzcf": "0",  # 行政处罚
                    "dydj": "0"
                }, ensure_ascii=False),
                "sourceType": "W",
            },
            tries=2,
            proxies=proxy,
        )
        if not response or response.status_code != 200:
            return None
        search_content_d = response.json()

        if 'result' not in search_content_d['data']:
            logger.warning(f'fail at request_search data {keyword} {task.event_id} data={search_content_d}')
            return None

        search_items = search_content_d['data']['result']['data']
        total_page = search_content_d['data']['result']['totalPage']
        search_result = ''
        for item in search_items:
            # name = re.sub('<[^>]+>', '', item.get('entName', ''))  # 飘红去除
            # reg_number = re.sub('<[^>]+>', '', item.get('regNo', ''))  # 飘红去除
            credit_code = re.sub('<[^>]+>', '', item.get('uniscId', ''))  # 飘红去除
            if 'pripid' not in item or 'nodeNum' not in item or 'entType' not in item:
                continue
            item_result = f'{item["pripid"]}#{item["nodeNum"]}#{item["entType"]}'
            if task.eventlog.selector.word == credit_code:
                search_result = item_result
                logger.info('search target=%s', search_result)
            entry: Optional[Entry] = self.entry_dao.get_by_word(credit_code)
            if entry:
                inst: Inst = entry.get_inst(inst=self.spider_name, init=True)
                inst.inst_info['detail_ts'] = cur_ts_sec()
                inst.inst_info['detail_data'] = item_result
                ret = self.entry_dao.update_entry(entry, inst_name=self.spider_name)
                logger.info(f'update detail {task.event_id} {credit_code} {item_result} ret={ret}')
        if cur_page + 1 < total_page and cur_page < self.ALIAS_SEARCH_PAGES and search_result == '':
            search_result_next = self.request_search(keyword, filter_status, cur_page+1)
            if search_result_next is not None and search_result_next != '':
                search_result = search_result_next
        return search_result

    def request_base(self, pripid: str, node_num, ent_type, proxy=None) -> Optional[str]:
        base_url = 'https://app.gsxt.gov.cn/gsxt/corp-query-entprise-info-primaryinfoapp-entbaseInfo'
        url = '{}-{}.html?nodeNum={}&entType={}&sourceType=W'.format(base_url, pripid, node_num, ent_type)
        response = request(
            url=url,
            response_validate_func=self.response_validate,
            post_data="{}",
            tries=1,
            proxies=proxy,
        )
        return None if (not response or response.status_code != 200) else response.text.replace('\n', ' ')

    @staticmethod
    def response_validate(response: Response, ctx) -> bool:
        if 'Failed To Get Valid Ip' in response.text:  # {"code":206,"message":"Failed To Get Valid Ip"}
            return False
        if 'ECONNRESET' in response.text:  # {"code":533,"message":"Error: read ECONNRESET"}
            return False
        if 'NGIDERRORCODE' in response.text:  # {"NGIDERRORCODE":561}
            return False
        if 'ECONNREFUSED' in response.text:  # {"code":532,"message":"Error: connect ECONNREFUSED *************:20693"}
            return False
        if 'AnyProxy Inner Error' in response.text:  # <!DOCTYPE html><html lang="en"><head><title>AnyProxy Inner Error</title>
            return False
        if '由于您操作过于频繁，请稍后返回首页重新操作' in response.text:
            return False
        if len(response.text) < 50 and '"code":"","data":{},' in response.text:  # {"code":"","data":{},"message":"","status":200}
            return False
        return True
