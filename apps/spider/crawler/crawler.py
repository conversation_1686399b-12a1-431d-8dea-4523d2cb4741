# encoding=utf8
import logging
from abc import abstractmethod
from typing import Dict
from threading import Lock, current_thread
import traceback

from libs.dt import cur_ts_sec
from apps.octopus.core.eventlog import Eventlog
from apps.spider.utils.obs_manager import OBSManager
from apps.octopus.core.eventlog_store import EventlogStore, EventlogStorePos
from apps.spider.core.conf import CrawlerConf


logger = logging.getLogger(__name__)


# Crawler的每个线程holder一个CrawlerTask，对应一个eventlog
class CrawlerTask(object):
    def __init__(self, eventlog: Eventlog):
        self.eventlog: Eventlog = eventlog
        self.event_id = self.eventlog.event_id
        self.pages: Dict[str, str] = dict()


class Crawler(object):
    def __init__(self, spider_name: str, crawler_conf: CrawlerConf, task_cls):
        self.spider_name = spider_name  # the spider's name
        self.task_cls = task_cls
        self.page_bak_count = crawler_conf.page_bak_count
        self.obs_manager = OBSManager()
        self.tasks: Dict[int, task_cls] = dict()
        self.tasks_lock = Lock()
        self.eventlog_store = EventlogStore(max_workers=4)

    def crawl(self, eventlog: Eventlog) -> Eventlog:
        logger.info(f'==== BEGIN {eventlog}')
        with self.tasks_lock:
            tid = current_thread().ident
            self.tasks[tid] = self.task_cls(eventlog)

        eventlog.spider.receive_ts = cur_ts_sec()
        self.do_crawl()
        task = self.get_crawler_task()

        # send responses to OBS
        page_ts = self.obs_manager.upload_pages(
            pages=task.pages,
            bak_count=self.page_bak_count,
            base_dir=f'page/{eventlog.selector.inst_name}/{eventlog.selector.word}',
        )
        if page_ts > 0:
            eventlog.spider.spider_data['page_ts'] = page_ts
        self.eventlog_store.save(eventlog, pos=EventlogStorePos.CRAWLED)
        return eventlog

    @abstractmethod
    def do_crawl(self):
        pass

    def get_crawler_task(self):
        with self.tasks_lock:
            tid = current_thread().ident
            if tid not in self.tasks:
                raise RuntimeError(f'not task tid={tid}')
            return self.tasks[tid]

    @staticmethod
    def custom_traceback(e):
        exc_type = type(e)
        exc_value = e
        tb = e.__traceback__
        """
        自定义打印 traceback，仅显示用户代码的报错信息
        """
        # 提取 traceback 信息
        tb_list = traceback.extract_tb(tb)
        user_tb = [frame for frame in tb_list if 'pygs-work-parent' in frame.filename]
        error = ''
        if user_tb:
            logger.error("Traceback (most recent call last):")
            # 格式化并打印用户代码的 traceback 信息
            logger.error("".join(traceback.format_list(user_tb)))
            logger.error(f"{exc_type.__name__}: {exc_value}")
            error += "".join(traceback.format_list(user_tb)) + f"{exc_type.__name__}: {exc_value}"
        else:
            logger.error(f"{exc_type.__name__}: {exc_value}")
            error += f"{exc_type.__name__}: {exc_value}"
        return error


class MyException(Exception):

    def __init__(self, message):
        self.message = message
        super().__init__(self.message)
