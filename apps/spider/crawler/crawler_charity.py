import requests
import urllib3
import time
from requests.adapters import HTTPA<PERSON>pter
import json
from loguru import logger
from typing import Union
from urllib.parse import quote
import re
import copy
from bs4 import BeautifulSoup
from threading import Lock
import random
import time
from datetime import datetime
import cv2
import base64
import numpy as np

from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.spider.crawler.crawler import Crawler, CrawlerTask, MyException
from apps.spider.core.conf import CrawlerConf

urllib3.disable_warnings()


class CharityCrawlerTask(CrawlerTask):

    def __init__(self, eventlog: Eventlog):
        self.search = eventlog.selector.word
        self.session = requests.session()
        self.session.proxies = {
            'http': 'http://10.99.138.95:30636',
            'https': 'http://10.99.138.95:30636'
        }
        self.session.mount('http://', HTTPAdapter(max_retries=1))
        self.session.mount('https://', HTTPAdapter(max_retries=1))

        super().__init__(eventlog)


class CharityCrawler(Crawler):

    def __init__(self, spider_name: str, crawler_conf: CrawlerConf):
        self.headers = {
            "Referer": "https://cszg.mca.gov.cn/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/121.0.0.0 Safari/537.36",
        }
        self.timeout = 4
        super().__init__(spider_name=spider_name, crawler_conf=crawler_conf, task_cls=CharityCrawlerTask)

    def do_crawl(self):
        task: CharityCrawlerTask = self.get_crawler_task()
        eventlog: Eventlog = task.eventlog

        pages = dict()
        keyword = task.search
        if keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.code = EventlogCode.GIVE_UP
            task.pages = pages
            return

        try:
            task.pages = self.crawl_(task)
            return
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'{task.search} -->搜索为空')
                eventlog.code = EventlogCode.SEARCH_EMPTY
                task.pages = pages
                return
            if '连续失败' in e.message:
                logger.warning(f'连续失败 {eventlog}')
                eventlog.code = EventlogCode.FAIL
                task.pages = pages
                return

        except Exception as e:
            raise e

    def crawl_(self, task: CharityCrawlerTask):
        pages = dict()
        self.index(task)

        for _ in range(6):
            if _ == 5:
                raise MyException('连续失败')
            pic: dict = self.get_pic(task)
            move_x = self.Captcha(pic['c']['oriImage'])
            check = self.check(task, move_x)
            if check['status'] == 'success':
                break

        html = self.search(task, task.search)
        info = self.parse(html)
        pages['info.json'] = json.dumps(info, ensure_ascii=False)
        return pages

    def search(self, task: CharityCrawlerTask, key):
        url = "https://cszg.mca.gov.cn/biz/ma/csmh/a/csmhaDoSort.html"
        params = {
            "pageNo": "1",
            "aaee0102_03": key,
            "field": "aaee0105",
            "sort": "desc",
            "flag": "0",
            "_": f"{int(time.time() * 1000)}"
        }
        response = self.request(task, 'GET', url, params=params, name='search')
        aaee0101 = re.findall(r'aaee0101=(.*?)" target=', response)
        if not aaee0101:
            raise MyException('搜索为空')

        response = self.request(task, 'GET', f'https://cszg.mca.gov.cn/biz/ma/csmh/a/csmhadetail.html?aaee0101={aaee0101[0]}', name='search')
        return response

    def check(self, task: CharityCrawlerTask, x):
        url = "https://cszg.mca.gov.cn/biz/ma/csmh/filter/slideCaptchaCheck.html"
        params = {
            "slidevalue": base64.b64encode(str(x).encode()),
            "_": f"{int(time.time() * 1000)}"
        }
        response = self.request(task, 'GET', url, params=params, name='check', tojson=True)
        return response

    def get_pic(self, task: CharityCrawlerTask, ):
        url = "https://cszg.mca.gov.cn/biz/ma/csmh/filter/getSlideCaptcha.html"
        params = {
            "_": f"{int(time.time() * 1000)}"
        }
        response = self.request(task, 'GET', url, params=params, name='get_pic', tojson=True)
        return response

    def index(self, task: CharityCrawlerTask, ):
        url = "https://cszg.mca.gov.cn/biz/ma/csmh/filter/slideCaptchaindex.html"
        self.request(task, 'GET', url, name='index')

    def request(self, task: CharityCrawlerTask, method: str, url: str, headers: dict = None, params: dict = None, data: dict = None,
                json: dict = None, path: str = None, name: str = '', tojson=False) -> Union[dict, str]:

        for i in range(2):
            response = None
            try:
                a = time.time()
                response = task.session.request(**{
                    'method': method,
                    'url': url,
                    'data': data,
                    'headers': headers if headers else self.headers,
                    'verify': False,
                    'timeout': self.timeout,
                    'params': params,
                    'json': json
                })

                status = response.status_code
                if status != 200:
                    logger.warning(f'{name} {i} --> {status}')
                    del task.session.cookies['proxyBase']
                    continue

                logger.success(f'{name} --> {response.status_code}-->time: {time.time() - a}')
                if name in ['check']:
                    a = response.text.replace("\n", "").replace('\r', '').replace('\t', '')
                    logger.info(f'{name} --> {a}')

                if tojson:
                    return response.json()
                return response.text

            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'continue{i} exception: {e}')
                del task.session.cookies['proxyBase']
                continue
            except Exception as e:
                if response:
                    status = response.status_code if response else "空"
                    text = response.text if response else "空"
                    logger.warning(f'continue-{i} 状态码：{status} res: {text} exception: {e}')
                del task.session.cookies['proxyBase']
                continue

        raise MyException('接口连续失败')

    @staticmethod
    def parse(html):
        soup = BeautifulSoup(html, "lxml")
        trs = soup.select_one('.djtable').select('tbody tr')
        keys = []
        values = []
        for tr in trs:
            for idx, td in enumerate(tr.select('td')):
                str_ = list(td.stripped_strings)
                if idx % 2 == 0:
                    keys.append(str_[0])
                else:
                    values.append(str_[0] if str_ else "")
        result = dict(zip(keys, values))
        # logger.info(result)
        return result

    @staticmethod
    def Captcha(base64_str: str):
        base64_data = base64.b64decode(base64_str)
        np_data = np.frombuffer(base64_data, np.uint8)
        img = cv2.imdecode(np_data, cv2.IMREAD_COLOR)
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        lower_white = np.array([0, 0, 255])
        upper_white = np.array([180, 30, 255])
        mask = cv2.inRange(hsv, lower_white, upper_white)
        result = cv2.bitwise_and(img, img, mask=mask)
        result[np.where((result != [255, 255, 255]).all(axis=2))] = [0, 0, 0]

        k = np.ones((10, 10), np.uint8)
        opening = cv2.morphologyEx(result, cv2.MORPH_OPEN, k)

        opening = cv2.Canny(opening, 100, 200)
        contours, _ = cv2.findContours(opening, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            cv2.rectangle(img, (x, y), (x + w, y + h), (0, 255, 0), 2)
            return x


if __name__ == '__main__':
    pass
