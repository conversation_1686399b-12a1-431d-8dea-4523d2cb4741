# encoding=utf8

import re
import json
import logging
from typing import Optional
from urllib.parse import quote_plus
from requests import Response
from dao.company_clean_info import CompanyCleanInfoDao, CompanyCleanInfo
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.octopus.utils.conf_manager import OctopusConfManager
from apps.octopus.core.inst import Inst
from apps.octopus.entries.entry_dao import Entry, EntryDao
from apps.spider.crawler.crawler import Crawler, CrawlerTask
from apps.spider.core.conf import CrawlerConf
from libs.req import SessionRequestManager
from libs.dt import cur_ts_sec

logger = logging.getLogger(__name__)


class GSXTAppCrawlerTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        self.proxy_list = []
        # response = requests.get('http://*************:8015/tydaili2', timeout=3.0)
        # if response.status_code == 200:
        #     for s in response.text.split('\n'):
        #         if len(s.strip()) > 10:
        #             self.proxy_list.append(s.strip())
        # logger.info(f'load proxy_list {len(self.proxy_list)}')
        super().__init__(eventlog)


class GSXTAppCrawler(Crawler):
    CACHE_SECS = 86000  # 86400
    ALIAS_SEARCH_PAGES = 10  # 10
    REQUEST_TIMEOUT = 8
    USE_PROXY = True
    proxy_list = []

    def __init__(self, spider_name: str, crawler_conf: CrawlerConf):
        self.request_manager = SessionRequestManager()
        self.company_clean_info_dao = CompanyCleanInfoDao()
        conf_manager = OctopusConfManager(json_conf_name='gsxt_app.credit.json', reload_conf_interval_sec=0)
        self.entry_dao: EntryDao = conf_manager.get_exact_one().entry_dao.obj
        super().__init__(spider_name=spider_name, crawler_conf=crawler_conf, task_cls=GSXTAppCrawlerTask)

    def do_request_search_alias(self, eventlog: Eventlog) -> Optional[str]:
        # do search for alias
        cid = eventlog.selector.info.get('cid', None)
        if not isinstance(cid, int):
            return None
        company_clean_info: CompanyCleanInfo = self.company_clean_info_dao.get(cid)
        if not company_clean_info or not isinstance(company_clean_info.alias, str) or len(company_clean_info.alias) < 3:
            return None
        logger.info(f'{eventlog.event_id} alias={company_clean_info.alias}')
        return self.request_search(keyword=company_clean_info.alias, filter_status=True)

    # 有详情数据 则不走搜索 否则
    # 模糊搜索 或者 精确搜索 命中结果 则更新 detail_page_data
    # return pripid#node_num#ent_type or None or ''
    def do_request_search(self, eventlog: Eventlog, inst: Inst) -> Optional[str]:
        if inst.inst_info.get('detail_ts', 0) + self.CACHE_SECS > cur_ts_sec():
            logger.info(f'{eventlog.event_id} use cache detail_data')
            eventlog.spider.spider_data['cache_detail_data'] = True
            return inst.inst_info['detail_data']
        return self.do_request_search_alias(eventlog) or self.request_search(keyword=eventlog.selector.word)

    def do_crawl(self):
        task: GSXTAppCrawlerTask = self.get_crawler_task()
        self.proxy_list = task.proxy_list
        eventlog = task.eventlog

        # assert entry credit and get it
        if eventlog.selector.entry_name != 'credit':
            logger.warning(f'need entry_name=credit  {task.event_id}')
            eventlog.code = EventlogCode.GIVE_UP
            return
        entry: Optional[Entry] = self.entry_dao.get_by_word(eventlog.selector.word)
        if not entry:
            logger.warning(f'fail to get entry entry_name=credit {task.event_id}')
            eventlog.code = EventlogCode.GIVE_UP
            return
        inst: Inst = entry.get_inst(inst=self.spider_name, init=True)

        search_result = self.do_request_search(eventlog, inst)
        if search_result is None:
            logger.warning(f'fail at request_search {task.event_id}')
            eventlog.code = EventlogCode.FAIL
            return
        if search_result == '':
            logger.warning(f'search empty {task.event_id}')
            eventlog.code = EventlogCode.SEARCH_EMPTY
            return
        prip_id, node_num, ent_type = search_result.split('#')
        base_content = self.request_base(pripid=prip_id, node_num=node_num, ent_type=ent_type)
        if not base_content:
            logger.warning(f'fail at request_base {task.event_id}')
            eventlog.code = EventlogCode.FAIL
            return
        task.pages['base.txt'] = base_content

        detail_content_d = json.loads(base_content)
        ent_type_for_query = detail_content_d['entTypeForQuery']
        report_content = self.request_report(pripid=prip_id, node_num=node_num, ent_type=ent_type_for_query)
        if not report_content:
            logger.warning(f'fail at report_content {task.event_id}')
            eventlog.code = EventlogCode.FAIL
            return
        task.pages['report.txt'] = report_content

    # return pripid#node_num#ent_type return '' if empty, None if errors
    def request_search(self, keyword: str, filter_status=False, cur_page=0, proxy=None) -> Optional[str]:
        if filter_status:
            # 当前假设filter_status为alias_search
            if cur_page >= self.ALIAS_SEARCH_PAGES:
                return None

        task = self.get_crawler_task()
        logger.info(f'{task.event_id} keyword={keyword} cur_page={cur_page}')
        response = self.request_manager.request(
            url=f'https://app.gsxt.gov.cn/gsxt/corp-query-app-search-{cur_page+1}.html',
            response_validate_func=self.response_validate,
            post_data={
                "searchword": quote_plus(keyword),
                "conditions": json.dumps({
                    "excep_tab": "0",
                    "ill_tab": "0",
                    "area": "0",
                    "cStatus": "1" if filter_status else "0",
                    "xzxk": "0",  # 行政许可
                    "xzcf": "0",  # 行政处罚
                    "dydj": "0"
                }, ensure_ascii=False),
                "sourceType": "W",
            },
            tries=2,
            timeout=self.REQUEST_TIMEOUT,
            use_proxy=self.USE_PROXY,
            proxies=self.get_random_proxy(),
        )
        if not response or response.status_code != 200:
            return None
        search_content_d = response.json()

        if 'result' not in search_content_d['data']:
            logger.warning(f'fail at request_search data {keyword} {task.event_id} data={search_content_d}')
            return None

        search_items = search_content_d['data']['result']['data']
        total_page = search_content_d['data']['result']['totalPage']
        search_result = ''
        for item in search_items:
            # name = re.sub('<[^>]+>', '', item.get('entName', ''))  # 飘红去除
            # reg_number = re.sub('<[^>]+>', '', item.get('regNo', ''))  # 飘红去除
            credit_code = re.sub('<[^>]+>', '', item.get('uniscId', ''))  # 飘红去除
            if 'pripid' not in item or 'nodeNum' not in item or 'entType' not in item:
                continue
            item_result = f'{item["pripid"]}#{item["nodeNum"]}#{item["entType"]}'
            if task.eventlog.selector.word == credit_code:
                search_result = item_result
                logger.info('search target=%s', search_result)
            entry: Optional[Entry] = self.entry_dao.get_by_word(credit_code)
            if entry:
                inst: Inst = entry.get_inst(inst=self.spider_name, init=True)
                inst.inst_info['detail_ts'] = cur_ts_sec()
                inst.inst_info['detail_data'] = item_result
                ret = self.entry_dao.update_entry(entry, inst_name=self.spider_name)
                logger.info(f'update detail {task.event_id} {credit_code} {item_result} ret={ret}')
        if cur_page + 1 < total_page and cur_page < self.ALIAS_SEARCH_PAGES and search_result == '':
            search_result_next = self.request_search(keyword, filter_status, cur_page+1)
            if search_result_next is not None and search_result_next != '':
                search_result = search_result_next
        return search_result

    def request_base(self, pripid: str, node_num, ent_type) -> Optional[str]:
        base_url = 'https://app.gsxt.gov.cn/gsxt/corp-query-entprise-info-primaryinfoapp-entbaseInfo'
        url = '{}-{}.html?nodeNum={}&entType={}&sourceType=W'.format(base_url, pripid, node_num, ent_type)
        response = self.request_manager.request(
            url=url,
            response_validate_func=self.response_validate,
            post_data="{}",
            tries=1,
            timeout=self.REQUEST_TIMEOUT,
            use_proxy=self.USE_PROXY,
            proxies=self.get_random_proxy(),
        )
        return None if (not response or response.status_code != 200) else response.text.replace('\n', ' ')

    def request_report(self, pripid: str, node_num, ent_type) -> Optional[str]:
        report_url = 'https://app.gsxt.gov.cn/gsxt/corp-query-entprise-info-anCheYearInfo'
        url = f'{report_url}-{pripid}.html?nodeNum={node_num}&entType={ent_type}&sourceType=W'
        response = self.request_manager.request(
            url=url,
            response_validate_func=self.response_validate,
            tries=1,
            timeout=self.REQUEST_TIMEOUT,
            use_proxy=self.USE_PROXY,
            proxies=self.get_random_proxy(),

        )
        return None if (not response or response.status_code != 200) else response.text.replace('\n', ' ')

    def get_random_proxy(self):
        # proxy = random.choice(self.proxy_list)
        # return {"https": f"http://{proxy}", "http": f"http://{proxy}"}
        return None

    @staticmethod
    def response_validate(response: Response, ctx) -> Optional[bool]:
        if 'Failed To Get Valid Ip' in response.text:  # {"code":206,"message":"Failed To Get Valid Ip"}
            return None
        if 'ECONNRESET' in response.text:  # {"code":533,"message":"Error: read ECONNRESET"}
            return None
        if 'NGIDERRORCODE' in response.text:  # {"NGIDERRORCODE":561}
            return None
        if 'ECONNREFUSED' in response.text:  # {"code":532,"message":"Error: connect ECONNREFUSED *************:20693"}
            return None
        if 'AnyProxy Inner Error' in response.text:  # <!DOCTYPE html><html lang="en"><head><title>AnyProxy Inner Error</title>
            return None
        if '由于您操作过于频繁，请稍后返回首页重新操作' in response.text:
            return None
        if len(response.text) < 50 and '"code":"","data":{},' in response.text:  # {"code":"","data":{},"message":"","status":200}
            return False
        return True
