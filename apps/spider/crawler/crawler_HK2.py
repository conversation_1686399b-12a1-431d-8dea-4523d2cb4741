from datetime import datetime, timedelta
import json
import base64
import re
import time
from PIL import Image, ImageSequence
import ddddocr
from loguru import logger
import requests
from requests import Session, Response
from threading import current_thread, Lock
import zipfile
from typing import Dict, Optional, Union

from libs.feishu import send_feishu_message_attachments as feishu
from apps.spider.crawler.crawler import <PERSON><PERSON><PERSON><PERSON><PERSON>, Crawler, MyException
from clients.obs_client import OBSClient
from dao.company_hk import CompanyHk
from dao.hk.hk_pay_info import HkPayInfoDao
from dao.hk.hk_pdf_download_record import HkPdfDownloadRecordDao
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.spider.core.conf import CrawlerConf
from resx.mysql_dao import MySQLDao
from resx.config import *


class CrawlerHKTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        self.cid = 0
        super().__init__(eventlog)


class CrawlerHK(Crawler):
    def __init__(self, spider_name: str, crawler_conf: CrawlerConf):
        self.sessions: Dict[int, Session] = dict()
        self.obs = OBSClient(bucket_name='jindi-oss-gsxt')
        self.HkPayInfoDao = HkPayInfoDao()
        self.headers = {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
        }
        self.busy = False
        self.HkPdfDownloadRecordDao = HkPdfDownloadRecordDao()
        self.timeout = 30
        self.lock = Lock()
        self.chat_id = 'oc_c1a20434dc7c3ca4c6fc279ddfaeb879'
        self.dao_hk = MySQLDao(db_tb_name='prism.company_hk', **CFG_MYSQL_GS_OUTER, primary_index_fields=(['br_num'], []), entity_class=CompanyHk,
                               ignore_fields=['id', 'company_id', 'updatetime'])
        super().__init__(spider_name=spider_name, crawler_conf=crawler_conf, task_cls=CrawlerHKTask)

    def do_crawl(self):
        task: CrawlerHKTask = self.get_crawler_task()
        eventlog = task.eventlog
        br_no = eventlog.selector.word

        try:
            ispay = True
            for n in range(5):
                if self.keep_alive(br_no):
                    break
                if self.Log_in(ispay, br_no):
                    if not self.keep_alive(br_no, num=2):
                        continue
                    break
                elif n > 3:
                    logger.error(f'br_no: [{br_no}] - 登录失败')
                    raise MyException('登录失败')

            c_num: Optional[str] = self.ps_company_name_mapping_cr_br_(br_no)
            if not c_num:
                logger.error('c_num: [{}] - 搜索为空'.format(br_no))
                raise MyException('搜索为空')

            # 照面信息
            base_info = self.search_by_brno(br_no)
            base_info['crNo'] = c_num
            task.pages['res.json'] = json.dumps(base_info, ensure_ascii=False)
            # 曾用名信息
            res_his = self.ps_company_name_history_search_history_data(br_no)
            task.pages['res_his.json'] = json.dumps(res_his, ensure_ascii=False)
            if not ispay:
                return

            # 影像记录
            image_list = self.ps_document_index_list_search(br_no)

            p = self.Determine(br_no)
            if p is None:
                logger.error('brno: [{}] - 付费逻辑无法处理新增抓取'.format(br_no))
                raise MyException('失败')
            if p is False:
                eventlog.code = EventlogCode.SUCCESS
                return

            refNo = self.pay({'brNo': br_no}, 1, br_no)
            if not refNo:
                raise MyException('失败')

            share = self.ps_company_particulars_details_share_capital(refNo, br_no)
            secretary = self.ps_company_particulars_details_company_secretary(refNo, br_no)
            auth = self.ps_company_particulars_details_auth_rep(refNo, br_no)
            directors = self.ps_company_particulars_details_directors_list(refNo, br_no)
            shareholder = self.ps_company_particulars_details_shareholder_information(refNo, br_no)

            for item, var in enumerate(directors[:3]):
                parm = {key: (var[key] if var[key] else '') for key in ['engSname', 'engOname', 'chiName']}
                for A in ['hkid', 'pptNo']:
                    match = re.search(r'[\da-zA-Z]+', var[A] if var.get(A) else '')
                    parm[A] = match.group(0) if match else ''
                refNo = self.pay(parm, 2, br_no)
                if not refNo:
                    raise MyException('放弃')
                director_detail = self.ps_directors_index_director_based_company_list_individual_director(refNo, br_no, parm['chiName'])
                task.pages[f'list_individual_director_{str(item)}.html'] = director_detail

            task.pages['balance.json'] = self.account_balance(br_no)
            task.pages['image_list.json'] = json.dumps(image_list, ensure_ascii=False)
            task.pages['share_capital.json'] = json.dumps(share, ensure_ascii=False)
            task.pages['company_secretary.json'] = json.dumps(secretary, ensure_ascii=False)
            task.pages['auth_rep.json'] = json.dumps(auth, ensure_ascii=False)
            task.pages['directors_list.json'] = json.dumps(directors, ensure_ascii=False)
            task.pages['shareholder_information.json'] = json.dumps(shareholder, ensure_ascii=False)

            barcode = self.find_barcode(image_list, br_no)
            if not barcode:
                return
            parm = {'barcode': barcode, 'brNo': br_no, 'coyTyp': base_info['coyTyp']}
            refNo = self.pay(parm, 3, br_no)
            if not refNo:
                raise MyException('放弃')
            pdf = self.ps_document_index_downloads(refNo)
            if not pdf:
                feishu(chat_id=self.chat_id, ats=['丁良益'], text=f'PDF爬取失败!!!')
                logger.error(f'brno: [{br_no}] - pdf爬取失败')
                raise MyException('失败')
            self.obs.put(f'hk_pdf/{barcode}.pdf', pdf)
            # self.HkPdfDownloadRecordDao.i_u_by_any(**{'record_id': barcode, 'obs_url': f'hk_pdf/{barcode}.pdf', 'pdf_source': 2})
        except MyException as e:
            if '失败' in e.message:
                eventlog.code = EventlogCode.FAIL
            if e.message == '放弃':
                eventlog.code = EventlogCode.GIVE_UP
            if e.message == '搜索为空':
                eventlog.code = EventlogCode.SEARCH_EMPTY
        except Exception as e:
            self.custom_traceback(e)
            eventlog.code = EventlogCode.FAIL

    def account_balance(self, brno):
        url = "https://www.e-services.cr.gov.hk/ICRIS3EP/system/dashboard/information.do"
        res = self.request("GET", url)
        balance = re.search(r',"totalBal":(.*?),"', res)
        if not balance:
            return '余额获取失败'
        logger.info(f'brno: {brno} 余额: {balance.group(1)}')
        return balance.group(1)

    def Determine(self, brNo):
        hk_id = self.dao_hk.get(br_num=brNo)
        if not hk_id:
            logger.error('brno: [{}] - 未在company_hk表中发现该数据'.format(brNo))
            return None
        last = self.HkPayInfoDao.get(dict(hk_id)['id'], 'hk_id')
        if last:
            time_diff = datetime.now().date() - dict(last)['last_pay_time'].date()
            if time_diff <= timedelta(days=1):
                logger.info('brno: [{}] - 未超过一天时间, 无需抓取'.format(brNo))
                return False
        return True

    def find_barcode(self, image_list, brno):
        for i in image_list['01'] if image_list['01'] else []:
            if i['docChName'].find('周年申報表') > -1 and 'AVAILABLE' in i['statusForDisplay']:
                barcode = i['docID']
                logger.info('brno: [{}] barcode: [{}]'.format(brno, barcode))
                if self.obs.exists_file('hk_pdf/{}.pdf'.format(barcode)):
                    logger.info('brno: [{}] - {} PDF 已存在, 无需爬取'.format(brno, barcode))
                    return
                return barcode

    def pay(self, parm, num, brno):
        refNo = self.ps_viewOnline_order(parm, num)
        if not refNo:
            logger.warning('brno: {} - 获取公司详细信息订单失败'.format(brno))
            raise MyException('失败')
        logger.info('brno: [{}] - refNo:[{}] '.format(brno, refNo))

        sys = self.system_payment_selection_info_(refNo)
        if not sys:
            logger.warning('brno: {} - 扣款失败'.format(brno))
            raise MyException('失败')
        self.ps_viewOnline_pay(refNo)
        return refNo

    def Log_in(self, ispay, br_no):
        self.system_login_unregistered_or_userPassword(ispay)
        for _ in range(3):
            captcha: dict = self.system_common_captcha()
            code = self.Base64Gif_to_code(captcha)
            success = self.ps_public_search_access_statement_accept(code, captcha, ispay)
            if success:
                return True

    def keep_alive(self, br_no, num=1):
        url = 'https://www.e-services.cr.gov.hk/ICRIS3EP/user/menu/list.do'
        res = self.request("GET", url, tojson=True, name='保活')
        if res.get('successful'):
            logger.info(f'br_no: [{br_no}] - 保活成功:{num}')
            return True
        else:
            logger.warning('br_no: [{}] - 保活失败'.format(br_no))
            return False

    def system_login_unregistered_or_userPassword(self, ispay):
        json_data_free = {"loginType": "iguest"}
        url_free = "https://www.e-services.cr.gov.hk/ICRIS3EP/system/login/unregistered.do"
        json_data = {
            'userId': 'fairview0529',
            # 'userId': 'tyc2024',
            'passwordSha1': 'DbEeUpk/v3N/owK0ciQ/ZIcWpj0=',
            'passwordSha256': '9c07c64178f579bb1dd26cfd2f2fb0ff09235ac1c449b2ed13f0f1a0437773a0', 'loginType': 'subscriber',
        }
        url = 'https://www.e-services.cr.gov.hk/ICRIS3EP/system/login/userPassword.do'
        self.request("POST", url if ispay else url_free, json=json_data if ispay else json_data_free)

    def system_common_captcha(self):
        url = "https://www.e-services.cr.gov.hk/ICRIS3EP/system/common/captcha.do"
        res = self.request("GET", url, tojson=True, name='captcha.do')
        return res

    def ps_public_search_access_statement_accept(self, code, captcha, ispay):
        url = "https://www.e-services.cr.gov.hk/ICRIS3EP/ps/public-search/access/statement_accept.do"
        data_free = {"acceptList": [1], "captchaId": captcha['data']['captchaId'], "captchaImg": captcha['data']['captchaImg'],
                     "captchaCode": code, "loginType": "iguest", "chiName": "天眼查", "engSname": "TYC", "engOname": "TYC",
                     "docType": "3", "idDocNum": "1", "issPlace": "CHN", "issAuth": "1"}
        data = {'acceptList': [1, ], 'captchaId': captcha['data']['captchaId'], 'captchaImg': captcha['data']['captchaImg'], 'captchaCode': code,
                'loginType': 'subscriber', 'chiName': '赵', 'engSname': 'Zhao Zhao', 'engOname': 'ZHAO', 'docType': '2',
                'idDocNum': '130421199611260462', 'issPlace': 'CHN', 'issAuth': 'PRC'}
        res = self.request("POST", url, json=data if ispay else data_free, tojson=True, name='statement_accept.do')
        if res.get('successful'):
            return True

    @staticmethod
    def Base64Gif_to_code(base64_string):
        try:
            def overlay_images(image1, image2):
                # 调整图像的透明度
                image1 = adjust_opacity(image1, 0.7)
                image2 = adjust_opacity(image2, 0.7)
                # 对齐两张图片
                image_size = (max(image1.width, image2.width), max(image1.height, image2.height))
                image1 = image1.resize(image_size)
                image2 = image2.resize(image_size)
                # 创建一个空白的合并图像
                merged_image = Image.new("RGBA", image_size)
                # 将两张图片叠加在一起
                merged_image = Image.alpha_composite(merged_image, image1)
                merged_image = Image.alpha_composite(merged_image, image2)
                return merged_image

            def adjust_opacity(image, opacity):
                # 获取图像的Alpha通道
                alpha = image.split()[3]
                # 调整透明度
                alpha = alpha.point(lambda p: p * opacity)
                # 合并调整后的Alpha通道到原图像中
                image.putalpha(alpha)
                return image

            # 去除Base64字符串中的数据类型前缀
            base64_string = base64_string['data']['captchaImg'].split('base64,')[-1]
            # 将Base64字符串解码为字节数组
            gif_data = base64.b64decode(base64_string)
            # 将字节数组转换为字节流
            gif_stream = io.BytesIO(gif_data)

            image = Image.open(gif_stream)
            frame_num = 0
            ocr = ddddocr.DdddOcr(show_ad=False)
            result = Image.new('RGBA', image.size)
            for frame in ImageSequence.Iterator(image):
                new_image = Image.new("RGBA", image.size)
                new_image.paste(frame)
                result = overlay_images(result, new_image)
                frame_num += 1
            res = ocr.classification(result)
            return res
        except Exception as e:
            logger.error(e)
            return e

    def ps_company_name_mapping_cr_br_(self, br_no) -> Optional[str]:
        hk: CompanyHk = self.dao_hk.get(br_num=br_no)
        if hk and hk.company_num and hk.company_num != br_no:
            logger.info(f'get from db {br_no} {hk.company_num}')
            return hk.company_num

        url = f'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/company-name/mapping/br/{br_no}.do'
        res = self.request("GET", url, tojson=True, name='mapping.do')
        if res.get('successful') and res.get('data'):
            return res.get('data').get('zufgjNo')
        return ''

    def search_by_brno(self, brNo):
        url = f'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/company-name/base-information/search-brno.do?brNo={str(brNo)}'
        res: dict = self.request("GET", url, tojson=True, name=f'{brNo}-照面信息爬取完成')
        return res['data']['baseData']

    def ps_company_name_history_search_history_data(self, brNo):
        url = f'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/company-name-history/search-history-data.do?brNo={brNo}'
        res = self.request("GET", url, tojson=True, name=f'{brNo}-曾用名信息爬取完成')
        return res['data']

    def ps_document_index_list_search(self, brNo):
        url = "https://www.e-services.cr.gov.hk/ICRIS3ES/ps/document-index/list-search.do"
        data = {'brNo': brNo, 'group': '01', 'pageIndex': 1, 'pageSize': 50, 'yearSearchType': 0}
        res = self.request("POST", url, json=data, tojson=True, name=f'{brNo}-影像记录爬取完成')
        return {'01': res['data']}

    # 详细资料
    def ps_viewOnline_order(self, param, num):
        if num == 1:
            params = {
                'params': {'searchMode': -1, 'brNo': param['brNo'], 'ofc': False, 'systemClock': round(time.time() * 1000)},
                'actvyCode': 'S-CRSS', 'additionalDesc': param['brNo'], 'productCode': 'CPS', 'nextAction': 'FEPS003-S04', 'lang': 'zh-CN',
                'cancelUri': f'/ICRIS3ES/ps/company-particulars/basic-information/brno.do?brNo={param["brNo"]}&searchMode=-1&ofc=false',
            }
        elif num == 2:
            params = {
                'productCode': 'DBDS',
                'params': {'directorType': 'I', 'searchMode': 'c', 'engSname': param['engSname'], 'engOname': param['engOname'], 'chiName': param['chiName'],
                           'hkid': param['hkid'], 'hkidChkDgt': None, 'hkidPartial': '', 'pptNo': param['pptNo'], 'pptNoPartial': '', 'ofc': False,
                           'systemClock': datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%fZ')},
                'nextAction': 'FEPS004-S09-INDV', 'additionalDesc': '', 'actvyCode': 'S-DBDS', 'lang': 'zh-CN',
                'cancelUri': '/ICRIS3ES/ps/directors-index/director-based.do',
            }
        else:
            params = {
                'params': {'barcode': param['barcode'], 'docSize': 'M', 'bulkyInd': 'undefined', 'lsaInd': 'undefined',
                           'brNo': param['brNo'], 'group': '01', 'yearSearchType': 0, 'coyTyp': param['coyTyp']},
                'actvyCode': 'R-DOCN-M', 'additionalDesc': '', 'productCode': 'DOCUMENT', 'nextAction': 'FEPS008-S06',
                'returnAction': '/ps/document-index/search-list.do',
                'returnParams': {'group': '01', 'yearSearchType': 0, 'brNo': param['brNo']},
                'lang': 'zh-CN', 'cancelUri': '/ICRIS3ES/ps/document-index/search-list.do?group=01&yearSearchType=0&brNo={}'.format(param['brNo']),
            }

        json_data = {
            'navigations': [{
                'name': 'PS.COMMON.LBL.COMPANY_UNDER_CO',
                'subNavs': [
                    {'path': '/ps/company-name.do', 'name': 'PS.COMMON.LBL.COMPANY_NAME'},
                    {'path': '/ps/company-particulars.do', 'name': 'PS.COMMON.LBL.COMPANY_PARTICULAR_TITLE', },
                    {'path': '/ps/document-index.do', 'name': 'PS.COMMON.LBL.IMAGE_RECORD_SEARCH_INCLUDING_DOC_IDX'},
                    {'path': 'ps/directors-index.do', 'name': 'PS.FEPS004.LBL.DIRECTOR_INDEX_SEARCH_TITLE', },
                    {'path': '/ps/register-of-charges.do', 'name': 'PS.COMMON.LBL.REGISTER_OF_CHARGES'},
                    {'path': '/ps/disqualification-order-search.do', 'name': 'PS.COMMON.LBL.REGISTER_DQ_ORDERS'},
                    {'path': '/ps/other-entities.do', 'name': 'PS.COMMON.LBL.OTHER_ENTITIES'},
                ]}, {'name': 'PS.COMMON.LBL.COMPANY_PARTICULAR_TITLE'}]}
        url = 'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/viewOnline/order.do'
        res: dict = self.request("POST", url, json=dict(json_data, **params), tojson=True, name='order.do')
        return res['data']['refNo'] if res.get('successful') else ''

    def system_payment_selection_info_(self, refNo):
        url = 'https://www.e-services.cr.gov.hk/ICRIS3ES/system/payment/selection_info.do'
        res: dict = self.request("POST", url, params={'refNo': refNo}, tojson=True, name='selection_info.do')
        return res['data']['paymentData'] if res.get('successful') else False

    def ps_viewOnline_pay(self, refNo):
        url = 'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/viewOnline/pay.do?refNo={}'.format(refNo)
        self.request("GET", url, name='pay.do')

    def ps_company_particulars_details_share_capital(self, orderNo, brno):
        url = 'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/company-particulars/details/share-capital.do?orderNo={}'.format(orderNo)
        res = self.request("GET", url, tojson=True, name=f'{brno}-股本爬取完成')
        return res['data']

    def ps_company_particulars_details_company_secretary(self, orderNo, brno):
        url = 'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/company-particulars/details/company-secretary.do?orderNo={}'.format(orderNo)
        res = self.request("GET", url, tojson=True, name=f'{brno}-公司秘书爬取完成')
        return res['data']

    def ps_company_particulars_details_auth_rep(self, orderNo, brno):
        url = 'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/company-particulars/details/auth-rep.do?orderNo={}'.format(orderNo)
        res = self.request("GET", url, tojson=True, name=f'{brno}-授权代表爬取完成')
        return res['data']

    def ps_company_particulars_details_directors_list(self, orderNo, brno):
        url = 'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/company-particulars/details/directors-list.do?orderNo={}'.format(orderNo)
        res = self.request("GET", url, tojson=True, name=f'{brno}-董事信息爬取完成')
        return res['data']

    def ps_company_particulars_details_shareholder_information(self, orderNo, brno):
        url = f'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/company-particulars/details/shareholder-information.do?orderNo={orderNo}'
        res = self.request("GET", url, tojson=True, name=f'{brno}-股东信息爬取完成')
        return res['data']

    def ps_directors_index_director_based_company_list_individual_director(self, orderNo, brno, chiName):
        url = 'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/directors-index/director-based/company-list/individual-director.do?key=0&orderNo={}'.format(orderNo)
        html = self.request("GET", url, name=f'{brno}-{chiName}-董事详细信息爬取完成')
        return html

    def ps_document_index_downloads(self, refNo):
        for _ in range(3):
            try:
                url = "https://www.e-services.cr.gov.hk/ICRIS3EP/system/encrypt.do"
                timestamp = str(int(time.time() * 1000))
                data = {"orderNo": refNo, "orderItmNo": 1, "transactionDate": timestamp}
                res = self.request("POST", url, json=data, tojson=True, name='pdf-encrypt.do')

                url = "https://www.e-services.cr.gov.hk/ICRIS3ES/ps/shoppingCart/download.do"
                params = {"orderNo": refNo, "orderItmNo": "1", "transactionDate": timestamp, "xm": res['data']}
                response = self.request('GET', url, params=params, toRaw=True, name='pdf-download.do')
                pdf = self.extract_pdf_from_zip(response.content)
                if '%PDF-' in str(pdf[:10]):
                    return pdf
            except Exception as e:
                logger.warning(f'pdf下载失败: {e} continue')
        raise MyException('连续失败')

    def request(self, method: str, url: str, params: dict = None, data: Union[dict, str] = None, json: dict = None,
                path: str = None, name: str = '', tojson=False, toRaw=False, long_proxy=False, long_timeout=30, isDetail=False) -> Union[dict, str, Response]:
        with self.lock:
            tid = current_thread().ident
            if tid not in self.sessions:
                self.sessions[tid] = Session()
            session = self.sessions[tid]

        for i in range(15):
            response = None
            try:
                a = time.time()
                request_params = {'method': method, 'url': url, 'data': data, 'headers': self.headers,
                                  'verify': False, 'timeout': self.timeout, 'params': params, 'json': json,
                                  }
                # 'proxies': {'http': 'http://10.99.138.95:30636', 'https': 'http://10.99.138.95:30636'}
                if long_proxy:
                    # request_params['proxies'] = self.get_long_proxy()
                    request_params['timeout'] = long_timeout
                response = session.request(**request_params)

                if response.status_code != 200 and name != '保活':
                    logger.warning(f'{name}-{i + 1} --> {response.status_code}')
                    del session.cookies['proxyBase']
                    continue
                logger.success(f'{name} --> {response.status_code} --> time: {time.time() - a}')

                if toRaw:
                    return response
                if isDetail:
                    a = re.sub(r'[\n\r\t]', '', response.text)
                    logger.info(f'{name} --> {a}')
                if tojson:
                    return response.json()
                return response.text
            except requests.exceptions.JSONDecodeError as e:
                raise MyException('网站维护中')
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'{name} --> continue{i} exception: {e}')
                del session.cookies['proxyBase']
                continue
            except Exception as e:
                status = response.status_code if response else "空"
                text = response.text if response else "空"
                logger.warning(f'{name} --> continue{i} 状态码：{status} exception: {e} res: {text}')
                del session.cookies['proxyBase']
                continue
        raise MyException('接口连续失败')

    @staticmethod
    def extract_pdf_from_zip(zip_: bytes):
        with zipfile.ZipFile(io.BytesIO(zip_), 'r') as zip_ref:
            for file_name in zip_ref.namelist():
                pdf_binary_data = zip_ref.read(file_name)
                return pdf_binary_data


if __name__ == '__main__':
    pass
