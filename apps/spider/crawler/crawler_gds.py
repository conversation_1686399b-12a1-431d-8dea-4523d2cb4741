import re
import requests
import urllib3
import time
from requests.adapters import HTTPAdapter
import json
import aiohttp
import traceback
import asyncio
from loguru import logger
import sys
import base64
import random
from typing import Union
from fake_useragent import UserAgent
import copy
import uuid
import ddddocr
from jsonpath import jsonpath

from dao.company import CompanyDao, Company
from dao.gds import FirmDao, CodeDao, CodeEntity, FirmEntity, BrandDao, ProductDao, ProductEntity
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.spider.crawler.crawler import <PERSON><PERSON><PERSON>, CrawlerTask, MyException
from apps.spider.core.conf import CrawlerConf
from gslib.gtin import get_firm_sample_ean13, add_mask_ean13

urllib3.disable_warnings()
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())


class GdsCrawlerTask(CrawlerTask):

    def __init__(self, eventlog: Eventlog):
        self.search = GdsCrawler.replace_bracket(eventlog.selector.info.get('name'), cn_to_en=True)
        self.certificate_code = eventlog.selector.word
        self.cid = eventlog.selector.info.get('cid')
        self.session = requests.session()
        self.session.proxies = {
            'http': 'http://10.99.138.95:30636',
            'https': 'http://10.99.138.95:30636'
        }
        self.session.mount('http://', HTTPAdapter(max_retries=1))
        self.session.mount('https://', HTTPAdapter(max_retries=1))

        super().__init__(eventlog)


class GdsCrawler(Crawler):
    def __init__(self, spider_name: str, crawler_conf: CrawlerConf):
        self.headers = {
            "Origin": "https://www.gds.org.cn",
            "Referer": "https://www.gds.org.cn/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/119.0.0.0 Safari/537.36",
        }
        self.headers_ancc = {
            "Origin": "http://www.ancc.org.cn",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 "
                          "Safari/537.36 Edg/121.0.0.0",
            "token": "null"
        }
        self.proxies = {
            'http': 'http://10.99.138.95:30636',
            'https': 'http://10.99.138.95:30636'
        }
        self.fim_dao = FirmDao()
        self.brand_dao = BrandDao()
        self.product_dao = ProductDao()
        self.company_dao = CompanyDao()
        self.code_dao = CodeDao()
        self.timeout = 5
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        self.ua = UserAgent()
        super().__init__(spider_name=spider_name, crawler_conf=crawler_conf, task_cls=GdsCrawlerTask)

    def do_crawl(self):
        task: GdsCrawlerTask = self.get_crawler_task()
        eventlog: Eventlog = task.eventlog

        if task.search is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.code = EventlogCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'{task.search} -->搜索为空')
                eventlog.code = EventlogCode.SEARCH_EMPTY
            if e.message == '403':
                logger.warning(f'{task.search} -->403 ip被封重试 {base64.b64decode(task.session.cookies.get("proxyBase", "")).decode()}')
                eventlog.code = EventlogCode.FAIL
            if e.message in ['206:Failed To Get Valid Ip', '异常IP', '参数解密异常']:
                logger.warning(f'{task.search} {eventlog.selector.info["cid"]} {eventlog.selector.word} --> {e.message} 放弃')
                eventlog.code = EventlogCode.FAIL
            if '连续失败' in e.message:
                logger.warning(f'{task.search} -->{e.message}')
                eventlog.code = EventlogCode.FAIL
            task.pages = {}

    def crawl_(self, task: GdsCrawlerTask):
        pages = dict()
        firm = self.get_firm_from_db(task)
        brands_old = self.get_brand_from_db(firm.firm_id) if firm else {}
        error_guess_barcodes = firm.json_data.get('error_guess_barcodes', []) if firm else []

        f_id, company_info = self.start_search(task, firm)
        barcodes = self.get_barcodes(task, f_id)
        # brand_info, no_crawled_brand = self.get_brands(task, f_id, firm, brands_old)
        brand_info: list = self.get_brand_list(task, f_id)['Data']['Items']

        barcodes_by_brand = barcodes_del = info_be = []
        brands_del = []
        if brand_info:
            barcodes_by_brand, brand_id_count_barcodes, barcodes_del, info_be, crawled_brands = self.get_barcodes_ByBrand(task, brand_info, brands_old)
            for idx, i in enumerate(brand_info):
                brand_name = i["brandcn"]
                if brand_name not in crawled_brands:
                    brands_del.append(brand_name)
                    continue
                # 更新brand id
                i['brand_id'] = brand_id_count_barcodes.get(f'{brand_name}_id', 0)
                # 猜测barcode
                brand_barcodes_old = brands_old.get(f'{brand_name}_barcodes', [])
                if firm and len(brand_barcodes_old) > 180 or i['itemcount'] > 180:
                    new_old_barcodes = list(set(brand_id_count_barcodes[f'{brand_name}_barcodes'] + brand_barcodes_old))
                    barcodes_by_brand.extend(self.guess_barcodes(task, new_old_barcodes, error_guess_barcodes, self.verify_barcode_effectiveness2))

        # 合并公司未按品牌分类和所有品牌的barcodes
        barcodes.extend(barcodes_by_brand)
        barcodes = list(set(barcodes))
        company_info["barcodes"] = copy.deepcopy(barcodes)

        if firm:
            barcodes = list(set(barcodes) - set(self.get_old_barcodes_from_db(firm.firm_id)))  # 去除以前调度已爬过的
        barcodes = [product for product in barcodes if product not in barcodes_del]  # 删除(未公开 and 详细页无数据)的barcode 和 搜索过的商品（随机检测未公开中的）

        # 爬取所有barcode
        products_info = self.search_retail(barcodes)
        products_info.extend(info_be)

        # 删除未没有商品的品牌
        brand_info = [brand for brand in brand_info if brand['brandcn'] in brands_del]

        # company_info['no_crawled_brand'] = no_crawled_brand
        company_info["error_guess_barcodes"] = list(set(error_guess_barcodes))
        pages['company_info.txt'] = json.dumps(company_info, ensure_ascii=False)
        pages['brand_info.txt'] = json.dumps(brand_info, ensure_ascii=False)
        pages['products_info.txt'] = json.dumps(products_info, ensure_ascii=False)

        return pages

    def start_search(self, task: GdsCrawlerTask, firm_ent: FirmEntity, crawled_company_info: bool = True):
        barcode_cast = task.eventlog.selector.meta.get('barcode', '')
        barcodes_old = firm_ent.json_data.get('barcodes') if firm_ent else []
        if barcode_cast or barcodes_old:
            barcode = barcodes_old[0] if not barcode_cast else barcode_cast
        else:
            barcode = self.crawl_barcode(task)

        search_result = self.search(task, barcode)
        f_id = search_result[0]['f_id']
        company_info = {}
        if crawled_company_info:
            company_info = self.get_company_info(task, f_id)

        return f_id, company_info

    def crawl_barcode(self, task: GdsCrawlerTask):
        firm_name = task.search
        certificate_code = task.eventlog.selector.word
        reg_number = task.eventlog.selector.info.get('reg_number', '')
        codes = self.get_firm_codes_from_db(certificate_code, firm_name, task.eventlog.selector.info.get('reg_number', ''))
        barcode_prefixs = list(set([code.barcode_prefix for code in codes]))
        barcodes = [code.other_info.get('barcode') for code in codes if code.other_info.get('barcode', '') != '']
        if barcodes:
            return barcodes[0]

        if not barcode_prefixs:
            nacc_data = self.nacc_search_by_firmName(firm_name)
            self.save_nacc_to_db(nacc_data)
            barcode_prefixs = jsonpath(nacc_data, f'$..hits[?(@._source.firm_name == "{firm_name}")]._source.code')
            barcode_prefixs = barcode_prefixs if barcode_prefixs else []
            barcode_prefixs = self.find_same_certification_code(nacc_data, certificate_code, reg_number) + barcode_prefixs
            if not barcode_prefixs:
                raise MyException('搜索为空')
        logger.info(f"firm_name:{firm_name} certificate_code:{certificate_code} barcode_prefixs:{barcode_prefixs}")

        for idx, bp in enumerate(list(set(barcode_prefixs))):
            for i in [0, 1, 101, 5, 501, 9, 1001, 51, 1501, 201, 2001]:
                barcode = get_firm_sample_ean13(firm_ean_id=bp, offset=i)
                if len(barcode) != 14:
                    continue
                try:
                    data = self.guess_barcodes(task, [barcode], [], self.verify_barcode_effectiveness2, only_one=True)
                    if data:
                        logger.info(f'存在 firm_name:{firm_name} certificate_code:{certificate_code} barcode:{barcode}')
                        self.code_dao.update_barcode(bp, barcode)  # 做缓存
                        return data[0]
                except MyException as e:
                    continue

        raise MyException('搜索为空')

    def nacc_search_by_firmName(self, firm_name):
        for _ in range(10):
            res = None
            try:
                url = "http://dynamic.gs1cn.org/office/captcha.jpg"
                uuid_ = uuid.uuid4()
                params = {"uuid": uuid_}
                response = requests.get(url, headers=self.headers_ancc, params=params, verify=False, proxies=self.proxies, timeout=2)
                captcha = self.ocr.classification(response.content)

                url = "http://dynamic.gs1cn.org/office/codeSearch/api/domesticCode"
                data = {"type": "2", "uuid": str(uuid_), "code": firm_name, "captcha": captcha}
                res = requests.post(url, headers=self.headers_ancc, json=data, verify=False, proxies=self.proxies, timeout=2)
                if res.json().get('msg', '') == 'success':
                    return res.json()
            except Exception as e:
                logger.warning(f"ancc 重试 status:{res.status_code if res else ''} res:{res.text if res else ''}")
        raise MyException('nacc连续搜索失败')

    def search(self, task: GdsCrawlerTask, barcode: str) -> list:
        url = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductListByGTIN"
        params = {"PageSize": "30", "PageIndex": "1", "SearchItem": barcode}
        response = None
        for i in range(20):
            try:
                response = task.session.get(url, headers=self.headers, params=params, verify=False, timeout=self.timeout)
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout):
                del task.session.cookies['proxyBase']
                continue
            status = response.status_code
            if status == 200:
                if response.json()['Msg'] == 'Success':
                    break
            elif status == 429:
                time.sleep(1.5)
            else:
                del task.session.cookies['proxyBase']

        if response.status_code == 403:
            raise MyException('403')
        if not response.json()['Data']['Items']:
            raise MyException(f'国外或没有barcode({barcode})')

        logger.info(response.json()['Data']['Items'][0]['f_id'])
        time.sleep(random.randint(1, 2))

        return response.json()['Data']['Items']

    def get_company_info(self, task: GdsCrawlerTask, f_id) -> dict:
        url = "https://bff.gds.org.cn/gds/searching-api/FirmService/FirmInfo"
        params = {"PageSize": "24", "PageIndex": "1", "Fid": f_id}
        response: dict = self.request(task, 'GET', url, params=params, name='get_company_info', tojson=True)
        return response['Data']['Items'][0]

    def get_brands(self, task: GdsCrawlerTask, f_id, firm, brands_old) -> (list, list):
        no_crawled_brand = []
        no_crawled_brand_old = firm.json_data.get('no_crawled_brand', []) if firm else []
        crawled_brand_info = []
        data = self.get_brand_list(task, f_id)['Data']['Items']
        if not data:
            return [], []

        n = 0
        brand_count = 0
        while data:
            brand_info = data.pop(0)
            if (no_crawled_brand_old and brand_info['brandcn'] not in no_crawled_brand_old) or brand_info.get('itemcount', 0) == 0:
                logger.info('brand商品数量为0或增量处理已经爬过，continue')
                continue
            if brand_info['brandcn'] in brands_old:
                crawled_brand_info.append(brand_info)
                continue
            url2 = "https://bff.gds.org.cn/gds/branding-api/Brand/GetBrandByIdAsync"
            res = {}
            try:
                res: dict = self.request(task, 'GET', url2, params={"BrandId": brand_info['brandid']}, name='get_brand_retail', tojson=True)
            except MyException as e:
                n += 1
                if n >= 3:
                    raise MyException('连续失败')
                if e.message in ['206:Failed To Get Valid Ip', '接口连续失败']:
                    task.session.cookies.clear()
                    f_id, _ = self.start_search(task, firm, crawled_company_info=False)
                    data = self.get_brand_list(task, f_id)['Data']['Items']
                    crawled_brand_info = []
                    brand_count = []
                    continue
            brand_info.update(res['Data'])
            crawled_brand_info.append(brand_info)
            time.sleep(random.randint(1, 2))
            brand_count += 1
            if brand_count >= 50:
                break

        return crawled_brand_info, no_crawled_brand

    def get_brand_list(self, task: GdsCrawlerTask, f_id) -> dict:
        url = "https://bff.gds.org.cn/gds/searching-api/Brand/GetPagedFirmBrand"
        params = {"PageSize": "180", "PageIndex": "1", "FId": f_id}
        result: dict = self.request(task, 'GET', url, params=params, name='get_brand_list', tojson=True)
        return result

    def get_barcodes(self, task: GdsCrawlerTask, id_) -> list:
        url = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductListByFID"
        params = {
            "PageSize": "200",
            "PageIndex": 1,
            "SearchItem": id_
        }
        response: dict = self.request(task, 'GET', url, params=params, name='get_barcodes', tojson=True)
        items = [item['gtin'] for item in response['Data']['Items']]
        logger.info(items)
        return items

    def get_barcodes_ByBrand(self, task: GdsCrawlerTask, brands, brands_old) -> (list, dict, list, list):
        barcodes = []
        barcodes_delete = []
        brand_id_count_barcodes = {}
        data_products = []
        crawled_brands = []
        temp = {}  # 临时储存

        for brand in brands:
            brand_name = brand.get('brandcn', '')
            if brand_name == '':
                continue
            if not brand.get('itemcount', 0):
                continue
            url2 = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductListByBrandNameFID"
            params2 = {"PageSize": "200", "PageIndex": "1", "SearchItem": brand_name, "FId": brand['fid']}
            try:
                response2: dict = self.request(task, 'GET', url2, params=params2, name='get_products_ByBrand_retail', tojson=True)
            except MyException:
                logger.warning(f'{task.search} 品牌barcode列表 未完成 做增量处理')
                break
            items = response2['Data']['Items']
            if not items:
                brand_id_count_barcodes.update({f'{brand_name}_id': 0, f'{brand_name}_barcodes': []})
                crawled_brands.append(brand_name)
                continue
            temp.update({f'{brand_name}': items})
            time.sleep(random.randint(1, 2))

        for brand_name, items in temp.items():
            barcodes_Bybrand = [item['gtin'] for item in items]  # 总
            brand_id = 0
            if brand_name not in brands_old:
                barcode_random = random.sample(barcodes_Bybrand, k=1)  # 随机
                barcodes_delete.extend(barcode_random)  # 删除爬过
                products_info = self.search_retail(barcode_random)
                if products_info:
                    data_products.extend(products_info)
                    brand_id = products_info[0]['BrandID']
            else:
                brand_id = brands_old[f'{brand_name}_id']
            barcodes.extend(barcodes_Bybrand)
            brand_id_count_barcodes.update({f'{brand_name}_id': brand_id, f'{brand_name}_barcodes': barcodes_Bybrand})
            crawled_brands.append(brand_name)
        logger.info(barcodes)
        return barcodes, brand_id_count_barcodes, barcodes_delete, data_products, crawled_brands

    def search_retail(self, barcodes: list) -> list:
        ips = []

        async def search_retail(barcode):
            for i in range(3):
                try:
                    connector = aiohttp.TCPConnector(ssl=False)
                    timeout = aiohttp.ClientTimeout(total=self.timeout)
                    proxy = self.proxies['http']
                    self.headers.update({"User-Agent": self.ua.chrome})
                    async with aiohttp.ClientSession(connector=connector) as session:
                        url = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductListByGTIN"
                        params = {"PageSize": "30", "PageIndex": "1", "SearchItem": barcode}
                        response = await session.get(url, headers=self.headers, params=params, timeout=timeout, proxy=proxy)
                        status = response.status
                        IP = base64.b64decode(response.cookies.get("proxyBase", Error).value).decode()
                        logger.info(f'使用了IP {IP}')

                        template = "barcode:%s reason:{} satus:%s res:%s" % (barcode, status, await response.text())
                        if status == 429:
                            logger.warning(template.format('重试'))
                            await asyncio.sleep(1.5)
                            continue
                        if status == 403:
                            ips.append(IP)
                            logger.warning(f"barcode:{barcode} 403 ip被封重试 {IP}")
                            if i < 1:
                                continue
                            raise MyException('403')
                        if status == 500:
                            if 'ECONNREFUSED' not in await response.text():
                                logger.warning(template.format('continue'))
                                continue
                        if status != 200:
                            logger.error(template.format('error'))
                            raise MyException(f'{status}')

                        if not (await response.json())['Data']['Items']:
                            logger.warning(f"国外或没有barcode: {barcode}")
                            return {}

                        data: dict = (await response.json())['Data']['Items'][0]
                        base_id = data['base_id']

                        # 注册
                        url_ = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductSimpleInfoByGTIN"
                        response = await session.get(url_, params={"gtin": barcode, "id": base_id}, headers=self.headers, timeout=timeout, proxy=proxy)
                        await asyncio.sleep(random.randint(1, 2))

                        url2 = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductInfoByGTIN"
                        response = await session.get(url2, params={"gtin": barcode, "id": base_id}, headers=self.headers, timeout=timeout, proxy=proxy)

                        if not (await response.json())['Data']['Items']:
                            logger.warning(f"barcode:{barcode} is_private:{data['is_private']} gtinstatus:{data['gtinstatus']}"
                                           f" 商品详细为空 状态码: {response.status} res: {await response.json()}")
                            return {}
                        else:
                            logger.info(f"barcode:{barcode} is_private:{data['is_private']} gtinstatus:{data['gtinstatus']}")

                        data.update((await response.json())['Data']['Items'][0]['ProductDetailsViewInfoNationalList'][0])
                        logger.info(data)
                        return data
                except (TypeError, KeyError, aiohttp.client_exceptions.ContentTypeError) as e:
                    logger.warning(f'barcode:{barcode} {e.__class__.__name__} 状态码: {response.status} res: {await response.text()}')
                    raise MyException(f'{e.__class__.__name__}')
                except (aiohttp.client_exceptions.ClientHttpProxyError, aiohttp.client_exceptions.ServerDisconnectedError,
                        asyncio.exceptions.TimeoutError, aiohttp.client_exceptions.ClientConnectorError) as e:
                    logger.warning(f'barcode:{barcode} 商品详细任务 代理问题:{e.__class__.__name__} 重试')
                    raise MyException(f'{e.__class__.__name__} 重试')
                except MyException as e:
                    raise e
                except Exception as e:
                    response and logger.warning(f'barcode:{barcode} Exception 状态码: {response.status} res: {await response.text()}')
                    response and response.status != 403 and logger.warning(f"barcode:{barcode} 商品详细任务 未知错误 {traceback.format_exc()}")
                    raise e
            raise Exception('超过3次')

        async def main(list_):
            data = []
            barcodes_list = list_
            error_count = {}
            while barcodes_list:
                barcodes_ = barcodes_list[:20]
                barcodes_list = barcodes_list[20:]
                tasks = [asyncio.create_task(search_retail(barcode), name=f"{barcode}") for barcode in barcodes_]
                done, _ = await asyncio.wait(tasks, return_when=asyncio.ALL_COMPLETED)
                for task in done:
                    if task.exception() is None:
                        if not task.result():
                            continue
                        data.append(task.result())
                    else:
                        barcode_failed = task.get_name()
                        barcodes_list.append(barcode_failed)
                        error_count[barcode_failed] = error_count.get(barcode_failed, 0) + 1
                        if error_count[barcode_failed] > 20:
                            logger.warning(f'barcode网站接口搜索有问题 {barcode_failed} 重试次数超过10次')
                            return data
                        logger.warning(f"barcode：{barcode_failed} failed 添加到数组 {task.exception()}")

            logger.info(f"len: {len(data)}")
            logger.warning(f"被封ip: {ips}")
            return data

        results = asyncio.run(main(barcodes))
        return results

    def guess_barcodes(self, task: GdsCrawlerTask, barcodes: list, error_guess_barcodes: list, func, only_one=False):
        async def main():
            new_barcodes = []
            if not only_one:
                for barcode in barcodes:
                    temp_barcodes = self.guess_barcode(barcode[1:-1])
                    new_barcodes.extend(temp_barcodes)
                new_barcodes = list(set(new_barcodes) - set(barcodes) - set(error_guess_barcodes))
            else:
                new_barcodes = barcodes

            guess_count = len(new_barcodes)
            valid_barcodes = []
            while new_barcodes:
                new_barcodes_ = new_barcodes[:15]
                new_barcodes = new_barcodes[15:]
                tasks = [asyncio.create_task(func(barcode), name=barcode) for barcode in new_barcodes_]
                done, _ = await asyncio.wait(tasks, return_when=asyncio.ALL_COMPLETED)
                for taskio in done:
                    if not taskio.exception() and taskio.result():
                        valid_barcodes.append(taskio.result())
                    elif not taskio.exception() and not taskio.result():
                        error_guess_barcodes.append(taskio.get_name())
                    else:
                        new_barcodes.append(taskio.get_name())

            logger.info(f'{task.search} 有效猜测-{len(valid_barcodes)} 无效猜测-{guess_count - len(valid_barcodes)}')
            return valid_barcodes

        new_barcodes2 = asyncio.run(main())
        return new_barcodes2

    async def verify_barcode_effectiveness(self, barcode):
        # 只有条形码有效返回True，其他情况（无效条形码、已注销、未公开）返回False、部分正常也返回False例如雅戈尔
        for _ in range(3):
            try:
                connector = aiohttp.TCPConnector(ssl=False)
                timeout = aiohttp.ClientTimeout(total=3)
                proxy = self.proxies['http']
                self.headers.update({"User-Agent": self.ua.chrome})
                async with aiohttp.ClientSession(connector=connector) as session:
                    uuid_ = str(uuid.uuid4())
                    params = {"uuid": uuid_}
                    url = "http://dynamic.gs1cn.org/office/captcha.jpg"
                    response = await session.get(url, headers=self.headers_ancc, params=params, proxy=proxy, timeout=timeout)
                    captcha = self.ocr.classification(await response.read())

                    url = "http://dynamic.gs1cn.org/office/vbgOutSearch/api/verified"
                    data = {"gtin": barcode, "uuid": uuid_, "captcha": captcha}
                    response = await session.post(url, headers=self.headers_ancc, json=data, proxy=proxy, timeout=timeout)
                    product = await response.json()
                    if product['msg'] == '验证码不正确':
                        continue
                    if product['data'] and product['data'][0].get('productDescription', []):
                        logger.info(f'正确猜测 barcode:{barcode}')
                        return barcode
                    logger.info(f'错误猜测 barcode:{barcode}')
                    return ''
            except Exception as e:
                logger.warning(f'猜测{barcode}-{_ + 1} continue')
                continue
        raise MyException('接口连续失败')

    async def verify_barcode_effectiveness2(self, barcode):
        for _ in range(3):
            try:
                connector = aiohttp.TCPConnector(ssl=False)
                timeout = aiohttp.ClientTimeout(total=3)
                proxy = self.proxies['http']
                headers = {"User-Agent": self.ua.chrome}
                async with aiohttp.ClientSession(connector=connector) as session:
                    url = "https://mp.weixin.qq.com/cgi-bin/newscanproductinfo"
                    params = {"action": "GetPage", "barcode_type": "EAN13", "barcode": barcode[1:]}
                    res = await session.get(url, headers=headers, params=params, proxy=proxy, timeout=timeout)
                    product = await res.json()
                    if 'topic_page' in product and product['topic_page'].get('title', ''):
                        logger.info(f'正确猜测 barcode:{barcode}')
                        return barcode
                    logger.info(f'错误猜测 barcode:{barcode}')
                    return ''
            except Exception as e:
                logger.warning(f'猜测{barcode}-{_ + 1} continue')
                continue
        raise MyException('接口连续失败')

    def request(self, task: GdsCrawlerTask, method: str, url: str, params: dict = None, data: dict = None,
                json: dict = None, path: str = None, name: str = '', tojson=False) -> Union[dict, str]:
        for i in range(3):
            response = None
            try:
                response = task.session.request(**{'method': method, 'url': url, 'data': data, 'headers': self.headers,
                                                   'verify': False, 'timeout': self.timeout, 'params': params, 'json': json})

                status = response.status_code
                if status == 500:
                    if 'ECONNREFUSED' in response.text:
                        logger.error(f'break 状态码：{response.status_code} res: {response.text}')
                        break
                    logger.warning(f'continue 状态码：{response.status_code} res: {response.text}')
                    continue
                if status == 206:
                    logger.error(f'break 状态码：{response.status_code} res: {response.text}')
                    raise MyException('206:Failed To Get Valid Ip')
                if status in [403, 429]:
                    logger.warning(f'continue 状态码：{response.status_code} res: {response.text}')
                    time.sleep(2)
                    continue

                logger.success(f'{name} --> {response.status_code}')
                a = response.text.replace("\n", "").replace('\r', '').replace('\t', '')
                logger.info(f'{name} --> {a}')

                if tojson:
                    """
                    {"Code":4,"Msg":"BrandController调用方法出现异常!Decrypt 异常IP异常:**************","Data":null}(200)
                    {"Code":4,"Msg":"","Data":"参数解密异常"}(200)
                    您今日的查询次数达到上限，请明日再查看！(200)
                    您的操作过于频繁，请稍后再试(429)
                    Unknown Exception(500)
                    """
                    if re.findall(r'(次数|解密异常|异常IP)', response.text):
                        logger.warning(f'break 状态码：{response.status_code} res: {response.text}')
                        break
                    return response.json()
                return response.text
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'continue exception: {e}')
                if i > 1:
                    break
                continue
            except Exception as e:
                status = response.status_code if response else "空"
                text = response.text if response else "空"
                logger.warning(f'continue 状态码：{status} res: {text} exception: {e}')
                continue
        raise MyException('接口连续失败')

    def get_firm_from_db(self, task: GdsCrawlerTask):
        firm: FirmEntity = self.firm_dao.get(task.cid, 'company_id')
        return firm

    def get_brand_from_db(self, firm_id):
        brand_all = {}
        for brand in self.brand_dao.get_many(firm_id, 'firm_id'):
            if brand.brand_id == 0:
                continue
            brand_all.update({
                f'{brand.cn_name}_id': brand.brand_id,
                f'{brand.cn_name}_barcodes': [product.barcode for product in self.product_dao.get_many(brand.brand_id, 'brand_id')],
                f'{brand.cn_name}': True
            })
        return brand_all

    def get_old_barcodes_from_db(self, firm_id):
        barcode_all = [product.barcode for product in self.product_dao.get_many(firm_id, 'firm_id')]
        return barcode_all

    def get_firm_codes_from_db(self, certificate_code, firm_name, reg_number):
        codes = list(self.code_dao.get_many(certificate_code, 'certificate_code'))
        if not codes:
            codes.extend(list(self.code_dao.get_many(firm_name, 'firm_name')))
            codes.extend(list(self.code_dao.get_many(reg_number, 'certificate_code')))
        return codes

    def save_nacc_to_db(self, nacc_data):
        for i in nacc_data['data']['hits']['hits']:
            data = i['_source']
            code = data['code']
            firm_name_ = data['firm_name']
            if not self.code_dao.get(code, 'barcode_prefix'):
                certificate_code = self.check_certificate_code(data['certificate_code'].strip())
                if not certificate_code:
                    company: Company = self.company_dao.get(self.replace_bracket(firm_name_), 'name')
                    certificate_code = company.credit_code if company else ''
                code_entity = CodeEntity.from_dict({
                    "firm_name": firm_name_,
                    "firm_id": data['f_id'],
                    "barcode_prefix": code,
                    "certificate_code": certificate_code,
                    "other_info": data
                })
                self.code_dao.save_by_cmp(code_entity, fields=["barcode_prefix"])

    @staticmethod
    def check_certificate_code(certificate_code: str):
        if re.fullmatch(r'[0-9A-Z]{18}', certificate_code):
            return ''
        else:
            return certificate_code

    @staticmethod
    def replace_bracket(name, cn_to_en=False):
        if cn_to_en:
            return name.replace('（', '(').replace('）', ')')
        return name.replace('(', '（').replace(')', '）')

    @staticmethod
    def guess_barcode(barcode_prefix):
        result = []
        for i in range(1, 2):
            result.append(add_mask_ean13(str(int(barcode_prefix) + i).zfill(12)))
            result.append(add_mask_ean13(str(int(barcode_prefix) - i).zfill(12)))
        return result

    @staticmethod
    def find_same_certification_code(data, certificate_code, reg_number):
        list_ = []
        for i in data['data']['hits']['hits']:
            if i['_source']['certificate_code'].strip() in [certificate_code, reg_number]:
                list_.append(i['_source']['code'])
        return list_


class Error:
    value = ""


if __name__ == '__main__':
    pass
