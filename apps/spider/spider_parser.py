# encoding=utf8

import json
import argparse
from threading import Lock
from concurrent.futures import Future
from libs.log2 import setup_logger
from libs.env import get_stack_info
from libs.concurrent import BoundedExecutor
from clients.redis.redis_queue import RedisQueue
from clients.kafka_client import KafkaProducerClient
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.spider.core.constants import SPIDER_REDIS, SPIDER
from apps.spider.parser import *
from apps.spider.utils.conf_manager import SpiderConfManager
from apps.octopus.core.constants import FEEDBACK_TOPIC, FEEDBACK_KAFKA


class Main(object):  # only one instance
    def __init__(self, conf_name: str):
        self.spider_manager = SpiderConfManager(reload_conf_interval_sec=5, json_conf_name=conf_name)
        self.spider_conf = self.spider_manager.get_exact_one()
        assert self.spider_conf
        assert isinstance(self.spider_conf.parser.obj, Parser)

        self.input_queue: RedisQueue = RedisQueue(
            name=f'{SPIDER}:{self.spider_conf.spider_name}:crawler_parser_queue',
            max_length=40000,
            use_zset=False,
            **SPIDER_REDIS,
        )

        self.output_queue = KafkaProducerClient(kafka_topic=FEEDBACK_TOPIC, **FEEDBACK_KAFKA)

        self.fs = dict()
        self.fs_lock = Lock()

    def run(self):
        with BoundedExecutor(
                max_workers=self.spider_conf.parser.parser_threads,
                thread_name_prefix='parser',
        ) as process_pool:
            for s in self.input_queue.generate():
                try:
                    d = json.loads(s)
                    eventlog = Eventlog.from_dict(d)
                    if not eventlog:
                        logger.warning(f'error from dict {s}')
                        continue
                except Exception as e:
                    logger.warning(f'error eventlog {e}')
                    continue
                parser_obj: Parser = self.spider_conf.parser.obj
                future: Future = process_pool.submit(parser_obj.parse, eventlog)
                with self.fs_lock:
                    self.fs[future] = eventlog
                future.add_done_callback(self.callback_fn)
                # break  # TEST

    def callback_fn(self, future: Future):
        eventlog = self.fs[future]
        with self.fs_lock:
            del self.fs[future]
        try:
            eventlog = future.result()
        except Exception as e:
            eventlog.code = EventlogCode.GIVE_UP
            logger.info(f'error process {eventlog} set SpiderCode.GIVE_UP {e} {get_stack_info()}')
        eventlog_str = eventlog.to_json()
        ret = self.output_queue.write(eventlog_str)
        logger.info(f'OUTPUT {eventlog_str} ret={ret}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='统一爬虫——解析器')
    ap.add_argument('--conf-name', type=str, default='acftu.json')
    ap.add_argument('--backup-days', type=int, default=3, help='日志保存天数')
    ap_args = ap.parse_args()
    app_name = '.'.join(['spider_parser', ap_args.conf_name])
    logger = setup_logger(use_file_log=True, app_name=app_name, backup_count=ap_args.backup_days, rotate_mode='D')
    Main(ap_args.conf_name).run()
