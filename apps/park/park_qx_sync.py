import json
import uuid
from enum import Enum
from concurrent.futures import Future
from typing import List
import argparse
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor
from clients.redis.redis_queue import RedisQueue
from dao.company import CompanyGraph, CompanyGraphDao
from apps.park.deps.qx_park import QXPark, QXParkDao
from apps.park.deps.qx_park_relation import QXParkRelation, QXParkRelationDao
from apps.park.deps.park import Park, ParkDao
from apps.park.deps.park_relation import ParkRelation, ParkRelationDao
from gslib.id_center import id_center_query, EntityType
from apps.wangbangxu.qx_eid import QxbCodeEid, QxbCodeEidDao


logger = setup_logger()

fs = dict()
process_code_stat = Counter()

company_graph_dao = CompanyGraphDao()
qx_park_dao = QXParkDao()
qx_park_relation_dao = QXParkRelationDao(batch_size=50000)
park_dao = ParkDao()
park_relation_dao = ParkRelationDao()
qx_eid_dao = QxbCodeEidDao()
redis_queue_input_qx = RedisQueue(db=3, name='park:tasks', delay_secs=30, max_length=100000, **ConstantProps.PROPS_GS_REDIS_ONLINE)


class ProcessCode(Enum):
    OK = '正常'
    DUP = '重复数据'
    EXCEPTION = '抛出异常'
    ERROR = '错误'


def get_base(province):
    base_dict = {
        "sh": "上海", "yn": "云南", "nmg": "内蒙古", "bj": "北京", "jl": "吉林", "sc": "四川",
        "tj": "天津", "nx": "宁夏", "ah": "安徽", "sd": "山东", "sx": "山西", "gd": "广东",
        "gx": "广西", "xj": "新疆", "js": "江苏", "jx": "江西", "hb": "河北", "hen": "河南", "zj": "浙江",
        "han": "海南", "hub": "湖北", "hun": "湖南", "gs": "甘肃", "fj": "福建", "xz": "西藏", "gz": "贵州",
        "ln": "辽宁", "cq": "重庆", "snx": "陕西", "qh": "青海", "hlj": "黑龙江",
    }
    for k, v in base_dict.items():
        if v in province:
            return k
    return ''


def do_sync(qx_park: QXPark, qx_rel_list: List[QXParkRelation]) -> bool:
    companies = []
    seen_companies = set()
    cid_by_dao, cid_by_http = 0, 0
    for qx_rel in qx_rel_list:
        qx_eid: QxbCodeEid = qx_eid_dao.get(value=qx_rel.eid, field='eid')
        if qx_eid and qx_eid.cid:
            cid = qx_eid.cid
            cid_by_dao += 1
        else:
            if len(qx_rel.company_name) < 5 or '*' in qx_rel.company_name:
                logger.info(f'bad name {qx_rel.company_name}')
                continue
            ent_type, cid = id_center_query(name=qx_rel.company_name)
            if ent_type != EntityType.ORG:
                logger.info(f'cannot find name for {qx_rel.company_name}')
                continue
            cid_by_http += 1
        company_graph: CompanyGraph = company_graph_dao.get_by_cid(cid)
        if not company_graph:
            logger.info(f'no graph for cid {cid}')
            continue
        if company_graph.cgid in seen_companies:
            logger.warning(f'already seen {company_graph.cgid} {qx_rel.company_name}')
            continue
        seen_companies.add(company_graph.cgid)
        companies.append((company_graph.cgid, qx_rel.company_name))
    logger.info(f'cid_by_dao={cid_by_dao} cid_by_http={cid_by_http}')
    # qx_park.ploy 只有以下两种情形
    # [[[109.970405, 22.678456], [109.975211, 22.681623], [109.981734, 22.672437], [109.977271, 22.670219]]]
    # []
    coordinate_list = []
    ploy_obj = json.loads(qx_park.ploy or '[]')
    if len(ploy_obj) == 1 and isinstance(ploy_obj[0], list):
        for pt in ploy_obj[0]:
            if isinstance(pt, list) and len(pt) == 2:
                coordinate_list.append(f'{pt[0]},{pt[1]}')

    park: Park = Park.from_dict({
        'park_name': qx_park.name,
        'province': qx_park.province,
        'base': get_base(qx_park.province),
        'city': f'{qx_park.city}, {qx_park.district}',
        'province_code': f'{qx_park.district_code[:2]}0000',
        'city_code': f'{qx_park.district_code[:4]}00',
        'district_code': qx_park.district_code,
        'park_area': f'{qx_park.area}',
        'company_number': f'{len(companies)}',
        'coordinate': ';'.join(coordinate_list),
        'center_coordinate': '',
        'company_total': len(companies),
        'graph_id_list': '		;		'.join(str(x[0]) for x in companies[:4000]),
        'company_name_list': '	;	'.join(x[1] for x in companies[:4000]),
        'uuid': str(uuid.uuid4()).replace('-', ''),
        'province_code_new': f'00{qx_park.district_code[:2]}0000',
        'city_code_new': f'00{qx_park.district_code[:4]}00',
        'district_code_new': f'00{qx_park.district_code}',
    })
    if not park:
        return False

    changed, updated, park_id = park_dao.save_by_cmp(
        fields=['park_name', 'district_code'],
        ignore_fields=['id', ],
        only_insert_fields=['uuid'],
        item=park,
    )

    if len(companies) == 0:
        return True

    park_relation_lst = []
    for gid, name in companies:
        park_relation: ParkRelation = ParkRelation.from_dict(
            {
                'park_id': park_id,
                'company_graph_id': gid,
                'company_name': name
            }
        )
        if not park_relation:
            continue
        park_relation_lst.append(park_relation)

    park_relation_dao.save_by_group(
        items=park_relation_lst,
        group_fields=['park_id', ],
        key_fields=['company_graph_id', ],
        group_values=[park_id, ],
    )
    return True


def process(item: QXPark) -> ProcessCode:
    logger.info(f'BEGIN {item}')
    if not item:
        return ProcessCode.ERROR
    park_id, park_name, district_code = item.id, item.name, item.district_code
    if len(district_code) != 6:
        logger.warning(f'bad district_code park_id={park_id} district_code={district_code}')
        return ProcessCode.ERROR
    dup_items = list(qx_park_dao.get_many_ex(values=[park_name, district_code], fields=['name', 'district_code']))
    if len(dup_items) == 0:
        return ProcessCode.ERROR

    real_item, real_rel_list = dup_items[0], []
    for dup_item in dup_items:
        dup_item: QXPark
        dup_rel_list = list(qx_park_relation_dao.get_many(value=dup_item.self_id, field='self_id'))
        if len(dup_rel_list) > len(real_rel_list):
            real_rel_list = dup_rel_list
            real_item = dup_item
    logger.info(f'real_item={real_item.id} rel={len(real_rel_list)}')

    ret = do_sync(real_item, real_rel_list)
    if not ret:
        logger.info(f'do_sync fail {item.id} {real_item}')
        return ProcessCode.ERROR
    return ProcessCode.OK


def callback_fn(f: Future):
    c = fs[f]
    del fs[f]
    try:
        ret: ProcessCode = f.result()
        process_code_stat[ret] += 1
    except Exception as e:
        logger.info(f'error process {c}  e={e} {get_stack_info()}')
        process_code_stat[ProcessCode.EXCEPTION] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
    ):
        for s in redis_queue_input_qx.generate():
            qx_park_item = qx_park_dao.get(value=s, field='self_id')
            future: Future = worker_pool.submit(process, qx_park_item)
            fs[future] = qx_park_item
            future.add_done_callback(callback_fn)


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多任务处理程序')
    ap.add_argument('--worker-num', type=int, default=8, help='')
    main(ap.parse_args())
    # o = qx_park_dao.get(129356)
    # process(o)

