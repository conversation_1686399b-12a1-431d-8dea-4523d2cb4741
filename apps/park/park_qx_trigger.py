from datetime import datetime, timed<PERSON><PERSON>
import argparse
from libs.log2 import setup_logger
from apps.park.deps.qx_park import QXPark, QXParkDao
from apps.park.deps.qx_park_relation import QXParkRelation, QXParkRelationDao
from apps.park.deps.park import ParkDao
from apps.park.deps.park_relation import Park<PERSON>elation<PERSON>ao
from apps.park.park_qx_sync import redis_queue_input_qx

logger = setup_logger()

qx_park_dao = QXParkDao()
qx_park_relation_dao = QXParkRelationDao(batch_size=50000)
park_dao = ParkDao()
park_relation_dao = ParkRelationDao()


def main(args):
    logger.info(f'args={args}')
    start_dt = datetime.now() - timedelta(seconds=args.start_secs)
    dao = qx_park_dao if args.table == 'qx_park' else qx_park_relation_dao
    for idx, item in enumerate(dao.scan(
            start=start_dt,
            scan_key='row_update_time',
            infinite=True,
            infinite_sleep_secs=30,
    )):

        if isinstance(item, QXParkRelation):
            qx_park_item: QXPark = qx_park_dao.get(value=item.self_id, field='self_id')
        else:
            qx_park_item: QXPark = item

        if qx_park_item:
            ret = redis_queue_input_qx.push(value=qx_park_item.self_id)
            logger.info(f'push {qx_park_item} ret={ret}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多任务处理程序')
    ap.add_argument('--start-secs', type=int, default=3600 * 24 * 7, help='增量数据消费开始时间')
    ap.add_argument('--table', type=str, default='qx_park', choices=['qx_park', 'qx_park_relation'])

    main(ap.parse_args())
