from datetime import datetime
from entity.deps.entity import BaseEntity
from libs.env import get_props_mysql
from dao.deps.mysql_dao import MySQLDao


class QXParkRelation(BaseEntity):
    id: int
    name: str
    yid: str
    self_id: str
    md5: str
    eid: str
    company_name: str
    last_updated_time: int
    created_time: int
    row_update_time: datetime
    local_row_update_time: datetime


class QXParkRelationDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in get_props_mysql('mysql.tyc.qxb.b').items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'db_business_reproduce.t_whole_park_relation')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', QXParkRelation)
        super().__init__(**kwargs)


if __name__ == '__main__':
    dao = QXParkRelationDao()
    data = dao.get(27396489)
    print(data)
