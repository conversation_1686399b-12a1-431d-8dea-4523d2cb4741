from typing import Optional
from datetime import datetime
from entity.deps.entity import BaseEntity
from libs.env import get_props_mysql
from dao.deps.mysql_dao import MySQLDao


class QXPark(BaseEntity):
    id: int
    name: str
    yid: str
    self_id: str
    province: str
    city: str
    district: str
    district_code: str
    area: int
    description: Optional[str]
    ploy: str
    url: str
    last_updated_time: int
    created_time: int
    row_update_time: datetime
    local_row_update_time: datetime


class QXParkDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in get_props_mysql('mysql.tyc.qxb.b').items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'db_business_reproduce.t_whole_park')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', QXPark)
        super().__init__(**kwargs)


if __name__ == '__main__':
    qx_park_dao = QXParkDao()
    data = qx_park_dao.get(124468)
    print(data)
