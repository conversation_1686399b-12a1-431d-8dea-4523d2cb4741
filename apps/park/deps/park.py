from __future__ import annotations
from pydantic import Field
from typing import Optional, List
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao


class Park(BaseEntity):
    id: Optional[int] = Field(default=None)
    park_name: str
    province: str
    city: str
    base: str
    province_code: str
    city_code: str
    district_code: str
    park_area: str
    company_number: str
    coordinate: Optional[str] = Field(default=None)
    center_coordinate: Optional[str] = Field(default=None)
    company_name_list: str
    company_total: int
    graph_id_list: str
    uuid: str
    province_code_new: str = Field(default='')
    city_code_new: str = Field(default='')
    district_code_new: str = Field(default='')

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class ParkDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.qianzhan_park')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', Park)
        super().__init__(**kwargs)

