from __future__ import annotations
from datetime import datetime
from pydantic import Field
from typing import Optional
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao


class ParkRelation(BaseEntity):
    id: Optional[int] = Field(default=None)
    park_id: int
    company_graph_id: int
    company_name: str
    deleted: int = Field(default=0)
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class ParkRelationDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.qianzhan_park_relation')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', ParkRelation)
        super().__init__(**kwargs)
