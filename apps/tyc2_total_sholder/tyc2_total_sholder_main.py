# encoding=utf8

import json
import re
import argparse
import logging
from datetime import datetime
from decimal import Decimal
from typing import List, Dict

from clients.oss_client import OSSClient
from gslib.id_center import id_center_query, EntityType
from dao.investors.sholder_mirror import SholderMirror, SholderMirrorDao
from dao.investors.investor_snap_copy import InvestorSnapDao, InvestorSnap
from dao.enterprise import Enterprise, EnterpriseDao
from dao.company import Company, CompanyDao, CompanyGraphDao
from dao.human import HumanGraphDao
from libs.concurrent import BoundedExecutor
from libs.dt import to_date
from apps.tyc2_total_sholder.tyc2_total_sholder_deps import input_queue

sholder_mirror_dao = SholderMirrorDao()
investor_snap_dao = InvestorSnapDao()
enterprise_dao = EnterpriseDao()
company_dao = CompanyDao()
company_graph_dao = CompanyGraphDao()
human_graph_dao = HumanGraphDao()
oss = OSSClient(bucket_name='jindi-oss-shareholder')

capital_pats = [
    ('([0-9]+.?[0-9]*)万元', 10000, '人民币'),
    ('([0-9]+.?[0-9]*)万美元', 10000, '美元'),
    ('([0-9]+.?[0-9]*)万港元', 10000, '港元'),
    ('未披露', 0, ''),
]

logger = logging.getLogger(__file__)


def process(gid: int):
    # enterprise信息不存在或者company表不存在，则输出为空 报警
    enterprise: Enterprise = enterprise_dao.get(gid, 'graph_id')
    if not enterprise or enterprise.deleted != 0:
        logger.info(f'not ok enterprise gid={gid}')
        investor_snap_dao.remove_by_gid(gid)
        return
    company: Company = company_dao.get(enterprise.id)
    if not company or not company.establish_date or not company.approved_date:
        logger.info(f'not ok company gid={gid}')
        investor_snap_dao.remove_by_gid(gid)
        return

    # 股东镜像表无信息 输出为空 报警
    sholder_mirror_item: SholderMirror = sholder_mirror_dao.get(gid, 'company_gid')
    if sholder_mirror_item is None or sholder_mirror_item.deleted != 0:
        logger.info(f'no sholder_mirror for gid={gid}')
        investor_snap_dao.remove_by_gid(gid)
        return

    # 不包含股东的企业类型，输出为空
    if company.company_org_type:
        if '个体' in company.company_org_type or '分' in company.company_org_type:
            logger.info(f'not ok company type gid={gid} org_type={company.company_org_type}')
            investor_snap_dao.remove_by_gid(gid)
            return

    # oss信息不正确，报警退出
    if not sholder_mirror_item.oss_url or not re.fullmatch(r'company_graph_id-\d+', sholder_mirror_item.oss_url):
        logger.info(f'sholder_mirror for gid={gid} bad oss_url={sholder_mirror_item.oss_url}')
        return
    oss_data = oss.get_file_content_by_name_from_oss(sholder_mirror_item.oss_url)
    if not isinstance(oss_data, str):
        logger.info(f'fail get oss_data oss_url={sholder_mirror_item.oss_url}')
        return
    try:
        js = json.loads(oss_data)
    except (TypeError, json.JSONDecodeError):
        logger.warning(f'fail load oss_data as json oss_data={oss_data} gid={gid}')
        return
    sholder_collections = js.get('items', [])
    if len(sholder_collections) == 0:
        logger.info(f'sholder_mirror len=0 for gid={gid} ')
        return

    seen_investor_gid = set()
    investor_snap_items: List[InvestorSnap] = []
    for sholder_list in sholder_collections:
        if not isinstance(sholder_list, List):
            continue
        for sholder_item in sholder_list:
            if not isinstance(sholder_item, Dict):
                continue
            if sholder_item.get('hasData', False) is False:
                continue
            snap_date = to_date(sholder_item['dateTimeStr'])
            if snap_date is None:
                continue
            elif snap_date < company.establish_date:
                snap_date = company.establish_date
            elif snap_date > company.approved_date and snap_date > datetime.now().date():
                snap_date = company.approved_date

            # select * from test.test_prism_gsxt_investor_snap_copy_df where pt='20231106'
            # and investor_name regexp '^[\\u4E00-\\u9FA5\\s]+公司$' and investor_name regexp '\\s' and char_length(investor_name) > 5
            if re.fullmatch(r'[\u4E00-\u9FA5（）\s]+公司', sholder_item['name']) and len(sholder_item['name']) > 5:
                investor_name = re.sub(r'\s', '', sholder_item['name'])
            else:
                investor_name = sholder_item['name']
            investor_name = re.sub(r'（万元）', '', investor_name).strip()  # 万元） 黄光辉
            investor_name = re.sub(r'</br>', '', investor_name).strip()  # </br>
            # logger.info(f'investor_name={investor_name}')

            if sholder_item['type'] == '1':
                investor_type_mirror, investor_gid_mirror = 1, int(sholder_item['hgid'])
            elif sholder_item['type'] == '2':
                investor_type_mirror, investor_gid_mirror = 2, int(sholder_item['cgid'])
            elif sholder_item['type'] == '3':
                investor_type_mirror, investor_gid_mirror = 3, 0
            else:
                continue

            if len(investor_name) < 2 or len(investor_name) > 32:
                entity_type, entity_id = 2, 0
            else:
                entity_type, entity_id = id_center_query(name=investor_name)
            if entity_type == EntityType.HUMAN:
                investor_hg = human_graph_dao.get_by_hid(entity_id)
                if not investor_hg:
                    continue
                investor_type, investor_gid = 1, investor_hg.hgid
            elif entity_type == EntityType.ORG:
                investor_cg = company_graph_dao.get_by_cid(entity_id)
                if not investor_cg:
                    continue
                enterprise: Enterprise = enterprise_dao.get(investor_cg.cgid, 'graph_id')
                if not enterprise or enterprise.deleted != 0:
                    logger.info(f'not ok enterprise gid={gid} investor_cg={investor_cg}')
                    continue
                investor_type, investor_gid = 2, investor_cg.cgid

            elif entity_type == EntityType.TYPE3:
                investor_type, investor_gid = 3, 0
            elif entity_type == EntityType.UNSET:
                investor_type, investor_gid = 2, 0
            else:
                logger.warning(f'error id_center_query, back to queue {gid} investor_name="{investor_name}"')
                input_queue.push(value=gid, realtime=True)
                return

            if investor_type_mirror != investor_type or investor_gid_mirror != investor_gid:
                logger.info(f'gid={gid} {investor_name} ({investor_type_mirror},{investor_gid_mirror}) -> ({investor_type},{investor_gid})')

            if investor_gid_mirror == 0:
                logger.info(f'empty gid={gid} {investor_gid== 0} {investor_name} ({investor_type_mirror},{investor_gid_mirror}) -> ({investor_type},{investor_gid})')

            investor_gid_key = (investor_gid, snap_date)
            if investor_gid > 0 and investor_gid_key in seen_investor_gid:
                logger.info(f'dup investor_gid_key={investor_gid_key} gid={gid}')
                continue
            seen_investor_gid.add(investor_gid_key)

            subscript_detail = []  # date='' amount currency payment=''
            capital = sholder_item['capital']
            for pat, factor, currency in capital_pats:
                mo = re.fullmatch(pat, capital)
                if mo:
                    if len(mo.groups()) == 0:
                        amount = 0
                    else:
                        amount = float(Decimal(mo.group(1)) * factor)
                    break
            else:
                amount = 0
                currency = ''
                logger.warning(f'bad capital={capital} gid={gid} investor_name={investor_name}')
            subscript_detail.append({'date': '', 'amount': amount, 'currency': currency, 'payment': ''})
            subscript_detail_str = json.dumps(subscript_detail, ensure_ascii=False)
            investor_snap_item: InvestorSnap = InvestorSnap.from_dict(dict(
                gid=gid,
                investor_gid=investor_gid,
                investor_name=investor_name,
                investor_type=investor_type,
                subscript_detail=subscript_detail_str,
                actual_detail='[]',
                data_source='sholder_mirror',
                snap_date=snap_date,
            ))
            investor_snap_items.append(investor_snap_item)
    delete_count, update_count, insert_count = investor_snap_dao.save_by_group(
        items=investor_snap_items,
        group_fields=['gid'],
        key_fields=['snap_date', 'investor_gid', ],
        group_values=[gid, ],
        save_mode=2,
    )
    logger.info(f'OUTPUT gid={gid} delete_count={delete_count} update_count={update_count} insert_count={insert_count}')


def main():
    with BoundedExecutor(max_workers=ap_args.process_workers) as process_executor:
        for s in input_queue.generate():
            if re.fullmatch(r'\d+', s):
                gid = int(s)
                process_executor.submit(process, gid)


if __name__ == '__main__':
    from libs.log2 import setup_logger
    ap = argparse.ArgumentParser(description='tyc2_total_sholder_main')
    ap.add_argument('--process-workers', type=int, default=1, help='处理线程数')
    ap_args = ap.parse_args()
    logger = setup_logger(app_name='tyc2_total_sholder_main', rotate_mode='D', use_file_log=True, backup_count=10)
    main()
