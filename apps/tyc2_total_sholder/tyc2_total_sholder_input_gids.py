# encoding=utf8

import csv
import argparse
from libs.log import setup_logger
from apps.tyc2_total_sholder.tyc2_total_sholder_deps import input_queue

# scp -i ~/Documents/work.pem /Users/<USER>/Downloads/query-hive-375680.csv work@10.99.204.71:/home/<USER>/pygs-work-parent/apps/tyc2_total_sholder/gid.part.0.csv
#  nohup ../../venv/bin/python3 tyc2_total_sholder_input_gids.py --input gid.part.0.csv 2>&1 > ../../logs/tyc2_total_sholder_input_gids.log.0 &


def main():
    logger.info('args=%s', ap_args)
    reader = csv.DictReader(ap_args.input)
    for did, d in enumerate(reader):
        if did % 1000 == 0:
            logger.info(f'did={did}')
        gid = d.get('company_graph_id', None)
        if not gid:
            continue
        input_queue.push(int(gid), realtime=False)


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='tyc2_total_sholder_input_binlog.py')
    ap.add_argument('--input', type=argparse.FileType('r'), required=True, help='输入csv文件')
    ap_args = ap.parse_args()
    logger = setup_logger()
    main()
