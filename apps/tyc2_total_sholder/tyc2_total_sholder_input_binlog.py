# encoding=utf8

import time
import json
import argparse
from clients.kafka_client import KafkaConsumerClient
from libs.log import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor
from apps.tyc2_total_sholder.tyc2_total_sholder_deps import input_queue
from queue import Queue

q = Queue()


def process_func(d: dict):
    data = d['data']
    if not isinstance(data, list):
        return
    for item in data:
        if not isinstance(item, dict):
            continue
        q.put_nowait((time.time(), item['company_graph_id']))


def dump_func(process_executor: BoundedExecutor):
    topic_name = '77699.json.strategy_algorithm.history_shareholders_mirror_all'
    consumer = KafkaConsumerClient(kafka_topic=topic_name, group_id='tyc2_total_sholder', **ConstantProps.PROPS_GS_KAFKA_DATA)
    for sid, s in enumerate(consumer.read()):
        try:
            # logger.info(f'{sid} {s}')
            data = json.loads(s)
            process_executor.submit(process_func, data)
        except Exception as e:
            logger.warning(f'{e} {s} {get_stack_info()}')
            continue
    consumer.close()


def main():
    logger.info('args=%s', ap_args)
    with BoundedExecutor(max_workers=ap_args.dump_workers) as dump_executor, \
            BoundedExecutor(max_workers=ap_args.process_workers) as process_executor:
        for _ in range(ap_args.dump_workers):
            dump_executor.submit(dump_func, process_executor)
        while True:
            while True:
                if q.empty():
                    break
                ts, item = q.get_nowait()
                delta_ts = time.time() - ts
                if delta_ts < 60:
                    logger.info(f'sleep {60 - delta_ts} for realtime')
                    time.sleep(60 - delta_ts)
                input_queue.push(value=item, realtime=False)
                logger.info(f'new input {item}')
            time.sleep(5)


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='tyc2_total_sholder_input_binlog.py')
    ap.add_argument('--dump-workers', type=int, default=1, help='kafka分区')
    ap.add_argument('--process-workers', type=int, default=1, help='处理线程数 可以设置大些')
    ap.add_argument('--delay-secs', type=int, default=30, help='binlog延迟消费')

    ap_args = ap.parse_args()
    logger = setup_logger()
    main()
