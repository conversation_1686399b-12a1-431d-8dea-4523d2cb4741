
from typing import Any, Optional, List
from pydantic import Field
from entity.deps.entity import BaseEntity


class ClassArgsConf(BaseEntity):
    clazz: str
    obj: Optional[Any] = Field(default=None)
    args: dict = Field(default={})


class EntryDaoConf(ClassArgsConf):
    # None表示无法抓取新增的inst，False/True代表可以抓取新增但是打开/关闭
    # 更进一步说 has_clue=None不用更新info信息
    has_clue: Optional[bool] = Field(default=None)
    code2_prefix: str = Field(default='')  # 需要按顺序给出
    max_try: int = Field(default=3)
    batch_size: int = Field(default=2048)
    word_pat: str = Field(default='.*')  # 用于过滤调度


class SenderConf(ClassArgsConf):
    spider_timeout_sec: int = Field(default=0)  # 0表示爬虫不返回数据 >0表示超时控制
    sender_threads: int = Field(default=1)
    sender_queue_size: int = Field(default=100)  # 爬虫队列长度  设置的过长容易出现超时
    sender_hour_range: str = Field(default="0 24")  # 可投词时间段 [) 22 1 表示22点凌晨1点
    send_per_minute: int = Field(default=0)  # 每分钟可投词量 一天一共1.4k分量 默认<=0表示无限制
    min_success_ratio: float = Field(default=0)  # 低于此值 会进行报警
    warning_ats: List[str] = Field(default=[])


class SchedulerConf(BaseEntity):
    exp_send_ratio: float = Field(default=0.1)  # 期望的动态扔词比例
    max_rest_sec: int = Field(default=60)
    inst_name: str  # 爬虫实例
    entry_dao: EntryDaoConf
    sender: SenderConf

