# encoding=utf8

import logging
from pydantic import Field, conint
from entity.deps.entity import BaseEntity
from libs.dt import cur_ts_sec
logger = logging.getLogger(__name__)


class Inst(BaseEntity):
    scheduling: bool = Field(default=False)  # 是否在 调度中
    last_success_ts: int = Field(default=0)  # 上次成功抓取时间
    last_empty_ts: int = Field(default=0)  # 上次搜索无结果时间
    last_fail_ts: int = Field(default=0)  # 上次抓取失败，包括give_up时间
    last_schedule_ts: int = Field(default=0)  # 上次调度时间
    try_id: conint(ge=0) = Field(default=0)  # 重试id
    reason: str = Field(default='')  # 立即调度的reason  重试的时候 有可能是schedule
    inst_info: dict = Field(default={})  # 不参与eventlog 用于存储和爬虫相关的外部参数
    meta: dict = Field(default={})

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def schedule(self) -> bool:
        if self.scheduling:
            return False
        self.scheduling = True
        self.last_schedule_ts = cur_ts_sec()
        self.reason = ''
        return True

    def success(self, search_empty=False) -> bool:
        if not self.scheduling:
            return False
        self.scheduling = False
        self.try_id = 0
        self.reason = ''
        if search_empty:
            self.last_empty_ts = cur_ts_sec()
        else:
            self.last_success_ts = cur_ts_sec()
        return True

    def fail(self, max_try=3) -> bool:
        if not self.scheduling:
            return False
        self.scheduling = False
        self.try_id += 1
        if self.try_id >= max_try:
            self.try_id = 0
            self.last_fail_ts = cur_ts_sec()
        return True
