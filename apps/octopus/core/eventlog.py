# encoding=utf8

import enum
import logging
from pydantic import Field
from entity.deps.entity import BaseEntity

logger = logging.getLogger(__name__)


class EventlogCode(int, enum.Enum):
    UNFILLED = -1
    SUCCESS = 0
    FAIL = 1
    SEARCH_EMPTY = 11
    GIVE_UP = 2  # octopus表示不再重试，放弃  例如解析失败
    FEEDBACK_ERROR = 90  # 无法正确回收
    TIMEOUT = 99  # 爬虫超时，由系统返回的状态值

    def human(self) -> str:
        if self == EventlogCode.UNFILLED:
            return '初始值'
        if self == EventlogCode.SUCCESS:
            return '成功'
        if self == EventlogCode.FAIL:
            return '失败'
        if self == EventlogCode.SEARCH_EMPTY:
            return '搜索无结果'
        if self == EventlogCode.GIVE_UP:
            return '爬虫放弃'
        if self == EventlogCode.FEEDBACK_ERROR:
            return '回收异常'
        if self == EventlogCode.TIMEOUT:
            return '爬虫超时'
        return self.name


class SelectorLog(BaseEntity):
    send_ts: int = Field(default=-1)
    receive_ts: int = Field(default=-1)
    reason: str
    clue: bool
    entry_name: str
    inst_name: str
    word: str
    info: dict = Field(default={})
    try_id: int = Field(default=0)
    meta: dict = Field(default={})
    weight: int = Field(default=-1)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class SpiderLog(BaseEntity):
    receive_ts: int = Field(default=-1)
    send_ts: int = Field(default=-1)
    spider_data: dict = Field(default={})
    ab_info: dict = Field(default=dict())

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class Eventlog(BaseEntity):
    event_id: str
    code: EventlogCode = Field(default=EventlogCode.UNFILLED)
    selector: SelectorLog
    spider: SpiderLog

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
