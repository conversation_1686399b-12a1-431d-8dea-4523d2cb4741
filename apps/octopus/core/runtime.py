import logging
from typing import Generator
from clients.redis._redis import Redis
from libs.dt import cur_ts_sec
from apps.octopus.core.constants import OCTOPUS, OCTOPUS_REDIS
from apps.octopus.core.eventlog import Eventlog

logger = logging.getLogger(__file__)
redis = Redis(**OCTOPUS_REDIS)


# entry全局锁
def get_entry_lock(entry_name, entry_word):
    return redis.lock(name=f'{OCTOPUS}:{entry_name}:{entry_word}:entry_lock', timeout=1.0)


def get_code_offset(entry_name, inst_name):
    return redis.get(name=f'{OCTOPUS}:{entry_name}:{inst_name}:offset')


def set_code_offset(entry_name, inst_name, offset):
    return redis.set(name=f'{OCTOPUS}:{entry_name}:{inst_name}:offset', value=offset)


def set_timeout(eventlog: Eventlog):
    entry_name = eventlog.selector.entry_name
    inst_name = eventlog.selector.inst_name
    queue_name = f'{OCTOPUS}:{entry_name}:{inst_name}:timeout_queue'
    dict_name = f'{OCTOPUS}:{entry_name}:{inst_name}:timeout_dict'
    redis.hset(name=dict_name, key=eventlog.event_id, value=eventlog.to_json())
    redis.zadd(queue_name, {eventlog.event_id: cur_ts_sec()})


def remove_timeout(eventlog: Eventlog):
    entry_name = eventlog.selector.entry_name
    inst_name = eventlog.selector.inst_name
    queue_name = f'{OCTOPUS}:{entry_name}:{inst_name}:timeout_queue'
    dict_name = f'{OCTOPUS}:{entry_name}:{inst_name}:timeout_dict'
    redis.zrem(queue_name, eventlog.event_id)
    redis.hdel(dict_name, eventlog.event_id)


def get_many_timeout(entry_name, inst_name, timeout_sec: int) -> Generator[str, None, None]:
    queue_name = f'{OCTOPUS}:{entry_name}:{inst_name}:timeout_queue'
    dict_name = f'{OCTOPUS}:{entry_name}:{inst_name}:timeout_dict'
    for s in redis.zrangebyscore(
            name=queue_name,
            min=0,
            max=cur_ts_sec() - timeout_sec,
    ):
        event_id = s
        eventlog_str = redis.hget(dict_name, event_id)
        if eventlog_str is None:
            logger.warning(f'no eventlog for {event_id}')
            continue
        redis.zrem(queue_name, event_id)
        redis.hdel(dict_name, event_id)
        yield eventlog_str
