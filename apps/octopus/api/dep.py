# -*- coding: utf-8 -*-

import logging

logger = logging.getLogger(__name__)


class CustomResponse(object):
    def __init__(self, param_dict=None):
        self.param_dict = param_dict
        self.code = 0
        self.message = 'success'
        self.data = {}

    def set_data(self, key, value):
        self.data[key] = value

    def get_data(self, key):
        return self.data.get(key, None)

    def set_code_message(self, code, message):
        self.code = code
        self.message = message

    def dict(self) -> dict:
        d = {
            'code': self.code,
            'message': self.message,
            'data': self.data,
        }
        logger.info('param_dict=%s ret=%s', self.param_dict, d)
        return d
