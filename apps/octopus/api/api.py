# -*- coding: utf-8 -*-

from typing import Dict
import logging
from datetime import datetime
from apps.octopus.entries.entry_dao import EntryDao
from apps.octopus.core.eventlog_store import EventlogStore
from apps.octopus.utils.conf_manager import OctopusConfManager
logger = logging.getLogger(__name__)


class OctopusAPI(object):
    def __init__(self):
        # self.item_dao_cache = ItemDaoCache(using_persistent_db=False, max_connections=4)
        self.eventlog_store = EventlogStore()
        self.conf_manager = OctopusConfManager(reload_conf_interval_sec=5)
        super().__init__()

    def query_user_input(self, user_input: str, show_all=False, only_gsxt=False) -> dict:
        result = dict()
        for (entry_name, inst_name), conf in self.conf_manager.conf_dict.items():
            entry_dao: EntryDao = conf.entry_dao.obj
            try:
                if entry_dao.entity_class.valid_word(user_input):
                    word = user_input
                    break
            except:
                pass
        else:
            result['alert'] = f'{user_input}没有对应的entry'
            return result
        eventlog_dict: Dict[str, dict] = {}
        for meta, encoded_filename in self.eventlog_store.read(entry_name, word):
            eventlog_row = {
                'ts': meta.ts,
                'octopus_time': datetime.fromtimestamp(meta.ts).strftime('%Y-%m-%d %H:%M:%S'),
                'entry_name': entry_name,
                'word': word,
                'inst_name': meta.inst_name,
                'status': meta.status.human(),
                'status_raw': meta.status,
                'code': meta.code.human(),
                'reason': meta.reason,
                'changed': '是' if meta.changed else '否',
                'cost': str(meta.cost),
                'weight': str(meta.weight),
                'try_id': str(meta.try_id),
                'link': encoded_filename,
            }
            if meta.event_id not in eventlog_dict:
                eventlog_dict[meta.event_id] = eventlog_row
            elif eventlog_dict[meta.event_id]['status_raw'] < eventlog_row['status_raw']:
                eventlog_dict[meta.event_id] = eventlog_row
        result['eventlog_list'] = list(sorted(eventlog_dict.values(), key=lambda x: x['ts'], reverse=True))
        return result

    def query_record_detail(self, user_input: str) -> str:
        return self.eventlog_store.read_content(encoded_file_info=user_input)
