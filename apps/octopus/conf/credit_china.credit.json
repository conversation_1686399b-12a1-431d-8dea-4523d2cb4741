{"exp_send_ratio": 0.2, "inst_name": "credit_china", "entry_dao": {"clazz": "EntryCreditDao", "code2_prefix": "", "batch_size": 4000, "word_pat": ".*", "max_try": 3, "has_clue": false, "args": {"check_branch": true, "check_status": true, "check_geti": true, "important_interval_days": 3}}, "sender": {"clazz": "RedisZsetSender", "sender_queue_size": 4000, "spider_timeout_sec": 3600, "sender_threads": 8, "min_success_ratio": 0.1, "warning_ats": ["金炜"], "args": {"redis_name": "redis.tyc.zhuan", "redis_db": 5, "redis_key": "searchCompany_licenseAndPunish_queue1"}}}