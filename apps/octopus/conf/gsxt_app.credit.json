{"exp_send_ratio": 0.3, "inst_name": "gsxt_app", "entry_dao": {"clazz": "EntryCreditGsxtAppDao", "code2_prefix": "", "word_pat": "9.*", "batch_size": 4000, "max_try": 3, "has_clue": false, "args": {"check_branch": true, "check_status": true, "check_geti": true, "important_interval_days": 365}}, "sender": {"clazz": "RedisZsetSender", "sender_queue_size": 500, "spider_timeout_sec": 3600, "sender_threads": 8, "args": {"redis_name": "redis.tyc.gs", "redis_db": 9, "redis_key": "gsxt_app"}}}