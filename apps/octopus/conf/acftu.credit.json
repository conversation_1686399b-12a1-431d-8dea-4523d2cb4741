{"exp_send_ratio": 0.1, "max_rest_sec": 20, "inst_name": "acftu", "entry_dao": {"clazz": "EntryCreditACFTUDao", "code2_prefix": "", "word_pat": "(81|89).{16}", "batch_size": 2000, "max_try": 3, "has_clue": true}, "sender": {"clazz": "RedisZsetSender", "sender_queue_size": 50, "spider_timeout_sec": 600, "sender_threads": 1, "sender_hour_range": "0 24", "send_per_minute": 5, "args": {"redis_name": "redis.tyc.gs", "redis_db": 9, "redis_key": "acftu"}}}