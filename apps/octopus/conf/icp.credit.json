{"exp_send_ratio": 0.3, "inst_name": "icp", "entry_dao": {"clazz": "EntryCreditDao", "code2_prefix": "", "word_pat": "9.*", "batch_size": 1000, "max_try": 3, "has_clue": false, "args": {"check_branch": true, "check_status": true, "check_geti": true, "important_interval_days": 15}}, "sender": {"clazz": "RedisZsetSender", "sender_queue_size": 100, "spider_timeout_sec": 3600, "sender_threads": 2, "min_success_ratio": 0.1, "warning_ats": ["张鑫明"], "args": {"redis_name": "redis.tyc.zhuan", "redis_db": 8, "redis_key": "searchCompany_license_queue5"}}}