{"exp_send_ratio": 0.4, "inst_name": "abn_sh", "entry_dao": {"clazz": "EntryCreditDao", "code2_prefix": "91031", "word_pat": "9.*", "batch_size": 1000, "max_try": 3, "has_clue": false, "args": {"check_branch": true, "check_status": true, "check_geti": true, "important_interval_days": 30}}, "sender": {"clazz": "RedisZsetSender", "sender_queue_size": 1000, "spider_timeout_sec": 0, "send_per_minute": 300, "sender_threads": 2, "args": {"redis_name": "redis.tyc.zhuan", "redis_db": 0, "redis_key": "abnormal:queue:sh"}}}