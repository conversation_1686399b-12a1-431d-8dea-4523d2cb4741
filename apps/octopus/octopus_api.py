# -*- coding: utf-8 -*-

from flask import Flask, request, render_template
from apps.octopus.api.api import OctopusAPI

app = Flask('octopus')

octopus_api = OctopusAPI()


@app.route('/octopus', methods=['GET'])
def query():
    return render_template('octopus.html')


@app.route('/octopus/<user_input>', methods=['GET'])
def query_user_input(user_input):
    show_all = request.args.get('show_all', False)
    if not isinstance(show_all, bool):
        if show_all == '0':
            show_all = False
        elif show_all == 'true':
            show_all = True
        else:
            show_all = False
    only_gsxt = request.args.get('only_gsxt', False)
    if not isinstance(only_gsxt, bool):
        if only_gsxt == '0':
            only_gsxt = False
        elif only_gsxt == 'true':
            only_gsxt = True
        else:
            only_gsxt = False
    return octopus_api.query_user_input(user_input, show_all, only_gsxt)


@app.route('/octopus/detail/<user_input>', methods=['GET'])
def query_record_detail(user_input):
    return octopus_api.query_record_detail(user_input)


if __name__ == '__main__':
    app.run()
