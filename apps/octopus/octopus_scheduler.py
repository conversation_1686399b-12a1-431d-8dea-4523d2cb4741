# encoding=utf8

import time
import argparse
from libs.dt import cur_ts_sec
from libs.log2 import setup_logger
from apps.octopus.entries import *
from apps.octopus.sender import *
from apps.octopus.utils.conf_manager import OctopusConfManager


class Main(object):  # only one instance
    def __init__(self, conf_name: str):
        self.conf_name = conf_name
        self.octopus_conf_manager = OctopusConfManager(
            reload_conf_interval_sec=5,
            json_conf_name=conf_name,
        )

    def run(self):
        # 单线程   infinite loop
        rest_sec = 5.0
        for loop_id in range(cur_ts_sec(), int(1e20)):
            conf = self.octopus_conf_manager.get_exact_one()
            if not conf:
                logger.warning('no octopus_conf, sleep for...')
                time.sleep(5.0)
                continue

            entry_dao_obj: EntryDao = conf.entry_dao.obj
            sender_obj: Sender = conf.sender.obj

            entries_realtime = entry_dao_obj.select_realtime(conf.inst_name)
            entries_normal = entry_dao_obj.select_normal(conf.inst_name)
            logger.info(f'loop_id={loop_id}  normal={len(entries_normal)} realtime={len(entries_realtime)}')

            items = entries_realtime + entries_normal

            weighted_items = []
            for item in items:
                weight, reason = entry_dao_obj.weight(item, conf.inst_name)
                if weight > 0:
                    weighted_items.append((weight, reason, item))
            weighted_items.sort(key=lambda x: x[0], reverse=True)
            select_info = '#'.join('{}={}'.format(x[2].word, x[0]) for x in weighted_items)
            logger.info(f'select_info {select_info}')
            select_count = len(weighted_items)

            queue_size_ratio, send_count = sender_obj.send(weighted_items)
            logger.info(f'loop_id={loop_id} send {len(items)}->{select_count}->{send_count} queue_size_ratio={queue_size_ratio}')

            send_ratio = send_count / select_count if select_count > 0 else 0
            if queue_size_ratio < 0.2:  # 队列饥饿
                if sender_obj.send_per_minute == 0:
                    rest_sec = max(rest_sec * 0.8, .8)
            elif queue_size_ratio > 0.95:  # 队列阻塞
                rest_sec = min(rest_sec * 1.2, conf.max_rest_sec)
            elif send_ratio < conf.exp_send_ratio:  # 扔词率太低
                if sender_obj.send_per_minute == 0:
                    rest_sec = min(rest_sec * 1.2, conf.max_rest_sec)
            elif send_ratio > conf.exp_send_ratio:  # 扔词率太高
                rest_sec = max(rest_sec * 0.8, .8)

            logger.info(f'loop_id={loop_id} {self.conf_name} rest={rest_sec:.1f} ratio={send_ratio:.3f}')

            if rest_sec > 1.0:
                time.sleep(rest_sec)


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='统一调度——调度器')
    ap.add_argument('--conf-name', type=str, default='foundation.credit.json')
    ap.add_argument('--backup-days', type=int, default=1, help='日志保存天数')
    ap_args = ap.parse_args()
    app_name = '.'.join(['octopus_scheduler', ap_args.conf_name])
    logger = setup_logger(use_file_log=True, app_name=app_name, backup_count=ap_args.backup_days, rotate_mode='D')
    Main(ap_args.conf_name).run()
