# encoding=utf8
import logging
from typing import Tuple
from libs.env import ConstantProps
from dao.company import CompanyDao, Company, MySQLDao
from apps.octopus.core.constants import DEFAULT_REASON
from apps.octopus.entries.entry import Entry
from apps.octopus.entries.credit import EntryCreditDao
from apps.octopus.core.conf import EntryDaoConf

logger = logging.getLogger(__file__)
company_dao = CompanyDao()


# "name":"成都威尔斯基文化传媒有限公司","establish_date":"2018-04-16","cid":*********,"status":"存续（在营、开业、在册）","reg_number":"510104000545510"
# 搜索无结果的 不用再做
# 分支机构的不用调度， 非公司的不用调度


class EntryCreditGDSDao(EntryCreditDao):
    def __init__(self, conf: EntryDaoConf):
        self.company_dao = CompanyDao()
        self.gds_firm_code_dao = MySQLDao(
            **ConstantProps.PROPS_GS_INNER_RW,
            db_tb_name='prism.gds_firm_code',
            pk_name='id',
            entity_class=None,
        )

        super().__init__(conf=conf)

    def weight(self, entry: Entry, inst_name: str) -> Tuple[int, str]:
        weight, reason = super().weight(entry, inst_name)
        if weight == 0:
            return weight, reason
        inst = entry.get_inst(inst=inst_name, init=True)

        cid = entry.info.get('cid', None)
        if not isinstance(cid, int):
            return 0, ''
        c: Company = self.company_dao.get(cid)
        if not c:
            return 0, ''

        if reason == DEFAULT_REASON:
            if inst.last_empty_ts > 0:
                return 0, reason
            if '分' in (c.company_org_type or ''):
                return 0, ''
            if '销' in (c.reg_status or ''):
                return 0, ''

        o1 = self.gds_firm_code_dao.get(value=c.name, field='firm_name')
        o2 = self.gds_firm_code_dao.get(value=entry.word, field='certificate_code')
        if not o1 and not o2:
            return 0, ''

        return weight, reason


if __name__ == '__main__':
    from libs.log2 import setup_logger
    from apps.octopus.utils.conf_manager import OctopusConfManager

    logger = setup_logger()
    manager = OctopusConfManager(json_conf_name='gds.credit.json', reload_conf_interval_sec=0)
    octopus_conf = manager.get_by_name(entry_name='credit', inst_name='gds')
    entry_dao: EntryCreditGDSDao = octopus_conf.entry_dao.obj
    o = entry_dao.get_by_word('91330303552851372F')
    logger.info(f'{o.word} weight={entry_dao.weight(o, "gds")}')
