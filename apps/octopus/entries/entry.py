# encoding=utf8

import abc
import json
from typing import Dict, Optional
import logging
from datetime import datetime
from pydantic import Field, constr, PrivateAttr
from entity.deps.entity import BaseEntity
from libs.dt import cur_ts_sec
from apps.octopus.core.constants import EntryFlag, CODE_LEN, CODE_PREFIX_LEN, OCTOPUS
from apps.octopus.core.inst import Inst
from apps.octopus.core.eventlog import Eventlog

logger = logging.getLogger(__name__)


# entry 基本调度单元表
class Entry(BaseEntity):
    id: int = Field(default=0)  # 0用于外部数据插入 自增主键
    word: str  # 调度单元唯一标识 可以是搜索词，对于company表 可以是cid 具有唯一性
    info: dict = Field(default={})  # json 用于cache调度单元其他业务信息，也可以从业务表获取info信息
    flag: EntryFlag = Field(default=EntryFlag.CLUE)  # 用于标记有效性 无索引
    realtime: int = Field(default=0)  # 时效性标记 0表示无时效性  其他标记为对应的业务 1表示多个业务标记 10以上表示对应业务
    inst_map: Dict[str, Inst] = Field(default={})  # 实例调度信息
    code1: constr(min_length=CODE_LEN, max_length=CODE_LEN)  # 索引因子1 用于调度遍历 唯一性
    code2: constr(min_length=CODE_LEN+CODE_PREFIX_LEN, max_length=CODE_LEN+CODE_PREFIX_LEN)  # 索引因子2 用于调度遍历
    create_time: datetime = Field(default_factory=datetime.now)  # 创建时间 数据库自动写入
    update_time: datetime = Field(default_factory=datetime.now)  # 更新时间 数据库自动写入
    _changed: bool = PrivateAttr(default=False)  # 暂时未使用到

    def __init__(self, **kwargs):
        inst_map = kwargs.get('inst_map', None) or '{}'
        if isinstance(inst_map, str):
            kwargs['inst_map'] = json.loads(inst_map)
        info = kwargs.get('info', None) or '{}'
        if isinstance(info, str):
            kwargs['info'] = json.loads(info)
        word: str = kwargs.get('word', '')
        if not self.valid_word(word):
            raise ValueError('bad word=' + word)
        super().__init__(**kwargs)

    def set_info(self, k, v, overwrite=True):
        if k not in self.info or (overwrite and self.info[k] != v):
            self.info[k] = v
            self._changed = True

    def get_inst(self, inst, init=False) -> Optional[Inst]:
        if inst in self.inst_map:
            return self.inst_map[inst]
        if init:
            self.inst_map[inst] = Inst.from_dict({})
            return self.inst_map[inst]
        return None

    @staticmethod
    def valid_word(word) -> bool:
        return True

    @staticmethod
    def get_code2(word, info) -> str:
        return '000000'

    def reload_info(self, ) -> bool:
        """
        更新info信息 会导致选取性能变差，默认不更新 返回信息是否被更新 外部逻辑是每间隔时间N更新一次
        :return:
        """
        pass

    def refresh_realtime(self, force_realtime_0=False) -> bool:
        """
        更新realtime
        :return:
        """
        realtime = 0
        if not force_realtime_0:
            for inst_name, inst in (self.inst_map or {}).items():
                # if inst.try_id > 0 or inst.reason != '':
                if inst.reason != '':
                    if realtime == 0:
                        realtime = self.get_inst_id(inst_name)
                    else:
                        realtime = 1

        if self.realtime != realtime:
            self.realtime = realtime
            return True
        return False

    @staticmethod
    @abc.abstractmethod
    def get_inst_id(inst_name):
        """
        返回inst对应的id 需要返回>=10的id
        :param inst_name:
        :return:
        """
        pass

    @classmethod
    def get_name(cls):
        return cls.__name__.lower()

    def make_eventlog(self, inst_name: str, weight: int, reason: str) -> Eventlog:
        inst = self.get_inst(inst=inst_name, init=False)
        d = dict(
            event_id=f'{OCTOPUS}-{self.get_name()}-{inst_name}-{self.word}-{cur_ts_sec()}',
            selector=dict(
                send_ts=cur_ts_sec(),
                reason=reason,
                clue=(self.flag == EntryFlag.CLUE),
                entry_name=self.get_name(),
                inst_name=inst_name,
                word=self.word,
                info=self.info,
                try_id=0 if not inst else inst.try_id,
                meta={} if not inst else inst.meta,
                weight=weight,
            ),
            spider={},
        )
        return Eventlog.from_dict(d)


# class EntryInstSchedulerInfo(object):
#     def __init__(self, entry: Entry, inst_name: str, inst: Inst):
#         self.entry: Entry = entry
#         self.inst_name = inst_name
#         self.inst: Inst = inst
#         self.weight: int = 0
#         self.reason: str = ''
#

if __name__ == '__main__':
    from libs.log import setup_logger
    logger = setup_logger()
    logger.info(Entry.get_name())
