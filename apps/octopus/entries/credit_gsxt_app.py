# encoding=utf8
import logging
from datetime import timed<PERSON>ta, datetime
from typing import <PERSON>ple
from libs.dt import cur_ts_sec
from dao.company_supplement import CompanySupplementDao, CompanySupplement
from dao.reports.annual_report import AnnualReportDao
from dao.company import CompanyDao, Company
from apps.octopus.entries.entry import Entry
from apps.octopus.core.inst import Inst
from apps.octopus.entries.credit import EntryCreditDao
from apps.octopus.core.conf import EntryDaoConf

logger = logging.getLogger(__file__)
company_dao = CompanyDao()


class EntryCreditGsxtAppDao(EntryCreditDao):
    def __init__(self, conf: EntryDaoConf):
        self.comp_sup_dao = CompanySupplementDao()
        self.report_dao = AnnualReportDao()
        self.company_dao = CompanyDao()
        super().__init__(conf=conf)

    def weight(self, entry: Entry, inst_name: str) -> Tuple[int, str]:
        w, reason = super().weight(entry, inst_name)
        cid = entry.info.get('cid', None)

        if w >= 900:
            return w, reason

        if not entry.word.startswith('91'):
            return 0, ''
        if entry.word[:6] in ['914401', '914403']:
            return 0, ''

        # province_list = ['11', '31', '32', '37', '41', '44']
        # city_list = ['3301', '2102', '3401', '5101', '6501']
        # if entry.word[2:4] not in province_list and entry.word[2:6] not in city_list:
        #     return 0, ''

        if not isinstance(cid, int):
            return 0, ''

        c: Company = self.company_dao.get(cid)
        if not c:
            return 0, ''
        if c.last_crawled_time and c.last_crawled_time + timedelta(days=3) > datetime.now():
            w = max(100, w - 300)
        if not (c.name or '').endswith('公司') and '合伙' not in (c.name or ''):
            return 0, ''
        if c.company_org_type and '分' in c.company_org_type:
            return 0, ''
        if not c.reg_status or '销' in c.reg_status:
            return 0, ''
        inst: Inst = entry.get_inst(inst=inst_name, init=True)
        detail_ts = inst.inst_info.get('detail_ts', 0)
        if detail_ts + 86000 > cur_ts_sec():
            # w += 5
            w = 800

        # o: CompanySupplement = self.comp_sup_dao.get(value=cid, field='company_id')
        #
        # if not o or o.company_org_code == '' or o.industry_code == '':
        #     w = min(w + 50, 800)
        #     reason = 'new_org_code'

        report = self.report_dao.get_ex(values=[cid, 2023], fields=['company_id', 'report_year'])
        if not report:
            w = min(w + 200, 800)
            reason = 'chk_new_report_2023'

        return w, reason
