# encoding=utf8
import logging
from typing import Tuple
from libs.dt import to_date, date2str, cur_ts_sec
from gslib.id_center import id_center_query, EntityType
from gslib.credit_code import credit_code_valid, credit_code_incr
from dao.company import Company, CompanyDao, CompanyGraph, CompanyGraphDao
from dao.investors.equity_ratio import EquityRatioDao, EquityRatio
from apps.octopus.entries.entry_dao import EntryDao
from apps.octopus.entries.entry import Entry, EntryFlag
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.octopus.core.conf import EntryDaoConf
from apps.octopus.core.constants import PLATFORM_REASON, PROVINCE_CODES, CITY_CODES
from gslib.id_center import id_center_query
logger = logging.getLogger(__file__)

company_dao = CompanyDao()
company_graph_dao = CompanyGraphDao()
equity_ratio_dao = EquityRatioDao()


class Credit(Entry):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @staticmethod
    def valid_word(word) -> bool:
        return credit_code_valid(word)

    @staticmethod
    def get_code2(word, info: dict) -> str:
        # 根据info信息设置 返回 6位字符串
        # EG：工商公司非销=000  全体公司

        code2 = ['0'] * 6

        if not credit_code_valid(word):
            return ''.join(code2)

        # 1-2：实体类型 信用代码前两位
        code2[:2] = word[:2]

        # 3：实体状态  正常0  不正常（倾向不再调度）1
        reg_status = info.get('status', '')
        if reg_status in ['注销', '吊销，已注销', '该单位已注销', '撤销', '已撤销', '已注销']:
            code2[2] = '1'  # 约占工商数据40% 足够，不再枚举
        else:
            code2[2] = '0'

        # 4-5：省份编码 10作为国家级  最合理应结合登记机关判断， 简单来可以直接用代码判断 # 国家是10
        code2[3:5] = word[2:4]

        # 6: 暂时不使用
        pass

        return ''.join(code2)

    @staticmethod
    def get_inst_id(inst_name):
        inst_name_2_id = {
            'china_npo': 11,
            'cods': 12,
            'gds': 13,
            'foundation': 14,
            'credit_china': 15,
            'tax_owe_announce': 16,
            'acftu': 17,
            'china_charity': 18,
            'gsxt_app': 19,
            'icp': 21,
            'institution_gj': 22,
            'law_firm_gj': 23,
        }
        if inst_name not in inst_name_2_id:
            logger.warning('当前调度使用默认的inst_id=10 !!!')
        return inst_name_2_id.get(inst_name, 10)

    def reload_info(self, ) -> bool:
        """
        更新info信息 会导致选取性能变差，默认不更新 返回信息是否被更新 外部逻辑是每间隔时间N更新一次
        :return:
        """
        if 'establish_date' not in self.info:
            ent_type, cid = id_center_query(credit_no=self.word)
            if ent_type == EntityType.ORG:
                company: Company = company_dao.get(value=cid)
                if company:
                    self.set_info(k='cid', v=company.cid)
                    self.set_info(k='name', v=company.name)
                    self.set_info(k='establish_date', v=date2str(to_date(company.establish_date)))
                    self.set_info(k='status', v=company.reg_status)
                    self.code2 = self.get_code2(self.word, self.info) + self.code1
                    return True
        return False


class EntryCreditDao(EntryDao):
    STEP_MAP = {
        "MB080": {'12130635': 1, '12420000': 1, '12620100': 1, '12411700': 1, '12430423': 1, '12411328': 1,
                  '12420602': 1, '12130126': 3, '12652328': 2, '12360922': 1, '12510107': 1, '12420702': 2,
                  '12451223': 1, '12451029': 1, '12210113': 1, '12410823': 2, '12220283': 1, '12341124': 1,
                  '12513432': 1, '12430523': 1, '12620422': 1, '12141029': 1, '12450722': 1, '12211303': 1,
                  '12411526': 3, '12150100': 1, '12411426': 7, '12410308': 1, '12421100': 1, '12450924': 1,
                  '12150600': 2, '12210106': 4, '12140927': 1, '12420582': 1, '12510821': 1, '12411200': 1,
                  '12140521': 1, '12340000': 1, '12410323': 2, '12210504': 1, '12420100': 1, '12420222': 1,
                  '12620522': 1, '12622900': 1, '12410311': 2, '12410622': 1, '12130638': 5, '12360925': 1,
                  '12350124': 1, '12130684': 1, '12530128': 1, '12421200': 1},
        "MB083": {'12652722': 1, '12650000': 2, '12411122': 3, '12350123': 1, '12532801': 1, '12431181': 1,
                  '12530300': 1, '12411328': 1, '12410184': 1, '12510107': 1, '12410221': 1, '12130900': 1,
                  '12450000': 1, '12340000': 1, '12130582': 3, '12420381': 1, '12360922': 1, '12410823': 1,
                  '12210802': 2, '12530402': 1, '12511102': 1, '12653101': 1, '12422800': 1, '12360124': 1,
                  '12140927': 2, '12220106': 1, '12620105': 1, '12620422': 1, '12411426': 3, '12533100': 1,
                  '12451021': 1, '12533300': 1, '12350681': 1, '12430726': 1, '12421224': 2, '12421100': 1,
                  '12410621': 2, '12360981': 1, '12210681': 1, '12210283': 1, '12621000': 1, '12410323': 1,
                  '12220521': 1, '12450400': 1, '12410622': 1, '12411503': 1},
        "MB087": {'12620826': 1, '12410522': 1, '12429006': 1, '12211381': 1, '12140400': 1, '12150100': 1,
                  '12510107': 1, '12211303': 1, '12653000': 1, '12530181': 1, '12411426': 16, '12450305': 1,
                  '12411121': 2, '12411724': 1, '12410622': 1, '12411300': 1, '12653121': 1, '12211321': 1,
                  '12652327': 1, '12350802': 1, '12360731': 1, '12411328': 1, '12511124': 1, '12450803': 1,
                  '12451121': 1, '12360981': 1, '12450924': 2, '12532527': 1, '12150429': 1, '12530128': 1,
                  '12410704': 2, '12220322': 1, '12220103': 1, '12513428': 1},
        "MB253": {'12370523': 1, '12371000': 1, '12370103': 2, '12370406': 2, '12370923': 2, '12370500': 1,
                  '12371424': 1, '12370105': 6, '12371482': 1, '12370503': 1, '12371427': 1, '12370403': 1,
                  '12371702': 2, '12371525': 2, '12370811': 18, '12370303': 1, '12371100': 2, '12370211': 3,
                  '12370300': 1, '12371724': 3, '12371700': 2, '12371082': 1, '12371428': 1, '12371083': 27,
                  '12371727': 1, '12370000': 2, '12371481': 1, '12371122': 1, '12370700': 1, '12371402': 1,
                  '12371626': 3, '12371321': 2, '12371602': 1, '12370683': 4, '12371621': 4, '12371500': 1},
        "MB2E4": {'12440511': 2, '12440883': 8, '12441502': 7, '12441623': 3, '12441200': 10, '12440983': 2,
                  '12445100': 5, '12440111': 2, '12440823': 2, '12441300': 16, '12440900': 2, '12440309': 4,
                  '12440300': 16, '12445102': 6, '12440118': 4, '12440233': 2, '12440106': 1, '12440904': 5,
                  '12440882': 6, '12441302': 2, '12441581': 3, '12445281': 1, '12440703': 2, '12441881': 2,
                  '12441882': 5, '12445224': 6, '12442000': 6, '12441625': 2, '12440117': 2, '12441622': 2,
                  '12440811': 2, '12441322': 9, '12441426': 2, '12441721': 3, '12440881': 13, '12440800': 4,
                  '12441602': 2, '12440100': 1, '12440304': 7, '12440705': 4, '12440785': 3, '12440000': 6,
                  '12441700': 6, '12440307': 5, '12445303': 3, '12440607': 1, '12441224': 4, '12445222': 2,
                  '12441427': 1, '12441803': 8, '12441900': 4, '12445321': 3, '12441324': 2, '12440114': 2,
                  '12440229': 2, '12440605': 5, '12441624': 2, '12440204': 1, '12440404': 2, '12441600': 2,
                  '12441284': 3, '12445122': 21, '12445302': 1, '12441500': 1, '12440203': 2, '12440825': 1,
                  '12441403': 1, '12440514': 3, '12441402': 1, '12440105': 1, '12440103': 1, '12441821': 1,
                  '12441225': 5, '12440981': 6, '12440700': 1, '12441323': 4, '12440281': 2, '12440403': 1,
                  '12440803': 1, '12445200': 3, '12440902': 3, '12441521': 2, '12440112': 4, '12441202': 1,
                  '12440704': 2, '12440606': 1, '12440402': 11, '12441802': 5, '12440400': 1, '12441800': 1,
                  '12440781': 2, '12441400': 1, '12440310': 3, '12445103': 16, '12440115': 3, '12445203': 1,
                  '12440113': 1, '12445322': 2, '12441823': 9, '12440104': 1, '12441203': 3, '12440604': 2,
                  '12441223': 1},
        "MD030": {'31230000': 1, '31310000': 3, '31520000': 7, '31370000': 2, '31320000': 4, '31440000': 5,
                  '31460000': 1, '31410000': 5, '31610000': 18, '31350000': 2, '31340000': 1, '31430000': 1,
                  '31110000': 3, '31530000': 2, '31130000': 1, '31360000': 2, '31510000': 1, '31450000': 1},
        "MD031": {'31530000': 17, '31440000': 26, '31310000': 7, '31230000': 3, '31370000': 16, '31360000': 3,
                  '31350000': 11, '31510000': 16, '31450000': 3, '31130000': 10, '31500000': 7, '31520000': 16,
                  '31410000': 17, '31340000': 6, '31110000': 16, '31150000': 6, '31460000': 1, '31220000': 4,
                  '31320000': 6, '31330000': 6, '31430000': 1, '31420000': 6, '31140000': 6, '31120000': 1,
                  '31210000': 4, '31620000': 1},
        "MD034": {'31440000': 87, '31410000': 51, '31650000': 17, '31540000': 3, '31120000': 11, '31370000': 17,
                  '31360000': 10, '31330000': 26, '31310000': 10, '31420000': 37, '31340000': 20, '31210000': 12,
                  '31510000': 23, '31150000': 9, '31500000': 12, '31450000': 19, '31530000': 16, '31140000': 11,
                  '31640000': 4, '31130000': 10, '31320000': 58, '31610000': 22, '31430000': 12, '31220000': 13,
                  '31520000': 32, '31230000': 9, '31350000': 17, '31110000': 9, '31620000': 11, '31630000': 6,
                  '31460000': 17},
        "MJ051": {'51120113': 8, '52120116': 6, '52120117': 8, '51120103': 6, '52120101': 12, '52120112': 3,
                  '51120115': 7, '51120119': 3, '52120119': 4, '52120118': 3, '52120114': 9, '51120101': 5,
                  '51120111': 5, '52120111': 3, '52120410': 1, '51120114': 3, '51120000': 16, '52120000': 2,
                  '51120117': 3, '51120110': 2, '52120102': 4, '52120115': 6, '52120110': 5, '51120116': 3,
                  '52120105': 2, '52120317': 2, '52120103': 2, '51120112': 2, '52120316': 2, '51120106': 1,
                  '52120113': 3, '51120118': 1, '52120106': 1, '51120102': 1},
        "MJ0B0": {'51511300': 4, '51511902': 1, '51640121': 1, '51469027': 1, '52411081': 1, '52410307': 1,
                  '51131100': 1, '51411081': 1, '52620825': 2, '52230602': 1, '52220202': 2, '51231102': 1,
                  '52130403': 2, '51410800': 2, '51411100': 1, '51511923': 1, '52130402': 1, '51410202': 2,
                  '51222404': 2, '51130500': 2, '51513430': 2, '51411623': 1, '51410727': 1, '51510525': 1,
                  '51130528': 2, '51513426': 1, '51411328': 3, '51130900': 2, '51220100': 1, '52411327': 1,
                  '52220173': 1, '51220721': 1, '51230602': 1, '52510321': 1, '51411523': 1, '51130804': 1,
                  '51640502': 1, '52230603': 1, '52130205': 1, '52410102': 1, '52130400': 2, '51620000': 3,
                  '52230302': 1, '51510300': 1, '52621221': 1, '51510302': 1, '51230624': 1, '51469029': 2,
                  '52510703': 4, '51130400': 1, '52410108': 2, '51411726': 2, '52230321': 1, '51410122': 1,
                  '51130408': 1, '52411600': 1, '51131023': 2, '51130871': 1, '51513233': 1, '51410100': 2,
                  '51640422': 1, '52131100': 1, '51411400': 1, '51411282': 1, '52130683': 1, '52220172': 1,
                  '52130123': 2, '51411425': 2, '52230100': 2, '52410973': 1, '51640302': 2, '51410900': 2,
                  '52410926': 1, '51411326': 1, '51640521': 2, '52230110': 1, '51411000': 2, '52620523': 1,
                  '51411322': 2, '52640104': 1, '51623025': 1, '52231282': 1, '51230113': 1, '51621100': 2,
                  '52460200': 1, '51410185': 1, '51230722': 1, '51510623': 1, '52130406': 1, '52220502': 1,
                  '51411323': 1, '51230421': 1, '51131122': 1, '51411600': 2, '52231202': 1, '51510000': 3,
                  '52410411': 2, '51130302': 1, '52131082': 3, '51411681': 2, '51222400': 1, '51411522': 1,
                  '51230100': 1, '52621226': 1, '51511523': 1, '52469007': 1, '52510700': 1, '51411103': 1,
                  '52220881': 1, '51130582': 1, '51220106': 1, '53510000': 1, '51640200': 1, '51220102': 1,
                  '51510703': 1, '51410182': 9, '52230522': 1, '51230000': 2, '52131103': 1, '52419001': 1,
                  '51511621': 1, '52510000': 1, '52130600': 1, '51411025': 1, '52130423': 1, '52620922': 1,
                  '52130108': 1, '52130181': 1, '51410422': 1, '52410711': 2, '51410222': 1, '51130225': 1,
                  '52410404': 1, '52130324': 1, '51410500': 1, '52469006': 1, '51513400': 1, '52513402': 1,
                  '51222401': 1, '51513436': 1, '52130110': 1, '51411300': 1, '52131003': 1, '52130408': 1,
                  '51620723': 1, '52130131': 1, '51460200': 2, '51410811': 1, '51620521': 1, '52510681': 1,
                  '51411625': 1, '51411521': 1, '51411424': 3, '52411627': 1, '52511304': 1, '51460100': 1},
        "MJ0B1": {'52621126': 2, '51640121': 1, '52220881': 2, '52620000': 2, '52511527': 1, '51621200': 3,
                  '52130636': 1, '52411303': 3, '52130771': 2, '51410300': 5, '51469029': 2, '51411328': 4,
                  '51411322': 1, '52410300': 2, '51411425': 2, '51410971': 1, '51620302': 1, '52130528': 2,
                  '52620700': 1, '51511702': 1, '51130125': 2, '51220102': 2, '52411423': 2, '52130606': 4,
                  '51130606': 1, '52410326': 1, '52410421': 2, '52510623': 6, '52230200': 2, '52231222': 1,
                  '51410182': 4, '52620302': 1, '51621000': 1, '52411722': 3, '52130684': 2, '51511825': 2,
                  '51640302': 2, '52130626': 3, '51130435': 1, '51410000': 8, '53410000': 2, '51410621': 1,
                  '52130432': 1, '52130600': 1, '51130823': 2, '52130408': 1, '51231002': 1, '51620802': 1,
                  '51130700': 8, '52220000': 5, '51130726': 2, '52511921': 1, '52220821': 12, '52130922': 1,
                  '51469027': 2, '51511133': 1, '52410223': 1, '52130703': 2, '52130425': 3, '52230506': 1,
                  '51130525': 1, '52130531': 3, '51511526': 3, '51410928': 2, '51230804': 1, '51511781': 1,
                  '51411327': 1, '52510704': 2, '51469022': 1, '52460106': 2, '51511722': 2, '52411003': 1,
                  '51411003': 1, '52130100': 2, '51411625': 5, '51460000': 7, '51410305': 3, '51411426': 2,
                  '52411624': 1, '51511500': 1, '52640302': 1, '51410422': 4, '52410100': 9, '52621222': 1,
                  '52410972': 2, '52130803': 2, '52220300': 1, '51511300': 2, '51130100': 3, '52511303': 1,
                  '52231281': 2, '52511700': 1, '51130283': 2, '52220113': 2, '52640122': 1, '52411603': 3,
                  '51230300': 1, '51230113': 1, '51513427': 2, '51410104': 1, '51511323': 1, '52131002': 3,
                  '51131022': 1, '52230202': 1, '51411421': 1, '51513402': 2, '52220100': 4, '51411622': 1,
                  '51411326': 1, '51460107': 2, '52220605': 1, '52130427': 3, '52230523': 1, '51511903': 1,
                  '52130607': 1, '52131022': 1, '51130404': 1, '52130404': 3, '52220781': 1, '52130823': 1,
                  '51510504': 4, '52130722': 1, '51410421': 1, '51469001': 2, '51220800': 1, '52411300': 2,
                  '52511800': 2, '52130435': 1, '53640000': 1, '51511324': 1, '51130683': 1, '51130900': 3,
                  '51222401': 2, '52411323': 2, '52230110': 2, '52130302': 5, '51510683': 3, '51220322': 1,
                  '52410621': 3, '51469002': 1, '51469007': 3, '51460400': 1, '52410711': 1, '51410100': 7,
                  '52130921': 1, '52131122': 2, '52130131': 1, '51411025': 2, '51410103': 3, '52469028': 1,
                  '52410927': 1, '51411624': 2, '51411623': 3, '51510302': 1, '51469028': 1, '52130223': 1,
                  '51410900': 1, '51130424': 3, '51133100': 2, '51621226': 1, '52410522': 1, '51230200': 2,
                  '52220602': 1, '51511302': 1, '52130984': 2, '51230110': 1, '52230602': 2, '52511902': 3,
                  '51622900': 2, '51621122': 2, '51620621': 1, '52220882': 1, '52222426': 1, '51130130': 2,
                  '52410811': 1, '52411503': 1, '52131003': 5, '51410482': 2, '52469005': 1, '52131127': 2,
                  '51510704': 1, '52220203': 3, '52130923': 1, '52130629': 1, '51130630': 1, '51511826': 2,
                  '51130302': 2, '52220702': 1, '51623027': 1, '51620825': 1, '51623024': 1, '52220800': 2,
                  '52130826': 1, '51130800': 2, '51131121': 1, '52510000': 2, '52131121': 1, '52640500': 2,
                  '51130109': 1, '51411200': 2, '51621123': 1, '52130730': 1, '51410800': 1, '51130110': 1,
                  '51220183': 2, '52510525': 1, '51510922': 1, '52511302': 2, '52231221': 1, '52411328': 2,
                  '51230221': 1, '51230000': 5, '51130921': 1, '52230828': 2, '51131100': 2, '51411521': 3,
                  '51130703': 2, '52130726': 1, '51411323': 1, '52220700': 1, '51230500': 1, '51460100': 4,
                  '52230803': 1, '51410926': 1, '51411522': 1, '51410311': 1, '52130104': 1, '52410172': 1,
                  '52469002': 3, '52469024': 1, '51411503': 1, '51627100': 1, '52511502': 4, '52230302': 2,
                  '51220100': 3, '51131002': 2, '51131023': 1, '52410926': 5, '51512002': 1, '51411727': 1,
                  '51640500': 1, '51131126': 1, '51511028': 3, '52411473': 1, '52230811': 2, '52410202': 1,
                  '52411381': 1, '51410423': 1, '52411702': 1, '51621100': 2, '52230108': 1, '53130000': 2,
                  '52620725': 1, '52130000': 1, '52411627': 1, '51130984': 1, '52460100': 1, '51511400': 1,
                  '52511400': 1, '51460200': 3, '52130132': 1, '51511403': 2, '52511381': 1, '51513230': 1,
                  '51622922': 1, '51131128': 1, '51640522': 1, '51130981': 1, '51621224': 1, '51620722': 1,
                  '52231200': 1, '52620500': 3, '51230523': 1, '52131125': 1, '52220322': 1, '52410104': 1,
                  '51511321': 1, '51622923': 1, '51511523': 1, '53510000': 1, '52130102': 3, '51511725': 1,
                  '51640105': 1, '51513327': 1, '52510604': 1, '52410505': 2, '52130982': 1, '51130533': 1,
                  '51411726': 2, '51131025': 1, '52130609': 3, '51410702': 1, '52460203': 2, '52220200': 1,
                  '51640221': 3, '51130406': 1, '51620200': 1, '52130281': 1, '51621026': 1, '51130200': 1,
                  '51411100': 2, '52411625': 2, '52130683': 1, '52130203': 1, '52220191': 2, '52640100': 2,
                  '52410902': 3, '51510811': 1, '52510726': 1, '52620922': 1, '52130900': 1, '51220000': 7,
                  '51510304': 2, '52511702': 1, '52411525': 1, '51511025': 2, '51220622': 2, '52230109': 2,
                  '52460000': 3, '52410973': 1, '52130671': 1, '52510603': 1, '52640202': 2, '52130200': 1,
                  '51411300': 4, '52411623': 2, '51460202': 1, '52411600': 1, '51131000': 3, '51411203': 1,
                  '51130725': 1, '52230204': 1, '51410222': 5, '52410882': 2, '51230521': 1, '52230882': 1,
                  '51511681': 2, '52130582': 2, '52130828': 1, '51410882': 2, '52131124': 1, '52410225': 1,
                  '51410308': 1, '52131000': 1, '52131028': 1, '51130731': 1, '52640400': 1, '52510322': 1,
                  '52511324': 1, '52411081': 1, '52130202': 1, '51130131': 3, '52460200': 2, '52130700': 1,
                  '51513431': 1, '51130400': 2, '52621202': 1, '51410403': 2, '52220303': 1, '51510322': 1,
                  '52411502': 1, '52130402': 2, '51411627': 1, '51620700': 2, '52220171': 2, '52130526': 2,
                  '52410173': 3, '51130532': 1, '51511123': 2, '51513424': 1, '52130126': 2, '51510521': 2,
                  '52130403': 1, '51510000': 2, '51130709': 1, '52411426': 2, '51130500': 1, '52130111': 1,
                  '52410482': 3, '52130204': 1, '52410782': 1, '51411082': 1, '51230400': 2, '52411725': 1,
                  '52220500': 1, '51411000': 1, '51130129': 2, '52622925': 1, '52411000': 2, '52130533': 4,
                  '51640106': 2, '51621021': 1, '51469026': 1, '52131182': 1, '51410902': 1, '52510703': 1,
                  '52410423': 2, '51130600': 1, '51620000': 1, '52130632': 1, '52230723': 1, '51231181': 1,
                  '52131102': 2, '51411321': 1, '51410402': 3, '52410185': 2, '52410402': 1, '52130300': 2,
                  '51411221': 1, '51220200': 2, '51640425': 1, '51231121': 1, '52410182': 1, '51220113': 2,
                  '52220421': 3, '51410185': 1, '51411526': 1, '52511600': 1, '51511600': 1, '51511024': 1,
                  '51130181': 1, '51130274': 1, '51510781': 1, '52130983': 1, '52131082': 1, '52411403': 1,
                  '52640324': 1, '52130628': 1, '51411725': 1, '52411002': 1, '52230881': 2, '52410600': 3,
                  '52469021': 1, '51230281': 1, '52513224': 1, '51231085': 1, '52130426': 1, '51130322': 1,
                  '51640202': 1, '52130322': 1, '52410800': 1, '52511825': 1, '51510681': 1, '52410923': 1,
                  '52469006': 1, '51130426': 1, '52411527': 1, '52511402': 1, '52220323': 1, '51131125': 1,
                  '51230104': 1, '52411025': 1, '51620402': 1, '51510921': 1, '51131024': 1, '52410422': 1,
                  '51460203': 2, '52621200': 1, '51130632': 1, '52130108': 1, '52130633': 4, '51411325': 1,
                  '52220802': 1, '52230112': 1, '51131028': 2, '51230622': 1, '51411700': 1, '51130223': 1,
                  '52231181': 1, '52230624': 1, '52410527': 1, '52620602': 1, '52410329': 1, '51230604': 1,
                  '52130324': 1, '52411224': 1, '51511525': 1, '51620800': 1, '51230109': 1, '51410303': 1,
                  '51621024': 1, '52220190': 1, '51131102': 1, '52410102': 1, '52510502': 2, '51640122': 1,
                  '52220122': 1, '51220822': 1, '51222406': 1, '51511800': 1, '51510600': 1, '51511126': 1,
                  '51511325': 1, '51220381': 1, '51510903': 4, '51411600': 1, '52511703': 1, '51622924': 1,
                  '51640000': 2, '51130608': 1, '51513400': 1, '51130184': 1, '51130425': 2, '51511423': 1,
                  '52419001': 1, '52220103': 1, '51220106': 1, '51622925': 1, '51512000': 1, '51511502': 1,
                  '51130682': 1, '51469023': 1, '51460106': 1, '52511423': 1, '52130129': 1, '51130133': 2,
                  '52410823': 1, '51469021': 1, '51130300': 1, '52411726': 1, '52622924': 1, '51510502': 1,
                  '51469024': 2, '52130681': 1, '51130681': 2, '51130102': 1, '51130638': 1, '51510524': 1,
                  '52510311': 1, '52130532': 1, '51220881': 1, '51510603': 1, '51511724': 1, '51230805': 1,
                  '51231000': 1, '51411681': 1, '52410183': 1, '51411400': 1, '51130627': 1, '51130636': 1,
                  '51130535': 1, '52411681': 1, '51220300': 1, '51130202': 1, '52130371': 1, '52511900': 1,
                  '51623025': 1, '52460105': 1, '52130121': 1, '52410403': 2, '52460400': 1, '52130434': 1,
                  '52130171': 1, '52133100': 2, '51411121': 1, '52510303': 3, '51511823': 1, '51469005': 1,
                  '51510700': 1, '52511425': 1, '52510400': 1, '52131071': 1, '51410327': 1, '51230522': 1,
                  '52640221': 1, '52130503': 1, '51511002': 1, '51411502': 1, '52230103': 1, '52511681': 1,
                  '52222401': 1, '51230123': 1, '51131003': 1, '52640106': 1, '51511504': 1, '51510421': 1,
                  '51620724': 1, '51511100': 1, '52230184': 1, '52460107': 1, '52511528': 1, '51511381': 1,
                  '52130638': 1, '52621100': 1, '52130306': 1, '51220503': 1, '51130529': 1, '51620521': 1,
                  '52410105': 1, '52220400': 1, '51231226': 1, '51410411': 1, '52230113': 2, '52510922': 1,
                  '51410923': 1, '52513423': 1, '52620522': 1, '51640300': 1, '51130925': 1, '52130926': 1,
                  '52130682': 1, '51130371': 1},
        "MJ0B2": {'52130207': 1, '51510525': 1, '52131003': 2, '51411623': 2, '52220122': 1, '52410181': 1,
                  '52130602': 2, '52230110': 3, '51511129': 1, '51410928': 2, '51130500': 4, '52410926': 2,
                  '52131081': 3, '51411525': 4, '52621000': 1, '52510623': 1, '51511902': 1, '52410225': 1,
                  '52620600': 1, '52460400': 1, '52621124': 4, '51620600': 1, '51231084': 1, '51230110': 3,
                  '52130430': 2, '51411082': 2, '52130902': 1, '52620902': 2, '51512022': 1, '52640400': 1,
                  '52220104': 1, '51130600': 4, '52410221': 2, '52131103': 3, '52222404': 3, '52231004': 2,
                  '51411100': 3, '51130281': 1, '52130132': 2, '52131102': 3, '52130500': 1, '51130129': 1,
                  '52411627': 2, '51511321': 3, '52130131': 3, '51230000': 4, '51510603': 1, '52130433': 1,
                  '51510727': 1, '51511803': 1, '52130130': 1, '51130684': 2, '51411502': 1, '52460205': 1,
                  '52620923': 1, '52130121': 3, '52130481': 4, '51511025': 1, '51232722': 1, '51130400': 4,
                  '52130609': 2, '52623021': 1, '52131082': 5, '52130630': 1, '52131123': 1, '51460100': 2,
                  '52511504': 1, '51220171': 1, '52130926': 3, '51511011': 1, '51130407': 1, '51130800': 3,
                  '51410100': 2, '51130127': 1, '52220882': 1, '52231281': 1, '51220600': 2, '52411324': 1,
                  '52511321': 1, '51410103': 2, '51131024': 1, '51510923': 1, '52410100': 5, '52130109': 2,
                  '51410900': 6, '51130703': 1, '51510504': 1, '52511802': 1, '52130632': 1, '51410926': 4,
                  '51130705': 1, '52411300': 2, '51130730': 1, '52620502': 2, '51511112': 1, '52131025': 1,
                  '51411400': 1, '52410311': 3, '52230109': 3, '51511024': 1, '52130571': 1, '51410173': 1,
                  '52220211': 1, '51510421': 4, '52410773': 1, '52620000': 2, '52130208': 1, '52130403': 1,
                  '52620522': 2, '51460000': 8, '52230604': 1, '51131182': 2, '51220781': 1, '51511000': 1,
                  '51640200': 2, '51232723': 2, '52230103': 1, '51230100': 4, '52410104': 1, '52511100': 1,
                  '51220000': 3, '52510300': 1, '51511424': 1, '51410621': 1, '51511725': 1, '51511826': 1,
                  '52620922': 1, '51130973': 1, '52620503': 2, '52130526': 1, '51511303': 1, '51130000': 2,
                  '51130626': 1, '52411528': 1, '51620000': 1, '51511524': 2, '52411729': 1, '52133100': 1,
                  '52510500': 2, '52411602': 2, '52622925': 2, '52220821': 11, '52410703': 3, '52640425': 1,
                  '52411302': 4, '51460200': 3, '52410105': 2, '51411528': 2, '52411603': 1, '52130321': 1,
                  '51621102': 1, '51220582': 1, '52410324': 1, '51130104': 2, '52621200': 1, '51410700': 2,
                  '52510000': 2, '52131128': 1, '51510722': 1, '52130302': 8, '52513230': 1, '52411025': 1,
                  '52130681': 2, '52410403': 1, '52410502': 2, '51627100': 1, '51510500': 1, '51231181': 2,
                  '51460205': 1, '52513402': 1, '52410581': 1, '52411702': 1, '51130773': 1, '51511525': 1,
                  '51410311': 3, '52510811': 1, '52510303': 4, '51510300': 3, '52640302': 1, '51623025': 1,
                  '51510000': 5, '51131100': 5, '51130131': 1, '52131028': 1, '52622901': 1, '51410527': 1,
                  '51230382': 1, '52411002': 2, '52231202': 2, '52411700': 1, '52230800': 1, '51231202': 1,
                  '51130606': 3, '52130828': 1, '52131127': 3, '52410526': 2, '52510802': 2, '52130771': 1,
                  '51220102': 2, '52222403': 3, '51511600': 1, '52130606': 2, '51411328': 2, '52231200': 1,
                  '51511421': 2, '51410600': 1, '52222401': 5, '52130300': 3, '52410329': 2, '52220381': 3,
                  '51410422': 1, '51130682': 3, '52411481': 3, '51220106': 1, '51640221': 3, '51410305': 8,
                  '52410783': 1, '51410104': 2, '52230108': 4, '51511900': 2, '51131121': 1, '52511400': 5,
                  '52130100': 1, '52130983': 2, '52130435': 1, '52230000': 1, '52620623': 3, '51130302': 1,
                  '52130600': 1, '51130726': 1, '51220303': 2, '52620523': 3, '52411426': 1, '52511024': 1,
                  '51620500': 2, '51230229': 1, '52510923': 1, '51622925': 1, '52411327': 1, '52469002': 2,
                  '51621122': 1, '51620302': 2, '51510683': 1, '52411623': 3, '52411622': 1, '51220302': 1,
                  '52411325': 1, '52220172': 1, '51640324': 1, '52410173': 3, '51513300': 2, '52130527': 1,
                  '51410302': 1, '52411500': 2, '51511903': 2, '52621002': 3, '51621002': 1, '52220523': 1,
                  '52460100': 2, '52130533': 3, '51131023': 1, '52130434': 2, '52460000': 2, '52130608': 2,
                  '51410773': 1, '51621100': 1, '52220721': 1, '51640104': 2, '52511381': 1, '51130434': 1,
                  '51410185': 5, '51511500': 1, '52130408': 1, '51513402': 3, '52510823': 1, '52410811': 1,
                  '51410922': 1, '52622922': 1, '51511304': 3, '52411100': 2, '51130823': 1, '52410782': 3,
                  '52411503': 1, '53460000': 1, '51411603': 2, '52130903': 3, '51511722': 4, '51411600': 3,
                  '51510802': 1, '52130371': 2, '52231282': 2, '51410781': 1, '51131082': 2, '51230500': 1,
                  '51411002': 1, '52410727': 1, '51411526': 1, '51410882': 1, '52640122': 1, '52130825': 1,
                  '51230882': 1, '51511723': 1, '51130681': 1, '51130200': 1, '52410902': 2, '51510922': 2,
                  '52511300': 2, '51620725': 2, '52469025': 1, '52130274': 1, '52130624': 1, '51510521': 1,
                  '51510904': 1, '52130528': 1, '51410400': 1, '52410622': 3, '52410182': 1, '52620802': 1,
                  '52130110': 1, '52230502': 2, '52410482': 1, '51410222': 2, '52220000': 3, '51130408': 1,
                  '51130533': 1, '51130900': 4, '52130607': 1, '51469029': 1, '51230881': 1, '51130523': 1,
                  '52130283': 3, '51130731': 1, '52620521': 1, '52130425': 2, '51220702': 1, '51410300': 2,
                  '51411329': 1, '51622900': 1, '51620981': 1, '51220104': 1, '52230805': 1, '52130921': 1,
                  '52511421': 1, '52513423': 1, '51411724': 1, '51230800': 1, '52230724': 1, '52220284': 1,
                  '51419001': 1, '51621126': 1, '51640402': 1, '52410108': 1, '51511502': 1, '51410303': 2,
                  '51220200': 2, '51410200': 1, '52230302': 2, '51511527': 1, '51511403': 2, '51640000': 1,
                  '51130607': 2, '51410505': 1, '51469027': 1, '51640122': 1, '51130132': 1, '51510302': 1,
                  '51410000': 1, '51620423': 2, '51230208': 1, '51230826': 1, '51512002': 2, '51411300': 3,
                  '52621100': 1, '51511802': 1, '52510600': 1, '52410672': 1, '51130227': 1, '51410181': 1,
                  '51621200': 1, '51513232': 2, '51410902': 1, '51513400': 1, '51410503': 1, '52411423': 2,
                  '51640425': 1, '52411402': 2, '52410821': 1, '51640381': 1, '52410300': 2, '51510903': 1,
                  '51130624': 2, '52511304': 1, '52622924': 1, '52510704': 2, '51130984': 2, '52130984': 2,
                  '51640106': 3, '51411024': 2, '52620826': 1, '52411723': 1, '51621224': 1, '52410883': 1,
                  '52640181': 1, '52621227': 1, '52220102': 1, '52640323': 1, '51411025': 2, '52130502': 1,
                  '52130638': 2, '51130133': 1, '51510821': 1, '52220202': 1, '52130628': 1, '52131100': 1,
                  '52230221': 1, '52130700': 1, '52411502': 1, '51130828': 1, '51410500': 1, '52411722': 1,
                  '52621202': 1, '51460400': 1, '51230600': 1, '51640522': 2, '51220300': 1, '52410305': 1,
                  '51131000': 1, '52130432': 1, '51130638': 1, '52130581': 1, '51130627': 2, '52510700': 4,
                  '52410184': 3, '51411500': 1, '52130730': 1, '52230227': 2, '52510322': 1, '52131002': 1,
                  '52130431': 1, '51510811': 1, '52230124': 1, '52411000': 1, '51511400': 2, '51640423': 1,
                  '52130204': 1, '52130273': 1, '52220700': 2, '51130183': 1, '52460105': 2, '51130303': 1,
                  '52230100': 1, '51510304': 1, '52411522': 1, '51410223': 1, '51620922': 1, '51230822': 1,
                  '52510922': 1, '52411621': 2, '52130826': 1, '51510800': 1, '52130111': 2, '52130203': 1,
                  '51510726': 1, '52231081': 1, '52130423': 1, '52131071': 1, '51410783': 1, '51230606': 1,
                  '51410727': 2, '51411521': 2, '52130281': 1, '52410102': 1, '51130223': 1, '51222426': 1,
                  '51131028': 1, '51130602': 1, '52460204': 1, '51510402': 1, '51130827': 1, '52410922': 1,
                  '51510812': 1, '51411302': 1, '52230602': 1, '52130503': 1, '52130200': 1, '52230881': 1,
                  '51411281': 2, '52511528': 2, '51513200': 1, '52410973': 1, '51411524': 1, '52411081': 1,
                  '51511702': 1, '51130672': 1, '52510681': 1, '52130202': 2, '51130503': 1, '52513400': 1,
                  '52460106': 1, '51410324': 2, '51411422': 2, '51510600': 1, '51230111': 1, '51130671': 1,
                  '52512022': 8, '52220581': 1, '53220000': 2, '51511700': 1, '51510524': 1, '51411481': 1,
                  '51130184': 1, '52130972': 1, '52620725': 1, '52411726': 2, '52411403': 6, '51510824': 1,
                  '52513401': 1, '51512021': 1, '52410172': 3, '52460202': 1, '51130927': 1, '51130702': 2,
                  '51621228': 1, '51130700': 1, '51511323': 3, '51411003': 1, '51130426': 1, '51220112': 1,
                  '51411424': 1, '52512021': 1, '52130731': 1, '51410804': 1, '51131081': 1, '51130683': 1,
                  '52410923': 1, '51230921': 1, '51230206': 1, '52419001': 1, '51411503': 1, '51411627': 1,
                  '51130623': 1, '52510800': 1, '51130724': 1, '51513428': 1, '51130609': 1, '52230102': 2,
                  '51130100': 1, '52230804': 1, '52410303': 1, '52640522': 3, '51130635': 1, '52410871': 1,
                  '52131171': 1, '51231000': 1, '52231002': 1, '52510603': 1, '52627100': 1, '51513401': 1,
                  '51640202': 1, '51510682': 1, '51130524': 1, '52411572': 1, '51621121': 1, '52130672': 1,
                  '51230605': 1, '52231221': 1, '52411523': 2, '52130709': 1, '51510781': 1, '52130627': 1,
                  '51410325': 1, '52411024': 1, '51130634': 1, '52511621': 1, '51511827': 1, '51640303': 1,
                  '51510723': 1, '52620722': 3, '52510604': 1, '52621226': 1, '52640502': 1, '51220113': 1,
                  '51640500': 1, '52131125': 1, '51513424': 1, '51510900': 1, '52130631': 1, '52410927': 1,
                  '51410725': 1, '52411526': 1},
        "MJ0B3": {'51411602': 1, '51130427': 1, '51513426': 1, '52130300': 1, '52410926': 5, '52230725': 1,
                  '51130672': 1, '51130628': 2, '51410702': 1, '51130633': 1, '52230108': 1, '52411421': 2,
                  '52220427': 1, '51230229': 2, '51130582': 1, '52620802': 2, '52410800': 3, '51130100': 3,
                  '52131002': 4, '51131082': 2, '52510812': 1, '51130626': 2, '51511621': 1, '52622925': 2,
                  '52130800': 2, '51411024': 1, '51130730': 3, '51220200': 2, '51511502': 2, '52130403': 1,
                  '51130123': 1, '52411572': 1, '51130731': 1, '51220100': 1, '51411523': 2, '52220605': 1,
                  '51130426': 1, '52469023': 2, '52410105': 7, '52411424': 1, '52131030': 1, '52410902': 5,
                  '52230803': 4, '52130109': 1, '51130608': 1, '52511304': 1, '51130636': 1, '51512021': 1,
                  '52130727': 1, '52513423': 5, '52410300': 3, '51230400': 1, '52621122': 1, '51130637': 1,
                  '52411621': 3, '52621226': 1, '52230805': 2, '52220100': 1, '51130131': 4, '51130503': 2,
                  '52130131': 1, '52230881': 2, '52419001': 2, '51230524': 1, '51130425': 4, '52513424': 1,
                  '52410423': 2, '52410184': 1, '52410724': 1, '51511011': 1, '52510802': 1, '52130630': 1,
                  '51622926': 1, '52130402': 1, '51411325': 2, '52220104': 1, '52410104': 1, '51511528': 2,
                  '51130300': 5, '52469005': 3, '51410523': 1, '52510903': 1, '51130930': 2, '51510600': 4,
                  '52411681': 6, '51130171': 2, '52513402': 1, '51220581': 3, '52410102': 2, '51230882': 1,
                  '52130110': 1, '52220421': 1, '51411321': 1, '52130629': 2, '51411724': 2, '51220104': 1,
                  '51460200': 6, '51130130': 1, '52511425': 1, '52230521': 1, '51620402': 1, '52411300': 3,
                  '52220113': 1, '51220702': 1, '51130502': 2, '51130631': 1, '51220802': 1, '51130223': 1,
                  '52410308': 2, '51510704': 1, '51511025': 1, '51640425': 1, '52130205': 2, '52410973': 1,
                  '52131182': 1, '52511011': 3, '51469029': 2, '51510900': 2, '52621124': 1, '51130607': 1,
                  '51411473': 2, '52411627': 3, '51231202': 1, '52410425': 2, '51410425': 1, '52621221': 1,
                  '52130771': 14, '51640000': 3, '51130900': 2, '52220700': 2, '52231121': 3, '52410181': 1,
                  '52220721': 1, '51460108': 1, '51511126': 1, '51410200': 2, '52410100': 3, '51130981': 2,
                  '52640200': 2, '51130283': 2, '52411323': 3, '51230124': 2, '51511124': 1, '52130532': 1,
                  '52220000': 2, '52510623': 2, '51510521': 1, '52130730': 1, '52130607': 1, '51511300': 1,
                  '51220000': 11, '52410702': 4, '51460000': 4, '51620921': 1, '53620000': 2, '52130209': 1,
                  '52410821': 2, '52411122': 2, '52130404': 1, '52220882': 1, '52410621': 2, '51510604': 1,
                  '52620921': 1, '52510600': 1, '51460205': 2, '52620800': 1, '52130111': 2, '51620900': 1,
                  '51411300': 3, '51411627': 2, '51220183': 1, '51621122': 3, '51220500': 1, '51130800': 4,
                  '51231200': 2, '51513424': 1, '52130102': 2, '52513226': 1, '52130700': 4, '51640221': 2,
                  '51130722': 2, '51410325': 1, '52640522': 1, '52460205': 1, '52130000': 1, '52220183': 2,
                  '51410928': 1, '52230828': 12, '52511523': 1, '51220821': 1, '52460000': 4, '51511500': 1,
                  '51220721': 1, '51469005': 3, '52130626': 1, '51411625': 1, '51621102': 1, '52411081': 1,
                  '52511803': 1, '51411603': 2, '51510000': 2, '52220172': 1, '52220582': 1, '52231202': 3,
                  '52620621': 1, '51130600': 2, '52640121': 1, '52620622': 1, '51411200': 1, '51410305': 2,
                  '51511423': 1, '51230109': 1, '52622922': 2, '51232721': 1, '51411700': 2, '51220700': 1,
                  '52220200': 1, '51230223': 1, '51410700': 1, '51410308': 2, '52130982': 4, '51620421': 1,
                  '51511602': 1, '52131102': 3, '52511529': 1, '51411600': 3, '52130302': 10, '52130281': 1,
                  '52231282': 4, '51510403': 1, '51511525': 1, '51640300': 1, '52230104': 1, '51513422': 1,
                  '52130203': 2, '51411322': 1, '51513436': 1, '52410923': 1, '52621200': 2, '51640521': 2,
                  '52230400': 1, '52411082': 1, '52131126': 1, '52410721': 1, '51220723': 1, '51411381': 2,
                  '52130602': 1, '51460100': 2, '51511781': 1, '51130000': 1, '51640424': 1, '52513330': 1,
                  '52410422': 1, '51410300': 1, '52510922': 1, '52411426': 1, '52460400': 2, '52622900': 1,
                  '51220681': 1, '51410900': 1, '52130421': 1, '52220600': 1, '52621021': 1, '52230203': 1,
                  '52411400': 1, '51640106': 1, '51640202': 2, '51621021': 1, '51130683': 1, '51510921': 1,
                  '52513337': 1, '52410411': 4, '52512022': 5, '51410100': 1, '52411302': 1, '51511321': 3,
                  '52410502': 2, '51511129': 1, '51130903': 1, '52511922': 1, '51410400': 2, '52627100': 1,
                  '51130700': 1, '52130903': 1, '52220623': 1, '52130200': 1, '52511903': 1, '51130183': 3,
                  '51410303': 1, '52411500': 1, '52230811': 2, '51130684': 1, '52130606': 3, '52130623': 1,
                  '51460202': 1, '51220400': 1, '52411521': 1, '51620700': 1, '52410726': 2, '52622923': 1,
                  '51620300': 1, '52130681': 2, '52410825': 1, '51130531': 1, '51131025': 1, '52410802': 2,
                  '53220000': 2, '52411327': 1, '51220106': 1, '52621102': 2, '51220600': 1, '52411522': 1,
                  '52130225': 1, '51410725': 1, '52411603': 1, '52411281': 1, '51510500': 1, '52460105': 1,
                  '51222404': 2, '52511302': 1, '51230000': 3, '51510811': 1, '51410185': 1, '52511100': 1,
                  '52410500': 1, '51511504': 1, '52511504': 1, '51410727': 1, '51513226': 1, '51620400': 1,
                  '52410403': 6, '51130208': 2, '52411622': 1, '52410526': 2, '52640000': 1, '51410212': 2,
                  '52511802': 1, '51511802': 1, '52510823': 1, '52640106': 2, '52130726': 2, '51640200': 1,
                  '52411600': 1, '51511028': 1, '51130529': 1, '51513330': 1, '51131100': 1, '52220202': 1,
                  '51513300': 2, '51230523': 2, '52220102': 1, '51410421': 1, '51513331': 1, '51510824': 2,
                  '51511323': 1, '52460204': 1, '51411424': 2, '51460204': 1, '52230225': 1, '52469002': 1,
                  '52410482': 3, '51411002': 1, '52411002': 1, '51510504': 1, '52410172': 1, '51469027': 1,
                  '51230500': 1, '51131103': 1, '52620522': 9, '52621223': 1, '52410000': 2, '51410000': 4,
                  '53410000': 1, '51510322': 1, '52130407': 1, '51410825': 1, '51640500': 2, '51511403': 1,
                  '51411727': 1, '52230100': 1, '51627100': 1, '52131071': 1, '51511100': 3, '52130123': 2,
                  '51130581': 1, '51410500': 1, '52130922': 1, '52510603': 1, '51512000': 2, '52230602': 1,
                  '51510903': 1, '52620502': 1, '51410721': 1, '52410325': 2, '51131028': 1, '52410883': 1,
                  '52640202': 2, '51410783': 3, '51511400': 1, '51230231': 1, '52410711': 1, '51130129': 1,
                  '52511923': 1, '51410223': 1, '51621223': 1, '51511700': 1, '52130502': 1, '52620525': 1,
                  '51230724': 2, '52511423': 1, '51410202': 1, '51130133': 1, '52130633': 2, '51511803': 1,
                  '51513225': 1, '51469025': 1, '51230382': 1, '51410821': 1, '51411622': 1, '52220723': 1,
                  '52511700': 1, '51410724': 1, '52230110': 1, '51220605': 1, '52411423': 1, '52131123': 1,
                  '51411402': 1, '51511324': 1, '51510700': 1, '51130109': 2, '51410204': 1, '51411525': 2,
                  '52411203': 3, '52230112': 1, '51411202': 1, '51510726': 2, '52511381': 1, '51230828': 1,
                  '52623001': 1, '52130432': 1, '52410103': 1, '51130184': 1, '51230725': 1, '51130430': 1,
                  '51410581': 2, '51640324': 1, '51220781': 1, '52411330': 1, '52510705': 1, '51411303': 1,
                  '51130225': 1, '52512021': 1, '51460203': 1, '52130634': 1, '52411322': 1, '51513233': 1,
                  '52621002': 1, '51511524': 1, '51130500': 1, '52640100': 1, '52621225': 1, '52130427': 1,
                  '51131128': 1, '52410222': 1, '51220103': 1, '51130630': 1, '52411700': 1, '51511529': 1,
                  '51622925': 1, '51230800': 1, '52130207': 1, '51130982': 1, '51410311': 1, '52130224': 1,
                  '52410323': 2, '52131003': 2, '52220191': 1, '51510705': 1, '51220191': 1, '51511703': 1,
                  '51130125': 2, '52469030': 1, '51510524': 2, '52130100': 1, '51220113': 1, '52513433': 1,
                  '51510802': 1, '51130984': 1, '51130771': 1, '51230722': 1, '51411122': 2, '52220781': 1,
                  '51511724': 2, '52230206': 1, '51410323': 1, '51620800': 1, '51419001': 1, '51410527': 1,
                  '52411329': 2, '51131024': 1, '51411329': 1, '51511702': 1, '52130425': 1, '52220881': 1,
                  '52230904': 1, '51511113': 1, '52410173': 1, '52410927': 1, '51410482': 1, '51131000': 1,
                  '51130725': 2, '52130104': 1, '52513401': 1, '51510683': 1, '52411625': 1, '52410302': 2,
                  '51220421': 1, '52622926': 1, '51411100': 2, '52410704': 1, '51130802': 1, '51130204': 1,
                  '51640502': 2, '51620821': 1, '52130128': 1, '51511681': 2, '52510402': 1, '51231086': 1,
                  '51510922': 1, '51622922': 2, '51511722': 1, '51510823': 1, '51620600': 2, '51130403': 1,
                  '52231005': 1, '52130371': 1, '52222424': 1, '51230521': 2, '52231223': 1, '51130681': 1,
                  '51411481': 1, '51222406': 1, '51222400': 2, '51469023': 1, '52411303': 1, '52222405': 1,
                  '51410105': 1, '51469030': 1, '51410104': 1, '51511526': 1, '52621126': 1, '51130302': 1,
                  '51411524': 1, '51411403': 1, '51511903': 1, '51640402': 1, '52130184': 1, '52410221': 1,
                  '51513222': 1, '51620403': 1, '52411722': 1, '52411328': 1, '52130927': 1, '52411100': 1,
                  '51410422': 1, '52130132': 1, '52220581': 1, '52220381': 1, '52131121': 1, '52640122': 1,
                  '52130600': 1, '52411502': 1, '51220221': 2, '52130928': 1, '52511725': 1, '51130127': 1,
                  '52411326': 1, '52620725': 1, '51469024': 1, '51131026': 1, '52410804': 1, '52222406': 1,
                  '51130202': 1, '52410400': 1, '51410926': 1, '51511322': 1, '52130400': 1, '51220722': 1,
                  '51510311': 1, '52411729': 2, '52410783': 1},
        "MJ0B4": {'52640100': 1, '51130929': 2, '52130902': 4, '52130108': 2, '51130581': 2, '51511381': 1,
                  '52130371': 3, '51130533': 1, '51131003': 3, '52131003': 4, '51230811': 1, '51511300': 1,
                  '51410900': 3, '52411702': 2, '52131182': 1, '52410900': 1, '52469007': 2, '52130121': 1,
                  '51230000': 5, '51130203': 2, '51130526': 1, '52620982': 1, '52511802': 1, '52131028': 2,
                  '51220300': 3, '51130183': 2, '52511300': 1, '51130623': 1, '52130606': 1, '51469028': 2,
                  '51411082': 3, '51130302': 1, '52130533': 1, '52411381': 2, '52130184': 1, '51130682': 1,
                  '51130223': 1, '52131102': 5, '51232721': 2, '52130302': 2, '51513436': 1, '51411481': 4,
                  '51130728': 1, '52469028': 1, '51220722': 1, '52130726': 1, '51131128': 1, '51460000': 5,
                  '51230113': 1, '52513201': 1, '52230102': 4, '52131082': 2, '51620982': 1, '52511423': 2,
                  '52130722': 1, '52130102': 1, '52220174': 1, '52131000': 1, '52230603': 1, '51640381': 1,
                  '52513402': 1, '52130273': 1, '51230606': 1, '52410300': 2, '51410308': 2, '52411600': 1,
                  '52511600': 5, '51511600': 2, '51510311': 3, '51130000': 5, '52621102': 2, '51469030': 3,
                  '52410102': 1, '52230828': 1, '52510000': 1, '51510000': 1, '52410421': 1, '51130629': 1,
                  '52513423': 8, '51510525': 1, '51230500': 1, '52410526': 2, '52511302': 4, '51411329': 1,
                  '52230811': 1, '52230110': 1, '52220621': 1, '51130971': 1, '51130125': 3, '52510781': 1,
                  '52130632': 1, '51130129': 1, '51220106': 1, '52231224': 1, '51510723': 2, '51231102': 1,
                  '51511622': 2, '51510300': 4, '52130525': 1, '52131103': 2, '51220211': 1, '51130126': 1,
                  '51130109': 2, '52512022': 4, '51621126': 2, '52460400': 2, '52419001': 1, '52510600': 5,
                  '52410225': 2, '51411724': 1, '52220600': 2, '51130531': 1, '53220000': 1, '51230900': 2,
                  '51220000': 5, '52130823': 1, '52130400': 1, '52410803': 1, '52411726': 3, '51410782': 1,
                  '52231223': 6, '51410305': 4, '51511700': 1, '51130633': 1, '52130435': 1, '51230208': 1,
                  '51510322': 1, '52130582': 1, '51513226': 1, '52411323': 1, '51130705': 1, '52230803': 1,
                  '51131082': 2, '51220400': 2, '51511900': 2, '52410783': 1, '51410783': 4, '51411603': 1,
                  '52411572': 1, '52620921': 2, '51620403': 1, '51411400': 3, '52130300': 1, '52220176': 1,
                  '51511113': 1, '51410773': 1, '51130702': 1, '52130923': 1, '52220100': 1, '52231221': 2,
                  '51410724': 1, '51410825': 2, '51130530': 3, '51411572': 1, '51410324': 1, '51410883': 2,
                  '51130823': 1, '51510682': 1, '51411621': 1, '51230700': 2, '51130423': 1, '52620400': 1,
                  '51130984': 3, '52130202': 2, '51130209': 1, '52410172': 1, '51411423': 1, '52621100': 2,
                  '52220381': 3, '51130281': 3, '51230223': 1, '51130130': 3, '51621202': 2, '52460000': 3,
                  '52510904': 2, '51131124': 1, '51510812': 1, '51130729': 1, '51411527': 1, '51131100': 2,
                  '52410226': 1, '52130684': 1, '51621100': 1, '52621122': 1, '51510725': 1, '51410500': 3,
                  '52510700': 1, '51411303': 1, '52220000': 1, '51411300': 1, '52640400': 1, '52410711': 1,
                  '51511400': 3, '52620802': 2, '51130824': 1, '52469002': 1, '51410502': 1, '52411203': 1,
                  '51622926': 1, '51511133': 1, '51131081': 1, '52622925': 4, '52410000': 1, '51410000': 1,
                  '51411600': 5, '52220113': 1, '51130503': 1, '51469007': 2, '51130683': 1, '52130700': 4,
                  '52410902': 2, '51130133': 1, '51510903': 1, '51620900': 3, '52410702': 2, '51130128': 1,
                  '51460200': 2, '51510321': 1, '51410404': 1, '51130131': 3, '51230100': 2, '51510923': 1,
                  '51410423': 2, '51469005': 1, '51130825': 1, '51511111': 1, '51131026': 1, '51410329': 1,
                  '51130771': 2, '52640202': 1, '51510802': 1, '51130709': 1, '51410802': 3, '51411521': 1,
                  '51411224': 1, '51130982': 3, '52130204': 1, '52131126': 1, '51130803': 1, '51220183': 1,
                  '52131023': 1, '51511100': 2, '51231226': 1, '52130000': 2, '52230502': 2, '51511523': 1,
                  '51130424': 2, '52410700': 2, '51460202': 1, '51511124': 1, '52230100': 2, '52130431': 1,
                  '51220284': 1, '52513401': 2, '52220881': 1, '52130200': 1, '51220113': 1, '52511323': 1,
                  '51512002': 1, '51131023': 1, '51130821': 1, '52620525': 1, '51130703': 1, '51231200': 1,
                  '51130571': 1, '51130529': 2, '52620302': 8, '52410329': 1, '52411025': 1, '51130827': 2,
                  '51640200': 1, '51410506': 1, '52130306': 1, '52220882': 1, '52130600': 2, '51130600': 2,
                  '51130638': 1, '51411422': 7, '51511423': 2, '51230805': 1, '51411100': 4, '51511323': 1,
                  '52410182': 1, '52511400': 2, '52411221': 1, '51130681': 2, '51130731': 1, '52130183': 1,
                  '52469027': 1, '51469023': 1, '52410173': 1, '51230227': 1, '51230523': 1, '51621227': 1,
                  '51620523': 1, '51640502': 2, '52621227': 1, '51220700': 2, '52620521': 1, '51130672': 1,
                  '51220600': 1, '51410927': 1, '51230800': 1, '51410104': 1, '51130202': 1, '52411421': 1,
                  '52222404': 1, '51410223': 2, '52411281': 1, '51411024': 5, '51410222': 1, '52640200': 1,
                  '51220200': 1, '51469027': 1, '51622922': 1, '51410205': 1, '51510900': 3, '52230500': 1,
                  '52231226': 1, '52130207': 1, '51640422': 1, '52411000': 1, '52410423': 1, '51130582': 1,
                  '52620623': 2, '52411603': 2, '51130923': 2, '52511722': 1, '52620600': 1, '52230900': 1,
                  '51513323': 1, '52411521': 1, '51130534': 1, '51510700': 1, '51510681': 1, '52220200': 2,
                  '52411481': 1, '51130224': 1, '51230300': 1, '51640221': 1, '52131121': 1, '51621225': 1,
                  '51222401': 1, '51130481': 1, '52469022': 2, '52230104': 1, '52130630': 1, '51130925': 1,
                  '52511902': 1, '51230111': 1, '51410800': 1, '51230882': 1, '52410184': 1, '51511500': 1,
                  '52230109': 2, '52130110': 1, '52231202': 1, '52220112': 1, '51410482': 1, '51620503': 1,
                  '52130527': 1, '51511800': 2, '52511402': 1, '51410203': 1, '51130283': 1, '51130104': 1,
                  '51130204': 1, '52220211': 1, '51230109': 1, '52411002': 2, '52231181': 1, '51130402': 1,
                  '52410325': 1, '51511502': 1, '52411525': 1, '52130407': 1, '51130802': 1, '51130684': 1,
                  '51220174': 1, '51220122': 1, '52460105': 1, '52220122': 1, '52130503': 2, '52410221': 1,
                  '52510411': 1, '52131127': 1, '51130225': 2, '51131002': 1, '51410804': 1, '51130205': 1,
                  '51510904': 1, '52130434': 1, '51620700': 1, '51231000': 1, '51460107': 1, '52130635': 1,
                  '52620200': 1, '51230281': 1, '52130924': 1, '51130430': 1, '52411329': 2, '51411524': 2,
                  '51621124': 1, '52130681': 2, '52130728': 1, '52130126': 1, '52130427': 2, '52131122': 3,
                  '51511321': 1, '51130208': 1, '51620922': 1, '51130400': 1, '51230184': 1, '51130100': 1,
                  '52410323': 2, '52410773': 1, '51130229': 1, '52620602': 2, '51131000': 1, '52130623': 1,
                  '51511181': 1, '51621224': 1, '52230700': 1, '51130227': 1, '52511421': 2, '51460108': 1,
                  '51469002': 1, '52411423': 1, '52130281': 1, '51230400': 2, '51513327': 1, '52513322': 1,
                  '52411302': 1, '51410821': 1, '51460105': 1, '52130105': 1, '52220821': 2, '51640105': 1,
                  '52220422': 1, '51621122': 1, '52130928': 1, '51640122': 1, '51220105': 1, '52511028': 1,
                  '52410105': 1, '51410600': 1, '52511502': 1, '52622923': 3, '52130123': 1, '52220421': 1,
                  '52511724': 1, '52410603': 1, '52511903': 1, '51511112': 1, '51513335': 1, '53460000': 1,
                  '51131125': 1, '51130426': 1, '51460106': 1, '52410183': 1, '51130107': 1, '52469023': 1,
                  '51621000': 1, '51222406': 1, '51231081': 1, '51230881': 1, '51130723': 1, '51130207': 1,
                  '52513223': 1, '51410225': 1, '51510822': 1, '52130800': 2, '52130529': 1, '51621102': 1,
                  '52230111': 1, '51411000': 1, '52460203': 1, '52511822': 1, '51131025': 1, '51220723': 2,
                  '52511921': 1, '52220172': 1},
        "MJ164": {'52140624': 1, '52140822': 2, '52140702': 12, '52140426': 2, '52140000': 6, '51140802': 3,
                  '51140723': 2, '52140900': 7, '51140200': 8, '52141002': 10, '51141123': 1, '51141027': 2,
                  '51141102': 2, '51140100': 20, '51140825': 2, '51140600': 5, '52140403': 2, '52140202': 9,
                  '51140225': 4, '51140728': 3, '52140303': 7, '52140108': 2, '51140922': 4, '51140800': 10,
                  '51141028': 3, '51140781': 2, '52140922': 8, '52140502': 4, '51140221': 3, '51140000': 13,
                  '52141124': 2, '51140311': 1, '52141024': 3, '51140822': 2, '51140500': 7, '52140427': 2,
                  '52140500': 4, '51140405': 2, '51141100': 5, '51140110': 3, '51140700': 4, '52140525': 2,
                  '51140830': 2, '52141021': 3, '52141032': 2, '51140428': 2, '51140726': 1, '51140727': 2,
                  '51140403': 1, '52140623': 4, '51140981': 4, '52140107': 2, '52140821': 3, '51140300': 5,
                  '51140924': 1, '52140929': 1, '51141082': 2, '52140222': 2, '52140405': 2, '51140921': 2,
                  '52140825': 3, '51140202': 1, '52141121': 1, '52141181': 4, '51140431': 3, '51141125': 2,
                  '51140623': 1, '51141122': 3, '51140624': 2, '52140122': 1, '51140823': 7, '52140200': 3,
                  '51141129': 3, '52140109': 2, '52140726': 2, '52140524': 2, '52140600': 2, '51140502': 1,
                  '51141081': 2, '51140224': 5, '51140603': 2, '51140702': 2, '51140729': 1, '51140223': 2,
                  '52141033': 1, '52141129': 2, '51141031': 1, '52140924': 2, '51140829': 4, '51141182': 2,
                  '52141102': 3, '51140123': 4, '52141100': 2, '51141021': 2, '52140781': 1, '51140427': 4,
                  '52141000': 6, '52140212': 1, '51140212': 1, '51141121': 3, '51140881': 1, '52140828': 1,
                  '51141034': 1, '52140603': 2, '51140821': 2, '52140802': 4, '52140881': 2, '51141024': 5,
                  '52140300': 3, '52141082': 2, '52140622': 1, '52140800': 1, '53140000': 5, '52140602': 3,
                  '52140181': 6, '51140105': 4, '51140109': 2, '51140321': 2, '51140425': 2, '51140882': 6,
                  '52141022': 4, '52140224': 1, '52140703': 7, '51140900': 3, '51140824': 1, '52140724': 2,
                  '51140525': 1, '51140703': 1, '51140923': 6, '51141128': 1, '52140425': 2, '52140727': 2,
                  '51140215': 3, '51140926': 1, '51140581': 3, '52140121': 1, '51140524': 1, '52140928': 1,
                  '52140830': 1, '51140521': 1, '51141130': 1, '51140181': 1, '51140724': 2, '52140882': 2,
                  '51140602': 2, '52141081': 1, '51140214': 1, '51140826': 2, '51141124': 1, '51140827': 3,
                  '52140700': 1, '51141181': 1, '52140100': 1, '51140108': 1, '52141128': 4, '51140902': 2,
                  '52140829': 2, '52141182': 1, '51140721': 3, '51141000': 1, '52140725': 1, '52140729': 5,
                  '51141025': 2, '51140423': 3, '51140932': 1, '52140110': 1, '51140107': 1, '52141023': 1,
                  '52140824': 1, '52140406': 1, '51141022': 1, '51141033': 1, '52140581': 2, '51140929': 1,
                  '52140302': 3, '52140521': 1, '51140122': 2, '52140923': 1, '51140400': 1, '51140429': 1,
                  '51141127': 1, '51140622': 1, '51140322': 1},
        "MJ295": {'52210213': 8, '51210213': 5, '52210202': 4, '51210211': 2, '52210211': 9, '51210202': 1,
                  '51210290': 2, '52210212': 3, '51210200': 6, '52210283': 6, '52210290': 2, '52210203': 2,
                  '51210204': 1, '52210281': 1, '51210212': 2, '52210214': 3, '51210281': 2, '52210200': 1,
                  '52210204': 1, '51210214': 1, '52210224': 1},
        "MJ873": {'52330127': 6, '52330112': 5, '52330110': 7, '52330109': 34, '52330113': 27, '52330108': 18,
                  '52330000': 8, '52330102': 8, '52330225': 1},
        "MJ895": {'52330225': 6, '52330212': 22, '52330226': 4, '52330200': 5, '52330205': 13, '52330203': 20},
        "MJA61": {'51340124': 86},
        "MJA65": {'51340121': 221},
        "MJA90": {'51340421': 204},
        "MJA98": {'51341504': 70},
        "MJB02": {'51341525': 105},
        "MJB24": {'51340822': 51},
        "MJB27": {'51340828': 64},
        "MJB36": {'51341622': 85},
        "MJB38": {'51341622': 167},
        "MJB40": {'51341503': 158},
        "MJB41": {'51341602': 211},
        "MJB66": {'51350582': 6, '51350525': 2, '51350524': 3, '51350182': 1, '51350605': 4, '52350104': 7,
                  '51350722': 1, '51350781': 1, '51350000': 3, '52350902': 1, '52350100': 3, '52350825': 2,
                  '51350604': 3, '51350500': 1, '52350702': 1, '51350481': 3, '51350502': 1, '51350400': 1,
                  '52350583': 1, '51350624': 2, '52350481': 2, '52350199': 1, '52350624': 1, '51350100': 2,
                  '52350821': 2, '52350125': 1, '51350881': 1, '51350703': 1, '52350500': 4, '52350700': 1,
                  '51350121': 1, '51350629': 7, '51350627': 174, '51350782': 1, '51350424': 1, '51350628': 105,
                  '52350124': 1, '52350322': 1, '52350703': 1, '51350583': 2, '51350900': 1, '51350303': 1,
                  '51350800': 3, '52350782': 1, '51350700': 1, '51350981': 1, '52350128': 2, '51350821': 1,
                  '51350581': 1, '51350602': 51, '51350693': 4, '52350424': 1, '51350426': 1, '51350921': 1,
                  '51350823': 1, '51350622': 13, '52350924': 1, '51350603': 22, '51350696': 2, '52350622': 1,
                  '51350925': 1, '51350429': 1, '52350122': 1, '51350505': 1, '52350111': 1, '51350425': 1,
                  '51350725': 1, '51350322': 1, '52350181': 1, '52350881': 1, '51350695': 1, '51350526': 1,
                  '51350305': 1},
        "MJB67": {'52350583': 3, '51350693': 42, '51350623': 168, '51350603': 56, '51350622': 145, '51350627': 3,
                  '51350628': 116, '51350696': 68, '51350800': 2, '51350825': 1, '51350902': 1, '51350802': 1,
                  '51350626': 75, '51350629': 53, '51350926': 2, '51350605': 47, '51350581': 2, '51350695': 65,
                  '52350921': 3, '52350423': 1, '51350526': 1, '51350124': 1, '51350602': 15, '52350405': 1,
                  '51350000': 1, '52350305': 1, '51350400': 1, '51350821': 1, '51350104': 1, '51350781': 2,
                  '51350111': 1, '51350583': 1, '52350981': 1, '52350721': 1, '51350502': 1, '52350425': 1,
                  '51350524': 1, '51350182': 1, '51350426': 1, '51350624': 27, '52350603': 1, '52350125': 1,
                  '52350000': 2, '51350782': 1, '51350823': 1, '51350128': 1, '51350604': 60, '51350305': 2,
                  '51350982': 2, '52350825': 1, '52350181': 6, '51350582': 1, '52350503': 1, '52350521': 1,
                  '51350122': 1, '51350405': 1, '52350722': 1, '52350400': 1, '52350581': 1},
        "MJB68": {'51350604': 129, '51350693': 2, '51350800': 2, '51350629': 22, '51350624': 187, '51350622': 16,
                  '52350111': 10, '51350982': 9, '51350000': 4, '51350124': 2, '51350623': 19, '52350182': 5,
                  '52350421': 1, '52350100': 7, '52350602': 1, '51350923': 2, '51350103': 3, '52350124': 5,
                  '51350505': 1, '51350525': 10, '52350525': 1, '52350429': 2, '52350922': 1, '51350582': 23,
                  '52350981': 3, '51350100': 3, '51350125': 4, '51350600': 9, '52350622': 4, '51350627': 1,
                  '51350783': 3, '52350700': 3, '51350128': 5, '52350821': 3, '52350181': 11, '51350824': 5,
                  '51350392': 4, '51350581': 2, '51350521': 6, '51350605': 4, '51350900': 6, '52350103': 4,
                  '52350582': 3, '51350583': 14, '51350481': 1, '51350500': 6, '51350721': 1, '51350821': 1,
                  '51350502': 5, '52350628': 1, '51350405': 4, '52350502': 5, '51350722': 1, '52350122': 3,
                  '52350584': 1, '52350782': 2, '51350322': 2, '51350803': 5, '52350702': 1, '52350199': 1,
                  '51350423': 3, '51350123': 4, '52350626': 3, '52350521': 2, '51350404': 2, '52350128': 4,
                  '52350424': 1, '51350430': 1, '52350824': 3, '51350424': 1, '51350302': 2, '52350800': 1,
                  '52350121': 2, '52350524': 19, '52350629': 1, '52350802': 9, '51350700': 1, '52350430': 2,
                  '52350982': 4, '51350981': 1, '52350825': 4, '51350524': 9, '51350602': 2, '51350121': 8,
                  '52350583': 5, '51350724': 3, '51350921': 5, '52350803': 3, '51350526': 4, '51350421': 1,
                  '51350802': 1, '52350723': 1, '51350823': 3, '51350584': 1, '51350725': 1, '51350702': 2,
                  '51350400': 4, '51350102': 2, '51350626': 1, '52350102': 2, '52350302': 1, '51350104': 1,
                  '51350825': 2, '51350303': 4, '51350926': 1, '52350304': 1, '52350104': 3, '51350902': 1,
                  '53350000': 6, '51350300': 2, '52350900': 1, '51350603': 1, '51350429': 1, '52350823': 2,
                  '52350627': 2, '51350122': 2, '52350125': 2, '51350922': 2, '52350902': 1, '52350400': 1,
                  '51350111': 2, '52350926': 1, '52350921': 1, '52350526': 1, '52350694': 1, '52350703': 2,
                  '52350581': 2, '52350404': 2, '51350782': 2, '52350781': 1, '51350504': 1, '52350105': 1,
                  '52350605': 1, '51350999': 1, '51350181': 2, '51350182': 1, '51350503': 1, '52350600': 1,
                  '52350923': 1, '52350504': 1},
        "MJB98": {'52350200': 9, '51350200': 5, '51350212': 15, '51350213': 12, '51350205': 1, '52350213': 5,
                  '52350211': 1, '51350203': 3, '52350205': 1, '51350211': 1, '51350206': 3, '52350206': 1},
        "MJD00": {'51360722': 14, '52360722': 40},
        "MJL21": {'52440306': 9, '51440303': 5, '51440305': 6, '51440306': 5, '51440304': 6, '52440300': 34,
                  '51440307': 2, '52440305': 9, '51440300': 46, '52440307': 3, '52440303': 3, '52440304': 6,
                  '52440308': 4, '51440308': 2},
        "MJN87": {'51450330': 6, '52450603': 2, '51450181': 4, '51450102': 3, '51451300': 6, '51451200': 6,
                  '52450126': 1, '52450902': 3, '51450881': 4, '51451223': 1, '52450400': 1, '51450108': 1,
                  '51450702': 1, '51450000': 11, '52451322': 4, '51450804': 1, '51450922': 11, '52450324': 4,
                  '51451224': 1, '51450821': 8, '52450100': 12, '52451400': 3, '52451103': 1, '51450902': 3,
                  '52450900': 3, '51450204': 1, '51451425': 3, '51450600': 4, '51450923': 3, '51450325': 8,
                  '51451225': 1, '51450225': 2, '51450321': 3, '52450924': 2, '51450924': 4, '51451226': 2,
                  '51451100': 4, '51450200': 8, '52450200': 1, '51450421': 2, '52450305': 1, '52450422': 4,
                  '51450422': 2, '51451302': 5, '52450000': 8, '51450521': 3, '52450300': 7, '51451322': 3,
                  '51450921': 5, '51450205': 1, '51451324': 2, '51450100': 16, '52450202': 2, '51450105': 2,
                  '52450312': 1, '51450300': 5, '51450900': 3, '52450103': 3, '51451000': 3, '51451227': 2,
                  '51450700': 2, '51450305': 1, '51450800': 3, '51450981': 3, '52450500': 3, '52450602': 1,
                  '51450500': 6, '51450602': 2, '52451000': 4, '51450481': 3, '51451321': 2, '51451423': 1,
                  '51450503': 3, '51450223': 1, '51451202': 2, '51450681': 4, '51450403': 1, '51450512': 1,
                  '51450124': 1, '52450304': 1, '52450800': 3, '52451100': 3, '52450922': 1, '52450721': 3,
                  '51450326': 2, '52450206': 2, '52450702': 1, '52450600': 2, '51450621': 2, '52450421': 1,
                  '51450381': 2, '52450181': 2, '51451002': 1, '51451229': 2, '51450109': 1, '52450205': 4,
                  '51450203': 3, '51450123': 1, '52450802': 1, '52450502': 2, '51450103': 1, '51451103': 2,
                  '52451202': 1, '52451122': 5, '52450903': 1, '52451228': 2, '52451225': 2, '51451381': 1,
                  '52450804': 1, '51451123': 1, '51451481': 2, '51451424': 1, '51450803': 2, '52450923': 4,
                  '51451031': 1, '51450222': 2, '53450000': 1, '51451402': 1, '52451123': 1, '52451324': 1,
                  '51451222': 1, '51450329': 1, '51450110': 2, '51450423': 1, '51450125': 1, '52450204': 2,
                  '51450400': 2, '52451321': 2, '52451302': 2, '52451422': 1, '51450327': 1, '52451082': 1,
                  '51451082': 1, '52451031': 3, '51451323': 1, '52451425': 1, '52450703': 2, '52451221': 1,
                  '52450330': 1, '51450328': 2, '52450621': 1, '51451421': 1, '52450125': 1, '51451029': 1,
                  '52450406': 1, '52451229': 1, '52450108': 1, '51451203': 1, '52451227': 2, '52450481': 1,
                  '51450224': 1, '52450107': 2, '52450403': 1, '51450703': 1, '52451102': 1, '52451481': 1,
                  '51451081': 1},
        "MJN88": {'51450205': 1, '52450802': 1, '51450502': 2, '52451203': 1, '52451000': 1, '51451003': 1,
                  '51451082': 1, '52450328': 1, '52451100': 1, '51450422': 2, '51450202': 1, '52450300': 1,
                  '52451225': 1, '51450405': 1, '51450204': 1, '51450981': 2, '51451324': 1, '51450923': 2,
                  '51450000': 6, '51450400': 1, '51451302': 1, '51450300': 4, '52450000': 1, '51450100': 7,
                  '52450600': 1, '51450900': 1, '52450800': 2, '51451321': 1, '51451228': 1, '51451421': 1,
                  '51450922': 1, '51450110': 1, '51450323': 1, '52450621': 1, '52451400': 1, '51451226': 1,
                  '52451022': 1, '51450881': 1, '52450881': 1, '51451300': 1, '51451423': 1, '52450603': 1,
                  '51451123': 1, '51451422': 2, '51450321': 1, '51451424': 1, '51450126': 1, '51451323': 1,
                  '52450500': 1, '51450803': 1},
        "MJP63": {'52500114': 8, '51500229': 5, '51500240': 15, '51500151': 6, '52500000': 20, '51500105': 5,
                  '52500105': 15, '52500102': 6, '51500154': 5, '52500236': 3, '51500231': 3, '51500113': 3,
                  '51500191': 4, '52500154': 5, '52500240': 3, '51500119': 4, '51500000': 15, '51500237': 4,
                  '52500108': 12, '51500117': 5, '52500152': 4, '52500103': 7, '51500112': 2, '52500112': 7,
                  '52500116': 12, '52500101': 4, '51500101': 5, '52500120': 9, '51500102': 6, '52500104': 12,
                  '51500193': 4, '52500118': 6, '52500113': 2, '51500242': 3, '51500109': 4, '51500230': 10,
                  '52500231': 5, '51500236': 7, '51500108': 2, '51500233': 4, '51500156': 4, '52500111': 1,
                  '51500116': 7, '53500000': 5, '52500110': 5, '51500235': 1, '51500192': 4, '51500103': 3,
                  '52500229': 4, '52500242': 2, '52500192': 4, '52500107': 6, '51500107': 5, '52500193': 4,
                  '52500151': 2, '51500115': 4, '51500241': 4, '51500110': 4, '51500111': 4, '51500114': 5,
                  '51500106': 7, '52500117': 5, '51500152': 1, '52500106': 7, '52500109': 5, '52500156': 1,
                  '51500238': 3, '52500238': 2, '52500153': 2, '52500241': 1, '52500119': 2, '52500191': 3,
                  '51500155': 1, '52500243': 2, '52500115': 2, '51500118': 2, '51500243': 1},
        "MJQ00": {'52510100': 15, '51510105': 3, '51510132': 3, '52510104': 4, '51510100': 9, '52510116': 6,
                  '51510110': 2, '52510182': 3, '52510107': 5, '52510108': 4, '51510114': 2, '52510121': 1,
                  '51510117': 4, '52510199': 3, '52510110': 5, '51510183': 1, '51510106': 1, '52510106': 2,
                  '51510113': 1, '52510129': 2, '52510105': 2, '51510131': 1, '51510107': 1, '52510180': 1,
                  '51510181': 1, '52510117': 1, '51510116': 2, '52510114': 1, '51510115': 1, '52510112': 1,
                  '51510182': 3, '52510115': 1},
        "MJQ02": {'51510100': 6, '51510184': 3, '51510132': 4, '52510100': 26, '52510105': 2, '51510116': 6,
                  '51510131': 1, '51510108': 4, '52510104': 1, '51510104': 3, '52510199': 3, '51510199': 4,
                  '52510110': 11, '51510117': 6, '52510112': 5, '52510107': 5, '52510183': 1, '52510185': 4,
                  '51510107': 3, '51510106': 5, '51510185': 5, '51510112': 3, '52510180': 1, '52510117': 3,
                  '51510110': 4, '52510129': 2, '51510105': 4, '51510183': 2, '52510113': 1, '52510181': 2,
                  '51510182': 10, '52510184': 2, '51510113': 3, '51510114': 2, '52510114': 1, '52510121': 1,
                  '52510116': 2, '51510181': 2, '51510115': 1, '52510132': 2, '51510129': 2, '51510180': 2},
        "MJR84": {'51520115': 4, '52520111': 8, '51520100': 11, '52520115': 15, '51520113': 1, '52520103': 9,
                  '52520100': 6, '51520122': 1, '51520123': 2, '52520181': 1, '51520103': 1, '52520113': 2,
                  '52520102': 8, '51520102': 3, '51520112': 1, '51520181': 1},
        "MJR93": {'51520328': 2, '52520302': 18, '51520322': 1, '51520304': 3, '52520327': 5, '52520381': 5,
                  '51520300': 12, '52520324': 1, '51520302': 2, '51520324': 2, '52520300': 7, '52520330': 9,
                  '52520304': 11, '52520303': 25, '51520303': 2, '51520326': 2, '52520325': 2, '51520330': 7,
                  '51520381': 1, '52520328': 1, '51520323': 1, '51520382': 4, '51520325': 1, '51520327': 1,
                  '52520382': 1, '52520329': 1},
        "MJT43": {'51530902': 1, '51532500': 2, '52533102': 4, '51530622': 4, '51530523': 4, '51532923': 3,
                  '52532626': 2, '51532525': 4, '51530629': 3, '51532928': 1, '51532600': 2, '52530112': 3,
                  '51530112': 1, '51530621': 2, '51530502': 1, '52530627': 4, '52532901': 3, '51532527': 2,
                  '51530724': 1, '51532302': 1, '51532627': 2, '51530823': 3, '51530723': 1, '51530628': 2,
                  '52532628': 16, '51533324': 2, '51530000': 2, '51530581': 3, '51532926': 1, '51532300': 2,
                  '52532503': 4, '52532531': 8, '51530721': 3, '51530700': 1, '52532504': 2, '51532504': 6,
                  '51530900': 4, '51530681': 1, '52530300': 2, '51532800': 1, '51530828': 3, '51530111': 1,
                  '52530721': 1, '52530302': 1, '51533400': 2, '51532524': 3, '52530303': 1, '51530821': 1,
                  '51532801': 4, '51530402': 1, '51530824': 2, '51530625': 3, '51530524': 2, '52530181': 1,
                  '52530127': 3, '51530100': 9, '51530324': 1, '51533423': 1, '51533401': 2, '51532531': 2,
                  '51532601': 1, '52530115': 1, '52532801': 3, '51530381': 2, '51532328': 1, '52532527': 1,
                  '52530802': 2, '51530800': 2, '51533422': 4, '52530102': 1, '51532628': 1, '51530325': 4,
                  '51532822': 1, '51530922': 2, '51530129': 1, '51532301': 1, '51532532': 2, '52532501': 1,
                  '52532622': 1, '51532503': 1, '52532327': 1, '51530500': 2, '52530427': 1, '52532928': 1,
                  '51532901': 1, '52532625': 1, '51530822': 2, '51532823': 2, '51532523': 1, '52532922': 1,
                  '52532601': 2, '51530623': 2, '51530627': 3, '51530127': 1, '51533123': 1, '52530902': 2,
                  '52530924': 1, '52530426': 1, '52530103': 1, '52533301': 1, '52530111': 3, '51533103': 1,
                  '52530322': 1, '51532626': 1, '51530124': 1, '51530624': 1, '51533102': 3, '51530925': 1,
                  '52532822': 1, '52530381': 1, '51532927': 1, '52533103': 2, '51532530': 2, '52530602': 1,
                  '51532623': 1, '51530702': 1, '51533325': 1, '51530602': 1, '51530125': 1, '51530481': 1},
        "MJT44": {'51530000': 3, '51532929': 1, '52530502': 2, '51532328': 1, '52530802': 3, '51533122': 2,
                  '52533401': 1, '51533401': 3, '51530113': 1, '51530500': 2, '51530114': 2, '51530523': 3,
                  '52532531': 1, '51530129': 2, '52530324': 1, '52532628': 14, '52530623': 3, '51532600': 6,
                  '51532500': 2, '52530111': 6, '51530821': 5, '52530602': 3, '51530100': 7, '51530802': 3,
                  '52533102': 3, '51530581': 1, '52532501': 2, '52530103': 3, '52532329': 1, '51532930': 1,
                  '51530823': 1, '52530000': 4, '53530000': 2, '51530926': 3, '51533103': 3, '52530500': 1,
                  '52530381': 2, '52530127': 1, '51530900': 2, '51532531': 1, '51532503': 3, '52530302': 1,
                  '52532822': 1, '51532524': 5, '52532322': 1, '51530924': 1, '51533324': 1, '52532503': 3,
                  '51530625': 1, '52532626': 3, '52530304': 1, '51530627': 3, '52532801': 7, '51532300': 2,
                  '51530127': 1, '51530828': 1, '51530824': 1, '52530194': 1, '51530723': 1, '51532532': 1,
                  '52530325': 3, '51532800': 2, '52533422': 1, '51533422': 1, '51530502': 2, '52530822': 2,
                  '52530102': 5, '52530828': 1, '51532822': 1, '52532901': 4, '52530112': 1, '52532529': 1,
                  '51532322': 1, '51532504': 2, '52532601': 7, '52530100': 1, '52532527': 1, '52530825': 3,
                  '52530300': 2, '51530423': 1, '51530403': 1, '51532329': 1, '52533103': 2, '51530681': 1,
                  '51530722': 2, '52530628': 1, '51530602': 1, '51530621': 4, '51530800': 1, '52530624': 2,
                  '52532624': 1, '51532924': 2, '51532923': 2, '52530303': 1, '51530381': 1, '51532626': 2,
                  '51530303': 2, '51532927': 1, '51532323': 1, '51533100': 3, '51530624': 2, '52533124': 1,
                  '51533400': 1, '51532926': 1, '51530304': 1, '51530521': 1, '51530325': 1, '51530115': 1,
                  '51530324': 1, '51530428': 1, '51533123': 2, '51532301': 1, '52532504': 1, '52530924': 2,
                  '51530400': 1, '52530400': 1, '52533123': 1, '51532901': 1, '51532325': 1, '52532627': 1,
                  '52530113': 1, '51530629': 1, '51532928': 1, '52530721': 1, '52530600': 1, '52530124': 1,
                  '52530622': 1, '51530827': 1, '51530326': 1, '52533122': 1, '51530702': 2, '52530926': 1,
                  '51532601': 1, '51532900': 1, '52532302': 2, '51532523': 1, '52530523': 1, '51532628': 2,
                  '51530322': 1, '51532627': 1, '51532324': 1, '51532527': 1, '52533100': 1},
        "MJX47": {'52653100': 2, '52650105': 2, '51653200': 6, '51652700': 3, '51652900': 4, '51654202': 1,
                  '52652327': 1, '51654026': 2, '52652800': 1, '51654025': 1, '52650109': 3, '52654003': 1,
                  '52652923': 1, '52652924': 1, '51652924': 1, '52652328': 1, '51652328': 1, '51650400': 2,
                  '52650203': 4, '51650105': 2, '52653129': 5, '51653223': 1, '52652324': 1, '51652827': 1,
                  '52653223': 1, '51653126': 2, '52654002': 3, '52653201': 1, '52653000': 1, '51652701': 2,
                  '52652701': 6, '51653100': 1, '51650100': 2, '51654224': 1, '51653225': 1, '52653125': 1,
                  '52650106': 2, '51654003': 1, '52654201': 1, '52654000': 1, '51652929': 1, '51652801': 1,
                  '51653101': 1, '52653122': 2, '52654226': 1, '51653201': 1, '52654027': 1, '52654004': 1,
                  '52652801': 1, '51652923': 3, '52652823': 2, '51654200': 1, '52652900': 2, '51650502': 1,
                  '51650000': 5, '51653124': 2, '52652301': 3, '51652922': 1, '52654324': 1, '51652825': 1,
                  '52652922': 1, '52653001': 1, '52652927': 1, '52653023': 1, '51652828': 2, '51650500': 1,
                  '51652926': 1, '52652722': 1},
        "MJX48": {'52652922': 1, '51650100': 8, '52650104': 6, '52650107': 1, '51653123': 1, '52650103': 5,
                  '52650109': 3, '51650103': 2, '52652323': 1, '52652800': 3, '51653223': 3, '51654025': 1,
                  '51652823': 1, '51653100': 4, '52653101': 2, '51650500': 2, '51654321': 2, '52653131': 1,
                  '51650400': 2, '52654201': 1, '52652824': 1, '51653126': 1, '51652827': 2, '51652929': 1,
                  '51652328': 1, '52653129': 3, '51650000': 8, '51654202': 1, '51654024': 1, '52654200': 1,
                  '52650200': 1, '51652900': 5, '52650522': 1, '51652923': 1, '52653226': 2, '52653022': 1,
                  '51652302': 2, '51650521': 1, '52652722': 2, '53650000': 3, '51652801': 2, '51654325': 1,
                  '51652301': 4, '51653200': 1, '52650105': 4, '51652323': 1, '51653224': 1, '51650200': 2,
                  '52650203': 1, '51650106': 1, '51654003': 3, '51653226': 1, '51652701': 1, '51653221': 1,
                  '51650522': 1, '51653000': 2, '51650105': 2, '52650106': 2, '52654002': 1, '52654026': 1,
                  '51652800': 2, '51652901': 1, '52650204': 2, '51652300': 3, '51654027': 1, '51654326': 1,
                  '51653201': 1, '51652824': 2, '52652701': 1, '51654000': 1, '52650000': 1, '51654200': 1,
                  '52654321': 2, '51654023': 1, '51654021': 1, '52650100': 1},
        "MJX96": {'52659010': 3, '52659002': 4, '51650000': 8, '52659001': 12, '51654200': 1, '51659004': 18,
                  '51659010': 19, '52650100': 8, '52659008': 1, '52659004': 2, '51659001': 6, '51659009': 8,
                  '51659008': 24, '51659007': 3, '52654200': 2, '52659003': 2, '51650500': 12, '51650100': 4,
                  '52650500': 6, '51659006': 6, '51659003': 27, '52659007': 2, '52659006': 2, '51659002': 15,
                  '52659005': 2},

    }

    def __init__(self, conf: EntryDaoConf):
        self.check_branch = conf.args.get('check_branch', False)  # 是否排除分支机构
        self.check_status = conf.args.get('check_status', False)  # 是否通过登记状态排除
        self.important_interval_days = conf.args.get('important_interval_days', 30)  # 重要公司调度间隔
        self.check_geti = conf.args.get('check_geti', False)  # 个体户是否调度

        super().__init__(entity_class=Credit, conf=conf)

    def weight(self, entry: Entry, inst_name: str) -> Tuple[int, str]:
        weight, reason = super().weight(entry, inst_name)

        entry.info['province'] = PROVINCE_CODES.get(entry.word[2:4], '')
        entry.info['city'] = CITY_CODES.get(entry.word[2:6], '')

        if weight == 0 or reason == PLATFORM_REASON:
            return weight, reason

        reg_status = entry.info.get('reg_status') or entry.info.get('status') or ''

        if self.check_branch:
            if entry.info.get('is_branch', False):
                return 0, ''
        if self.check_status:
            if '销' in reg_status:
                return 0, ''
        if self.check_geti:
            if entry.word.startswith('92') or entry.word.startswith('93'):
                return 0, ''

        if '销' in reg_status:
            weight = max(weight - 700, 5)

        # 1: top100w-pv  2: media 1w  3: manual 100  default 0
        if weight < 800 and entry.info.get('important_type', 0) > 0:
            inst = entry.get_inst(inst=inst_name, init=True)
            if inst.last_schedule_ts + self.important_interval_days * 86400 < cur_ts_sec():
                weight, reason = 810, 'important'

        # 非工商作为股东  略微提升权重
        if weight < 800 and not entry.word.startswith('9'):
            ent_type, eid = id_center_query(credit_no=entry.word)
            if ent_type == EntityType.ORG:
                cg: CompanyGraph = company_graph_dao.get(value=eid, field='company_id')
                if cg and cg.deleted == 0:
                    if equity_ratio_dao.get(value=cg.cgid, field='shareholder_graph_id'):
                        weight, reason = min(799, weight + 50), 'as_investor'

        return weight, reason

    def feedback(self, eventlog: Eventlog, entry: Entry) -> bool:
        if not super().feedback(eventlog, entry):
            return False
        credit_code = eventlog.selector.word
        if eventlog.code == EventlogCode.SUCCESS:
            pass
            # org_code_head = credit_code[8:13]
            # if self.has_clue and org_code_head in self.STEP_MAP:
            #     area_code_dic = self.STEP_MAP[org_code_head]
            #     for incr in range(1, 5):
            #         for _ in range(5):
            #             area_code = random.choices(list(area_code_dic.keys()), list(area_code_dic.values()))[0]
            #             credit_code_new = credit_code_incr(area_code + credit_code[8:], incr, validation=False)
            #             if not credit_code_new:
            #                 continue
            #             if entry2 := self.create_entry(word=credit_code_new, clue=True):
            #                 inst = entry2.get_inst(eventlog.selector.inst_name, init=True)
            #                 inst.reason = 'credit_step'
            #                 ret = self.update_entry(entry2, inst_name=eventlog.selector.inst_name)
            #                 logger.info(f'new clue {credit_code_new} {entry2} ret={ret}')
        elif eventlog.code == EventlogCode.SEARCH_EMPTY:
            if self.has_clue and entry.flag == EntryFlag.CLUE:
                org_code_head = credit_code[8:13]
                if org_code_head in self.STEP_MAP:
                    area_code_dic = self.STEP_MAP[org_code_head]
                    for area_code in area_code_dic.keys():
                        for inc in range(1, 10):
                            credit_code_new = credit_code_incr(area_code + credit_code[8:], inc, validation=False)
                            if not credit_code_new:
                                continue
                            if entry2 := self.get_by_word(credit_code_new):
                                if entry2.flag in (EntryFlag.BAD_CLUE, EntryFlag.ENTRY):
                                    entry.flag = EntryFlag.BAD_CLUE
                                    logger.warning(f'set bad clue {entry.word} by {entry2.word} {eventlog.event_id}')
                                    break
                        if entry.flag == EntryFlag.BAD_CLUE:
                            break
        return True
