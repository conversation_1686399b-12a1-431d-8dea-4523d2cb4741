# encoding=utf8
import logging
import re
from datetime import datetime, timed<PERSON>ta
from typing import <PERSON>ple
from apps.octopus.entries.entry import Entry
from apps.octopus.entries.credit import EntryCreditDao
from apps.octopus.core.conf import EntryDaoConf
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from libs.dt import to_datetime

logger = logging.getLogger(__file__)


class EntryCreditCODSDao(EntryCreditDao):
    def __init__(self, conf: EntryDaoConf):
        self.organization_info_dao = MySQLDao(
            **ConstantProps.PROPS_GS_OUTER_RW,
            db_tb_name='prism.organization_info',
            pk_name='id',
            entity_class=None,
        )
        super().__init__(conf=conf)

    def weight(self, entry: Entry, inst_name: str) -> Tuple[int, str]:
        weight, reason = super().weight(entry, inst_name)

        # 自然流量调整
        if weight < 800:
            o = self.organization_info_dao.get(value=entry.word, field='unified_social_credit_code')
            address = (o or {}).get('address', '')
            reg_institute = (o or {}).get('registration_authority', '')
            legal_person = (o or {}).get('legal_person', '')
            registration_date = (o or {}).get('registration_date', '')
            expiry_date = (o or {}).get('expiry_date', '')
            registered_capital = (o or {}).get('registered_capital', '')

            # 缺失关键字段
            if o and not (address and registered_capital and reg_institute and legal_person and registration_date and expiry_date):
                weight, reason = min(800, weight + 20), 'missing_important_field'

            # 事业单位和律所降权
            if entry.word.startswith('31') or entry.word.startswith('12'):
                weight = max(100, weight - 50)

            # expiry_date 2024-05-28 至 2029-05-28
            mo = re.search(r'(\d{4}-\d{2}-\d{2})$', expiry_date)
            if mo:
                expiry_dt = to_datetime(mo.group(1))
                if datetime.now() + timedelta(days=-180) < expiry_dt < datetime.now() + timedelta(days=30):
                    weight, reason = min(800, weight + 300), 'expiry_date'

        return weight, reason


def main():
    from libs.log2 import setup_logger
    from apps.octopus.utils.conf_manager import OctopusConfManager
    logger = setup_logger()
    manager = OctopusConfManager(json_conf_name='cods.credit.json', reload_conf_interval_sec=0)
    octopus_conf = manager.get_by_name(entry_name='credit', inst_name='cods')
    entry_dao: EntryCreditCODSDao = octopus_conf.entry_dao.obj
    o = entry_dao.get_by_word('11350100784507757L')
    logger.info(f'{o.word} weight={entry_dao.weight(o, "cods")}')


if __name__ == '__main__':
    main()
