# encoding=utf8
import logging
from typing import Tuple
from apps.octopus.core.constants import PLATFORM_REASON
from apps.octopus.entries.entry import Entry
from apps.octopus.entries.credit import EntryCreditDao
from apps.octopus.core.conf import EntryDaoConf

logger = logging.getLogger(__file__)


class EntryCreditChinaCharityDao(EntryCreditDao):
    def __init__(self, conf: EntryDaoConf):
        super().__init__(conf=conf)

    def weight(self, entry: Entry, inst_name: str) -> Tuple[int, str]:
        weight, reason = super().weight(entry, inst_name)
        if 0 < weight < 800:
            inst = entry.get_inst(inst=inst_name, init=True)
            # 搜索无结果，且未抓取成功， 且无收录标记的 不调度
            if inst and inst.last_empty_ts > 0 and inst.last_success_ts == 0 and not entry.info.get('is_charity', False):
                weight = 0
        return weight, reason


if __name__ == '__main__':
    from libs.log2 import setup_logger
    from apps.octopus.utils.conf_manager import OctopusConfManager

    logger = setup_logger()
    manager = OctopusConfManager(json_conf_name='china_charity.credit.json', reload_conf_interval_sec=0)
    octopus_conf = manager.get_by_name(entry_name='credit', inst_name='china_charity')
    entry_dao: EntryCreditChinaCharityDao = octopus_conf.entry_dao.obj
    o = entry_dao.get_by_word('51440111MJK9952426')
    logger.info(f'{o.word} weight={entry_dao.weight(o, "china_charity")}')
