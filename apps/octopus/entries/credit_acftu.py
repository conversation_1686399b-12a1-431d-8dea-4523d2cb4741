# encoding=utf8
import logging
from typing import Tuple
from apps.octopus.core.constants import PLATFORM_REASON
from apps.octopus.entries.entry import Entry
from apps.octopus.entries.credit import EntryCreditDao
from apps.octopus.core.conf import EntryDaoConf
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps

logger = logging.getLogger(__file__)


class EntryCreditACFTUDao(EntryCreditDao):
    def __init__(self, conf: EntryDaoConf):
        self.organization_info_dao = MySQLDao(
            **ConstantProps.PROPS_GS_OUTER_RW,
            db_tb_name='prism.organization_info',
            pk_name='id',
            entity_class=None,
        )
        super().__init__(conf=conf)

    def weight(self, entry: Entry, inst_name: str) -> Tuple[int, str]:
        weight, reason = super().weight(entry, inst_name)

        if entry.word.startswith('81') and 0 < weight < 800:
            o = self.organization_info_dao.get(value=entry.word, field='unified_social_credit_code')
            address = (o or {}).get('address', '')
            if not address:
                return 810, 'miss_address'

        return weight, reason
