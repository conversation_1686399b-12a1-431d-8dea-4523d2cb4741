# encoding=utf8
import json
import random
from typing import Optional, List, Type, Tuple
import re
import logging
from libs.dt import cur_ts_sec
from dao.deps.mysql_dao import MySQLDao
from dao.octopus.platform import PlatformTaskDao, TaskStatus
from apps.octopus.entries.entry import Entry
from apps.octopus.core.conf import EntryDaoConf
from apps.octopus.core.runtime import get_code_offset, set_code_offset, get_entry_lock
from apps.octopus.core.constants import *
from apps.octopus.core.eventlog import Eventlog, EventlogCode
logger = logging.getLogger(__name__)


class EntryDao(MySQLDao):
    def __init__(self, entity_class: Type[Entry], conf: EntryDaoConf):
        if not re.fullmatch(r'[a-zA-Z0-9_]+', entity_class.get_name()):
            raise ValueError('bad entry_name={}'.format(entity_class.get_name()))
        self.has_clue = conf.has_clue
        self.max_try = conf.max_try
        self.code2_prefix = conf.code2_prefix
        self.word_pat = conf.word_pat
        self.weight_range = (100, 800)
        self.clue_interval_sec = 60 * 60 * 24  # 线索选取间隔  权重为 weight_range[1]
        self.max_ts_diff = 60 * 60 * 24 * 365  # 1 year
        self.platform_task_dao = PlatformTaskDao()
        super().__init__(
            db_tb_name=f'{OCTOPUS}.entry_{entity_class.get_name()}',
            pk_name='id',
            entity_class=entity_class,
            batch_size=conf.batch_size,
            **OCTOPUS_DB,
        )
        self.entity_class: Type[Entry] = self.entity_class

    def get_by_word(self, word: str) -> Optional[Entry]:
        return self.get(value=word, field='word')

    def create_entry(self, word: str, clue=True, **info_data) -> Optional[Entry]:
        """
        新插入一个entry
        :return: 已存在则返回None 否则返回构建成功的Entry
        """
        if self.get_by_word(word):
            logger.debug(f'already exists {word} {info_data}')
            return None
        with get_entry_lock(self.entity_class.get_name(), word):
            for i in range(9):
                base10 = 10 ** (CODE_LEN // 2)
                code1 = random.randint(0, base10 - 1) * base10 + random.randint(0, base10 - 1)
                code1 = f'{code1:0{CODE_LEN}d}'
                code2 = self.entity_class.get_code2(word, info_data) + code1
                flag = EntryFlag.CLUE if clue else EntryFlag.ENTRY
                entry: Entry = self.entity_class.from_dict(
                    dict(word=word, info=info_data, code1=code1, code2=code2, flag=flag)
                )
                if not entry:
                    logger.warning(f'error make entry {word} {info_data} {code1} {code2}')
                    return None
                sql = f"insert ignore into {self.db_tb_name} set word=%s, info=%s, code1=%s, code2=%s, flag=%s"
                args = (word, json.dumps(info_data or {}, ensure_ascii=False), code1, code2, flag.value)
                ret = self.mysql_client.insert(sql=sql, args=args)
                if ret > 0:
                    return entry
            logger.warning(f'retry fail insert entry={entry}')
            return None

    def update_entry(self, entry: Entry, inst_name) -> bool:
        if not isinstance(entry, Entry):
            logger.warning(f'bad entry={entry} not Entry')
            return False
        with get_entry_lock(self.entity_class.get_name(), entry.word):
            entry0 = self.get_by_word(entry.word)
            if not entry0:
                logger.warning(f'update entry but not exist {entry.word}')
                return False
            fields, values = [], []
            if entry.flag != entry0.flag:
                fields.append('flag')
                values.append(entry.flag.value)
            if entry.code2 != entry0.code2:
                fields.append('code2')
                values.append(entry.code2)
            changed = False
            for k, v in entry.info.items():
                if entry0.info.get(k, None) != v:
                    changed = True
                    entry0.info[k] = v
            if changed:
                fields.append('info')
                values.append(json.dumps(entry0.info, ensure_ascii=False))
            if inst_name in (entry.inst_map or {}):
                if entry0.inst_map is None:
                    entry0.inst_map = {}
                if entry.inst_map[inst_name] != entry0.inst_map.get(inst_name, None):
                    fields.append('inst_map')
                    entry0.inst_map[inst_name] = entry.inst_map[inst_name]
                    inst_map_s = dict((name, inst.to_dict()) for name, inst in entry0.inst_map.items())
                    values.append(json.dumps(inst_map_s, ensure_ascii=False))
            if entry0.refresh_realtime(entry.info.get('force_realtime_0', False)):
                fields.append('realtime')
                values.append(entry0.realtime)
            if len(fields) == 0:
                logger.warning(f'nothing changed entry={entry} entry0={entry0}')
                return True
            sql_sets = ','.join(f'{field}=%s' for field in fields)
            sql = f"update ignore {self.db_tb_name} set {sql_sets} where word=%s"
            ret = self.mysql_client.execute(sql=sql, args=(*values, entry.word))
            return ret > 0

    def select_realtime(self, inst_name) -> List[Entry]:
        entries = []
        inst_id = self.entity_class.get_inst_id(inst_name)
        sql = f'select * from {self.db_tb_name} where realtime in (%s, %s) limit %s'
        for d in self.mysql_client.select_many(sql=sql, args=(1, inst_id, self.batch_size)):
            entry: Entry = self._to_entity(d)
            if entry is None:
                continue
            if entry.flag not in [EntryFlag.ENTRY, EntryFlag.CLUE, EntryFlag.BAD_CLUE]:
                continue
            if not self.has_clue and entry.flag in [EntryFlag.CLUE, EntryFlag.BAD_CLUE]:
                continue
            inst = entry.get_inst(inst=inst_name, init=True)
            if inst.scheduling or inst.reason == '':
                continue
            entries.append(entry)
        return entries

    def select_normal(self, inst_name) -> List[Entry]:
        if self.code2_prefix == '':
            return self.select_normal_sub(inst_name, 'code1', '')
        else:
            return self.select_normal_sub(inst_name, 'code2', self.code2_prefix)

    def select_normal_sub(self, inst_name, code_field, prefix) -> List[Entry]:
        entries = []
        prefix_sub = sorted(prefix.split('|'))

        code_offset = get_code_offset(self.entity_class.get_name(), inst_name)
        if code_offset is None:
            if code_field == 'code1':
                code_offset = '0' * CODE_LEN
            else:
                code_offset = prefix_sub[0] + '0' * (CODE_LEN + CODE_PREFIX_LEN - len(prefix_sub[0]))
        code_offset_init = code_offset
        select_many_count: int = 0
        while True:
            last_code: str = ''
            sql = f'select * from {self.db_tb_name} where {code_field} > %s limit %s'
            for d in self.mysql_client.select_many(sql=sql, args=(code_offset, self.batch_size)):
                select_many_count += 1
                entry: Entry = self._to_entity(d)
                if entry is None:
                    continue
                last_code = getattr(entry, code_field)
                if all(not last_code.startswith(sub) for sub in prefix_sub):
                    continue
                if entry.realtime != 0:
                    continue
                if entry.flag not in [EntryFlag.ENTRY, EntryFlag.CLUE]:
                    continue
                if not self.has_clue and entry.flag == EntryFlag.CLUE:
                    continue
                inst = entry.get_inst(inst=inst_name, init=True)
                if inst.scheduling:
                    continue
                entries.append(entry)

            reset_offset = False

            if last_code == '':
                reset_offset = True

            next_sub: str = ''
            for sub in prefix_sub:
                if last_code.startswith(sub):
                    code_offset = last_code
                    break
                if next_sub == '' and sub > last_code:
                    next_sub = sub
            else:  # code_offset 选取第一个 >的sub，如果不存在 则重置offset为初始值 并退出
                if next_sub == '':
                    reset_offset = True
                else:
                    code_offset = next_sub + '0' * (CODE_LEN + CODE_PREFIX_LEN - len(next_sub))

            if reset_offset:
                # 本次没有选取到数据 则重置offset为初始值 并退出
                if code_field == 'code1':
                    code_offset = '0' * CODE_LEN
                else:
                    code_offset = prefix_sub[0] + '0' * (CODE_LEN + CODE_PREFIX_LEN - len(prefix_sub[0]))
                break

            if len(entries) >= int(self.batch_size * 0.9):
                break

        logger.info(f'select count {len(entries)}/{select_many_count}')
        set_code_offset(self.entity_class.get_name(), inst_name, code_offset)
        logger.info(f'code offset {code_offset_init} -> {code_offset}')
        return entries

    def feedback(self, eventlog: Eventlog, entry: Entry) -> bool:
        # entry
        inst = entry.get_inst(inst=eventlog.selector.inst_name, init=False)
        if not inst:
            logger.warning(f'no inst {eventlog.event_id} {entry}')
            return False
        inst.meta = eventlog.selector.meta  # 直接覆盖

        platform_id = inst.inst_info.get('platform_id', None)

        if eventlog.code == EventlogCode.SUCCESS:
            if not inst.success(search_empty=False):
                logger.warning(f'error success {eventlog.event_id} {inst}')
                return False
            if entry.flag != EntryFlag.ENTRY:
                if not self.has_clue:
                    logger.warning(f'error inst not has_clue {eventlog.event_id} {inst}')
                    return False
                entry.flag = EntryFlag.ENTRY
                entry.set_info('force_realtime_0', True)  # 临时实现、clue->entry clear realtime
                ret = entry.reload_info()
                logger.info(f'clue->entry {eventlog.event_id} reload_info ret={ret}')
            if platform_id and eventlog.selector.reason == PLATFORM_REASON:
                inst.inst_info['platform_id'] = None
                self.platform_task_dao.set_task(platform_id, TaskStatus.SUCCESS, record=f'成功 {eventlog.event_id}')
        elif eventlog.code == EventlogCode.SEARCH_EMPTY:
            if not inst.success(search_empty=True):
                logger.warning(f'error success {eventlog.event_id} {inst}')
                return False
            if platform_id and eventlog.selector.reason == PLATFORM_REASON:
                inst.inst_info['platform_id'] = None
                self.platform_task_dao.set_task(platform_id, TaskStatus.FAILED, record=f'搜索无结果 {eventlog.event_id}')
            # 这里没有CLUE的过期逻辑 目前看来适合放在离线分析
        elif eventlog.code in [EventlogCode.TIMEOUT, EventlogCode.FAIL, EventlogCode.GIVE_UP]:
            max_try = self.max_try
            if eventlog.selector.reason != DEFAULT_REASON:
                # max_try *= 2
                pass
            if eventlog.code == EventlogCode.GIVE_UP:
                if eventlog.selector.reason != PLATFORM_REASON:
                    max_try = 0
            if not inst.fail(max_try=max_try):
                logger.warning(f'error fail {eventlog.event_id} {inst}')
                return False
            if inst.try_id != 0:
                inst.reason = eventlog.selector.reason
            else:
                if platform_id and eventlog.selector.reason == PLATFORM_REASON:
                    inst.inst_info['platform_id'] = None
                    self.platform_task_dao.set_task(platform_id, TaskStatus.FAILED, record=f'重试后失败 {eventlog.event_id}')
        else:
            logger.warning(f'bad code {eventlog.code} {eventlog.event_id}')
            return False
        return True

    def weight(self, entry: Entry, inst_name: str) -> Tuple[int, str]:
        if not re.fullmatch(self.word_pat, entry.word):
            return 0, ''

        inst = entry.get_inst(inst=inst_name, init=True)

        if entry.flag == EntryFlag.ENTRY:
            if inst.try_id > 0:
                if inst.reason == '' or inst.reason == DEFAULT_REASON:
                    return 950 + inst.try_id, DEFAULT_REASON
                elif inst.reason == PLATFORM_REASON:
                    return 990 + inst.try_id, PLATFORM_REASON
                else:
                    return 960 + inst.try_id, inst.reason
            elif inst.reason != '':
                if inst.reason == PLATFORM_REASON:
                    return 990, PLATFORM_REASON
                else:
                    return 920, inst.reason
            else:  # 普通entry调度
                # 抓取成功/搜索无结果/抓取失败
                # 对于遍历来讲，保证素有entry都过一遍
                # 最近一次是搜索无结果：默认场景是需要降低权重，特殊场景需要增加权重
                # 最近一次是失败：默认场景是需要降低权重，特殊场景需要增加、降低权重
                last_max_ts = max(inst.last_empty_ts, inst.last_success_ts)  # , inst.last_fail_ts
                ts_ratio = min(cur_ts_sec() - last_max_ts, self.max_ts_diff) / self.max_ts_diff
                weight = int(ts_ratio * (self.weight_range[1]-self.weight_range[0]) + self.weight_range[0])
                weight = min(weight, self.weight_range[1] - 1)
                return weight, DEFAULT_REASON
        elif entry.flag == EntryFlag.CLUE or entry.flag == EntryFlag.BAD_CLUE:
            if inst.last_success_ts > 0 and inst.reason in ['', DEFAULT_REASON, CLUE_REASON]:
                logger.warning(f'last_success_ts > 0 but clue {entry} {inst_name}')
                return 0, ''
            if inst.try_id > 0:  # 重试
                if inst.reason == '' or inst.reason == CLUE_REASON:
                    return 970 + inst.try_id, CLUE_REASON
                elif inst.reason == PLATFORM_REASON:
                    return 990 + inst.try_id, PLATFORM_REASON
                else:
                    return 980 + inst.try_id, inst.reason
            elif inst.reason != '':
                if inst.reason == PLATFORM_REASON:
                    return 990, PLATFORM_REASON
                else:
                    return 940, inst.reason
            else:  # 普通clue调度
                if cur_ts_sec() - inst.last_empty_ts < self.clue_interval_sec:
                    return 0, ''
                else:
                    return self.weight_range[1], CLUE_REASON
        else:
            logger.info(f'bad flag {entry}')
            return 0, ''


def main():
    from libs.log2 import setup_logger
    from apps.octopus.utils.conf_manager import OctopusConfManager
    global logger
    logger = setup_logger()
    manager = OctopusConfManager(json_conf_name='china_npo.credit.json', reload_conf_interval_sec=0)
    octopus_conf = manager.get_by_name(entry_name='credit', inst_name='china_npo')
    entry_dao: EntryDao = octopus_conf.entry_dao.obj
    logger.info(f'{entry_dao}')
    # entry = entry_dao.get_by_word('5110000050000012XQ')
    for entry in entry_dao.select_realtime('china_npo'):
        ret = entry_dao.weight(entry, 'china_npo')
        logger.info(f'{entry.word} {ret}')


if __name__ == '__main__':
    main()
