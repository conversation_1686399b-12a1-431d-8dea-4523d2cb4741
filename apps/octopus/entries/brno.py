# encoding=utf8
import logging
import re
from typing import <PERSON>ple
from datetime import datetime, timedelta
from libs.dt import to_date, date2str
from gslib.brno import get_brno
from dao.company_hk import CompanyHkDao, CompanyHk
from apps.octopus.core.constants import *
from apps.octopus.entries.entry_dao import EntryDao
from apps.octopus.entries.entry import Entry, EntryFlag
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.octopus.core.conf import EntryDaoConf
from dao.hk.hk_company_payed_require_report import HKCompanyPayedRequireReportDao

logger = logging.getLogger(__file__)
hk_dao = CompanyHkDao()
payed_report_dao = HKCompanyPayedRequireReportDao()


class Brno(Entry):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @staticmethod
    def valid_word(word) -> bool:
        return get_brno(word, offset=0) == word

    @staticmethod
    def get_code2(word, info: dict) -> str:
        # 企业状态是否正常 正常0 不正常1
        b0 = '1'
        reg_status = info.get('status', '')
        if reg_status == '仍注册':
            b0 = '0'
        if '恢复活动' in reg_status:
            b0 = '0'
        return f'{b0}00000'

    @staticmethod
    def get_inst_id(inst_name):
        if inst_name == 'hk':
            return 11
        return 10

    def reload_info(self, ) -> bool:
        """
        更新info信息 会导致选取性能变差，默认不更新 返回信息是否被更新 外部逻辑是每间隔时间N更新一次
        :return:
        """
        if 'company_num' not in self.info:
            hk: CompanyHk = hk_dao.get(value=self.word, field='br_num')
            if hk:
                self.set_info(k='company_num', v=hk.company_num)
                self.set_info(k='name', v=hk.name_cn_s)
                self.set_info(k='establish_date', v=date2str(to_date(hk.estiblish_time)))
                self.set_info(k='status', v=hk.reg_status_s)
                self.code2 = self.get_code2(self.word, self.info) + self.code1
                return True
        return False


class EntryBrnoDao(EntryDao):
    def __init__(self, conf: EntryDaoConf):
        super().__init__(entity_class=Brno, conf=conf)
        self.clue_interval_sec = 60 * 60 * 3

    def weight(self, entry: Entry, inst_name: str) -> Tuple[int, str]:
        weight, reason = super().weight(entry, inst_name)
        if weight == 0:
            return weight, reason

        if reason == DEFAULT_REASON:
            status = entry.info.get('status', '')
            if '已告解散' in (status or ''):
                weight = max(self.weight_range[0], weight - 400)
            else:
                establish_date = entry.info.get('establish_date')
                date1 = (datetime.now() - timedelta(days=1)).date()
                date2 = (datetime.now() - timedelta(days=15)).date()
                if establish_date and date2 < to_date(establish_date) < date1:
                    ret = hk_dao.mysql_client.select(
                        sql="""select hk_registered_office_new.registered_office as office from prism.company_hk 
                                            left join prism.hk_registered_office_new on company_hk.id=hk_registered_office_new.hk_id
                                            where company_hk.br_num=%s""", args=(entry.word,))
                    if ret is None or ret.get('office') is None or ret.get('office') == '':
                        weight = max(self.weight_range[0], weight + 200)
                        reason = 'miss_addr'
        return weight, reason

    def feedback(self, eventlog: Eventlog, entry: Entry) -> bool:
        if not super().feedback(eventlog, entry):
            return False
        inst = entry.get_inst(inst=eventlog.selector.inst_name, init=True)
        gid = inst.inst_info.get('gid', None)
        if eventlog.code == EventlogCode.SUCCESS:
            if eventlog.selector.reason == 'hk_payed_report' and gid:
                ret = payed_report_dao.set_task(gid=gid, status=1)
                logger.info(f'set task status=1 {eventlog.event_id} {gid} ret={ret}')
            for inc in range(1, 10):
                br_no = get_brno(entry.word, inc)
                if entry2 := self.create_entry(word=br_no, clue=True):
                    inst = entry2.get_inst(eventlog.selector.inst_name, init=True)
                    inst.reason = 'brno_step'
                    ret = self.update_entry(entry2, inst_name=eventlog.selector.inst_name)
                    logger.info(f'new clue {br_no} {entry2} ret={ret}')
        elif eventlog.code == EventlogCode.SEARCH_EMPTY:
            if eventlog.selector.reason == 'hk_payed_report' and gid:
                payed_report_dao.set_task(gid=gid, status=3)
                logger.info(f'set task status=3 {eventlog.event_id} {gid}')
            if entry.flag == EntryFlag.CLUE:
                if re.fullmatch(r'\d{8}', entry.word) and int(entry.word) < 76181305:
                    entry.flag = EntryFlag.BAD_CLUE
                    logger.warning(f'set bad clue {entry.word} old word, {eventlog.event_id}')
                elif entry.word.startswith('F') or entry.word.startswith('C'):
                    entry.flag = EntryFlag.BAD_CLUE
                    logger.warning(f'set bad clue {entry.word} old word, {eventlog.event_id}')
                else:
                    for inc in range(1, 100):
                        br_no = get_brno(entry.word, inc)
                        if entry2 := self.get_by_word(br_no):
                            if entry2.flag in (EntryFlag.BAD_CLUE, EntryFlag.ENTRY):
                                entry.flag = EntryFlag.BAD_CLUE
                                logger.warning(f'set bad clue {entry.word} by {entry2.word} {eventlog.event_id}')
                                break
        elif eventlog.code in [EventlogCode.GIVE_UP, EventlogCode.FAIL]:
            if eventlog.selector.reason == 'hk_payed_report' and gid and inst.try_id == 0:
                payed_report_dao.set_task(gid=gid, status=3)
                logger.info(f'set task status=3 {eventlog.event_id} {gid}')
        return True


if __name__ == '__main__':
    from libs.log2 import setup_logger
    from apps.octopus.utils.conf_manager import OctopusConfManager
    logger = setup_logger()
    manager = OctopusConfManager(json_conf_name='hk.brno.json', reload_conf_interval_sec=0)
    octopus_conf = manager.get_by_name(entry_name='brno', inst_name='hk')
    entry_dao: EntryBrnoDao = octopus_conf.entry_dao.obj
    o = entry_dao.get_by_word('77373294')
    logger.info(f'{o.word} weight={entry_dao.weight(o, "hk")}')
