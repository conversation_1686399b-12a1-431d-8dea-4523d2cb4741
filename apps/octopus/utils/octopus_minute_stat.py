# encoding=utf8
import decimal
import logging
from threading import Lock
from libs.env import ConstantProps
from libs.dt import cur_ts_sec
from dao.deps.mysql_dao import MySQLDao
from apps.octopus.core.eventlog import Eventlog
from apps.octopus.core.constants import OCTOPUS

logger = logging.getLogger(__name__)


# 抓取量统计，放到feedback入口处
class MinuteStatDao(MySQLDao):
    def __init__(self, **kwargs):
        self.lock = Lock()
        self.stat = {}  # (entry_name, inst_name, code, minute) -> count
        self.stat_ts = 0  # 上次计算时间  每N秒计算一次
        for k, v in ConstantProps.PROPS_GS_TEST.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', f'{OCTOPUS}.minute_stat_2')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', None)
        super().__init__(**kwargs)

    def write(self, eventlog: Eventlog):
        with self.lock:
            entry_name = eventlog.selector.entry_name
            inst_name = eventlog.selector.inst_name
            code = int(eventlog.code)
            reason = eventlog.selector.reason
            ts = eventlog.spider.receive_ts or eventlog.selector.send_ts
            minute = ts // 60
            key = (entry_name, inst_name, code, reason, minute)
            if key not in self.stat:
                self.stat[key] = {'count_all': 0, 'count_clue': 0, 'count_changed': 0}
            self.stat[key]['count_all'] += 1
            if eventlog.selector.clue:
                self.stat[key]['count_clue'] += 1
            if len(eventlog.spider.ab_info) > 0 and not eventlog.selector.clue:
                self.stat[key]['count_changed'] += 1

            if cur_ts_sec() - self.stat_ts >= 10:
                self.stat_ts = cur_ts_sec()
                for key in self.stat.keys():
                    if self.stat[key]['count_all'] == 0:
                        continue
                    entry_name, inst_name, code, reason, minute = key
                    sql = f'''insert into {self.db_tb_name} (entry_name, inst_name, code, reason, stat_minute, count_all, count_clue, count_changed) values (%s,%s,%s,%s,%s,%s,%s,%s) 
                    on duplicate key update count_all=count_all+%s, count_clue=count_clue+%s, count_changed=count_changed+%s'''
                    args = (
                        entry_name, inst_name, code, reason, minute,
                        self.stat[key]['count_all'], self.stat[key]['count_clue'], self.stat[key]['count_changed'],
                        self.stat[key]['count_all'], self.stat[key]['count_clue'], self.stat[key]['count_changed'],
                    )
                    self.mysql_client.execute(sql=sql, args=args)
                    self.stat[key] = {'count_all': 0, 'count_clue': 0, 'count_changed': 0}

    def current_success_ratio(self, entry_name: str, inst_name: str, last_n_minute: int = 10) -> float:
        minute = cur_ts_sec() // 60 - last_n_minute
        sql = f"""select sum(if(code=0, count_all, 0))/sum(count_all) from {self.db_tb_name} where stat_minute > %s and entry_name=%s and inst_name=%s"""
        ret = self.mysql_client.select(sql=sql, args=(minute, entry_name, inst_name))
        if not isinstance(ret, dict) or len(ret) != 1:
            logger.info(f'error ret={ret} {entry_name} {inst_name} {last_n_minute}')
            return 0.0
        v = ret.popitem()[1]
        if not isinstance(v, decimal.Decimal):
            logger.info(f'error ret={ret} {entry_name} {inst_name} {last_n_minute} assuming ok.')
            return 1.0
        return float(v)


if __name__ == '__main__':
    from libs.log2 import setup_logger
    logger = setup_logger()
    dao = MinuteStatDao()
    ret_ = dao.current_success_ratio('brno', 'hk', )
    logger.info(f'ret={ret_}')
