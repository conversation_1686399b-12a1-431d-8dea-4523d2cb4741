# encoding=utf8

import os
import logging
import json
import threading
import time
from typing import Dict, <PERSON><PERSON>, Optional, List
from threading import Lock
from libs.env import get_stack_info
from apps.octopus.core.constants import OCTOPUS_DIR
from apps.octopus.entries import *
from apps.octopus.sender import *
from apps.octopus.core.conf import SchedulerConf

logger = logging.getLogger(__file__)


class OctopusConfManager(object):
    def __init__(self, json_conf_name=None, reload_conf_interval_sec: int = 0):
        """
        :param reload_conf_interval_sec:  重新加载配置时间间隔 0表示不重新加载
        :param json_conf_name:  指定配置文件名  None表示所有配置
        """
        self.reload_conf_interval_sec = reload_conf_interval_sec
        self.json_conf_name = json_conf_name
        self.lock = Lock()
        self.conf_text_dict: Dict[str, str] = {}  # json_conf_name -> text
        self.conf_dict: Dict[<PERSON><PERSON>[str, str], SchedulerConf] = {}  # entry, inst -> conf
        self.run_()
        if self.reload_conf_interval_sec > 0:
            t = threading.Thread(target=self.run, args=(), daemon=True)
            t.start()

    def run_(self):
        for file in os.listdir(f'{OCTOPUS_DIR}/conf'):
            if not file.endswith('.json') or (self.json_conf_name and self.json_conf_name != file):
                continue
            with open(f'{OCTOPUS_DIR}/conf/{file}', 'r') as fp:
                try:
                    conf_text = fp.read()
                    if file in self.conf_text_dict and self.conf_text_dict[file] == conf_text:
                        continue
                    conf = SchedulerConf.from_dict(json.loads(conf_text))
                    entry_dao_class = globals()[conf.entry_dao.clazz]
                    entry_dao_: EntryDao = entry_dao_class(conf.entry_dao)
                    conf.entry_dao.obj = entry_dao_
                    if not entry_dao_.table_exists():
                        logger.warning(f'entry table not exists, exit. {conf}')
                        continue
                    sender_class = globals()[conf.sender.clazz]
                    sender_: Sender = sender_class(entry_dao_, conf.sender, conf.inst_name)
                    conf.sender.obj = sender_

                    with self.lock:
                        self.conf_text_dict[file] = conf_text
                        entry_name, inst_name = entry_dao_.entity_class.get_name(), conf.inst_name
                        self.conf_dict[(entry_name, inst_name)] = conf
                        logger.info(f'successfully load {file} {entry_name} {inst_name} {conf}')
                except Exception as e:
                    logger.warning(f'error load {file} {e} {get_stack_info()}')

    def run(self):
        while True:
            self.run_()
            time.sleep(self.reload_conf_interval_sec)

    def get_by_name(self, entry_name, inst_name) -> Optional[SchedulerConf]:
        with self.lock:
            return self.conf_dict.get((entry_name, inst_name), None)

    def get_exact_one(self) -> Optional[SchedulerConf]:
        with self.lock:
            if len(self.conf_dict) != 1:
                logger.warning(f'bad conf_dict size={len(self.conf_dict)}')
                return None
            return list(self.conf_dict.values())[0]

    def get_by_entry(self, entry_name) -> List[SchedulerConf]:
        with self.lock:
            conf_list = []
            for (entry_name_, inst_name_), conf in self.conf_dict.items():
                if entry_name == entry_name_:
                    conf_list.append(conf)
            return conf_list


if __name__ == '__main__':
    from libs.log2 import setup_logger
    logger = setup_logger()
    manager = OctopusConfManager()
    logger.info(f"{manager.get_exact_one()}")
