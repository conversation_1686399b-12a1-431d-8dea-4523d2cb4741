# -*- coding: utf-8 -*-

import logging
from typing import List
from apps.octopus.entries.entry import Entry, EntryFlag
from apps.octopus.entries import EntryDao
# from apps.octopus.selector import Selector
from apps.octopus.utils.conf_manager import OctopusConfManager

logger = logging.getLogger(__name__)


class EntryManager(object):
    def __init__(self, conf_manager=None):
        if conf_manager:
            self.conf_manager = conf_manager
        else:
            self.conf_manager = OctopusConfManager(reload_conf_interval_sec=10)

    def inst_immediate(
            self,
            entry_name: str,
            entry_word: str,
            inst_name: str,
            reason: str = 'immediate',
            inst_clue_none: bool = False,
            params: dict = None,  # params 放inst.meta
            diff_params_ret_fail: bool = True,  # params是否允许覆盖
            ignore_latest_search_empty: bool = True,  # 最后一次是搜索无结果 则不调度
    ) -> bool:
        conf = self.conf_manager.get_by_name(entry_name, inst_name)
        if not conf:
            logger.warning(f'no conf for entry_name={entry_name} inst_name={inst_name}')
            return False
        entry_dao: EntryDao = conf.entry_dao.obj
        entry: Entry = entry_dao.get_by_word(entry_word)
        if not entry:
            # create it anyway
            entry = entry_dao.create_entry(word=entry_word, clue=True)
            if not entry:
                logger.warning(f'{entry_name}:{entry_word} fail to create entry')
                return False
        if entry.flag not in [EntryFlag.CLUE, EntryFlag.ENTRY, EntryFlag.BAD_CLUE]:
            logger.warning(f'{entry_name}:{entry_word} bad entry_flag={entry.flag}')
            return False
        if entry.flag == EntryFlag.CLUE and not entry_dao.has_clue:
            # 新增抓取 but抓取实例不符合
            logger.warning(f'{entry_name}:{entry_word} clue but bad inst')
            return False
        if not inst_clue_none and entry_dao.has_clue is None:
            logger.warning(f'{entry_name}:{entry_word} inst is not ok')
            return False
        inst = entry.get_inst(conf.inst_name, init=True)
        if inst.scheduling:
            logger.info(f'{entry_name}:{entry_word} inst={conf.inst_name} at scheduling')
            return False
        if inst.reason != '':
            logger.info(f'{entry_name}:{entry_word} inst={conf.inst_name} at reason={inst.reason}')
            return False
        if ignore_latest_search_empty:
            if inst.last_empty_ts > 0 and inst.last_empty_ts > inst.last_schedule_ts and inst.last_success_ts == 0:
                logger.info(f'{entry_name}:{entry_word} inst={conf.inst_name} ignore_latest_search_empty')
                return False

        # if entry_dao.weight(entry, inst_name)[0] == 0:
        #     # 选取器 返回不符合选取规则
        #     logger.warning(f'{entry_name}:{entry_word} select weight 0')
        #     return False
        inst.reason = reason
        if params and isinstance(params, dict):
            for k, v in params.items():
                if k == 'platform_id':
                    inst.inst_info[k] = v  # 兼容
                if diff_params_ret_fail:
                    v0 = inst.meta.get(k, None)
                    if v0 is not None and v0 != v:
                        logger.warning(f'{entry_name}:{entry_word} {inst_name} error meta k={k}')
                        return False
                inst.meta[k] = v
        if not entry_dao.update_entry(entry, conf.inst_name):
            logger.warning(f'{entry_name}:{entry_word} error update entry={entry} {inst_name}')
            return False
        return True

    def entry_immediate(self, entry_name: str, entry_word: str, **kwargs) -> List[str]:
        conf_list = self.conf_manager.get_by_entry(entry_name)
        inst_list = []
        for conf in conf_list:
            if self.inst_immediate(entry_name, entry_word, conf.inst_name, **kwargs):
                inst_list.append(conf.inst_name)
        return inst_list


if __name__ == '__main__':
    from libs.log2 import setup_logger

    logger = setup_logger()
    manager = EntryManager()
    ret = manager.inst_immediate(
        entry_name='credit',
        entry_word='91140421MA0L4R1Q0M',
        inst_name='gds',
    )
    logger.info(f'ret={ret}')
