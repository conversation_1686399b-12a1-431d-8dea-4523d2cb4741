<html>
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
  <script src="../static/vue.js"></script>
  <script src="../static/axios.js"></script>
  <style>
    td {
      text-align: center;
    }
  </style>
  <title>『OCTOPUS』调度记录统一查询平台</title>
</head>
<body>
<div id="app">
  <p>请输入查询词****** ${entry_name}:${word} …… </p>
  <p>查询全部记录请打钩（否则只显示最近30条记录）<label> <input type="checkbox" v-model="show_all" /> </label> </p>
  <p>仅查询工商调度记录请打钩<label> <input type="checkbox" v-model="only_gsxt" /> </label> </p>
  <label>
    <input type="text" v-model="user_input" style="width: 300px; height: 50px;border-radius: 10px"/>
  </label>
  &nbsp;&nbsp;
  <button style="height: 50px; width: 50px; border-radius: 10px" @click="getDetail">查询</button>


  <table border="1">
    <tr>
      <th>调度时间</th>
      <th>调度类型</th>
      <th>调度单元</th>
      <th>抓取实例</th>
      <th>调度状态</th>
      <th>爬虫返回</th>
      <th>调度原因</th>
      <th>是否变更</th>
      <th>爬虫耗时</th>
      <th>调度权重</th>
      <th>重试次数</th>
      <th>详情</th>
    </tr>
    <tr v-if="eventlog_list.length !== 0" v-for="item in eventlog_list">
      <td>[[ item.octopus_time ]]</td>
      <td>[[ item.entry_name ]]</td>
      <td>[[ item.word ]]</td>
      <td>[[ item.inst_name ]]</td>
      <td>[[ item.status ]]</td>
      <td>[[ item.code ]]</td>
      <td>[[ item.reason ]]</td>
      <td>[[ item.changed ]]</td>
      <td>[[ item.cost ]]</td>
      <td>[[ item.weight ]]</td>
      <td>[[ item.try_id ]]</td>
      <td><a :href="'octopus/detail/' +  item.link" target="_blank">查看</a></td>
    </tr>
  </table>
</div>
<script>
  const App = {
    data() {
      return {
        user_input: '',
        eventlog_list: [],
        show_all: 0,
        only_gsxt: 0,
      };
    },
    delimiters: ["[[", "]]"],
    methods: {
      getDetail(event) {
        this.eventlog_list = [];
        if (this.user_input !== "") {
          axios.get("/octopus/" + this.user_input + "?show_all=" + this.show_all + "&only_gsxt=" + this.only_gsxt)
              .then((resp) => {
                if (resp.data.alert != null) {
                  alert(resp.data.alert)
                } else {
                  // this.meta = resp.data;
                  this.eventlog_list = resp.data.eventlog_list;
                }
              })
              .catch((err) => {
                console.log(err);
              });
        }
      },
    },
  };
  const app = Vue.createApp(App);
  app.mount("#app");
</script>
</body>
</html>
