# encoding=utf8
from libs.log2 import setup_logger
from dao.npo.npo import Npo, NpoDao
from apps.octopus.utils.conf_manager import OctopusConfManager
from apps.octopus.utils.entry_manager import EntryManager

logger = setup_logger()

manager = OctopusConfManager()
entry_manager = EntryManager()


def main():
    npo_dao = NpoDao(batch_size=2000)
    octopus_conf = manager.get_by_name(entry_name='credit', inst_name='china_npo')
    if not octopus_conf:
        logger.warning('no entry dao')
        return

    for item in npo_dao.scan(scan_key='unified_social_credit_code', start='51120118A04702678U', total=10000000):  #
        item: Npo
        if item.registration_date is None and len(item.unified_social_credit_code or '') == 18:
            ret = entry_manager.inst_immediate(
                entry_name='credit',
                inst_name='china_npo',
                reason='missing_reg_date',
                entry_word=item.unified_social_credit_code,
            )
            logger.info(f'{item.unified_social_credit_code}  ret={ret}')


if __name__ == '__main__':
    main()
