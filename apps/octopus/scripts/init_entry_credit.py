# encoding=utf8

import argparse
from libs.log2 import setup_logger
from libs.dt import to_date, date2str
from dao.company import Company, CompanyDao
from apps.octopus.entries.entry_dao import EntryDao
from apps.octopus.utils.conf_manager import OctopusConfManager
from apps.octopus.utils.entry_manager import EntryManager

logger = setup_logger()

manager = OctopusConfManager()
entry_manager = EntryManager()


def main(args):
    octopus_conf = manager.get_by_name(entry_name='credit', inst_name='china_npo')
    if not octopus_conf:
        logger.warning('no entry dao')
        return
    entry_dao: EntryDao = octopus_conf.entry_dao.obj
    company_dao = CompanyDao()

    if args.start == -1:
        start = company_dao.get_max_pk() - 100
    else:
        start = args.start
    logger.info(f'start={start}')

    for c in company_dao.scan(scan_key='id', start=start, infinite=True, infinite_sleep_secs=30):
        c: Company
        if not c.credit_code or len(c.credit_code) != 18:
            continue
        if not c.source_flag:
            continue
        if 'qyxy' in c.source_flag and c.prop2 is not None and c.prop2 > 0:
            continue
        entry = entry_dao.create_entry(
            word=c.credit_code,
            name=c.name,
            establish_date=date2str(to_date(c.establish_date)),
            cid=c.cid,
            status=c.reg_status,
            reg_number=c.reg_number,
            clue=False,
        )
        logger.info(f'create entry {c.cid} {c.credit_code} {None if not entry else entry.info}')


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='init_entry_credit')
    parser.add_argument('--start', type=int, default=-1, help='start cid')
    main(parser.parse_args())
