# encoding=utf8

import argparse
import re

from libs.log2 import setup_logger
from datetime import datetime, timedelta
from libs.env import ConstantProps
from clients.redis.redis_hash import RedisHash
from dao.npo.npo import Npo, NpoDao
from dao.qxb.enterprise import EnterpriseDao, Enterprise
from apps.octopus.utils.conf_manager import OctopusConfManager
from apps.octopus.utils.entry_manager import EntryManager

logger = setup_logger()
redis_offset = RedisHash(db=3, name='qx_enterprise_npo', **ConstantProps.PROPS_GS_REDIS_ONLINE)
npo_dao = NpoDao()
qx_enterprise_dao = EnterpriseDao(batch_size=2000)
manager = OctopusConfManager(json_conf_name='china_npo.credit.json')
entry_manager = EntryManager()


def process_one(item: Enterprise):
    item: Enterprise
    if not re.match(r'5[1-3]', item.credit_code or ''):
        return
    logger.info(f'get enterprise {item.name} {item.credit_code}')
    if npo_dao.get(value=item.credit_code, field='unified_social_credit_code'):
        logger.info(f'{item.credit_code} already exists')
        return
    ret = entry_manager.inst_immediate(
        entry_name='credit',
        inst_name='china_npo',
        reason='trigger_qx',
        entry_word=item.credit_code,
    )
    logger.info(f'{item.credit_code}  ret={ret}')


def process(items):
    for item in items:
        process_one(item)


def main(args):
    logger.info(f'{args}')

    row_update_time_default = datetime.now() - timedelta(days=args.start_days)

    qx_enterprise_dao.sharding_scan(
        process_fn=process,
        process_workers=args.process_num,
        scan_workers=args.dump_num,
        part_num=args.part_num,
        redis_offset=redis_offset,
        init_offset=row_update_time_default,
        infinite_wait_secs=30,
        scan_key='row_update_time',
        # condition="credit_no regexp '^5[1-3]'",
    )


if __name__ == '__main__':
    from libs.log2 import setup_logger

    ap = argparse.ArgumentParser(description='trigger_china_npo_qxb.py')
    ap.add_argument('--dump-num', type=int, default=4, help='dump线程数')
    ap.add_argument('--process-num', type=int, default=8, help='处理线程数')
    ap.add_argument('--part-num', type=int, default=256, help='part num')
    ap.add_argument('--start-days', type=int, default=6000, help='首次运行 row-update-time开始时间')
    logger = setup_logger()
    main(ap.parse_args())
    # process_one(qx_enterprise_dao.get('95895b40-c2df-11e7-be33-00163e1254b5'))
