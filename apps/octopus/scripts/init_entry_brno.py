# encoding=utf8
from libs.log2 import setup_logger
from libs.dt import to_date, date2str
from dao.company_hk import CompanyHkDao, CompanyHk
from apps.octopus.entries.entry_dao import EntryDao, EntryFlag
from apps.octopus.utils.conf_manager import OctopusConfManager
from apps.octopus.utils.entry_manager import EntryManager

logger = setup_logger()

manager = OctopusConfManager(json_conf_name='hk.brno.json', reload_conf_interval_sec=0)
entry_manager = EntryManager(manager)


# 从hk表导入brno数据
def main():
    #
    hk_dao = CompanyHkDao()
    octopus_conf = manager.get_by_name(entry_name='brno', inst_name='hk')
    if not octopus_conf:
        logger.warning('no entry dao')
        return
    entry_dao: EntryDao = octopus_conf.entry_dao.obj
    for item in hk_dao.scan(start=0, scan_key='id'):
        item: CompanyHk
        if item.br_num == '':
            continue
        entry = entry_dao.get_by_word(item.br_num)
        if entry:
            if entry.flag in [EntryFlag.CLUE, EntryFlag.BAD_CLUE]:
                if entry.reload_info():
                    entry.flag = EntryFlag.ENTRY
                    ret = entry_dao.update_entry(entry, inst_name='hk')
                    logger.info(f'reload info {item.id} {ret} {entry.to_json()}')
        else:
            entry = entry_dao.create_entry(
                word=item.br_num,
                company_num=item.company_num,
                name=item.name_cn_s,
                establish_date=date2str(to_date(item.estiblish_time)),
                status=item.reg_status_s,
                clue=False,
            )
            logger.info(f'create entry {item.id} {None if not entry else entry.to_json()}')


if __name__ == '__main__':
    main()
