# encoding=utf8

import time
import argparse
from libs.log2 import setup_logger
from libs.env import get_stack_info
from datetime import datetime
from chinese_calendar import is_workday
from dao.company import CompanyDao, Company, CompanyGraph, CompanyGraphDao
from dao.hk.hk_company_payed_require_report import HKCompanyPayedRequireReport, HKCompanyPayedRequireReportDao
from dao.company_hk import CompanyHkDao, CompanyHk
from libs.feishu import send_feishu_message_attachments
from apps.octopus.utils.entry_manager import EntryManager, OctopusConfManager

chat_id = 'oc_ff3954e7a265e2b30587f82d401d80f9'
logger = setup_logger()
dao = HKCompanyPayedRequireReportDao()
company_graph_dao = CompanyGraphDao()
company_hk_dao = CompanyHkDao()
company_dao = CompanyDao()

octopus_conf_manager = OctopusConfManager(json_conf_name='hk.brno.json', reload_conf_interval_sec=0)
entry_manager = EntryManager(conf_manager=octopus_conf_manager)


def process(task: HKCompanyPayedRequireReport) -> bool:
    cg: CompanyGraph = company_graph_dao.get(value=task.graph_id, field='graph_id')
    if not cg:
        logger.warning(f'no cg for {task.graph_id} {task}')
        return False
    c: Company = company_dao.get(cg.cid)
    if not c:
        logger.warning(f'no cg for {cg} {task}')
        return False

    for c2 in company_dao.get_many(value=str(c.cid), field='property2'):
        if c2.source_flag and c2.source_flag.startswith('hk_'):
            c = c2
            break
    if not c.source_flag or not c.source_flag.startswith('hk_'):
        logger.warning(f'bad c {c} not hk {task}')
        return False

    # get company_hk
    company_hk: CompanyHk = company_hk_dao.get(value=c.cid, field='company_id')
    if not company_hk or company_hk.br_num == '':
        logger.warning(f'bad c {c} no hk {task}')
        return False
    logger.info(f'task={task} brno={company_hk.br_num}')

    ret = entry_manager.inst_immediate(
        entry_name='brno',
        inst_name='hk',
        reason='hk_payed_report',
        entry_word=company_hk.br_num,
        params={
            'gid': task.graph_id,
        }
    )
    if not ret:
        logger.warning(f'fail {task} {company_hk.br_num}')
        return False
    logger.info(f'task success {task}')
    return True


def main(args):
    logger.info(f'args={args}')
    while True:
        now = datetime.now()
        if not is_workday(now.date()):
            logger.info(f'not at workday {now}, sleep for...')
            time.sleep(3600)
            continue

        if now.hour not in args.hours:
            logger.info(f'not at work hour {now}, sleep for...')
            time.sleep(60)
            continue

        for task in dao.get_tasks():
            logger.info(f'new task {task}')
            try:
                suc = process(task)
                if suc:
                    ret = dao.set_task(task.graph_id, status=4)
                    logger.info(f'set status=4 task={task} ret={ret}')
            except Exception as e:
                logger.warning(f'error process {e} {get_stack_info()}')
                suc = False
            if not suc:
                logger.warning(f'not suc task={task}')
                send_feishu_message_attachments(
                    chat_id=chat_id,
                    ats=['王帮旭', ],
                    text=f'香港付费报告创建抓取任务失败，请查看日志{task}',
                )
            time.sleep(60)  # 间隔60秒做下一个任务

        logger.info(f'next loop, sleep for...')
        time.sleep(60)


def test():
    o = HKCompanyPayedRequireReport.from_dict({
        'graph_id': 3191389347,
        'status': 0,
    })
    ret = process(o)
    logger.info(f'{o} {ret}')


if __name__ == '__main__':
    # test()
    # exit(0)
    parser = argparse.ArgumentParser(description='trigger_hk_payed_report.py')
    parser.add_argument('--hours', nargs='+', type=int, default=[9, 10, 11, 13, 14, 15, 16, 17])
    main(parser.parse_args())
