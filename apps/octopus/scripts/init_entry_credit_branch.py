# encoding=utf8

import argparse
from libs.log2 import setup_logger
from dao.company import Company, CompanyDao
from apps.octopus.entries.entry_dao import EntryDao, Entry
from apps.octopus.utils.conf_manager import OctopusConfManager
from apps.octopus.utils.entry_manager import EntryManager

logger = setup_logger()

manager = OctopusConfManager(json_conf_name='credit_china.credit.json', reload_conf_interval_sec=0)
entry_manager = EntryManager(manager)


def main(args):
    logger.info(f'args={args}')

    octopus_conf = manager.get_by_name(entry_name='credit', inst_name='credit_china')
    if not octopus_conf:
        logger.warning('no entry dao')
        return
    entry_dao: EntryDao = octopus_conf.entry_dao.obj
    company_dao = CompanyDao()

    for c in company_dao.scan(scan_key='id', start=args.start):
        c: Company
        if '分' not in (c.company_org_type or ''):
            continue
        if not c or not c.credit_code or len(c.credit_code) != 18:
            continue
        entry: Entry = entry_dao.get_by_word(c.credit_code)
        if not entry:
            continue
        entry.set_info('is_branch', True, overwrite=True)
        entry_dao.update_entry(entry=entry, inst_name='credit_china')
        logger.info(f'set entry is_branch {entry.word}')


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='init_entry_credit')
    parser.add_argument('--start', type=int, default=-1, help='start cid')
    main(parser.parse_args())
