# encoding=utf8

import argparse
from libs.log2 import setup_logger
from libs.env import ConstantProps
from dao.company import Company, CompanyDao
from dao.deps.mysql_dao import MySQLDao
from apps.octopus.entries.entry_dao import EntryDao, Entry
from apps.octopus.utils.conf_manager import OctopusConfManager
from apps.octopus.utils.entry_manager import EntryManager

logger = setup_logger()

manager = OctopusConfManager(json_conf_name='credit_china.credit.json', reload_conf_interval_sec=0)
entry_manager = EntryManager(manager)

important_dao = MySQLDao(
    db_tb_name='dispatch.company_important',
    **ConstantProps.PROPS_GS_INNER_RW,
    pk_name='id',
    entity_class=None,
)


def main(args):
    logger.info(f'args={args}')

    octopus_conf = manager.get_by_name(entry_name='credit', inst_name='credit_china')
    if not octopus_conf:
        logger.warning('no entry dao')
        return
    entry_dao: EntryDao = octopus_conf.entry_dao.obj
    company_dao = CompanyDao()

    for c in important_dao.scan(scan_key='id', start=args.start):
        c: dict
        important_type = int(c['type'])
        if important_type == 2:
            pass
        elif important_type == 3:
            pass
        elif 100 <= important_type < 200:
            important_type = 1
        else:
            important_type = 0
        c: Company = company_dao.get(c['company_id'])
        if not c or not c.credit_code or len(c.credit_code) != 18:
            continue
        entry: Entry = entry_dao.get_by_word(c.credit_code)
        if not entry:
            continue
        entry.set_info('important_type', important_type)
        entry_dao.update_entry(entry=entry, inst_name='credit_china')
        logger.info(f'set entry important_type {entry.word} {important_type}')


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='init_entry_credit')
    parser.add_argument('--start', type=int, default=-1, help='start cid')
    main(parser.parse_args())
