# encoding=utf8
import argparse
import time
from datetime import datetime, timedelta
from libs.log2 import setup_logger
from dao.channel.nacao_basic import NacaoBasicDao, NacaoBasic
from apps.octopus.utils.conf_manager import OctopusConfManager
from apps.octopus.utils.entry_manager import EntryManager

logger = setup_logger()

manager = OctopusConfManager(json_conf_name='china_npo.credit.json')
entry_manager = EntryManager()


def main(args):
    nacao_dao = NacaoBasicDao(batch_size=2000)
    octopus_conf = manager.get_by_name(entry_name='credit', inst_name='china_npo')
    if not octopus_conf:
        logger.warning('no entry dao')
        return
    for item in nacao_dao.scan(
            scan_key='updated',
            start=datetime.now()-timedelta(days=1),
            infinite=True,
            infinite_sleep_secs=30,
    ):  #
        item: NacaoBasic
        if item.credit_code[:2] not in ['51', '52', '53']:
            continue
        # if entry_dao.get_by_word(word=item.credit_code):
        #     continue
        ret = entry_manager.inst_immediate(
            entry_name='credit',
            inst_name='china_npo',
            reason='trigger_nacao',
            entry_word=item.credit_code,
        )
        logger.info(f'{item.credit_code}  ret={ret}')
        time.sleep(0.5)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='trigger_china_npo_channel.py')
    main(parser.parse_args())
