# encoding=utf8

import argparse
import json
import random
import time
from libs.log2 import setup_logger
from libs.env import get_props_redis
from clients.redis.redis_queue import RedisQueue
from clients.kafka_client import KafkaProducerClient
from libs.dt import cur_ts_sec
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.octopus.core.constants import FEEDBACK_KAFKA, FEEDBACK_TOPIC

logger = setup_logger()


def main(args):
    logger.info('args=%s', args)

    queue = RedisQueue(name='hk_brno', **get_props_redis(inst='redis.tyc.gs'), db=9, use_zset=True)
    kafka_producer = KafkaProducerClient(kafka_topic=FEEDBACK_TOPIC, **FEEDBACK_KAFKA)

    for s in queue.generate():
        d = json.loads(s)
        eventlog: Eventlog = Eventlog.from_dict(d)
        eventlog.spider.receive_ts = cur_ts_sec()

        if random.random() < 0.1:
            logger.info(f'exception, discard {s}')
            continue

        rnd = random.random()
        if rnd < 0.3:
            code = EventlogCode.SUCCESS
        elif rnd < 0.4:
            code = EventlogCode.GIVE_UP
        else:
            code = EventlogCode.FAIL
            eventlog.selector.meta['rnd'] = rnd

        eventlog.code = code
        eventlog.spider.spider_data['rnd'] = rnd

        # 模拟抓取耗时
        time.sleep(0.5)
        eventlog.spider.send_ts = cur_ts_sec()

        s2 = eventlog.to_json()
        ret = kafka_producer.write(message=s2)
        logger.info(f'write {s2} ret={ret}')


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='测试使用的爬虫程序')
    main(parser.parse_args())
