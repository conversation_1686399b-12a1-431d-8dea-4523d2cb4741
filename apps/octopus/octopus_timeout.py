# encoding=utf8

import json
import time
import argparse
from libs.dt import cur_ts_sec
from libs.log2 import setup_logger
from apps.octopus.utils.conf_manager import OctopusConfManager
from apps.octopus.core.runtime import get_many_timeout
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.octopus.sender import Sender


class Main(object):  # only one instance
    def __init__(self, ):
        self.octopus_conf_manager = OctopusConfManager(reload_conf_interval_sec=5)

    def run(self):
        # 单线程   infinite loop
        for loop_id in range(cur_ts_sec(), int(1e20)):
            time.sleep(5.0)
            conf_dict = dict(self.octopus_conf_manager.conf_dict.items())
            for (entry_name, inst_name), scheduler_conf in conf_dict.items():
                logger.info(f'start {loop_id} {entry_name} {inst_name}')
                for s in get_many_timeout(entry_name, inst_name, scheduler_conf.sender.spider_timeout_sec):
                    if scheduler_conf.sender.spider_timeout_sec == 0:
                        # 爬虫不返回eventlog 不用超时控制
                        continue
                    eventlog: Eventlog = Eventlog.from_dict(json.loads(s))
                    if not eventlog:
                        logger.warning(f'error from dict {s}')
                        continue
                    eventlog.code = EventlogCode.TIMEOUT
                    sender_: Sender = scheduler_conf.sender.obj
                    sender_.octopus_producer.write(message=eventlog.to_json())
                    logger.info(f'timeout eventlog {eventlog.to_json()}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='统一调度——超时控制')
    ap.add_argument('--backup-days', type=int, default=3, help='日志保存天数')
    ap_args = ap.parse_args()
    logger = setup_logger(
        use_file_log=True,
        app_name='octopus_timeout',
        backup_count=ap_args.backup_days,
        rotate_mode='D',
    )
    Main().run()
