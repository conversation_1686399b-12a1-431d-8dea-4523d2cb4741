# encoding=utf8
import time
import json
import argparse
import logging
from typing import Dict
from concurrent.futures import ThreadPoolExecutor, Future
from clients.kafka_client import KafkaConsumerClient
from libs.env import get_stack_info
from libs.dt import cur_ts_sec
from libs.concurrent import BoundedExecutor
from apps.octopus.core.constants import FEEDBACK_KAFKA, OCTOPUS, FEEDBACK_TOPIC
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.octopus.entries.entry import Entry
from apps.octopus.entries.entry_dao import EntryDao
from apps.octopus.core.runtime import remove_timeout
from apps.octopus.core.eventlog_store import EventlogStore, EventlogStorePos
from apps.octopus.utils.conf_manager import OctopusConfManager
from apps.octopus.utils.octopus_minute_stat import MinuteStatDao
logger = logging.getLogger(__file__)


class Main(object):
    def __init__(self, dump_worker_num, process_worker_num):
        self.dump_worker_num: int = dump_worker_num
        self.process_worker_num: int = process_worker_num
        self.octopus_conf_manager = OctopusConfManager(reload_conf_interval_sec=5)
        self.eventlog_store = EventlogStore(process_worker_num)
        self.minute_stat_dao = MinuteStatDao()

    def run(self):
        dump_executor = ThreadPoolExecutor(max_workers=self.dump_worker_num, thread_name_prefix='dump')
        process_executor = BoundedExecutor(
            max_workers=self.process_worker_num,
            cache_factor=20,
            thread_name_prefix='process'
        )
        for _ in range(ap_args.dump_worker_num):
            dump_executor.submit(self.dump_func, process_executor)
        while True:
            logger.info('main thread do nothing.')
            time.sleep(5)

    def dump_func(self, process_executor: BoundedExecutor):
        # event log consumer
        consumer = KafkaConsumerClient(kafka_topic=FEEDBACK_TOPIC, group_id=OCTOPUS, **FEEDBACK_KAFKA)
        fs: Dict[Future, str] = {}

        def callback_fn(f_: Future):
            s_ = fs[f_]
            del fs[f_]
            try:
                f_.result()
            except Exception as e_:
                logger.info(f'error process {s_} {e_} {get_stack_info()}')

        for sid, s in enumerate(consumer.read()):
            try:
                f = process_executor.submit(self.process_func, s)
                f.add_done_callback(callback_fn)
                fs[f] = s
                # exit(0)  # TEST ME
            except Exception as e:
                logger.warning(f'e={e} s={s} trace={get_stack_info()}')
                continue
        consumer.close()

    def process_func(self, s: str):
        if OCTOPUS not in s:
            logger.warning('not octopus s=%s', s)
            return
        d = json.loads(s)
        eventlog = Eventlog.from_dict(d)
        if not eventlog:
            logger.warning('error from dict s=%s', s)
            return
        try:
            selector = eventlog.selector
            selector.receive_ts = cur_ts_sec()
            scheduler_conf = self.octopus_conf_manager.get_by_name(selector.entry_name, selector.inst_name)
            if not scheduler_conf:
                logger.warning(f'no scheduler_conf {selector.entry_name} {selector.inst_name} {eventlog.event_id}')
                eventlog.code = EventlogCode.FEEDBACK_ERROR
            else:
                entry_dao: EntryDao = scheduler_conf.entry_dao.obj
                # 清理超时缓存
                remove_timeout(eventlog)
                # 调用solver
                entry: Entry = entry_dao.get_by_word(eventlog.selector.word)
                if not entry:
                    logger.warning(f'no entry {eventlog.event_id} {entry}')
                    eventlog.code = EventlogCode.FEEDBACK_ERROR
                else:
                    feedback_suc = entry_dao.feedback(eventlog=eventlog, entry=entry)
                    if feedback_suc:
                        ret = entry_dao.update_entry(entry, inst_name=eventlog.selector.inst_name)
                        if ret:
                            logger.info(f'update_entry ret={ret} {eventlog.event_id}')
                        else:
                            logger.warning(f'update_entry ret={ret} {entry} {eventlog.event_id}')
                    else:
                        logger.warning(f'error feedback {eventlog.event_id} {entry}')
                        eventlog.code = EventlogCode.FEEDBACK_ERROR
            self.eventlog_store.save(eventlog, pos=EventlogStorePos.FEEDBACK)
        except Exception as e:
            eventlog.code = EventlogCode.FEEDBACK_ERROR
            raise e
        finally:
            logger.info(f'EVENTLOG {eventlog.to_json()}')
            self.minute_stat_dao.write(eventlog)
            self.eventlog_store.save(eventlog, pos=EventlogStorePos.FEEDBACK)


if __name__ == '__main__':
    from libs.log2 import setup_logger
    ap = argparse.ArgumentParser(description='统一调度——回收器')
    ap.add_argument('--dump-worker-num', type=int, default=1, choices=[1, 2, 4, 8, 16])
    ap.add_argument('--process-worker-num', type=int, default=1, help='处理线程数')
    ap.add_argument('--backup-days', type=int, default=3, help='日志保存天数')
    ap_args = ap.parse_args()
    logger = setup_logger(
        use_file_log=True,
        app_name='octopus_feedback',
        backup_count=ap_args.backup_days,
        rotate_mode='D',
    )
    Main(dump_worker_num=ap_args.dump_worker_num, process_worker_num=ap_args.process_worker_num).run()
#     Main(dump_worker_num=ap_args.dump_worker_num, process_worker_num=ap_args.process_worker_num).process_func(
#         """
# {"event_id":"octopvs-brno-hk-68143545-1709038287","code":1,"selector":{"send_ts":1709038287,"receive_ts":1709038598,"reason":"hk_payed_report","clue":false,"entry_name":"brno","inst_name":"hk","word":"68143545","info":{"company_num":"F0023570","name":"稀美资源控股有限公司","establish_date":"2017-09-04","status":"仍注册"},"try_id":2,"meta":{},"weight":962},"spider":{"receive_ts":1709038318,"send_ts":-1,"spider_data":{"page_ts":1709038596}}}
#         """
#     )
