# encoding=utf8

from abc import abstractmethod
import logging
from typing import List, <PERSON><PERSON>
from threading import Lock
from datetime import datetime
from concurrent.futures.thread import ThreadPoolExecutor
from concurrent.futures import wait, ALL_COMPLETED
from chinese_calendar import is_workday
from clients.kafka_client import KafkaProducerClient
from libs.dt import cur_ts_sec
from libs.env import get_stack_info
from libs.feishu import send_feishu_message
from dao.octopus.platform import PlatformTaskDao, TaskStatus
from apps.octopus.entries.entry_dao import EntryDao, Entry
from apps.octopus.core.eventlog import Eventlog, EventlogCode
from apps.octopus.core.runtime import set_timeout
from apps.octopus.core.constants import FEEDBACK_TOPIC, FEEDBACK_KAFKA, PLATFORM_REASON
from apps.octopus.core.conf import SenderConf
from apps.octopus.core.eventlog_store import EventlogStore, EventlogStorePos
from apps.octopus.utils.octopus_minute_stat import MinuteStatDao

logger = logging.getLogger(__name__)


# 扔词器 单例模式
# 线程池方式处理多个Entry
# 功能点：
#     1、流量控制：根据最近N分钟的成功率，确定是否开启流量限速并飞书报警；另外还可以通过配置控制每分钟流量
#     2、消息回路：根据配置，可以替代爬虫返回默认的eventlog
class Sender(object):
    def __init__(self, entry_dao: EntryDao, conf: SenderConf, inst_name: str):
        self.entry_dao: EntryDao = entry_dao
        self.inst_name = inst_name
        self.spider_timeout_sec = conf.spider_timeout_sec
        self.octopus_producer = KafkaProducerClient(kafka_topic=FEEDBACK_TOPIC, **FEEDBACK_KAFKA)
        self.sender_queue_size = conf.sender_queue_size
        self.sender_hour_range = [int(x) for x in conf.sender_hour_range.split(' ')]
        assert len(self.sender_hour_range) == 2
        self.send_per_minute = conf.send_per_minute
        self.pool = ThreadPoolExecutor(thread_name_prefix='sender', max_workers=conf.sender_threads)
        self.send_count = 0
        self.cur_length = 0
        self.lock = Lock()
        self.eventlog_store = EventlogStore(conf.sender_threads)
        self.platform_task_dao = PlatformTaskDao()
        self.minute_stat_dao = MinuteStatDao()
        self.min_success_ratio = conf.min_success_ratio  # 最低成功率， 最近N分钟计算平均值  每M小时报警一次
        self.last_warning_ts = 0  # 上次报警时间
        self.sender_init_ts = cur_ts_sec()  # sender启动时间
        self.inst_success_ratio: float = 1.0
        self.last_ts_minute: int = 0  # 当前ts/60
        self.last_ts_minute_send: int = 0  # 当前分钟send
        self.feishu_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/3664d1a3-046a-48b3-aa5c-78e7d48aec5d'
        self.warning_ats = conf.warning_ats

    @abstractmethod
    def prepare_send(self):
        entry_name = self.entry_dao.entity_class.get_name()
        self.inst_success_ratio = self.minute_stat_dao.current_success_ratio(entry_name, self.inst_name)
        if self.inst_success_ratio < self.min_success_ratio:
            if cur_ts_sec() - self.last_warning_ts > 86400 and cur_ts_sec() > self.sender_init_ts + 120:  # 每天报警一次
                if is_workday(datetime.now().date()) and datetime.now().hour >= 10:
                    self.last_warning_ts = cur_ts_sec()
                    ret = send_feishu_message(
                        ats=self.warning_ats,
                        text=f'抓取成功率过低 {self.inst_name} {self.inst_success_ratio}<{self.min_success_ratio}',
                        url=self.feishu_url,
                    )
                    logger.warning(f'send feishu message ret={ret}')
        ts_minute = cur_ts_sec() // 60
        if ts_minute > self.last_ts_minute:
            self.last_ts_minute = ts_minute
            self.last_ts_minute_send = 0
        logger.info(f'size stat {self.cur_length}/{self.sender_queue_size} success_ratio={self.inst_success_ratio}')

    @abstractmethod
    def can_entry_send(self, entry: Entry, weight: int) -> bool:
        pass

    @abstractmethod
    def do_send(self, eventlog: Eventlog, weight: int) -> bool:
        pass

    def process(self, o: Tuple[int, str, Entry]):
        weight, reason, entry = o
        inst = entry.get_inst(inst=self.inst_name, init=True)
        with self.lock:
            if not self.can_entry_send(entry, weight):
                return

        eventlog = entry.make_eventlog(self.inst_name, weight, reason)
        if not eventlog:
            logger.warning(f'fail make eventlog {entry}')
            return
        # 先更新item表，然后发送eventlog
        if not inst.schedule():
            logger.warning(f'fail and ignore {entry}')
            return
        # reason=platform 此时  platform_id(int)、platform_args(dict) 放到info中
        if reason == PLATFORM_REASON:
            task_id = inst.inst_info.get('platform_id', None)
            if isinstance(task_id, int):
                ret = self.platform_task_dao.set_task(task_id, status=TaskStatus.EXECUTE, record='已调度')
                logger.warning(f'platform send {entry} ret={ret}')
        ret = self.entry_dao.update_entry(entry=entry, inst_name=self.inst_name)
        if not ret:
            logger.warning(f'update entry fail {entry.to_json()} ')

        if not self.do_send(eventlog, weight=weight):
            logger.warning(f'send eventlog fail {entry}')
            return
        self.eventlog_store.save(eventlog, pos=EventlogStorePos.SCHEDULED)
        if self.spider_timeout_sec == 0:
            eventlog.spider.receive_ts = cur_ts_sec()
            eventlog.spider.send_ts = cur_ts_sec()
            eventlog.code = EventlogCode.SUCCESS
            s = eventlog.to_json()
            if not self.octopus_producer.write(message=s):
                logger.warning(f'kafka send fail {s}')
        else:
            set_timeout(eventlog)
        logger.info(f'send reason={reason} weight={weight} eventlog={eventlog}')
        self.send_count += 1

    # 扔词 阻塞式
    # 先发送 然后再更新调度状态
    def send(self, items: List[Tuple[int, str, Entry]]) -> Tuple[float, int]:
        self.send_count = 0
        self.prepare_send()
        futures = dict()

        def callback_fn(f):
            item_ = futures[f]
            del futures[f]
            try:
                f.result()
            except Exception as e:
                logger.info(f'error process {item_} {e} {get_stack_info()}')

        for item in items:
            future = self.pool.submit(self.process, item)
            futures[future] = item
            future.add_done_callback(callback_fn)
        done, not_done = wait(futures, timeout=None, return_when=ALL_COMPLETED)
        logger.info(f'done={len(done)} not_done={len(not_done)}')
        return self.cur_length / self.sender_queue_size, self.send_count
