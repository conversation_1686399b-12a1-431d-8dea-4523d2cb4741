# encoding=utf8
import logging
from datetime import datetime
from clients.redis.redis_queue import RedisQueue
from libs.env import get_props_redis
from apps.octopus.entries.entry_dao import EntryDao, Entry
from apps.octopus.core.eventlog import Eventlog
from apps.octopus.sender.sender import Sender
from apps.octopus.core.conf import SenderConf

logger = logging.getLogger(__name__)


# using redis.ZSET as normal queue. that is, scores are always time.time()
class RedisZsetSender(Sender):
    def __init__(self, entry_dao: EntryDao, conf: SenderConf, inst_name: str):
        super().__init__(entry_dao=entry_dao, conf=conf, inst_name=inst_name)
        self.redis_queue = RedisQueue(
            name=conf.args['redis_key'],
            max_length=self.sender_queue_size,
            use_zset=True,
            db=conf.args['redis_db'],
            **get_props_redis(inst=conf.args['redis_name']),
        )

    def prepare_send(self):
        super().prepare_send()
        self.cur_length = self.redis_queue.redis.zcard(self.redis_queue.name)

    # 高优任务不应该增加队列长度，会有其他逻辑错误问题
    # 高优任务不应该插队
    # 整个队列不能全部扔高优任务
    def can_entry_send(self, entry: Entry, weight: int) -> bool:
        hour_from, hour_to = self.sender_hour_range
        if hour_from != 0 or hour_to != 24:
            hour_now = datetime.now().hour
            if hour_from <= hour_to:
                if hour_now < hour_from or hour_now >= hour_to:
                    return False
            else:
                if hour_to <= hour_now < hour_from:
                    return False
        max_size = self.sender_queue_size
        if self.inst_success_ratio < 0.01:
            max_size = max(max_size // 50, 10)
        send_total = max_size - self.cur_length
        if self.send_count > send_total:
            return False
        if send_total > 0 and self.send_count / send_total >= 0.9 and weight >= 800:
            # 最后10%留给普通任务
            return False
        if self.last_ts_minute_send >= self.send_per_minute > 0:
            return False
        return True

    def do_send(self, eventlog: Eventlog, weight: int) -> bool:
        self.redis_queue.push(value=eventlog.to_json(), realtime=0, wait=0.0)
        self.last_ts_minute_send += 1
        return True
