# encoding=utf8

import json
import argparse
from threading import Lock
from concurrent.futures import Future
from libs.log2 import setup_logger
from libs.env import get_env_prop, get_stack_info
from libs.concurrent import BoundedExecutor
from clients.redis.redis_queue import RedisQueue
from clients.kafka_client import KafkaProducerClient
from entity.eventlog import Eventlog, SpiderCode
from apps.gs_spider.spider_conf import SpiderConf
from apps.gs_spider.crawler import *


class CrawlerMain(object):  # only one instance
    def __init__(self, conf: SpiderConf, worker_num=1):
        self.worker_num: int = worker_num
        self.fs = dict()
        self.fs_lock = Lock()

        self.input_queue = RedisQueue(
            **get_env_prop(f'redis.tyc.{conf.crawler.resource_name}'),
            **conf.crawler.queue_args,
            use_zset=True)

        if conf.parser.use_redis_queue:
            output_queue = RedisQueue(
                **get_env_prop(f'redis.tyc.{conf.parser.resource_name}'),
                **conf.parser.queue_args, use_zset=False)
            self.eventlog_feedback_fn = output_queue.push
        else:
            output_queue = KafkaProducerClient(
                **get_env_prop(f'kafka.tyc.{conf.parser.resource_name}'),
                **conf.parser.queue_args)
            self.eventlog_feedback_fn = output_queue.write

        crawler_class = globals()[conf.crawler.clazz]
        self.crawler_obj: Crawler = crawler_class(
            name=conf.spider_name,
            page_bak_count=conf.crawler.page_bak_count,
        )

    def run(self):
        with BoundedExecutor(max_workers=self.worker_num) as process_pool:
            for s in self.input_queue.generate():
                try:
                    d = json.loads(s)
                    eventlog = Eventlog.from_dict(d)
                    if not eventlog:
                        logger.warning(f'error from dict {s}')
                        continue
                except Exception as e:
                    logger.warning(f'error eventlog {e}')
                    continue
                future: Future = process_pool.submit(self.crawler_obj.crawl, eventlog)
                with self.fs_lock:
                    self.fs[future] = eventlog
                future.add_done_callback(self.callback_fn)
                # break  # TEST

    def callback_fn(self, future: Future):
        eventlog = self.fs[future]
        with self.fs_lock:
            del self.fs[future]
        try:
            eventlog = future.result()
        except Exception as e:
            eventlog.spider_code = SpiderCode.GIVE_UP
            logger.info(f'error process {eventlog} set SpiderCode.GIVE_UP {e} {get_stack_info()}')
        eventlog_str = eventlog.to_json()
        ret = self.eventlog_feedback_fn(eventlog_str)
        logger.info(f'OUTPUT {eventlog_str} ret={ret}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用工商爬虫-抓取程序 抓取数据到OBS 并发送Kafka消息')
    ap.add_argument('-c', '--spider-conf', type=argparse.FileType('r'), default='conf/gds.json')
    ap.add_argument('--backup-days', type=int, default=1, help='日志保存天数')
    ap.add_argument('--max-workers', type=int, default=1, choices=range(1, 100), help='worker数量')
    ap_args = ap.parse_args()
    conf_ = SpiderConf.from_dict(json.loads(ap_args.spider_conf.read()))
    app_name = '.'.join(['gs_spider', conf_.spider_name, 'crawler'])
    logger = setup_logger(use_file_log=True, app_name=app_name, backup_count=ap_args.backup_days, rotate_mode='D', process_safe=True)
    crawler_main = CrawlerMain(conf_, worker_num=ap_args.max_workers)
    crawler_main.run()
