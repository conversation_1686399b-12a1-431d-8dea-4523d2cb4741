# encoding=utf8
from apps.gs_spider.crawler import APP2Crawler
from apps.gs_spider.parser import APP2Parser
from libs.log2 import setup_logger
from entity.eventlog import Eventlog

logger = setup_logger()


if __name__ == '__main__':
    # 本地抓取（更新/新增）一个公司
    logger = setup_logger(process_safe=True)

    eventlog = Eventlog.from_dict(
        {
            "event_id": "octopus_entry-app2-2199057-default-1703756336",
            "is_clue": False,
            "spider_code": -1,
            "crawlerType": 1,
            "crawlerCode": -1,
            "parserCode": -1,
            "fusionCode": -1,
            "selector": {
                "send_ts": 1703756336,
                "receive_ts": -1,
                "reason": "schedule",
                "item_name": "company",
                "inst_name": "app2",
                "word": '614691',
                "info": {
                    # "name": "上海兴富雏鹰私募投资基金合伙企业（有限合伙）",
                    # "registration_date": "2013-05-02",
                    # "status": "正常",
                    # "realtime_ts": 1692975192,
                    # "keyword": '913301067046373179'
                    # "keyword": '91110108551385082Q'
                    "keyword": '91110113802038721K'
                },
                "try_id": 0,
                "meta": None,
                "weight": 960
            },
            "spider": {
                "receive_ts": -1,
                "send_ts": -1,
                "item_insert": False,
                "ab_info": {}
            },
            "crawler": {},
            "parser": {},
            "fusion": {},
            "dims": {},
            "channel": {}
        }
    )

    crawler = APP2Crawler(name='app2')
    parser = APP2Parser(name='app2')

    # crawler.crawl(eventlog)
    parser.parse(eventlog)

    logger.info(f'OUTPUT eventlog={eventlog.to_json()}')

