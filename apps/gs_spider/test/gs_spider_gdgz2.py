# encoding=utf8
from apps.gs_spider.crawler import GDGZCrawler2
from apps.gs_spider.parser import GDGZParser2
from libs.log2 import setup_logger
from entity.eventlog import Eventlog
from loguru import logger

# logger = setup_logger()

if __name__ == '__main__':
    # 本地抓取（更新/新增）一个公司
    # logger = setup_logger(process_safe=True)



    eventlog = Eventlog.from_dict(
        {
            "event_id": "octopus_entry-gdgz-2199057-default-1703756336",
            "is_clue": False,
            "spider_code": -1,
            "crawlerType": 1,
            "crawlerCode": -1,
            "parserCode": -1,
            "fusionCode": -1,
            "selector": {
                "send_ts": 1703756336,
                "receive_ts": -1,
                "reason": "schedule",
                "item_name": "company",
                "inst_name": "gdgz",
                "word": '2808910078',
                "info": {
                    "keyword": '91440101MA5CP8X219'
                },
                "try_id": 0,
                "meta": None,
                "weight": 960
            },
            "spider": {
                "receive_ts": -1,
                "send_ts": -1,
                "item_insert": False,
                "ab_info": {}
            },
            "crawler": {},
            "parser": {},
            "fusion": {},
            "dims": {},
            "channel": {}
        }
    )

    crawler = GDGZCrawler2(name='gdgz')
    parser = GDGZParser2(name='gdgz')

    crawler.crawl(eventlog)
    # parser.parse(eventlog)

    logger.info(f'OUTPUT eventlog={eventlog.to_json()}')
