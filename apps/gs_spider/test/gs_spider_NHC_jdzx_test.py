# encoding=utf8
import threading
from apps.gs_spider.test.NHC_data import hospital_list, addr
from apps.gs_spider.crawler import crawler_NHC
def craw_by_jdzx():
    def task_simple(hospital_list):
        for hospital in hospital_list:
            try:
                for i in range(0, len(hospital), 4):
                    CrawlerNHC.find_entity_by_jdzx(hospital[i:i + 4])
            except Exception as e:
                print(e)


    thread = list()
    CrawlerNHC = crawler_NHC.CrawlerNHCJdzx('nhc')
    for hospital in range(1, 51):
        h = len(addr) // 50
        thread.append(threading.Thread(target=task_simple, args=(sorted(addr[h * (hospital - 1): h * hospital + 1], reverse=True),)))
    for hospital in range(1, 51):
        h = len(hospital_list) // 50
        thread.append(threading.Thread(target=task_simple, args=(sorted(hospital_list[h * (hospital - 1): h * hospital + 1], reverse=True),)))
    for th in thread:
        th.start()
    for th in thread:
        th.join()
    # task_simple(addr)

if __name__ == '__main__':
    craw_by_jdzx()
    # CrawlerNHC = crawler_NHC.CrawlerNHCJdzx('nhc')
    # CrawlerNHC.find_entity_by_jdzx('上海市闸北区彭浦镇社区卫生服务中心市北高新园区社区卫生服务站')