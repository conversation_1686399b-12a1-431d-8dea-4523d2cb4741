# encoding=utf8
import re
import threading
import time
import urllib
from urllib import parse
from concurrent import futures

import cpca
from memory_profiler import profile
from apps.gs_spider.crawler.crawler_NHC import CrawlerNHC
from apps.gs_spider.parser.parser_NHC import ParserN<PERSON>
from apps.gs_spider.test.NHC_data import hospital_list, new_hospital, new_hospital_2, addr
from libs.env import ConstantProps
from libs.log import setup_logger
from entity.eventlog import Eventlog, SpiderCode
import bs4
import pymysql
import requests
from apps.gs_spider.crawler import crawler_NHC
from dao.nhc.nhc_jdzx import NhcJdzxDao
from dao.nhc.nhc_hospital import NhcHospitalDao
from dao.nhc.nhc_company import NhcCompanyDao
from dao.nhc.nhc_chachonne import NhcChachonneDao
prvo = ['北京市',
        '上海市',
        '重庆市',
        '天津市',
        '江苏省',
        '广东省',
        '山东省',
        '辽宁省',
        '河北省',
        '河南省',
        '四川省',
        '黑龙江省',
        '山西省',
        '湖北省',
        '湖南省',
        '陕西省',
        '浙江省',
        '云南省',
        '吉林省',
        '安徽省',
        '广西壮族自治区',
        '江西省',
        '福建省',
        '新疆维吾尔自治区',
        '内蒙古自治区',
        '甘肃省',
        '贵州省',
        '海南省',
        '青海省',
        '宁夏回族自治区',
        '西藏自治区',
        ]

tab = ['三级甲等医院列表',
       '三级乙等医院列表',
       '三级丙等医院列表',
       '二级甲等医院列表',
       '二级乙等医院列表',
       '二级丙等医院列表',
       '一级甲等医院列表',
       '一级乙等医院列表',
       '一级丙等医院列表',
       ]

province_codes = {
    '110000': '北京市',
    '120000': '天津市',
    '130000': '河北省',
    '140000': '山西省',
    '150000': '内蒙古自治区',
    '210000': '辽宁省',
    '220000': '吉林省',
    '230000': '黑龙江省',
    '310000': '上海市',
    '320000': '江苏省',
    '330000': '浙江省',
    '340000': '安徽省',
    '350000': '福建省',
    '360000': '江西省',
    '370000': '山东省',
    '410000': '河南省',
    '420000': '湖北省',
    '430000': '湖南省',
    '440000': '广东省',
    '450000': '广西壮族自治区',
    '460000': '海南省',
    '500000': '重庆市',
    '510000': '四川省',
    '520000': '贵州省',
    '530000': '云南省',
    '540000': '西藏自治区',
    '610000': '陕西省',
    '620000': '甘肃省',
    '630000': '青海省',
    '640000': '宁夏回族自治区',
    '650000': '新疆维吾尔自治区'
}

abbreviation_to_province = {
    'bj': '北京市',
    'tj': '天津市',
    'heb': '河北省',
    'sx': '山西省',
    'nmg': '内蒙古自治区',
    'ln': '辽宁省',
    'jl': '吉林省',
    'hlj': '黑龙江省',
    'sh': '上海市',
    'js': '江苏省',
    'zj': '浙江省',
    'ah': '安徽省',
    'fj': '福建省',
    'jx': '江西省',
    'sd': '山东省',
    'hen': '河南省',
    'hub': '湖北省',
    'hun': '湖南省',
    'gd': '广东省',
    'gx': '广西壮族自治区',
    'han': '海南省',
    'cq': '重庆市',
    'sc': '四川省',
    'gz': '贵州省',
    'yn': '云南省',
    'xz': '西藏自治区',
    'snx': '陕西省',
    'gs': '甘肃省',
    'qh': '青海省',
    'nx': '宁夏回族自治区',
    'xj': '新疆维吾尔自治区'
}

event = {
    "event_id": "octopus_entry-org-133980-npo-1705305173",
    "is_clue": False,
    "spider_code": -1,
    "crawlerType": 1,
    "crawlerCode": -1,
    "parserCode": -1,
    "fusionCode": -1,
    "selector": {
        "send_ts": 1705305173,
        "reason": "schedule",
        "item_name": "org",
        "inst_name": "nhc",
        "word": "阳高县王官屯镇兴苑村卫生室",
        "info": {
            "prov": "山西省",
            "registration_date": "2017-04-15",
            "status": "正常",
            "realtime_ts": 1698740873,
            "source": 'jdzx',
        },
        "try_id": 0,
        "meta": None,
        "weight": 450
    },
    "spider": {
        "receive_ts": 1705305175,
        "send_ts": -1,
        "item_insert": False,
        "ab_info": {},
        "spider_data": {
            # "page_ts": 1710388285
        }
    },
    "crawler": {},
    "parser": {},
    "fusion": {},
    "dims": {},
    "channel": {}
}

def fun():
    def task(s, e):
        connection = pymysql.connect(
            host='886a213ba5de451eb4add2e0d1fb2ef6in01.internal.cn-north-4.mysql.rds.myhuaweicloud.com',
            user='jdhw_d_zhuan_dml',
            password='kmbpZRTr1pooyB9',
            database='data_experience_situation',
            cursorclass=pymysql.cursors.DictCursor)

        try:
            with connection.cursor() as cursor:
                sql = f"select * from (select * from data_experience_situation.gov_unit limit {s}, {e}) as t where name like '%医院%' or '%卫生%'"
                cursor.execute(sql)
                for row in cursor:
                    # print(row['name'])
                    eventlog = Eventlog.from_dict(event)
                    if row['name'].find('（') > -1:
                        eventlog.selector.word = row['name'][:row['name'].find('（')]
                    else:
                        eventlog.selector.word = row['name']
                    if row['us_credit_code'] is not None and row['us_credit_code'] != '' and re.match(
                            '[1-9A-HJ-NP-Z]{2}\d{6}[1-9A-Za-z][0-9A-Za-z]{8}', row['us_credit_code']):
                        eventlog.selector.info['prov'] = province_codes[row['us_credit_code'][2:4] + '0000']
                    elif row['base'] in abbreviation_to_province:
                        eventlog.selector.info['prov'] = abbreviation_to_province[row['base']]
                    else:
                        continue
                    eventlog.selector.info['source'] = '专项'
                    logger.info(f'OUTPUT eventlog={eventlog.to_json()}')
                    crawler.crawl(eventlog)
                    parser.parse(eventlog)
                    eventlog.spider_code = SpiderCode.UNFILLED
        finally:
            connection.close()

    crawler = CrawlerNHC(name='nhc')
    parser = ParserNHC(name='nhc')
    thread = list()

    for i in range(0, 1261672, 100000):
        thread.append(threading.Thread(target=task, args=(i, i + 100000)))
    thread.append(threading.Thread(target=task, args=(1200000, 1261672)))

    for th in thread:
        th.start()
    for th in thread:
        th.join()


def hospital_wiki():
    def task(p):
        for t in tab:
            headers = {
            }
            url = 'https://www.yixue.com/' + urllib.parse.quote(p + t)
            response = requests.get(
                url,
                headers=headers,
            ).text
            '#mw-content-text > div > ul:nth-child(9) > li:nth-child(1) > b > a'
            soup = bs4.BeautifulSoup(response, 'lxml')
            status = soup.select('#mw-content-text > div > ul:nth-child(5) > li')
            for sta in status:
                eventlog = Eventlog.from_dict(event)
                inc = sta.select('b > a')[0].text
                eventlog.selector.word = inc
                eventlog.selector.info['prov'] = p
                eventlog.selector.info['source'] = '医百科'

                logger.info(f'OUTPUT eventlog={eventlog.to_json()}')
                crawler.crawl(eventlog)
                parser.parse(eventlog)
                eventlog.spider_code = SpiderCode.UNFILLED

    crawler = CrawlerNHC(name='nhc')
    parser = ParserNHC(name='nhc')
    thread = list()

    for p in prvo:
        thread.append(threading.Thread(target=task, args=(p,)))

    for th in thread:
        th.start()
    for th in thread:
        th.join()


def dingxiang_wiki():

    def task(location):
        for grade in [2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15]:
            n = 1
            while n < 50:
                params = {
                    'page': n,
                    'location': location,
                    'grade': str(grade),
                }

                response = requests.get('https://y.dxy.cn/hospital/', params=params)

                soup = bs4.BeautifulSoup(response.text, 'lxml')
                status = soup.select('#hospitallist > div')
                if not status:
                    break
                for sta in status:
                    res = sta.select('div:nth-child(1) > div > div.info-wrap > div > a')
                    if res != []:
                        eventlog = Eventlog.from_dict(event)
                        eventlog.selector.word = res[0].text
                        eventlog.selector.info['prov'] = province_codes[location]
                        eventlog.selector.info['source'] = '丁香园'

                        logger.info(f'OUTPUT eventlog={eventlog.to_json()}')
                        crawler.crawl(eventlog)
                        parser.parse(eventlog)
                        eventlog.spider_code = SpiderCode.UNFILLED
                n += 1

    crawler = CrawlerNHC(name='nhc')
    parser = ParserNHC(name='nhc')
    thread = list()
    for p in province_codes:
        thread.append(threading.Thread(target=task, args=(p,)))
    for th in thread:
        th.start()
    for th in thread:
        th.join()


def violence(thread_num):
    def task_simple(start, end):

        for n in range(start, end):
            try:
                while current_time.tm_hour >= 1 and current_time.tm_hour < 5:
                    time.sleep(60)

                hospital = NhcJdzxDao.get(n, 'id')
                if not hospital:
                    continue
                hospital = dict(hospital)

                for name in [hospital['name_hospital'][i:i + 4] for i in range(0, len(hospital['name_hospital']), 4)]:

                    if NhcChachonneDao.get(name, 'name_hospital'):
                        logger.info(f"[{name}] 为废弃医院")
                        continue

                    if NhcHospitalDao.get(name, 'name_hospital'):
                        continue

                    eventlog = Eventlog.from_dict(event)
                    eventlog.selector.word = name
                    eventlog.selector.info['prov'] = hospital['addr_hospital']
                    eventlog.selector.info['source'] = '-'
                    logger.info(f'OUTPUT eventlog={eventlog.to_json()}')
                    crawler.crawl(eventlog)
                    eventlog.spider_code = SpiderCode.UNFILLED
                    parser.parse(eventlog)
                    eventlog.spider_code = SpiderCode.UNFILLED
                    # print(eventlog.selector.word, eventlog.selector.info['prov'])
            except Exception as e:
                logger.error(f'id: {str(n)} 爬取出问题了 Exception: {str(e)}')

    current_time = time.localtime()

    crawler = CrawlerNHC(name='nhc')
    parser = ParserNHC(name='nhc')

    # task_simple([{'name_hospital': '北京同仁', 'addr_hospital': '北京市'}])

    thread = list()
    res = NhcJdzxDao.acquire()
    for i in range(thread_num):
        thread.append(threading.Thread(target=task_simple, args=(res//thread_num * i, res//thread_num * (i+1))))

    for th in thread:
        th.start()
    for th in thread:
        th.join()


def violence_by_company(thread_num):
    def task_simple(start, end):

        for n in range(start, end):
            try:
                while current_time.tm_hour >= 1 and current_time.tm_hour < 5:
                    time.sleep(60)
                hospital = NhcCompanyDao.get(n, 'id')
                if not hospital:
                    continue
                hospital = dict(hospital)

                if hospital['base'] == '未知':
                    continue

                if NhcHospitalDao.get(hospital['name'], 'name_hospital'):
                    continue

                eventlog = Eventlog.from_dict(event)
                eventlog.selector.word = hospital['name']
                eventlog.selector.info['prov'] = hospital['base']
                eventlog.selector.info['source'] = '-'
                logger.info(f'OUTPUT eventlog={eventlog.to_json()}')
                crawler.crawl(eventlog)
                eventlog.spider_code = SpiderCode.UNFILLED
                parser.parse(eventlog)
                eventlog.spider_code = SpiderCode.UNFILLED
                # print(eventlog.selector.word, eventlog.selector.info['prov'])
            except Exception as e:
                logger.error(f'id: {str(n)} 爬取出问题了 Exception: {str(e)}')

    current_time = time.localtime()

    crawler = CrawlerNHC(name='nhc')
    parser = ParserNHC(name='nhc')

    # task_simple(1,2)
    thread = list()
    # res = NhcCompanyDao.acquire()
    res = 1073409
    for i in range(thread_num):
        thread.append(threading.Thread(target=task_simple, args=(res//thread_num * i, res//thread_num * (i+1))))

    for th in thread:
        th.start()
    for th in thread:
        th.join()


def craw_by_jdzx():
    def task_simple(hospital_list):
        for hospital in hospital_list:
            try:
                # if NhcChachonneDao.get(hospital, 'name_hospital'):
                #     logger.info(f"[{hospital}] 为废弃医院")
                #     continue
                #
                # if NhcJdzxDao.get(hospital, 'name_hospital'):
                #     continue
                for i in range(0, len(hospital), 4):
                    CrawlerNHC.find_entity_by_jdzx(hospital[i:i + 4])
            except Exception as e:
                print(e)


    thread = list()
    CrawlerNHC = crawler_NHC.CrawlerNHCJdzx('nhc')
    for hospital in range(1, 31):
        h = len(addr) // 30
        thread.append(threading.Thread(target=task_simple, args=(sorted(addr[h * (hospital - 1): h * hospital + 1], reverse=True),)))
    for hospital in range(1, 31):
        h = len(hospital_list) // 30
        thread.append(threading.Thread(target=task_simple, args=(sorted(hospital_list[h * (hospital - 1): h * hospital + 1], reverse=True),)))
    for th in thread:
        th.start()
    for th in thread:
        th.join()
    # task_simple(addr)


def craw_by_jd():
    def task(soup, prov):
        for j in soup.select('body > main > div > div:nth-child(3) > div:nth-child(2) > a'):
            href2 = j.get('href')
            response = requests.get(href2, headers=headers)
            soup = bs4.BeautifulSoup(response.text, 'lxml')
            p = soup.select('body > main > div > div:nth-child(2) > div:nth-child(3) > span')
            for k in range(1, int(p[-1].text[2:-2]) + 1):
                response = requests.get(href2 + f'-{str(k)}', headers=headers)
                soup = bs4.BeautifulSoup(response.text, 'lxml')
                for u in soup.select('body > main > div > div:nth-child(2) > ul > li'):
                    try:
                        while current_time.tm_hour >= 1 and current_time.tm_hour < 5:
                            time.sleep(60)

                        name = u.text.strip()
                        if NhcHospitalDao.get(name, 'name_hospital'):
                            continue
                        # print(name, prov)
                        eventlog = Eventlog.from_dict(event)

                        NhcCompanyDao.i_u_by_any(**{'name': name, 'base': prov})
                        eventlog.selector.word = name
                        eventlog.selector.info['prov'] = prov
                        eventlog.selector.info['source'] = '-'
                        logger.info(f'OUTPUT eventlog={eventlog.to_json()}')
                        crawler.crawl(eventlog)
                        eventlog.spider_code = SpiderCode.UNFILLED
                        parser.parse(eventlog)
                        eventlog.spider_code = SpiderCode.UNFILLED
                    except Exception as e:
                        logger.error(f'name: {str(u.text.strip())} 爬取出问题了 Exception: {str(e)}')


    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'sec-ch-ua': '"Microsoft Edge";v="123", "Not:A-Brand";v="8", "Chromium";v="123"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'cross-site',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    }
    current_time = time.localtime()
    crawler = CrawlerNHC(name='nhc')
    parser = ParserNHC(name='nhc')
    thread = list()
    response = requests.get('https://cont.jd.com/sitemap/69191887006721', headers=headers)
    soup = bs4.BeautifulSoup(response.text, 'lxml')
    for i in soup.select('body > main > div > div:nth-child(2) > div:nth-child(2) > a'):
        href = i.get('href')
        response = requests.get(href, headers=headers)
        soup = bs4.BeautifulSoup(response.text, 'lxml')
        prov = cpca.transform([i.text]).iloc[0, 0]
        if not prov:
            continue
        prov = prov.strip()
        thread.append(threading.Thread(target=task, args=(soup, prov)))
    for th in thread:
        th.start()
    for th in thread:
        th.join()

def run():
    eventlog = Eventlog.from_dict(event)
    crawler = CrawlerNHC(name='nhc')
    parser = ParserNHC(name='nhc')
    while True:
        eventlog.selector.word = '河南张仲景股份有限公司洛阳定鼎南路店中医坐堂医诊所'
        eventlog.selector.info['prov'] = '河南省'
        eventlog.selector.info['source'] = '-'
        logger.info(f'OUTPUT eventlog={eventlog.to_json()}')
        crawler.crawl(eventlog)
        # parser.parse(eventlog)
        eventlog.spider_code = SpiderCode.UNFILLED


'''
(IC) [work@s328-prod-spider-huawei test]$ grep '医百科查询为空' NHC.out -c
14393
(IC) [work@s328-prod-spider-huawei test]$ grep '专项查询为空' NHC.out -c
10399
'''



if __name__ == '__main__':
    NhcJdzxDao = NhcJdzxDao(**ConstantProps.PROPS_GS_TEST)
    NhcHospitalDao = NhcHospitalDao()
    NhcCompanyDao = NhcCompanyDao(**ConstantProps.PROPS_GS_TEST)
    NhcChachonneDao = NhcChachonneDao(**ConstantProps.PROPS_GS_TEST)
    logger = setup_logger()

    # run()
    # violence_by_company(100)
    violence(100)
    # craw_by_jd()


