const CryptoJS = require("crypto-js");

function getAesString(_0xf85d7c, _0x37b515, _0x60695f) {
    var _0x37b515 = CryptoJS['enc']['Utf8']['parse'](_0x37b515);
    var _0x60695f = CryptoJS['enc']['Utf8']['parse'](_0x60695f);
    var _0x3fca6a = CryptoJS['AES']['encrypt'](_0xf85d7c, _0x37b515, {
        'iv': _0x60695f,
        'mode': CryptoJS['mode']['CBC'],
        'padding': CryptoJS['pad']['Pkcs7']
    });
    return _0x3fca6a['toString']();
}

function geetTp() {
    var _0x2d36af = 'ihqozjgg8sh86quo';
    var _0x59800b = {
        'sbXHg': '1|3|0|2|4',
        'oUpLw': function (_0x5b96f6, _0x87c7c2, _0x1fd7d2, _0x195253) {
            return _0x5b96f6(_0x87c7c2, _0x1fd7d2, _0x195253);
        },
        'uPued': 'etp',
        'yrfXw': function (_0x179cfa, _0x125405) {
            return _0x179cfa + _0x125405;
        }
    };
    var _0x520fba = {};
    var _0x5c4e10 = _0x59800b['yrfXw'](new Date()['getTime'](), '');
    var _0x25d574 = _0x59800b['oUpLw'](getAesString, _0x5c4e10, _0x2d36af, _0x2d36af);
    _0x520fba[_0x59800b['uPued']] = _0x25d574;
    return _0x520fba;
}

function oneEncrypt(data) {
    var result = {};
    result['encryMethod'] = '';
    result['encryData'] = '';

    var dataResult = methodAnddataInfo(data);
    result.encryMethod = dataResult.resultMethod;
    result.encryData = dataResult.resultData;
    return result;
}

function infoQueryData(_0x51b476) {
    var _0x2d36af = '9rhgtr4teboimx2m';
    var _0x1fe5a2 = {
        'DoFku': '2|7|0|3|8|1|4|5|6',
        'vvPIX': function (_0x3e6472, _0x419891, _0x5796b3, _0x3fcf06) {
            return _0x3e6472(_0x419891, _0x5796b3, _0x3fcf06);
        },
        'TdTeH': 'etp',
        'lcbOC': function (_0x53dc83, _0x4647e1, _0x3c53d5, _0x3924a5) {
            return _0x53dc83(_0x4647e1, _0x3c53d5, _0x3924a5);
        },
        'uMjcK': function (_0x413d13, _0x21d675) {
            return _0x413d13 + _0x21d675;
        },
        'fcCke': '_ABCD_',
        'IBPYI': 'eitp',
        'ylzOf': 'esn',
        'JootN': function (_0x402b06, _0x242776) {
            return _0x402b06 + _0x242776;
        },
        'AweKc': function (_0x246a88, _0x7d2106, _0x1d8916, _0x53793f) {
            return _0x246a88(_0x7d2106, _0x1d8916, _0x53793f);
        },
        'whhFv': 'infoQueryController'
    };
    var _0x14d014 = {};
    var _0x1e8072 = _0x1fe5a2['JootN'](new Date()['getTime'](), '');
    var _0x6267b0 = _0x1fe5a2['vvPIX'](getAesString, _0x1e8072, _0x2d36af, _0x2d36af);
    var _0x500ce3 = _0x1fe5a2['lcbOC'](getAesString, _0x1fe5a2['uMjcK'](_0x1fe5a2['uMjcK'](_0x51b476, _0x1fe5a2['fcCke']), _0x1e8072), _0x2d36af, _0x2d36af);
    var _0x2a6fe9 = _0x1fe5a2['AweKc'](getAesString, _0x1fe5a2['whhFv'] + '_' + new Date()['getHours'](), _0x2d36af, _0x2d36af);
    _0x14d014[_0x1fe5a2['TdTeH']] = _0x6267b0;
    _0x14d014[_0x1fe5a2['IBPYI']] = _0x500ce3;
    _0x14d014[_0x1fe5a2['ylzOf']] = _0x2a6fe9;
    return _0x14d014;
}

function methodAnddataInfo(_0x274315) {
    var _0x150096 = '2yd5kjyp304d70d0';
    var _0x6151d4 = {
        'oblGh': '4|2|0|1|6|5|3',
        'OXEKS': function (_0x28fa17, _0x5be28c, _0x4950e2, _0x5b89c6) {
            return _0x28fa17(_0x5be28c, _0x4950e2, _0x5b89c6);
        },
        'WNRCe': function (_0x16ec70, _0x10fa60, _0x595c3b, _0x30d84e) {
            return _0x16ec70(_0x10fa60, _0x595c3b, _0x30d84e);
        },
        'UUgLY': 'resultData',
        'utufg': 'resultMethod'
    };
    var _0x46eb86 = {};
    var _0x18ec90 = _0x6151d4['WNRCe'](getAesString, _0x274315['esn'], _0x150096, _0x150096);
    var _0x339a16 = JSON['stringify'](_0x274315);
    var _0xa1759c = _0x6151d4['OXEKS'](getAesString, _0x339a16, _0x150096, _0x150096);
    _0x46eb86[_0x6151d4['utufg']] = _0x18ec90;
    _0x46eb86[_0x6151d4['UUgLY']] = _0xa1759c;
    return _0x46eb86
}

function globleEncrypt(data) {
    var dataResult = methodAnddataInfo(data);
    var encryObj = {}
    encryObj.encryMethod = dataResult.resultMethod;
    encryObj.encryData = dataResult.resultData;
    return encryObj
}

function openDetailPage(_0x33d270, _0x4e7ed3, _0x53ace1, _0x388949, _0x3ff439) {
    var _0x2d36af = '9rhgtr4teboimx2m';
    var _0x150096 = '2yd5kjyp304d70d0';

    var _0x381d1b = {
        'bMAYd': function (_0x5b4fc5, _0x2010f7) {
            return _0x5b4fc5(_0x2010f7);
        },
        'LVbyD': '4|6|0|3|2|5|1',
        'paTDD': 'resultMethod',
        'wLqpr': function (_0x36d819, _0x1aef93, _0x2affc0, _0x1acb65) {
            return _0x36d819(_0x1aef93, _0x2affc0, _0x1acb65);
        },
        'GWLwe': 'resultData',
        'LnuqA': 'script',
        'Hltut': function (_0x3b78fb, _0x540219) {
            return _0x3b78fb + _0x540219;
        },
        'elnMx': function (_0x26051a, _0x1387b9) {
            return _0x26051a >= _0x1387b9;
        },
        'unyQo': function (_0x27f6b0, _0x1f8682) {
            return _0x27f6b0 <= _0x1f8682;
        },
        'NrMCW': function (_0x44cc1b, _0x171964) {
            return _0x44cc1b <= _0x171964;
        },
        'Gafhz': function (_0x10e67f, _0x4b49ca) {
            return _0x10e67f === _0x4b49ca;
        },
        'rksyU': 'BsquX',
        'hMiRz': 'wMZlV',
        'QzoLN': function (_0x363b1a, _0x10a362) {
            return _0x363b1a + _0x10a362;
        },
        'GbdAq': function (_0x48eb28, _0x441042) {
            return _0x48eb28 <= _0x441042;
        },
        'ggvQq': function (_0x1b3393, _0x1278e9) {
            return _0x1b3393 !== _0x1278e9;
        },
        'JENMp': 'TZNYj',
        'DafId': function (_0x504838, _0x910a04) {
            return _0x504838 + _0x910a04;
        },
        'wFsHX': function (_0x3d51c9, _0x39812d) {
            return _0x3d51c9 + _0x39812d;
        },
        'hdiBI': function (_0x3a5c8b, _0x207167) {
            return _0x3a5c8b + _0x207167;
        },
        'luSKs': function (_0x3c99f8, _0x3c3520) {
            return _0x3c99f8 + _0x3c3520;
        },
        'CDHZY': function (_0x258a1b, _0x1a8d94) {
            return _0x258a1b + _0x1a8d94;
        },
        'gcLfa': function (_0x1a1b0d, _0xb12570) {
            return _0x1a1b0d + _0xb12570;
        },
        'VdONp': function (_0x49fb7d, _0x143783) {
            return _0x49fb7d + _0x143783;
        },
        'YOvwb': function (_0x339880, _0x54d67c, _0x2c0d40, _0x4d4364) {
            return _0x339880(_0x54d67c, _0x2c0d40, _0x4d4364);
        },
        'HLkgr': '.png',
        'MuNhw': '_ABCD_',
        'MrrEr': function (_0x49f149, _0x4b9b2f, _0x375661, _0x2c3ba9) {
            return _0x49f149(_0x4b9b2f, _0x375661, _0x2c3ba9);
        },
        'dGqjQ': 'publicInfoQueryServlet',
        'bZZUP': function (_0x46c753, _0x5babb) {
            return _0x46c753 + _0x5babb;
        },
        'jgjDJ': function (_0x31061f, _0x200779) {
            return _0x31061f + _0x200779;
        },
        'Jerdd': function (_0x3649c4, _0x476321) {
            return _0x3649c4 + _0x476321;
        },
        'YGFHY': function (_0x1de424, _0x4d371) {
            return _0x1de424 + _0x4d371;
        },
        'uXvdo': function (_0x39a086, _0x1dd189) {
            return _0x39a086 + _0x1dd189;
        },
        'KzIex': function (_0x4deac6, _0x3fc757) {
            return _0x4deac6 + _0x3fc757;
        },
        'kehuk': function (_0x17c4fe, _0x2ede31) {
            return _0x17c4fe + _0x2ede31;
        },
        'FTRGK': function (_0x1ec2b7, _0x4f451d) {
            return _0x1ec2b7 + _0x4f451d;
        },
        'WUevL': function (_0xe873cf, _0x2b7f6d) {
            return _0xe873cf + _0x2b7f6d;
        },
        'eacwW': function (_0x25cb70, _0x1a5922) {
            return _0x25cb70 + _0x1a5922;
        },
        'nYfcA': function (_0x83e2b, _0x490525) {
            return _0x83e2b + _0x490525;
        },
        'ePktw': '/ecipplatform/page/jiangsuDetail/jiangsudetai1.html?',
        'keRsP': 'org=',
        'sddhC': '&id=',
        'uxeIM': '&seqId=',
        'jsiUd': '&activeTabId=',
        'zwnWB': '&keyWord=',
        'xfNMz': '&etp=',
        'Mtujf': '&eitp=',
        'nYlzY': '&esn='
    };
    var _0x3b8754 = new Date();
    var _0x2cd500 = _0x3b8754['getFullYear']();
    var _0x15998a = _0x381d1b['Hltut'](_0x3b8754['getMonth'](), 0x1);
    var _0x1ed052 = _0x3b8754['getDate']();
    var _0x3492d7 = _0x3b8754['getHours']();
    if (_0x381d1b['elnMx'](_0x15998a, 0x1) && _0x381d1b['unyQo'](_0x15998a, 0x9)) {
        _0x15998a = _0x381d1b['Hltut']('0', _0x15998a);
    }
    if (_0x381d1b['elnMx'](_0x1ed052, 0x1) && _0x381d1b['NrMCW'](_0x1ed052, 0x9)) {
        if (_0x381d1b['Gafhz'](_0x381d1b['rksyU'], _0x381d1b['hMiRz'])) {
            _0x381d1b['bMAYd'](result, '0');
        } else {
            _0x1ed052 = _0x381d1b['QzoLN']('0', _0x1ed052);
        }
    }
    if (_0x381d1b['elnMx'](_0x3492d7, 0x0) && _0x381d1b['GbdAq'](_0x3492d7, 0x9)) {
        if (_0x381d1b['ggvQq'](_0x381d1b['JENMp'], _0x381d1b['JENMp'])) {
            var _0xe8255c = {};
            var _0x24a44e = _0x381d1b['wLqpr'](getAesString, data['esn'], _0x150096, _0x150096);
            var _0xbf6b9a = JSON['stringify'](data);
            var _0x34c34b = _0x381d1b['wLqpr'](getAesString, _0xbf6b9a, _0x150096, _0x150096);
            _0xe8255c[_0x381d1b['paTDD']] = _0x24a44e;
            _0xe8255c[_0x381d1b['GWLwe']] = _0x34c34b;
            return _0xe8255c;
        } else {
            _0x3492d7 = _0x381d1b['DafId']('0', _0x3492d7);
        }
    }
    var _0x45d535 = _0x381d1b['wFsHX'](_0x381d1b['hdiBI'](_0x381d1b['hdiBI'](_0x381d1b['luSKs'](_0x381d1b['CDHZY'](_0x381d1b['gcLfa'](_0x2cd500, '-'), _0x15998a), '-'), _0x1ed052), '\x20'), _0x3492d7);
    var _0x42333e = _0x381d1b['VdONp'](_0x381d1b['VdONp'](_0x1ed052, _0x381d1b['YOvwb'](getAesString, _0x45d535, _0x150096, _0x150096)), _0x381d1b['HLkgr']);
    var _0x4990db = _0x381d1b['VdONp'](new Date()['getTime'](), '');
    var _0x1b4e90 = _0x381d1b['YOvwb'](getAesString, _0x4990db, _0x2d36af, _0x2d36af);
    var _0x3289aa = _0x381d1b['YOvwb'](getAesString, _0x381d1b['VdONp'](_0x381d1b['VdONp'](_0x4e7ed3, _0x381d1b['MuNhw']), _0x4990db), _0x2d36af, _0x2d36af);
    var _0xf29980 = _0x381d1b['MrrEr'](getAesString, _0x381d1b['dGqjQ'] + '_' + new Date()['getHours'](), _0x2d36af, _0x2d36af);
    var _0x173f66 = _0x381d1b['bZZUP'](_0x381d1b['jgjDJ'](_0x381d1b['Jerdd'](
        _0x381d1b['YGFHY'](_0x381d1b['uXvdo'](_0x381d1b['uXvdo'](_0x381d1b['KzIex'](_0x381d1b['KzIex'](_0x381d1b['KzIex'](_0x381d1b['kehuk'](_0x381d1b['kehuk'](_0x381d1b['FTRGK'](_0x381d1b['FTRGK'](_0x381d1b['WUevL'](_0x381d1b['eacwW'](_0x381d1b['nYfcA'](_0x381d1b['ePktw'], _0x381d1b['keRsP']), _0x33d270), _0x381d1b['sddhC']), _0x4e7ed3), _0x381d1b['uxeIM']), _0x53ace1), _0x381d1b['jsiUd']), _0x388949), _0x381d1b['zwnWB']), _0x3ff439), _0x381d1b['xfNMz']), _0x1b4e90), _0x381d1b['Mtujf']), _0x3289aa), _0x381d1b['nYlzY']), _0xf29980);
    return {
        "etp": _0x1b4e90,
        "eitp": _0x3289aa,
        "esn": _0xf29980,
        "register_url": 'http://www.jsgsj.gov.cn:58888/ecipplatform/webnew/jiangsulist/' + _0x42333e
    }
}

function get_etp(){
    return geetTp().etp
}

function get_details(org, id, seqId, activeTabId, keyWord, client_ip) {
    let infoQueryDataResult = openDetailPage(org, id, seqId, activeTabId, keyWord)
    let paramsObj = {
        "org": org,
        "id": id,
        "seqId": seqId,
        "activeTabId": activeTabId,
        "ecid": id,
        "eitp": infoQueryDataResult['eitp'],
        "etp": infoQueryDataResult['etp'],
        "esn": infoQueryDataResult['esn'],
    }

    let result = oneEncrypt(paramsObj);
    result['etp'] = infoQueryDataResult['etp']
    result['esn'] = infoQueryDataResult['esn']
    result['eitp'] = infoQueryDataResult['eitp']
    result['detail_url'] = "http://www.jsgsj.gov.cn:58888/ecipplatform/" + result.encryMethod + '.json?queryPageView=true&typeMethod=md5Method&' + result.encryData
    result['register_url'] = infoQueryDataResult['register_url']
    return JSON.stringify(result)
}

function get_end(name, page) {
    let infoQueryDataResult = infoQueryData(name)
    let paramsObj = {
        "name": name,
        "searchType": 'qyxx',
        "pageNo": page,
        "pageSize": 10,
        "ecid": name,
        "eitp": infoQueryDataResult['eitp'],
        "etp": infoQueryDataResult['etp'],
        "esn": infoQueryDataResult['esn'],
    }
    let result = globleEncrypt(paramsObj);

    let url_end = 'http://www.jsgsj.gov.cn:58888/ecipplatform/' + result['encryMethod'] + ".json?selectCorpList=true&typeMethod=md5Method_1&" + result['encryData']
    let final_result = {}
    final_result["list_url"] = url_end
    final_result["etp"] = infoQueryDataResult['etp']
    return JSON.stringify(final_result)
}

function get_investor_info(org, id, seqId, activeTabId, keyWord) {
    let infoQueryDataResult = openDetailPage(org, id, seqId, activeTabId, keyWord)
    let paramsObj = {
        "org": org,
        "id": id,
        "seqId": seqId,
        "activeTabId": activeTabId,
        "ecid": id,
        "eitp": infoQueryDataResult['eitp'],
        "etp": infoQueryDataResult['etp'],
        "esn": infoQueryDataResult['esn'],
    }

    let paramsObj2 = {
        "org": org,
        "id": id,
        "seqId": seqId,
        "activeTabId": activeTabId,
        "ecid": id,
        "eitp": infoQueryDataResult['eitp'],
        "etp": infoQueryDataResult['etp'],
        "esn": infoQueryDataResult['esn'],
        "type": "rj"
    }

    let paramsObj3 = {
        "org": org,
        "id": id,
        "seqId": seqId,
        "activeTabId": activeTabId,
        "ecid": id,
        "eitp": infoQueryDataResult['eitp'],
        "etp": infoQueryDataResult['etp'],
        "esn": infoQueryDataResult['esn'],
        "type": "sj"
    }

    let result1 = oneEncrypt(paramsObj);
    let result2 = oneEncrypt(paramsObj2);
    let result3 = oneEncrypt(paramsObj3);

    let final_result1 = {}
    let final_result2 = {}
    let final_result3 = {}

    final_result1['etp'] = infoQueryDataResult['etp']
    final_result1['detail_url'] = "http://www.jsgsj.gov.cn:58888/ecipplatform/" + result1.encryMethod + '.json?queryFqrczxxxx=true&typeMethod=md5Method&' + result1.encryData

    final_result2['etp'] = infoQueryDataResult['etp']
    final_result2['detail_url'] = "http://www.jsgsj.gov.cn:58888/ecipplatform/" + result2.encryMethod + '.json?queryFqrczxxxx=true&typeMethod=md5Method&' + result2.encryData

    final_result3['etp'] = infoQueryDataResult['etp']
    final_result3['detail_url'] = "http://www.jsgsj.gov.cn:58888/ecipplatform/" + result3.encryMethod + '.json?queryFqrczxxxx=true&typeMethod=md5Method&' + result3.encryData


    return JSON.stringify({"ori": final_result1, "rj": final_result2, "sj": final_result3})
}
