export PYTHONPATH=/home/<USER>/pygs-work-parent/

spider = crawler + parser

eventlog数据流如下：
Redis.zset --> crawler --> (Redis.fixed_len_list OR Kafka.xxx) --> parser --> (Kafka.data_parser)

crawler
OUTPUT: cid + (key1:content、key2:content) -> OBS

parser
OUTPUT: cid + (company_base_info, company_staff, company_investor, company_change_info) -> MSV


待开发功能：
1：多源多版本写 股东 主要人员 变更记录
2：新增公司  word/cid关系 is_clue 什么阶段查询cid
3：法人为执行事务合伙人情形
4：eventlog 操作parser和crawler dict
5 多快照备份  DONE


计划:
1 parser 后续考虑多进程
2 crawl用户函数 需要有超时检查
3 部署命令写到README
4 动态load配置文件 高级需求 不紧急
5 重复解析
