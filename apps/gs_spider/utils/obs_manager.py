# encoding=utf8
import json
import logging
from typing import Dict
from libs.dt import cur_ts_sec
from clients.obs_client import OBSClient

logger = logging.getLogger(__name__)


class OBSManager(object):

    def __init__(self, ):
        self.obs_client = OBSClient(bucket_name='jindi-oss-gsxt')

    def upload_pages(self, pages: Dict[str, str], bak_count: int, base_dir: str) -> int:
        """ 有数据则进行存储 """
        if len(pages) == 0:
            logger.warning(f'empty pages base_dir={base_dir}')
            return 0
        ts = cur_ts_sec()
        page_dir_list = sorted([dir_ for dir_ in self.obs_client.list(base_dir, recursive=False) if (base_dir + '/') in dir_])
        page_new_dir = f'{base_dir}/{ts}'
        if len(page_dir_list) >= bak_count:
            self.obs_client.delete(path=page_dir_list[0], recursive=True)
        for page_name, page_content in pages.items():
            self.obs_client.put(path=f'{page_new_dir}/{page_name}', data=page_content)
        return ts

    def upload_cache(self, cache: Dict[str, str], bak_count: int, cache_base_dir: str) -> int:
        """ 留存本次请求的相关信息 """
        if len(cache) == 0:
            return 0
        cache_dir_list = [dir_ for dir_ in self.obs_client.list(cache_base_dir) if dir_.endswith("cache.json")]
        if len(cache_dir_list) >= bak_count:
            self.obs_client.delete(path=cache_dir_list[0], recursive=True)
        self.obs_client.put(path=f'{cache_base_dir}', data=json.dumps(cache, ensure_ascii=False))

    def download_pages(self, page_dir: str) -> Dict[str, str]:
        pages = dict()
        page_dir_list = [dir_ for dir_ in self.obs_client.list(page_dir) if not dir_.endswith("cache.json")]
        for page_dir_item in page_dir_list:
            page = self.obs_client.get(page_dir_item)
            page_name = page_dir_item.split('/')[-1]
            pages[page_name] = page
        return pages

    def download_cache(self, cache_dir: str) -> Dict[str, str]:
        cache = dict()
        if self.obs_client.list(cache_dir):
            cache = json.loads(self.obs_client.get(cache_dir))
        return cache


if __name__ == '__main__':
    from libs.log import setup_logger

    logger = setup_logger()
    o = OBSManager()
    o.download_pages(page_dir='page/app2/123/1699328304')
