# encoding=utf8

import json
import logging
from typing import List, Dict, Tuple
from pydantic import Field
from gslib.gs_enum import EntityType
from entity.deps.entity import BaseEntity
from apps.gs_spider.utils.msv import msv_write
from apps.gs_spider.utils.msv_enum import MSVSource, MSVTableName

logger = logging.getLogger(__name__)


class MSVInvestor(BaseEntity):
    investor_name: str
    investor_type: EntityType = Field(default=EntityType.UNSET)  # 0 1 2 3
    investor_id: int = Field(default=0)  # cid
    capital: List[Dict] = Field(default=[])  # 认缴列表
    capital_actl: List[Dict] = Field(default=[])  # 实缴列表
    detail: Dict = Field(default={})  # 股东的属性信息 例如国籍nationality


def msv_write_investor(items: List[MSVInvestor], cid: int, source: MSVSource) -> Tuple[bool, Dict]:
    row_data = []
    for item in items:
        item_dict = item.to_dict()
        if 'capital' in item_dict:
            item_dict['capital'] = json.dumps(item_dict['capital'], ensure_ascii=False)
        if 'capital_actl' in item_dict:
            item_dict['capital_actl'] = json.dumps(item_dict['capital_actl'], ensure_ascii=False)
        if 'capital' in item_dict:
            item_dict['detail'] = json.dumps(item_dict['detail'], ensure_ascii=False)
        row_data.append(item_dict)

    if source == MSVSource.XA or source == MSVSource.GSXT:
        source = MSVSource.GSXT_PAGE

    return msv_write(
        table_name=MSVTableName.INVESTOR.value,
        cid=cid,
        source=str(source.value),
        row_data=row_data,
        list_mode=True,
    )


if __name__ == '__main__':
    from dao.company import TEST_COMPANY
    o1 = MSVInvestor.from_dict(
        {
            'investor_name': '股东1',
            'investor_type': 2,
            'investor_id': 101,
            'capital': [{"amomon": "100.0万元", "time": "2049-07-25", "paymet": "货币"}, ],

        }
    )

    o2 = MSVInvestor.from_dict(
        {
            'investor_name': '股东2',
            'investor_type': 2,
            'investor_id': 102,
            'capital': [{"amomon": "200.0万元", "time": "2049-07-25", "paymet": "货币"}, ],
        }
    )
    msv_write_investor(cid=TEST_COMPANY.cid, source=MSVSource.APP2, items=[o1, o2])
