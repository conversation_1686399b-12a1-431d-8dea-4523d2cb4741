# encoding=utf8

from typing import List, Dict, Tuple
import logging
import requests
logger = logging.getLogger(__file__)


def msv_write(
        table_name: str,
        cid: int,
        source: str,
        row_data: List[Dict[str, str]],
        retry=1,
        list_mode=False,
) -> Tuple[bool, Dict[str, bool]]:
    # success, changed = True, False
    d = {
        'table_name': table_name,
        'cid': cid,
        'source': source,
        'row_data': list({'column_values': row_item} for row_item in row_data)
    }

    url = 'http://msv-gsdata.jindidata.com/' + ('write_list' if list_mode else 'write_one')
    for try_id in range(retry + 1):
        try:
            response = requests.post(url=url, json=d, timeout=8.0)
            if response.status_code == 200:
                resp = response.json()
                if resp.get('errcode', None) == 0:
                    delete_num = resp.get('data', {}).get('delete_data', 0)
                    insert_num = resp.get('data', {}).get('insert_data', 0)
                    update_num = resp.get('data', {}).get('update_data', 0)
                    return True, dict(insert_data=insert_num, delete_data=delete_num, update_data=update_num)
            logger.warning(f'fail {try_id} {response.status_code} {response.text}')
        except Exception as e:
            logger.warning(f'fail {try_id} {e} {d}')
    return False, {}
