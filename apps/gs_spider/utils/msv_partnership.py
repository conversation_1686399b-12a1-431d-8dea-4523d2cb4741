# encoding=utf8

import logging
from typing import List, Tuple, Dict
from pydantic import Field
from entity.deps.entity import BaseEntity
from gslib.gs_enum import EntityType
from apps.gs_spider.utils.msv import msv_write
from apps.gs_spider.utils.msv_enum import MSVSource, MSVTableName

logger = logging.getLogger(__name__)


class MSVPartnership(BaseEntity):
    executive: str
    executive_id: int = Field(default=0)
    executive_gid: int = Field(default=0)
    executive_role: str = Field(default='执行事务合伙人')
    executive_type: EntityType = Field(default=EntityType.UNSET)  # 0 1 2 3
    executive_order: int = Field(default=1)  # 顺序 从1开始
    represent: str = Field(default='', alias='represen')
    represent_id: int = Field(alias='represen_id', default=0)
    represent_gid: int = Field(alias='represen_gid', default=0)


def msv_write_partnership(items: List[MSVPartnership], cid: int, source: MSVSource) -> <PERSON>ple[bool, Dict]:
    row_data = []
    for item in items:
        item_dict = item.to_dict()
        row_data.append(item_dict)

    return msv_write(
        table_name=MSVTableName.PARTNERSHIP.value,
        cid=cid,
        source=source.value,
        row_data=row_data,
        list_mode=True,
    )
