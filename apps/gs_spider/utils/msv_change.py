# encoding=utf8

import logging
from typing import List, Optional, Tuple, Dict
from datetime import date, datetime
from pydantic import Field
from entity.deps.entity import BaseEntity
from apps.gs_spider.utils.msv import msv_write
from apps.gs_spider.utils.msv_enum import MSVSource, MSVTableName

logger = logging.getLogger(__name__)


class MSVChange(BaseEntity):
    change_item: Optional[str] = Field(default=None)
    change_time: Optional[date] = Field(default=None)
    content_before: Optional[str] = Field(default=None)
    content_after: Optional[str] = Field(default=None)


def msv_write_change(items: List[MSVChange], cid: int, source: MSVSource) -> Tuple[bool, Dict]:
    row_data = []
    for item in items:
        item_dict = item.to_dict()
        row_data.append(item_dict)

    return msv_write(
        table_name=MSVTableName.CHANGE.value,
        cid=cid,
        source=source.value,
        row_data=row_data,
        list_mode=True,
    )


if __name__ == '__main__':
    from dao.company import TEST_COMPANY
    o1 = MSVChange.from_dict(
        {
            'change_item': '主要人员变更',
            'change_time': datetime.now().date(),
            'content_after': '123',
        }
    )

    o2 = MSVChange.from_dict(
        {
            'change_item': 'xx变更',
            'change_time': datetime.now().date(),
            'content_before': '1123',
            'content_after': '123',
        }
    )
    msv_write_change(cid=TEST_COMPANY.cid, source=MSVSource.APP2, items=[o1, o2])
