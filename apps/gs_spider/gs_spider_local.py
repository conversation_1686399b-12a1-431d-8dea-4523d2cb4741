# encoding=utf8
from apps.gs_spider.crawler import SZCrawler2
from apps.gs_spider.parser import SZParser2
from dao.company import CompanyDao, Company
from libs.log2 import setup_logger
import time
from clients.kafka_client import KafkaProducerClient
from libs.env import get_env_prop
from entity.eventlog import Eventlog, SpiderCode

company_dao = CompanyDao()
fusion_producer_client = KafkaProducerClient(**get_env_prop(f'kafka.tyc.gs'), kafka_topic='gsxt.data_fusion')

if __name__ == '__main__':
    # 本地抓取（更新/新增）一个公司
    logger = setup_logger(process_safe=True)

    inst_name, word, is_clue = 'gdsz', '2855104928', False
    crawler = SZCrawler2(name='sz')
    parser = SZParser2(name='sz')

    ts = int(time.time())

    if not is_clue:
        company: Company = company_dao.get(int(word))
        if not company or len(company.credit_code or '') != 18:
            logger.error(f'bad company for {word}')
            exit(1)
        keyword = company.credit_code
    else:
        keyword = word

    eventlog = Eventlog.from_dict(
        {
            "event_id": f"octopus_{'clue' if is_clue else 'entry'}-company-{word}-{inst_name}-{ts}",
            "is_clue": is_clue,
            "spider_code": -1,
            "crawlerType": 1,
            "crawlerCode": -1,
            "parserCode": -1,
            "fusionCode": -1,
            "selector": {
                "send_ts": ts,
                "receive_ts": -1,
                "reason": 'local_update',
                "item_name": 'company',
                "inst_name": inst_name,
                "word": word,
                "info": {
                    "keyword": keyword,
                },
                "try_id": 0,
                "meta": None,
                "weight": 999,
            },
            "spider": {
                "receive_ts": -1,
                "send_ts": -1,
                "item_insert": False,
                "ab_info": {}
            },
            "crawler": {},
            "parser": {},
            "fusion": {},
            "dims": {},
            "channel": {}
        }
    )

    crawler.crawl(eventlog)
    parser.parse(eventlog)
    logger.info(f'OUTPUT eventlog={eventlog.to_json()}')
    if eventlog.spider_code == SpiderCode.SUCCESS:
        eventlog.spider.send_ts = int(time.time())
        eventlog_str = eventlog.to_json()
        ret = fusion_producer_client.write(eventlog_str)
