import json
from libs.dt import to_date
from entity.eventlog import Eventlog, SpiderCode
from apps.gs_spider.utils.msv_enum import MSVSource
from apps.gs_spider.parser.parser_gsxt import GSXT<PERSON>arserTask, GSXTParser, MSVBaseInfo, MSVPartnership, MSVInvestor, \
    MSVStaff, MSVChange
import re
from loguru import logger
from clients.mysql_client import MySQLClient
from libs.env import ConstantProps
from datetime import datetime


class SZParserTask(GSXTParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        self.mysql_client = MySQLClient(**ConstantProps.PROPS_GS_OUTER_RW)
        super().__init__(eventlog, pages)


class SZParser(GSXTParser):
    def __init__(self, name: str):
        super().__init__(name, task_cls=SZParserTask, msv_source=MSVSource.GDSZ)

    def do_parse(self):
        task: SZParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        pages = task.pages

        base_info = pages.get('base_info.txt', None)
        if not base_info:
            logger.warning(f'no page base {eventlog}')
            eventlog.spider_code = SpiderCode.FAIL
            return
        base_info = json.loads(base_info)

        # 标记
        sql = f'select * from prism.company where id = %s'
        info_pre = task.mysql_client.select(sql, args=(eventlog.selector.word,))
        if info_pre and info_pre.get('approved_time', ''):
            approved_time_pre = info_pre['approved_time']
            approved_time = datetime.strptime(base_info['apprdate'], '%Y-%m-%d')
            if approved_time > approved_time_pre:
                eventlog.spider.ab_info['time_changed'] = f"{approved_time_pre} --> {approved_time}"

        scope_info: dict = json.loads(pages['许可经营.txt'])[0]
        business_scope = f'一般经营项目是：{scope_info.get("cbuitem", "")}，许可经营项目是：{scope_info.get("pabuitem", "")}'

        msv_base_info_data = dict(
            name=base_info['entname'],
            # legal_person_name=base_info['lerep'],
            legal_person_name=re.sub(r'([(（]委派代表.*?[）)])$', '', base_info['lerep']),
            reg_number=base_info.get('regno', None),
            company_org_type=base_info.get('enttype', None),
            reg_location=base_info.get('dom', None),
            estiblish_time=to_date(base_info['estdate']),
            approved_time=to_date(base_info['apprdate']),
            from_time=to_date(base_info['estdate']),
            # to_time=to_time,
            business_scope=business_scope,
            # reg_institute=base_info.get('regOrg_CN', None),
            reg_status=base_info.get('entstatus', None),
            reg_capital=str(base_info['regcap']) + "万" + base_info.get('currency', '人民币') if (
                    'regcap' in base_info) else None,
            credit_code=base_info.get('unifsocicrediden', None),
        )
        msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)

        msv_partnerships = None
        company_org_type = msv_base_info.company_org_type
        legal_person_name = msv_base_info.legal_person_name
        if company_org_type and '合伙' in company_org_type and legal_person_name:
            msv_partnerships = [MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split(',')]
        task.set_base(msv_base_info, msv_partnerships)

        msv_investors = [MSVInvestor.from_dict(
            {
                'investor_name': x['inv'],
                'investor_type': 0,
                'capital': [{
                    'amomon': f"{x['subconam']}万元",
                    'time': '',
                    'paymet': ''
                }] if ('subconam' in x) else [],
                'detail': {
                    'amomon': x.get('subconam', None),
                    '合伙人/股东属性': x.get('invatt', None),
                    '合伙人类别': x.get('responway', None),
                    '股东类别': x.get('invtype', None),
                    'shareholder_type': x.get('responway', '') + x.get('invtype', '')
                }
            }) for x in json.loads(pages['股东信息.txt'])]
        task.set_investors(msv_investors)

        msv_staff = [MSVStaff.from_dict({
            'staff_name': i['name'],
            'staff_position': i['post']
        }) for i in json.loads(pages['主要人员.txt']) if i != {} and 'post' in i]
        task.set_staffs(msv_staff)

        msv_change = [MSVChange.from_dict(
            {
                'change_item': row['valueNew'],
                'change_time': to_date(row['altdate']),
                'content_before': row.get('altbe', ''),
                'content_after': row.get('altaf', '')
            }
        ) for row in json.loads(pages['变更信息.txt'])]
        task.set_changes(msv_change)

a = {
    "data": [
        {
            "data": [
                {
                    "name": "张庆平",
                    "posbrform": "任命",
                    "post": "监事"
                },
                {
                    "name": "章威",
                    "posbrform": "聘任",
                    "post": "总经理"
                },
                {
                    "name": "章威",
                    "posbrform": "由股东（ 大）会或股东选举、委派产生",
                    "post": "执行董事"
                }
            ],
            "name": "data",
            "vtype": "attr"
        }
    ]
}

b = {
    "data": [
        {
            "data": [
                {
                    "id": "C90698D39EA000018DD41D701C207BD0",
                    "inv": "王欣",
                    "invatt": "自然人",
                    "invtype": "自然人股东",
                    "subconam": 450
                },
                {
                    "id": "C90698D39F300001B2211BE217A06E80",
                    "inv": "深圳市聚播投资合伙企业（有限合伙）",
                    "invatt": "本地企业",
                    "invtype": "企业法人",
                    "subconam": 50
                }
            ],
            "name": "data",
            "vtype": "attr"
        }
    ]
}

c = {
    "data": [
        {
            "data": [
                {
                    "cbuitem": "互联网产品开发；网络技术咨询、技术开发、技术推广、技术转让；计算机系统设计；计算机编程；计算机系统集成；数据处理；网络信息、计算机技术领域内的技术开发、技术转让、技术咨询、技术服务；计算机软硬件的技术开发与销售；软件设计开发；电脑图文设计；通讯产品、网络设备、电子控制设备软硬件的技术开发与销售；经营电子商务；国内贸易；经营进出口业务；网络信息技术开发、技术服务、技术咨询；从事广告业务；展览展示策划；翻译服务；文化活动策划；公关活动策划；企业形象策划、设计；会议服务；市场调研；教育咨询（以上法律、行政法规、国务院决定禁止的项目除外，限制的项目须取得许可后方可经营）。",
                    "pabuitem": "经营性互联网信息服务，人力资源服务。",
                    "rownum": 1
                }
            ],
            "name": "data",
            "vtype": "attr"
        }
    ]
}

d  = {
    "data": [
        {
            "data": [
                {
                    "apprdate": "2025-03-05",
                    "currency": "人民币",
                    "dom": "深圳市南山区粤海街道科技园社区科苑路8号讯美科技广场3号楼10A59",
                    "entcode": "1130",
                    "entflag": "440300000012018022601843",
                    "entname": "深圳市云歌人工智能技术有限公司",
                    "entstatus": "开业（存续）",
                    "enttype": "有限责任公司",
                    "estdate": "2018-02-26",
                    "exportaddr": "",
                    "id": "8a80845d616fda0b0161c5b562720770",
                    "ismultiaddrent": "0",
                    "jinyinfuben": 1,
                    "lerep": "章威",
                    "opetype": "GS",
                    "opfrom": "2018-02-26",
                    "opscope": "互联网产品开发；网络技术咨询、技术开发、技术推广、技术转让；计算机系统设计；计算机编程；计算机系统集成；数据处理；网络信息、计算机技术领域内的技术开发、技术转让、技术咨询、技术服务；计算机软硬件的技术开发与销售；软件设计开发；电脑图文设计；通讯产品、网络设备、电子控制设备软硬件的技术开发与销售；经营电子商务；国内贸易；经营进出口业务；网络信息技术开发、技术服务、技术咨询；从事广告业务；展览展示策划；翻译服务；文化活动策划；公关活动策划；企业形象策划、设计；会议服务；市场调研；教育咨询（以上法律、行政法规、国务院决定禁止的项目除外，限制的项目须取得许可后方可经营）。",
                    "regcap": 500,
                    "regno": "440300203717601",
                    "rownum": 1,
                    "unifsocicrediden": "91440300MA5F0KKH0X"
                }
            ],
            "name": "data",
            "vtype": "attr"
        }
    ]
}

e = {
    "data": [
        {
            "data": [
                {
                    "persname": "章威"
                }
            ],
            "name": "data",
            "vtype": "attr"
        }
    ]
}