# encoding=utf8

import logging
from abc import abstractmethod
from typing import List, Optional
from entity.eventlog import Eventlog
from gslib.gs_enum import EntityType
from gslib.id_center import id_center_query
from libs.dt import date2str
from dao.investors.equity_ratio import EquityRatioDao
from service.graph_id import GraphIdService
from apps.gs_spider.utils.msv_enum import MSVSource, MSVTableName
from apps.gs_spider.utils.msv_base_info import MSVBaseInfo, msv_write_base_info
from apps.gs_spider.utils.msv_investor import MSVInvestor, msv_write_investor
from apps.gs_spider.utils.msv_staff import MSVStaff, msv_write_staff
from apps.gs_spider.utils.msv_change import MSVChange, msv_write_change
from apps.gs_spider.utils.msv_partnership import MSVPartnership, msv_write_partnership
from apps.gs_spider.parser.parser import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, SpiderCode
import re
from gslib.credit_code import credit_code_valid

logger = logging.getLogger(__name__)
graph_id_service = GraphIdService()


class GSXTParserTask(ParserTask):
    equity_ratio_dao = EquityRatioDao()

    def __init__(self, eventlog: Eventlog, pages):
        self.cid: Optional[int] = None
        self.msv_base_info: Optional[MSVBaseInfo] = None
        self.msv_investors: List[MSVInvestor] = []
        self.msv_staffs: List[MSVStaff] = []
        self.msv_changes: List[MSVChange] = []
        self.msv_partnerships: List[MSVPartnership] = []
        self.eventlog: Optional[Eventlog] = None
        super().__init__(eventlog, pages)

    def set_base(self, base: MSVBaseInfo, partners: List[MSVPartnership] = None):
        partners = partners or []
        for pid, partner in enumerate(partners):
            if partner.executive_id == 0 and len(partner.executive) > 1:
                id_center_result = id_center_query(name=partner.executive, type=partner.executive_type)
                partner.executive_type, partner.executive_id = id_center_result
            if len(partner.represent) > 1:
                _, partner.represent_id = id_center_query(name=partner.represent, type=EntityType.HUMAN)
            partner.executive_order = pid + 1

        # 更新法人信息 legal_person_type legal_person_id
        # 合伙企业，法人id=0 法人类型=1 法人名称用至多5个执行事务合伙人join by 【、】
        if len(partners) > 1:
            base.legal_person_type, base.legal_person_id = EntityType.UNSET, 0
            base.legal_person_name = '、'.join(p_item.executive for p_item in partners[:5])
        elif len(partners) == 1:
            base.legal_person_name = partners[0].executive
            id_center_result = id_center_query(name=base.legal_person_name)
            base.legal_person_type, base.legal_person_id = id_center_result
        elif base.legal_person_name is not None:
            if len(base.legal_person_name) <= 4:
                base.legal_person_type = EntityType.HUMAN
            id_center_result = id_center_query(name=base.legal_person_name, type=base.legal_person_type)
            base.legal_person_type, base.legal_person_id = id_center_result
        else:
            base.legal_person_type, base.legal_person_id = 1, 0

        # 获取cid 并和eventlog.cid做对比
        # 20141108：增加统代有效性的判断，去掉注册号的统代调度
        # if base.credit_code is not None and len(base.credit_code) > 1:
        if credit_code_valid(base.credit_code or ''):
            _, cid = id_center_query(credit_no=base.credit_code)
            if cid == 0:
                _, cid = id_center_query(credit_no=base.credit_code, name=base.name, type=EntityType.ORG, allow_empty_company=True)
                if cid != 0:
                    if not self.eventlog.is_clue:
                        logger.warning(f'eventlog is not clue but item_insert cid={cid} {self.event_id}')
                    self.eventlog.spider.item_insert = True
            else:
                if self.eventlog.is_clue:
                    logger.warning(f'eventlog is clue but not item_insert cid={cid} {self.event_id}')
            self.eventlog.cid = cid
            self.cid = cid
            if str(cid) != self.eventlog.selector.word and re.fullmatch(r'\d+', self.eventlog.selector.word):
                logger.warning(f'diff cid id_center={cid} {base} {self.event_id}')
            self.msv_base_info = base
            self.msv_partnerships = partners
        else:
            logger.warning(f'base not set, bad credit_code={base.credit_code} {self.event_id}')

        #     if cid == 0:
        #         logger.warning(f'cannot get cid from credit_code {base} {self.event_id}')
        # if cid == 0 and base.reg_number is not None and len(base.reg_number) > 1:
        #     _, cid0 = id_center_query(reg_number=base.reg_number)
        #     _, cid = id_center_query(reg_number=base.reg_number, name=base.name, type=EntityType.ORG, allow_empty_company=True)
        #     if cid == 0:
        #         logger.warning(f'cannot get cid from reg_number {base} {self.event_id}')
        # if cid != 0 or cid0 != 0:
        #     if self.eventlog.is_clue:
        #         logger.info(f'new company cid={cid} {base} {self.event_id}')
        #         self.cid = cid
        #         if cid0 == 0:
        #             self.eventlog.spider.item_insert = True
        #             logger.info(f'set item_insert of clue {self.event_id}')
        #     else:
        #         if cid != int(self.eventlog.selector.word):
        #             logger.warning(f'diff cid id_center={cid} {base} {self.event_id}')
        #         else:
        #             self.cid = cid

    def set_investors(self, investors: List[MSVInvestor]):
        if not self.cid:
            logger.warning(f'cid not set {self.event_id}')
            return
        for investor in investors:
            if investor.investor_id != 0:
                continue
            if len(investor.investor_name) <= 4 and investor.investor_type == EntityType.UNSET:
                investor.investor_type = EntityType.HUMAN
            name_list = re.findall('（[a-zA-Z ]+）', investor.investor_name)
            name = investor.investor_name
            if name_list:
                name = name.replace(name_list[0], '')
            id_center_result = id_center_query(name=name, type=investor.investor_type)
            investor.investor_type, investor.investor_id = id_center_result

        self.msv_investors = investors

        # parser打标记 parser.equity_ratio_diff=True/False
        investor_id_iter = (graph_id_service.get_graph_id(inv.investor_type, inv.investor_id) for inv in investors)
        msv_investor_id_list = sorted(filter(lambda x: x > 0, investor_id_iter))

        company_graph = graph_id_service.company_graph_dao.get_by_cid(self.cid)
        if company_graph:
            equity_ratio_iter = self.equity_ratio_dao.get_many(value=company_graph.cgid, field='company_graph_id')
            equity_ratio_list = filter(lambda x: x.deleted == 0 and x.source != 100, equity_ratio_iter)
            equity_ratio_id_list = sorted(equity_ratio.shareholder_graph_id for equity_ratio in equity_ratio_list)
            equity_ratio_diff = (equity_ratio_id_list != msv_investor_id_list)
            logger.info(f'equity_ratio_diff={equity_ratio_diff} {equity_ratio_id_list} {msv_investor_id_list} {self.event_id}')
            self.eventlog.parser['equity_ratio_diff'] = equity_ratio_diff

    def set_staffs(self, staffs: List[MSVStaff]):
        if not self.cid:
            logger.warning(f'cid not set {self.event_id}')
            return
        credit_code = self.msv_base_info.credit_code
        if credit_code and credit_code.startswith('93'):
            return
        for staff in staffs:
            if staff.staff_id != 0:
                continue
            _, staff.staff_id = id_center_query(name=staff.staff_name, type=EntityType.HUMAN)
        self.msv_staffs = staffs

    def set_changes(self, changes: List[MSVChange]):
        if not self.cid:
            logger.warning(f'cid not set {self.event_id}')
            return
        self.msv_changes = changes

    def msv_save(self, source: MSVSource) -> bool:
        eventlog = self.eventlog
        eventlog.parser['msv_res'] = {}

        # 照面信息如果不存在，直接返回失败
        if not self.cid:
            return False
        if self.msv_base_info is None:
            logger.warning(f'msv_base_info null {self.event_id}')
            return False

        success, changed = msv_write_base_info(cid=self.cid, source=source, item=self.msv_base_info)
        if not success:
            logger.warning(f'fail at msv_write_base_info {self.event_id}')
            return False
        eventlog.parser['msv_res'][MSVTableName.BASE.value] = changed

        if len(self.msv_partnerships) > 0:
            success, changed = msv_write_partnership(cid=self.cid, source=source, items=self.msv_partnerships)
            if not success:
                logger.warning(f'fail at msv_write_partnership {self.event_id}')
                return False
            eventlog.parser['msv_res'][MSVTableName.PARTNERSHIP.value] = changed

        if len(self.msv_investors) > 0:
            success, changed = msv_write_investor(cid=self.cid, source=source, items=self.msv_investors)
            if not success:
                logger.warning(f'fail at msv_write_investor {self.event_id}')
                return False
            eventlog.parser['msv_res'][MSVTableName.INVESTOR.value] = changed

        if len(self.msv_staffs) > 0:
            success, changed = msv_write_staff(cid=self.cid, source=source, items=self.msv_staffs)
            if not success:
                logger.warning(f'fail at msv_write_staff {self.event_id}')
                return False
            eventlog.parser['msv_res'][MSVTableName.STAFF.value] = changed

        if len(self.msv_changes) > 0:
            success, changed = msv_write_change(cid=self.cid, source=source, items=self.msv_changes)
            if not success:
                logger.warning(f'fail at msv_write_change {self.event_id}')
                return False
            eventlog.parser['msv_res'][MSVTableName.CHANGE.value] = changed
        return True


class GSXTParser(Parser):
    def __init__(self, name: str, task_cls, msv_source: MSVSource):
        self.name = name  # the spider's name
        self.msv_source: MSVSource = msv_source
        super().__init__(name, task_cls)

    @abstractmethod
    def do_parse(self):
        pass

    def post_parse(self):
        task: GSXTParserTask = self.get_parser_task()
        eventlog = task.eventlog
        if not task.msv_save(self.msv_source):
            logger.warning(f'msv save fail {task.event_id}')
            task.eventlog.spider_code = SpiderCode.FAIL

        if eventlog.spider_code == SpiderCode.UNFILLED and eventlog.is_clue and eventlog.spider.item_insert:
            # 设置 eventlog.selector.info['entry_word'] 以及 eventlog.selector.info['entry_*']
            eventlog.selector.info['entry_word'] = task.cid
            eventlog.selector.info['entry_name'] = task.msv_base_info.name
            eventlog.selector.info['entry_reg_status'] = task.msv_base_info.reg_status
            eventlog.selector.info['entry_credit_code'] = task.msv_base_info.credit_code
            eventlog.selector.info['entry_org_type'] = task.msv_base_info.company_org_type
            eventlog.selector.info['entry_establish_date'] = date2str(task.msv_base_info.establish_date)

        super().post_parse()
