import json
import logging
import re
from datetime import datetime
import bs4
from apps.gs_spider.parser.parser import ParserTask, Parser
from dao.nhc.nhc_hospital import NhcHospitalDao
from entity.eventlog import Eventlog, SpiderCode
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class ParserNHCTask(ParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        super().__init__(eventlog, pages)

    pass


class ParserNHC(Parser):
    def __init__(self, name: str):
        super().__init__(name, ParserNHCTask)
        self.NhcHospitalDao = NhcHospitalDao()

    def do_parse(self, *args, **kwargs):
        task: ParserNHCTask = self.get_parser_task()
        eventlog = task.eventlog
        for page in task.pages:
            self.Details_parse(eventlog, json.loads(task.pages[page]))
        eventlog.spider_code = SpiderCode.UNFILLED

    def Details_parse(self, eventlog, html):
        try:
            soup = bs4.BeautifulSoup(html, 'lxml')
            status = soup.select('.details_co .row')
            info = dict()
            map_ = {'省份': 'province', '审批机关': 'office', '备案机关':'office', '登记号': 'reg_code', '地址': 'address',
                    '诊疗科目': 'diagnosis_sub', '级别': 'hospital_level', '法定代表人': 'legal_name',
                    '主要负责人': 'principal_name',
                    '执业许可证有效期': 'validity', '备案编号': 'reg_code'}

            info.update({'name_hospital': soup.select('.page-header.text-center')[0].text})
            info.update({'deleted': 0})
            for s in status:
                field_title = s.select('.field_title')[0].text.replace('：', '')
                field_cxt = s.select('.field_cxt')[0].text.strip() if s.select('.field_cxt') else ''
                if field_title not in map_:
                    continue
                if field_title == '诊疗科目':
                    field_cxt = ';'.join(map(lambda x: x.strip().replace('*', ''), re.split(r'\s*\|\s*|/', field_cxt)))
                    info.update({map_[field_title]: field_cxt})
                elif field_title == '执业许可证有效期':
                    field_cxt = list(
                        map(lambda x: datetime.strptime(x.strip(), "%Y/%m/%d"), re.split(r'\n-至-\n', field_cxt)))
                    info.update({map_[field_title] + '_start': field_cxt[0]})
                    info.update({map_[field_title] + '_end': field_cxt[1]})
                else:
                    info.update({map_[field_title]: field_cxt})
            logger.info(info)
            self.NhcHospitalDao.i_u_by_any(**info)
            logger.info(f'{eventlog.selector.word} - 解析入库完成')

        except Exception as e:
            logger.error(
                '{} - 解析出现问题 - {} -  row={}'.format(eventlog.selector.word, e, e.__traceback__.tb_lineno))
