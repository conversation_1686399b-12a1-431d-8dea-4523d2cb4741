# encoding=utf8
import json
import logging
import re

from libs.dt import to_date
from entity.eventlog import SpiderCode
from apps.gs_spider.utils.msv_enum import MSVSource
from apps.gs_spider.parser.parser_gsxt import GSXTParserTask, GSXTParser, MSVBaseInfo, MSVPartnership, MSVInvestor, MSVStaff
from dao.company_supplement import CompanySupplementDao, CompanySupplement

logger = logging.getLogger(__name__)


class APP2ParserTask(GSXTParserTask):
    pass


class APP2Parser(GSXTParser):
    def __init__(self, name: str):
        self.company_sup_dao = CompanySupplementDao()
        super().__init__(name, APP2ParserTask, MSVSource.APP2)

    def do_parse(self):
        task: APP2ParserTask = self.get_parser_task()
        eventlog = task.eventlog
        pages = task.pages

        base_content = pages.get('base_info.txt', None)
        if not base_content:
            logger.warning(f'no page base {task.event_id}')
            eventlog.spider_code = SpiderCode.FAIL
            return

        base_content_d: dict = json.loads(base_content)
        base_info = base_content_d['result']

        reg_capital = None
        if 'regCap' in base_info and 'regCapCur_CN' in base_info:
            reg_capital = '{}万{}'.format(base_info['regCap'], base_info['regCapCur_CN'])

        msv_base_info_data = dict(
            name=base_info['entName'],
            legal_person_name=base_info['name'],
            reg_number=base_info.get('regNo', None),
            company_org_type=base_info.get('entType_CN', None),
            reg_location=base_info.get('dom', None),
            estiblish_time=to_date(base_info['estDate']),
            approved_time=to_date(base_info['apprDate']),
            from_time=base_info.get('opFrom', None),
            to_time=base_info.get('opTo', None),
            business_scope=base_info.get('opScope', '').strip(),
            reg_institute=base_info.get('regOrg_CN', None),
            reg_status=base_info.get('regState_CN', None),
            reg_capital=reg_capital,
            credit_code=base_info['uniscId'],
        )
        msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)
        if not msv_base_info:
            eventlog.spider_code = SpiderCode.GIVE_UP
        company_org_type = msv_base_info.company_org_type
        legal_person_name = msv_base_info.legal_person_name
        msv_partnerships = []
        if company_org_type and '合伙' in company_org_type and legal_person_name:
            msv_partnerships = list(MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split('、'))
        task.set_base(msv_base_info, msv_partnerships)

        msv_investors = [MSVInvestor.from_dict(
            {
                'investor_name': x['inv'],
                'investor_type': 0,
                'capital': [{
                    'amomon': x.get('liSubConAm'),
                    'time': '',
                    'paymet': ''
                }],
                'capital_actl': [{
                    'amomon': f"{x.get('liAcConAm')}万元" if x.get('liAcConAm') else None,
                    'time': '',
                    'paymet': ''
                }],
                'detail': {
                    'amomon': x.get('liSubConAm'),
                }
            }) for x in json.loads(pages['investor_info.txt'])]
        task.set_investors(msv_investors)

        msv_staff = [MSVStaff.from_dict({
            'staff_name': i['name'],
            'staff_position': i['position_CN']
        }) for i in json.loads(pages['staff_info.txt'])]
        task.set_staffs(msv_staff)

        company_sup: CompanySupplement = CompanySupplement.from_dict(dict(
            company_id=task.cid,
            name=msv_base_info.name,
            establish_date=msv_base_info.establish_date,
        ))
        if company_sup:
            # company_supplement.company_org_code
            pre_ent_type = base_content_d.get('preEntType', '')
            if re.fullmatch(r'\d{4}', pre_ent_type):
                company_sup.company_org_code = pre_ent_type
            # company_supplement.industry_code
            industry_code = base_info.get('industryPhy', '')
            if len(industry_code) == 1:
                company_sup.industry_code = industry_code
            ret = self.company_sup_dao.save_by_cmp(item=company_sup, fields=['company_id', ], ignore_fields=['ent_type', 'id'])
            logger.info(f'save_by_cmp {company_sup} ret={ret}')
