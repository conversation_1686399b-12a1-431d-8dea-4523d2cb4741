# encoding=utf8

import logging
from abc import abstractmethod
from threading import Lock, current_thread
from typing import Dict, List
from entity.eventlog import Eventlog, SpiderCode
from apps.spider.utils.obs_manager import OBSManager

logger = logging.getLogger(__name__)


class ParserTask(object):
    def __init__(self, eventlog: Eventlog, pages):
        self.eventlog: Eventlog = eventlog
        self.event_id = self.eventlog.event_id
        self.pages: Dict[str, str] = pages


class Parser(object):
    def __init__(self, name: str, task_cls):
        self.name = name  # the spider's name
        self.task_cls = task_cls
        self.obs_manager = OBSManager()
        self.tasks: Dict[int, task_cls] = dict()
        self.tasks_lock = Lock()

    def parse(self, eventlog: Eventlog) -> Eventlog:
        logger.info(f'==== BEGIN {eventlog}')

        # filter bad eventlog
        if eventlog.spider_code != SpiderCode.UNFILLED:
            return eventlog

        page_ts = eventlog.spider.spider_data.get('page_ts', 0)
        if page_ts == 0:
            eventlog.spider_code = SpiderCode.FAIL
            logger.warning(f'page_ts bad {eventlog.event_id}')
            return eventlog

        pages_obs_path = f'page/{eventlog.selector.inst_name}/{eventlog.selector.word}/{page_ts}'
        pages = self.obs_manager.download_pages(pages_obs_path)

        with self.tasks_lock:
            tid = current_thread().ident
            self.tasks[tid] = self.task_cls(eventlog, pages)
        self.do_parse()

        if eventlog.spider_code == SpiderCode.UNFILLED:
            self.post_parse()

        # 最后设置成功
        if eventlog.spider_code == SpiderCode.UNFILLED:
            eventlog.spider_code = SpiderCode.SUCCESS
            eventlog.crawler_code = 0
            eventlog.parser_code = 0

        return eventlog

    @abstractmethod
    def do_parse(self):
        pass

    def post_parse(self):
        # task: ParserTask = self.get_parser_task()
        # if eventlog.is_clue:
            # eventlog.spider.item_insert 这个需要 业务自己处理 parser_gsxt已处理
            # 设置 eventlog.selector.info['entry_word'] 以及 eventlog.selector.info['entry_*']
        pass

    def get_parser_task(self):
        with self.tasks_lock:
            tid = current_thread().ident
            if tid not in self.tasks:
                raise RuntimeError(f'not task tid={tid}')
            return self.tasks[tid]
