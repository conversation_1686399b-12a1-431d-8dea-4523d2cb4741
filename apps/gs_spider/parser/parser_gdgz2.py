import json
from libs.dt import to_date
from entity.eventlog import Eventlog, SpiderCode
from apps.gs_spider.utils.msv_enum import MSVSource
from apps.gs_spider.parser.parser_gsxt import GSXT<PERSON>arserTask, GSXTParser, MSVBaseInfo, MSVPartnership, MSVInvestor, \
    MSVStaff, MSVChange
from apps.gs_spider.parser.parser import <PERSON>rser, ParserTask
import re
from loguru import logger
from clients.mysql_client import MySQLClient
from libs.env import ConstantProps
from datetime import datetime
from clients.mysql_client import MySQLClient
from libs.env import ConstantProps
from clients.redis._redis import Redis
import time


class GDGZParserTask(GSXTParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        self.search = eventlog.selector.get_info('keyword')
        super().__init__(eventlog, pages)


class GDGZParser2(GSXTParser):
    def __init__(self, name: str):
        # self.mysql_client = MySQLClient(**ConstantProps.PROPS_GS_OUTER_RW)
        # self.redis_client = Redis(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com', password="3lvadmpiSj61ge", db=0)
        super().__init__(name, task_cls=GDGZParserTask, msv_source=MSVSource.GZ)

    def do_parse(self):
        task: GDGZParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        base_info = task.pages.get('base_info.txt', None)
        if not base_info:
            logger.warning(f'no page base {eventlog}')
            eventlog.spider_code = SpiderCode.FAIL
            return
        base_info = json.loads(base_info)

        if '执行事务合伙人' in base_info:
            base_info['法定代表人'] = base_info['执行事务合伙人']
        if '经营者' in base_info:
            base_info['法定代表人'] = base_info['经营者']
        dates = re.findall(r'(\d{4}年\d{1,2}月\d{1,2}日)', base_info.get('营业期限'))
        msv_base_info_data = dict(
            name=base_info['名称'],
            legal_person_name=re.sub(r'([(（]委派代表.*?[）)])$', '', base_info['法定代表人']),
            reg_number=base_info.get('注册号'),
            company_org_type=base_info.get('商事主体类型'),
            reg_location=base_info.get('地址'),
            estiblish_time=to_date(base_info['成立日期']),
            approved_time=to_date(base_info['核准日期']),
            from_time=to_date(dates[0]) if dates else None,
            to_time=to_date(dates[1]) if len(dates) > 1 else None,
            business_scope=base_info.get('经营范围'),
            reg_institute=base_info.get('登记机关'),
            reg_status=base_info.get('主体状态'),
            reg_capital=base_info.get('注册资本'),
            credit_code=base_info.get('统一社会信用代码'),
        )
        msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)

        msv_partnerships = None
        company_org_type = msv_base_info.company_org_type
        legal_person_name = msv_base_info.legal_person_name
        if company_org_type and '合伙' in company_org_type and legal_person_name:
            msv_partnerships = [MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split(',')]
        task.set_base(msv_base_info, msv_partnerships)
