import json
from libs.dt import to_date
from entity.eventlog import Eventlog, SpiderCode
from apps.gs_spider.utils.msv_enum import MSVSource
from apps.gs_spider.parser.parser_gsxt import GSXTParserTask, GSXTParser, MSVBaseInfo, MSVPartnership, MSVInvestor, \
    MSVStaff, MSVChange
import re
from loguru import logger
from clients.mysql_client import MySQLClient
from libs.env import ConstantProps
from datetime import datetime


class SZParserTask(GSXTParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        super().__init__(eventlog, pages)


class SZParser2(GSXTParser):
    def __init__(self, name: str):
        self.mysql_client = MySQLClient(**ConstantProps.PROPS_GS_OUTER_RW)
        super().__init__(name, task_cls=SZParserTask, msv_source=MSVSource.GDSZ)

    def do_parse(self):
        task: SZParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        pages = task.pages

        base_info = pages.get('base_info.txt', None)
        if not base_info:
            logger.warning(f'no page base {eventlog}')
            eventlog.spider_code = SpiderCode.FAIL
            return
        base_info = json.loads(base_info)

        if '执行事务合伙人' in base_info:
            base_info['法定代表人'] = base_info['执行事务合伙人']
        dates = re.findall(r'(\d{4}年\d{1,2}月\d{1,2}日)', base_info.get('营业期限'))
        msv_base_info_data = dict(
            name=base_info['企业名称'],
            legal_person_name=re.sub(r'([(（]委派代表.*?[）)])$', '', base_info['法定代表人']),
            reg_number=base_info.get('注册号'),
            company_org_type=base_info.get('类型'),
            reg_location=base_info.get('住所'),
            estiblish_time=to_date(base_info['成立日期']),
            approved_time=to_date(base_info['核准日期']),
            from_time=to_date(base_info['成立日期']),
            to_time=to_date(dates[1]) if len(dates) > 1 else None,
            business_scope=base_info.get('一般经营项目'),
            # reg_institute=base_info.get('regOrg_CN', None),
            reg_status=base_info.get('企业登记状态'),
            reg_capital=base_info.get('认缴注册资本总额', '').replace('(人民币)', '').replace('()', ''),
            credit_code=base_info.get('统一社会信用代码'),
        )
        msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)

        msv_partnerships = None
        company_org_type = msv_base_info.company_org_type
        legal_person_name = msv_base_info.legal_person_name
        if company_org_type and '合伙' in company_org_type and legal_person_name:
            msv_partnerships = [MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split(',')]
        task.set_base(msv_base_info, msv_partnerships)

        msv_investors = [MSVInvestor.from_dict(
            {
                'investor_name': x['name'],
                'investor_type': 0,
                'capital': [{
                    'amomon': x.get('认缴出资额'),
                    'time': '',
                    'paymet': ''
                }],
                'detail': {
                    'amomon': x.get('认缴出资额'),
                }
            }) for x in json.loads(pages['investor_info.txt'])]
        task.set_investors(msv_investors)

        msv_staff = [MSVStaff.from_dict({
            'staff_name': i['name'],
            'staff_position': i['position']
        }) for i in json.loads(pages['staff_info.txt'])]
        task.set_staffs(msv_staff)

        # todo 经营异常
        abnormal_infos = json.loads(pages['abnormal_info.txt'])
        if abnormal_infos:
            sql = f'select * from prism.company where name = %s'
            info_pre = self.mysql_client.select(sql, args=(base_info['企业名称'],))
            cid = info_pre['id'] if info_pre else 0
            if cid:
                sql = f'select * from prism.company_abnormal_info where company_id = %s'
                infos = self.mysql_client.select_many(sql, args=(cid,))
                for info in infos:
                    for abnormal_info in abnormal_infos:
                        if (info['put_reason'] == abnormal_info[0]) and info['put_date'].date() == to_date(abnormal_info[1]) and \
                                abnormal_info[3] in info['put_department']:
                            logger.info(f'已存在经营异常信息 {info}')
                            sql = f'update prism.company_abnormal_info set remove_reason=%s,remove_date=%s,remove_department=%s where id=%s'
                            try:
                                self.mysql_client.execute(sql, args=(abnormal_info[-4], to_date(abnormal_info[-3]), abnormal_info[-1], info['id']))
                            except Exception as e:
                                logger.error(f'更新经营异常信息失败 {e}')
