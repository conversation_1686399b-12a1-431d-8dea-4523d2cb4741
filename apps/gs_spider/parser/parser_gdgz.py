import json
from libs.dt import to_date
from entity.eventlog import Eventlog, SpiderCode
from apps.gs_spider.utils.msv_enum import MSVSource
from apps.gs_spider.parser.parser_gsxt import GSXT<PERSON>arserTask, GSXTParser, MSVBaseInfo, MSVPartnership, MSVInvestor, \
    MSVStaff, MSVChange
from apps.gs_spider.parser.parser import Parser, ParserTask
import re
from loguru import logger
from clients.mysql_client import MySQLClient
from libs.env import ConstantProps
from datetime import datetime
from clients.mysql_client import MySQLClient
from libs.env import ConstantProps
from clients.redis._redis import Redis
import time


class GDGZParserTask(ParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        self.search = eventlog.selector.get_info('keyword')
        super().__init__(eventlog, pages)


class GDGZParser(Parser):
    def __init__(self, name: str):
        self.mysql_client = MySQLClient(**ConstantProps.PROPS_GS_OUTER_RW)
        self.redis_client = Redis(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com', password="3lvadmpiSj61ge", db=0)
        super().__init__(name, task_cls=GDGZParserTask)

    def do_parse(self):
        task: GDGZParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        cid = eventlog.selector.word
        date = task.pages['核准日期']

        sql = f'select * from prism.company where id = %s'
        info_pre = self.mysql_client.select(sql, args=(cid,))
        if info_pre and info_pre.get('approved_time', ''):
            approved_time_pre = info_pre['approved_time']
            approved_time = datetime.strptime(date, '%Y年%m月%d日')
            if approved_time > approved_time_pre:
                eventlog.spider.ab_info['time_changed'] = f"{approved_time_pre} --> {approved_time}"
                str_ = json.dumps({'cid': cid, 'uniscid': task.search, 'approved_time': approved_time.strftime('%Y-%m-%d')}, ensure_ascii=False)
                self.redis_client.zadd('gdgz_approvedTime_change', {str_: int(time.time() * 1000)})
                logger.info(f'{task.search} 核准日期: {date}')
            else:
                logger.info(f'{task.search} 核准日期无变化')
