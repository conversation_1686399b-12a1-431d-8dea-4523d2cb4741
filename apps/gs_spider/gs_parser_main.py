# encoding=utf8

import json
import argparse
from threading import Lock
from concurrent.futures import Future
from libs.log2 import setup_logger
from libs.env import get_env_prop, get_stack_info
from libs.concurrent import BoundedExecutor
from libs.dt import cur_ts_sec
from clients.redis.redis_queue import RedisQueue
from clients.kafka_client import KafkaConsumerClient, KafkaProducerClient
from entity.eventlog import Eventlog, SpiderCode
from apps.gs_spider.spider_conf import SpiderConf
from apps.gs_spider.utils.obs_manager import OBSManager
from apps.gs_spider.parser import *


class ParserMain(object):
    def __init__(self, conf: SpiderConf, worker_num=1):
        self.worker_num: int = worker_num
        self.obs_manager = OBSManager()
        self.fs = dict()
        self.fs_lock = Lock()

        self.fusion_producer_client = KafkaProducerClient(
            **get_env_prop(f'kafka.tyc.gs'),
            kafka_topic='gsxt.data_fusion')

        self.input_queue = RedisQueue(
            **get_env_prop(f'redis.tyc.{conf.crawler.resource_name}'),
            **conf.crawler.queue_args,
            use_zset=True)

        if conf.parser.use_redis_queue:
            input_queue = RedisQueue(
                **get_env_prop(f'redis.tyc.{conf.parser.resource_name}'),
                **conf.parser.queue_args,
                use_zset=False)
            self.input_queue_eventlog_fn = input_queue.generate
        else:
            input_queue = KafkaConsumerClient(
                **get_env_prop(f'kafka.tyc.{conf.parser.resource_name}'),
                **conf.parser.queue_args)
            self.input_queue_eventlog_fn = input_queue.read

        parser_class = globals()[conf.parser.clazz]
        self.parser_obj: Parser = parser_class(name=conf.spider_name)

    def run(self):
        with BoundedExecutor(max_workers=ap_args.max_workers) as process_pool:
            for s in self.input_queue_eventlog_fn():
                try:
                    d = json.loads(s)
                    eventlog = Eventlog.from_dict(d)
                    if not eventlog:
                        logger.warning(f'error from dict {s}')
                        continue
                except Exception as e:
                    logger.warning(f'error eventlog {e}')
                    continue
                future: Future = process_pool.submit(self.parser_obj.parse, eventlog)
                with self.fs_lock:
                    self.fs[future] = eventlog
                future.add_done_callback(self.callback_fn)
                # break  # TEST

    def callback_fn(self, future: Future):
        eventlog = self.fs[future]
        with self.fs_lock:
            del self.fs[future]
        try:
            eventlog = future.result()
        except Exception as e:
            eventlog.spider_code = SpiderCode.GIVE_UP
            logger.info(f'error process {eventlog} set SpiderCode.GIVE_UP {e} {get_stack_info()}')

        eventlog.spider.send_ts = cur_ts_sec()
        eventlog_str = eventlog.to_json()
        ret = self.fusion_producer_client.write(eventlog_str)
        logger.info(f'OUTPUT {eventlog_str} ret={ret}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用工商爬虫-解析程序 解析数据到MSV 并发送Kafka消息')
    ap.add_argument('-c', '--spider-conf', type=argparse.FileType('r'), default='conf/hk.json')
    ap.add_argument('--backup-days', type=int, default=1, help='日志保存天数')
    ap.add_argument('--max-workers', type=int, default=2, choices=range(1, 100), help='worker数量')
    ap_args = ap.parse_args()
    conf_ = SpiderConf.from_dict(json.loads(ap_args.spider_conf.read()))
    app_name = '.'.join(['gs_spider', conf_.spider_name, 'parser'])
    logger = setup_logger(use_file_log=True, app_name=app_name, backup_count=ap_args.backup_days, rotate_mode='D')
    parser_main = ParserMain(conf_, worker_num=ap_args.max_workers)
    parser_main.run()
