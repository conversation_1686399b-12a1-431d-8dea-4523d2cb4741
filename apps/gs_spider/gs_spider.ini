


;[program:gs_spider_gds_crawler]
;directory = /home/<USER>/pygs-work-parent/apps/gs_spider
;command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/gds.json --max-workers 25
;autostart = true
;autorestart = true
;user = work
;stderr_logfile = /dev/null
;stdout_logfile = /dev/null
;[program:gs_spider_gds_parser]
;directory = /home/<USER>/pygs-work-parent/apps/gs_spider
;command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/gds.json
;autostart = true
;autorestart = true
;user = work
;stderr_logfile = /dev/null
;stdout_logfile = /dev/null

;[program:gs_spider_hk_crawler]
;directory = /home/<USER>/pygs-work-parent/apps/gs_spider
;command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/hk.json --max-workers 30
;autostart = true
;autorestart = true
;user = work
;stderr_logfile = /dev/null
;stdout_logfile = /dev/null
;[program:gs_spider_hk_parser]
;directory = /home/<USER>/pygs-work-parent/apps/gs_spider
;command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/hk.json --max-workers 10
;autostart = true
;autorestart = true
;user = work
;stderr_logfile = /dev/null
;stdout_logfile = /dev/null

[program:gs_spider_sz_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/sz.json --max-workers 40
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_sz_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/sz.json --max-workers 15
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null




;
;[program:gs_spider_npo_crawler]
;directory = /home/<USER>/pygs-work-parent/apps/gs_spider
;command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/npo.json --max-workers 10
;autostart = true
;autorestart = true
;user = work
;stderr_logfile = /dev/null
;stdout_logfile = /dev/null
;[program:gs_spider_npo_parser]
;directory = /home/<USER>/pygs-work-parent/apps/gs_spider
;command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/npo.json --max-workers 5
;autostart = true
;autorestart = true
;user = work
;stderr_logfile = /dev/null
;stdout_logfile = /dev/null

[program:gs_spider_monitor]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 monitor.py
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null


[program:holder_empty_detail]
directory = /home/<USER>/pygs-work-parent
command = /home/<USER>/pygs-work-parent/venv/bin/python3 apps/dingliangyi/cods/find_new.py
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

[program:gs_spider_gdgz_crawler]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_crawler_main.py --spider-conf conf/gdgz.json --max-workers 10
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null
[program:gs_spider_gdgz_parser]
directory = /home/<USER>/pygs-work-parent/apps/gs_spider
command = /home/<USER>/pygs-work-parent/venv/bin/python3 gs_parser_main.py --spider-conf conf/gdgz.json --max-workers 1
autostart = true
autorestart = true
user = work
stderr_logfile = /dev/null
stdout_logfile = /dev/null

