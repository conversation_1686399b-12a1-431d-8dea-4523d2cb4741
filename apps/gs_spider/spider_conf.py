# encoding=utf8

from typing import Optional
from pydantic import Field
from entity.deps.entity import BaseEntity


class CrawlerConf(BaseEntity):
    clazz: str
    resource_name: str  # 输入Redis实例
    queue_args: dict  # 输入Redis队列参数
    page_bak_count: int  # 响应数据备份版本数


class ParserConf(BaseEntity):
    clazz: str
    use_redis_queue: bool = Field(default=True)
    resource_name: str  # Redis实例或者kafka实例信息
    queue_args: dict  # 输入队列参数


class SpiderConf(BaseEntity):
    spider_name: str  # 爬虫名称 例如 app2
    crawler: CrawlerConf  # 抓取配置
    parser: ParserConf  # 解析配置
