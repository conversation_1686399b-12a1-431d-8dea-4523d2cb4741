import json
import logging
import random
import re
from time import sleep
import bs4
import cpca
import requests
from requests import Response
from apps.gs_spider.crawler.crawler import CrawlerTask, Crawler
from clients.obs_client import OBSClient
from dao.nhc.nhc_chachonne import NhcChachonneDao
from dao.nhc.nhc_jdzx import Nhc<PERSON>dzxDao
from entity.eventlog import Eventlog, SpiderCode
from libs.env import ConstantProps
from libs.req import SessionRequestManager

logger = logging.getLogger(__name__)


class CrawlerNHCTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        self.cid = 0
        super().__init__(eventlog)


class CrawlerNHCJdzx(Crawler):
    def __init__(self, name: str, **kwargs):
        super().__init__(name, task_cls=CrawlerNHCTask, **kwargs)
        self.request_manager = SessionRequestManager()
        self.NhcJdzxDao = NhcJdzxDao(**ConstantProps.PROPS_GS_TEST)
        self.prvo = ['北京市','上海市','重庆市','天津市','江苏省','广东省','山东省','辽宁省','河北省','河南省','四川省','黑龙江省','山西省','湖北省','湖南省','陕西省','浙江省','云南省','吉林省','安徽省','广西壮族自治区','江西省','福建省','新疆维吾尔自治区','内蒙古自治区','甘肃省','贵州省','海南省','青海省','宁夏回族自治区','西藏自治区',]

    def find_entity_by_jdzx(self, hospital):
        def response_validate_func(response: Response, ctx):
            try:
                res_cap = self.request_manager.request(self.splice_url('https://credit.jdzx.net.cn/portal/captcha', params), timeout=10)
                if res_cap.status_code != 200:
                    raise Exception
                cap = requests.post('http://*************:8888/cap/',
                                    files={'data': res_cap.content})
                # cap = self.ocr.classification(res_cap.content)
                if len(cap.json()) != 4:
                    raise Exception
                params['validCode'] = cap.json()
                result = self.request_manager.request(
                    self.splice_url('https://credit.jdzx.net.cn/portal/pubsearch/org/0114000000', params), timeout=10)
                if '验证码错误' in result.text:
                    logger.warning(f'{hospital} - 验证码错误')
                    raise Exception
            except:
                ctx['res'] = None
                return None
            else:
                ctx['res'] = result.text
                return True

        def response_validate_func_2(response: Response, ctx):
            try:
                if response.status_code != 200:
                    raise Exception
                soup = bs4.BeautifulSoup(response.text, 'lxml')
                names = soup.select('#formresult > tbody > tr')
            except:
                ctx['name'] = None
                return None
            else:
                ctx['name'] = names
                return True

        ctx = dict()
        params = {
            'NAME': hospital,
            'PASSCODE': '',
            'BEGIN_DATE': '',
            'END_DATE': '',
        }
        self.request_manager.request('https://credit.jdzx.net.cn/portal/pubsearch/org/0114000000', response_validate_ctx=ctx, response_validate_func=response_validate_func, tries=10, timeout=10)

        soup = bs4.BeautifulSoup(ctx['res'], 'lxml')
        if not soup.select('#formresult > tbody > tr'):
            logger.info(f'[{hospital}] 查询为空')
            return None

        status = soup.select('#public-query > div.right-box > div.cp-grid > div > span')
        pages = re.findall('共(\d+)页', status[0].text)[0]
        del params['validCode']
        for page in range(0, int(pages) + 1):
            params['page'] = page
            params['rows'] = 10
            ctx = {}
            self.request_manager.request(self.splice_url('https://credit.jdzx.net.cn/portal/pubsearch/org/0114000000', params), response_validate_ctx=ctx, response_validate_func=response_validate_func_2, tries=5, timeout=10)
            if not ctx['name']:
                logger.warning(f"page为{str(page)}的[{hospital}]查询为空")
                continue
            # for name in ctx['name']:
            #     value = cpca.transform([name.select('td:nth-child(1) > a')[0].text]).iloc[0, 0]
            #     if not value:
            #         continue

            for name in ctx['name']:
                info = dict()
                info.update({'name_hospital': name.select('td:nth-child(1) > a')[0].text})
                info.update({'org_category': name.select('td:nth-child(2)')[0].text})
                value1 = cpca.transform([name.select('td:nth-child(1)')[0].text]).iloc[0, 0]
                value2 = cpca.transform([name.select('td:nth-child(3)')[0].text]).iloc[0, 0]
                value3 = cpca.transform([name.select('td:nth-child(5)')[0].text]).iloc[0, 0]
                value = self.return_value(value1, value2, value3)
                info.update({'addr_medical': name.select('td:nth-child(3)')[0].text})
                info.update({'addr_hospital': value})
                info.update({'Issuing': name.select('td:nth-child(5)')[0].text})
                self.NhcJdzxDao.i_u_by_any(**info)

    
    def return_value(self, *args):
        if not (args[0] or args[1] or args[2]):
            return '未知'
        arr = list()
        for arg in args:
            if arg:
                arr.append(arg)
        arr = list(set(arr))
        result = '未知'
        for a in arr:
            if a not in self.prvo:
                continue
            result = a
        return result

    def splice_url(self, url, parm):
        url += '?'
        for item, var in parm.items():
            url += f'{str(item)}={str(var)}'
            url += '&'

        return url[:-1]



class CrawlerNHC(Crawler):
    def __init__(self, name: str, **kwargs):
        self.request_manager = SessionRequestManager()
        self.obs = OBSClient(bucket_name='jindi-oss-gsxt')
        self.prov = {"北京市": "11", "天津市": "12", "河北省": "13", "山西省": "14", "内蒙古自治区": "15",
                     "辽宁省": "21", "吉林省": "22", "黑龙江省": "23", "上海市": "31", "江苏省": "32", "浙江省": "33",
                     "安徽省": "34", "福建省": "35", "江西省": "36", "山东省": "37", "河南省": "41", "湖北省": "42",
                     "湖南省": "43", "广东省": "44", "广西壮族自治区": "45", "海南省": "46", "重庆市": "50",
                     "四川省": "51", "贵州省": "52", "云南省": "53", "西藏自治区": "54", "陕西省": "61", "甘肃省": "62",
                     "青海省": "63", "宁夏回族自治区": "64", "新疆维吾尔自治区": "65"}
        self.NhcChachonneDao = NhcChachonneDao(**ConstantProps.PROPS_GS_TEST)
        super().__init__(name, task_cls=CrawlerNHCTask, **kwargs)

    def do_crawl(self):
        task = self.get_crawler_task()
        eventlog = task.eventlog
        Flag = eventlog.selector.word
        logger.info('Flag: [{}] - {}'.format(Flag, eventlog.to_json()))
        busy = False
        try:
            hospital = [(Flag, eventlog.selector.info['prov'])]
            for Flag, prov in hospital:
                temp = Flag

                if len(Flag) > 20:
                    Flag = Flag[:20]
                for i in range(6):
                    if busy:
                        eventlog.spider_code = SpiderCode.FAIL
                        logger.error('Flag: [{}] - {}'.format(Flag, '该ip需要等待60秒'))
                        sleep(60)
                        busy = False


                    RequestVerificationToken = self.unit_index()
                    if RequestVerificationToken is None:
                        eventlog.spider_code = SpiderCode.FAIL
                        logger.warning('Flag: [{}] - {}'.format(Flag, '未查询到__RequestVerificationToken'))
                        continue

                    pos = dict()
                    pic = dict()
                    for n in range(10):
                        pos = self.ClickAPI_getVerCodePic()
                        if pos == {}:
                            eventlog.spider_code = SpiderCode.SEARCH_EMPTY
                            logger.warning('Flag: [{}] - {}'.format(Flag, '获取验证码为空'))
                            continue

                        pic = requests.post('http://*************:8888/rec/', json={'picInfo': pos['picInfo'], 'ckWords': pos['ckWords']}).json()
                        if pic is None:
                            logger.warning('Flag: [{}] - {}'.format(Flag, '验证码识别不全'))
                            continue
                        if not self.ClickAPI_GetIsPass(pos['guid'], pic):
                            continue
                        logger.info('Flag: [{}] - {}'.format(Flag, '验证码通过'))
                        break
                    if pic is None:
                        logger.warning('Flag: [{}] - {}'.format(Flag, '验证码错误, 重试'))
                        continue
                    logger.info(eventlog.to_json())
                    data = {
                        'Prov': self.prov[prov],
                        'Unit_Name': Flag,
                        'Check_Code': '{}@{}'.format(pos['guid'], pic),
                        '__RequestVerificationToken': RequestVerificationToken,
                    }
                    Details = self.unit_index(data)
                    if Details == 'empty':
                        eventlog.spider_code = SpiderCode.SEARCH_EMPTY
                        logger.error('Flag: [{}] - {}'.format(Flag, f'NHC查询为空'))
                        self.NhcChachonneDao.insert(temp)
                        break

                    if Details == 'busy':
                        logger.warning('Flag: [{}] - {}'.format(Flag, '忙重试'))
                        busy = True
                        continue

                    if Details is None:
                        continue

                    for item, Det in enumerate(Details):
                        res = self.Unit_Details(Det)
                        if res is None:
                            continue
                        task.pages[f'res_{str(item)}.html'] = json.dumps(res, ensure_ascii=False)
                    logger.info(f'{Flag} - 抓取完成')

                    break
        except Exception as e:
            logger.error('抓取出现问题 - {} - row={}'.format(e, e.__traceback__.tb_lineno))




    def ClickAPI_getVerCodePic(self):
        def response_validate_func(response: Response, ctx):
            try:
                res = response.json()
                ctx['ckWords'] = res.get('ckWords')
                ctx['picInfo'] = res.get('picInfo')
                ctx['guid'] = res.get('guid')
            except:
                return None
            else:
                # if ctx['ckWords'] is None or ctx['picInfo'] is None:
                #     return None
                return True

        params = {
            'guid': self.get_guid(),
            'width': '400',
            'height': '150',
        }
        url = self.splice_url('http://zgcx.nhc.gov.cn:9090/ClickAPI/getVerCodePic', params)
        ctx = dict()
        self.request_manager.request(
            url,
            response_validate_func=response_validate_func,
            response_validate_ctx=ctx,
            tries=1
        )
        return ctx

    def ClickAPI_GetIsPass(self, guid, json):
        def response_validate_func(response: Response, ctx):
            try:
                if response.text == '1':
                    ctx['res'] = True
                    return True
                else:
                    ctx['res'] = False
                    # return None
                    return True
            except:
                ctx['res'] = False
                return None

        url = 'http://zgcx.nhc.gov.cn:9090/ClickAPI/GetIsPass'
        data = {
            'guid': guid,
            'json': json
        }
        url = self.splice_url(url, data)
        ctx = {}
        self.request_manager.request(
            url,
            response_validate_func=response_validate_func,
            response_validate_ctx=ctx,
            tries=1,

        )
        return ctx.get('res', False)

    def unit_index(self, data=None):

        if data is None:
            sign = True
        else:
            sign = False
        url = 'http://zgcx.nhc.gov.cn:9090/unit/index'
        respone = self.request_manager.request(
            url,
            post_data=data,
            force_new_session=sign,
            timeout=40
        )
        if respone is None:
            return respone

        if data is None:
            res = re.findall('<input name="__RequestVerificationToken" type="hidden" value="(.*?)"', respone.text)
            return res[0] if res != [] else None
        else:
            res = re.findall('<a target="_blank" class="a" href="(.*?)">', respone.text)
            if res == []:
                if respone.text.find('未查询到符合条件的医疗机构') > -1:
                    logger.info(data)
                    # logger.info(respone.text)
                    return 'empty'
                elif respone.text.find('每次查询请间隔60秒') > -1:
                    return 'busy'

            return res if res != [] else None

    def Unit_Details(self, url_):

        def response_validate_func(response: Response, ctx=None):
            try:
                if response.status_code != 200:
                    return None
                elif response.text is None:
                    return None
                else:
                    return True
            except:
                return None

        url = 'http://zgcx.nhc.gov.cn:9090' + url_
        respone = self.request_manager.request(
            url,
            response_validate_func=response_validate_func,
            response_validate_ctx=None,
            tries=2
        )
        return respone.text

    def splice_url(self, url, parm):
        url += '?'
        for item, var in parm.items():
            url += f'{str(item)}={str(var)}'
            url += '&'

        return url[:-1]

    def get_guid(self):
        return '{:08x}-{:04x}-{:04x}-{:04x}-{:012x}'.format(
            random.randint(0, 0xFFFFFFFF),
            random.randint(0, 0xFFFF),
            random.randint(0, 0xFFFF),
            random.randint(0, 0xFFF) | 0x4000,
            random.randint(0, 0xFFFFFFFFFFFF) | 0x800000000000
        )

    # def Detection(self, image, ckWords):
    #     try:
    #         url = 'http://10.99.199.117:8888/rec/'
    #         # print(image)
    #         image = image.split(",")[1]
    #         binary_data = base64.b64decode(image)
    #         image_array = np.frombuffer(binary_data, np.uint8)
    #         im = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
    #         poses = self.model.predict(im, device='cpu')
    #
    #         word = ckWords.split(',')
    #         result = list()
    #         arr = list()
    #         res = list()
    #         for pos in poses[0].boxes.xyxy:
    #             x1, y1, x2, y2 = map(int, pos.tolist())
    #             cropped_image = im[y1:y2, x1:x2]
    #             success, binary_data = cv2.imencode('.png', cropped_image)
    #             binary_data = binary_data.tobytes()
    #             arr.append(("files", binary_data))
    #             result.append({"x": (x1 + x2) / 2, "y": (y1 + y2) / 2})
    #         req = requests.post(url, files=arr).json()
    #         # print(word, req)
    #         for w in word:
    #             if w in req:
    #                 res.append(result[req.index(w)])
    #             else:
    #                 return None
    #
    #     except Exception as e:
    #         logger.warning('Detection出现问题 - ' + str(e))
    #         return None
    #     else:
    #         return res
