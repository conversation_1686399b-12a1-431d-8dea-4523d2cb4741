import re
import json
from requests.adapters import HTTPAdapter
import requests
import time
from requests import Session
from loguru import logger
from fake_useragent import UserAgent
import urllib3
import execjs
import random
import string
import base64
from ddddocr import DdddOcr
import hashlib

from libs.env import get_stack_info
from entity.eventlog import Eventlog, SpiderCode
from apps.gs_spider.crawler.crawler import Crawler, CrawlerTask, MyException

ua = UserAgent()
urllib3.disable_warnings()


class SZCrawlerTask(CrawlerTask):

    def __init__(self, eventlog: Eventlog):
        self.search = eventlog.selector.get_info('keyword')
        self.session: Session
        self.rs_exits = False
        self.rs = {
            'url_md5': 'ca561ff3982af02ab63bdd2f473b5ced',
            'new': True,
            'path': '/outer/entEnt/listDetail.do'
        }
        self.c = 0
        self.new_session()

        super().__init__(eventlog)

    def new_session(self):
        self.session = requests.session()
        self.session.proxies = {
            'http': 'http://10.99.138.95:30636',
            'https': 'http://10.99.138.95:30636'
        }
        self.session.mount('http://', HTTPAdapter(max_retries=1))
        self.session.mount('https://', HTTPAdapter(max_retries=1))


class SZCrawler(Crawler):
    def __init__(self, name: str, **kwargs):
        self.headers = {
            "User-Agent": 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/114.0.0.0 Safari/537.36'
        }
        self.timeout = 4
        self.home = 'https://amr.sz.gov.cn/outer/entSelect/gs.html'
        # self.detail_url = 'https://amr.sz.gov.cn/outer/entEnt/listDetail.do'
        self.detail_url = 'https://amr.sz.gov.cn/outer/entEnt/detail.do'
        self.tag_url = 'https://amr.sz.gov.cn/outer/entEnt/tagDe.do'
        # self.zxhhr_url = 'https://amr.sz.gov.cn/outer/entEnt/zxhhr.do'
        self.zxhhr_url = 'https://amr.sz.gov.cn/outer/entEnt/fddbr.do'
        self.change_url = 'https://amr.sz.gov.cn/outer/entEnt/biangeng.do'
        self.bgdetail_url = 'https://amr.sz.gov.cn/outer/entEnt/bgdetail.do'
        self.ocr_ddd = DdddOcr(show_ad=False)
        self.pattern = re.compile(r'\s+')
        self.tags = [
            {'tag_id': 1, 'dimension': '许可经营'},
            {'tag_id': 2, 'dimension': '股东信息'},
            {'tag_id': 3, 'dimension': '主要人员'},
            {'tag_id': 4, 'dimension': '变更信息'},
        ]

        super().__init__(name, task_cls=SZCrawlerTask, **kwargs)

    def do_crawl(self):
        task: SZCrawlerTask = self.get_crawler_task()
        eventlog = task.eventlog

        if task.search is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.debug(f'搜索为空')
                eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            if '连续失败' in e.message:
                logger.debug(f'{e.message}')
                eventlog.spider_code = SpiderCode.FAIL
            task.pages = {}
            return
        except Exception as e:
            raise e

    def crawl_(self, task: SZCrawlerTask):
        pages = dict()

        self.headers.update({"User-Agent": ua.chrome})
        if not re.search(r'[\u4e00-\u9fff]', task.search):
            detail_req_data = {'unifsocicrediden': task.search, 'entname': ''}
        else:
            detail_req_data = {'unifsocicrediden': '', 'entname': task.search}

        self.request(task, self.home, name='index')

        company_info = self.request(task, self.detail_url, data=detail_req_data, path='/outer/entEnt/listDetail.do', name='基本信息')
        if not company_info['data'][0]['data']:
            raise MyException('搜索为空')
        company_info = company_info['data'][0]['data'][0]
        id_ = company_info['id']

        if company_info['opetype'] in ['HHQY', 'WZHH']:
            lerep_ = self.request(task, self.zxhhr_url, data={'id': id_}, path='/outer/entEnt/zxhhr.do')['data'][0]['data']
            lereps = [i['persname'] for i in lerep_]
            company_info['lerep'] = ','.join(lereps)

        pages['base_info.txt'] = json.dumps(company_info, ensure_ascii=False)
        logger.success(f'Req: {task.search} -> 基本信息')

        for i in self.tags:
            dimension = i['dimension']
            result = self.request(task, self.tag_url, data={'tagId': i['tag_id'], 'id': id_}, path='/outer/entEnt/tagDe.do', name=dimension)
            if dimension == '变更信息':
                result = self.crawl_change_info(task, id_, result)
                pages[f'{dimension}.txt'] = json.dumps(result, ensure_ascii=False)
            else:
                pages[f'{dimension}.txt'] = json.dumps(result['data'][0]['data'], ensure_ascii=False)
            logger.success(f'{company_info["entname"]}({id_}) -> {dimension}')

        logger.success(f'{company_info["entname"]}({id_}) crawl success.')
        return pages

    def crawl_change_info(self, task: SZCrawlerTask, id_: str, result: dict):
        dj = result['data'][0].get('data', [])
        len_dj = len(dj)
        logger.debug(f'{id_} change lens -> {len_dj}')

        change_list = []
        for idx, i in enumerate(dj):
            if not ('alttime' in i):
                return change_list

            data = {
                'id': id_,
                'alttime': i['alttime'],
                'pregino': i['regino']
            }
            change_data = self.request(task, self.change_url, data=data, path='/outer/entEnt/biangeng.do', name='变更列表')
            logger.debug(f'{id_} -> change_info -> {i["regino"]} -> ({idx + 1}/{len_dj})')

            values = set()
            for j in change_data['data'][0]['data']:
                # 防止重复抓取
                if (
                        'describe' not in j or
                        'altdate' not in j or
                        j['altemcodeNew'] in values
                ):
                    continue
                values.add(j['altemcodeNew'])

                if j['describe'] == '2':
                    pd = {
                        'altitemcode': j['altitemcode'],
                        'regino': i['regino'],
                        'id': id_,
                    }

                    d = self.request(task, self.bgdetail_url, data=pd, path='/outer/entEnt/bgdetail.do', name='变更详情')
                    logger.debug(f'{id_} -> change_detail -> {j["altitemcode"]}')

                    j = {
                        'altbe': ' '.join(
                            [str(i.get('content', '')) for i in d['data'][0]['data'] if i.get('bgtype') == '1']),
                        'altaf': ' '.join(
                            [str(i.get('content', '')) for i in d['data'][0]['data'] if i.get('bgtype') == '2']),
                        'altdate': j['altdate'],
                        'valueNew': j['valueNew'],
                    }

                if ('altaf' in j) or ('altbe' in j):
                    change_list.append(j)

        return change_list

    def request(self, task: SZCrawlerTask, url: str, data: dict = None, path: str = '', name: str = '', depth=0) -> dict:
        for _ in range(15):
            response = None
            try:
                self.update_rs_cookies(task, path)
                response = task.session.post(**{'url': url, 'data': data, 'headers': self.headers, 'verify': False, 'timeout': self.timeout})
                text = self.pattern.sub('', response.content.decode())[:120]
                logger.info(f"{task.search}-{response.status_code} --> {path.split('/')[-1]}:{name}({_ + 1}) --> {text}")

                if response.status_code in [206, 500, 404]:
                    task.new_session()
                    logger.error(f"{name} --> html {response.text} 重试")
                    continue
                if name == 'index' and '商事主体登记及备案信息查询' in response.content.decode():
                    return {}
                if '本站开启了验证码保护' in response.content.decode():
                    if_ = self.pass_capcha(task)
                    if if_:
                        continue
                    break
                if response.status_code == 521:
                    self.set_jiasule_cookies(task, url, response)
                    continue
                if response.status_code == 412:
                    task.rs.update({'html': response.text})
                    task.rs_exits = True
                    if name == 'index':
                        return {}
                    continue

                # logger.info(f"{task.search} --> {path.split('/')[-1]}:{name}({_ + 1}) --> {response.json()}")
                if name == '基本信息' and 'data' not in response.json()['data'][0]:
                    if _ == 3:
                        break
                    task.new_session()
                    time.sleep(3)
                    continue
                return response.json()
            except (requests.exceptions.ConnectionError, requests.exceptions.ProxyError, requests.exceptions.Timeout) as e:
                logger.warning(f"{task.search} --> {path.split('/')[-1]}:{name}({_ + 1}) --> 代理问题 重试：{e}")
                task.new_session()
                continue
            except MyException as e:
                if re.search(r'(失败)', e.message):
                    logger.warning(f"{task.search} --> {path.split('/')[-1]}:{name}({_ + 1}) --> {e.message}")
                    task.new_session()
                    continue
                raise e
            except Exception as e:
                html = response.text if response else ''
                status_code = response.status_code if response else ''
                logger.error(f"{name}-{status_code} - html: {html} 未知错误：{get_stack_info()}")
                task.new_session()
                continue
        raise MyException('接口连续失败')

    def update_rs_cookies(self, task: SZCrawlerTask, path: str):
        if task.rs_exits:
            if path:
                task.rs.update({'path': path})
            cookies = self.get_cookie(task.rs)
            if cookies:
                del task.session.cookies['EKyd12pLzdcZP']
                task.session.cookies.set("EKyd12pLzdcZP", cookies, domain='amr.sz.gov.cn')

    @staticmethod
    def get_cookie(data: dict):
        res = {}
        for _ in range(5):
            res = requests.post('http://10.98.47.31:19081/get_cookie', data=data, timeout=5).json()
            if 'cookie' in res:
                break
        if not res:
            return ''
        return res['cookie']

    def set_jiasule_cookies(self, task: SZCrawlerTask, url, resp_first: requests.Response, depth=0):
        if depth > 2:
            raise MyException('jiasule连续失败')
        self.delete_cookies(task)

        try:
            js_clearance = re.findall('cookie=(.*?);location', resp_first.text)[0]
            aa_result = execjs.eval(js_clearance).split(';')[0]  # 第一次生成
            task.session.cookies.set('__jsl_clearance_s', aa_result.split("=")[1], domain='amr.sz.gov.cn')

            resp_second = task.session.get(url=url, headers=self.headers, verify=False, timeout=self.timeout)
            dd = re.findall(r';go\((.*?)\)</script>', resp_second.text)[0]
        except Exception as e:
            task.new_session()
            url = 'https://amr.sz.gov.cn/outer/entSelect/gs.html'
            resp_first = task.session.get(url=url, headers=self.headers, verify=False, timeout=self.timeout)
            self.set_jiasule_cookies(task, url, resp_first, depth=depth + 1)
        else:
            # 第二次生成
            task.session.cookies.set('__jsl_clearance_s', self.go(json.loads(dd)), domain='amr.sz.gov.cn')
            logger.success(f'{task.search}({depth + 1}) --> set jiasule cookies success')

    def pass_capcha(self, task: SZCrawlerTask):
        for _ in range(20):
            try:
                task.c += 1
                image, sec = self.get_image(task)
                ans = self.ocr(image)
                # time.sleep(0.5)
                success = self.check_image(task, ans, sec)
                if success == 'ok':
                    return True
                logger.warning(f'{task.search} --> 验证码错误 {ans}')
            except Exception as e:
                raise MyException(f'验证码失败-{e}')
        return False

    def get_image(self, task: SZCrawlerTask):
        url = "https://amr.sz.gov.cn/cdn-cgi/captcha/v2/captcha/image"
        params = {"c": str(task.c), "s": self.random_string()}
        res = task.session.get(url, headers=self.headers, params=params, verify=False, timeout=3)
        logger.warning(f"{task.search} --> get_image {self.pattern.sub('', res.text[:50])}")
        return base64.b64decode(res.json()['image']), res.json()['sec']

    def check_image(self, task: SZCrawlerTask, ans: str, sec: str):
        url = "https://amr.sz.gov.cn/cdn-cgi/captcha/v2/captcha/image"
        res = task.session.post(url, data={'ans': ans, 'sec': sec}, headers=self.headers, verify=False, timeout=3)
        logger.warning(f"{task.search} --> check_image {self.pattern.sub('', res.text[:50])}")
        return res.json().get('msg', '')

    def ocr(self, image):
        ocr_result = self.ocr_ddd.classification(image)
        return ocr_result

    @staticmethod
    def delete_cookies(task: SZCrawlerTask):
        __jsluid_s = task.session.cookies.get('__jsluid_s')
        proxyBase = task.session.cookies.get('proxyBase')
        task.new_session()
        task.session.cookies.set('__jsluid_s', __jsluid_s, domain='amr.sz.gov.cn')
        task.session.cookies.set('proxyBase', proxyBase, domain='amr.sz.gov.cn')

    @staticmethod
    def random_string():
        characters = string.ascii_lowercase + string.digits
        return ''.join(random.choice(characters) for _ in range(6))

    @staticmethod
    def go(data):
        for i in range(len(data['chars'])):
            for j in range(len(data['chars'])):
                vales = data['bts'][0] + data['chars'][i] + data['chars'][j] + data['bts'][1]
                if data['ha'] == 'md5':
                    ha = hashlib.md5(vales.encode()).hexdigest()
                elif data['ha'] == 'sha1':
                    ha = hashlib.sha1(vales.encode()).hexdigest()
                elif data['ha'] == 'sha256':
                    ha = hashlib.sha256(vales.encode()).hexdigest()
                if ha == data['ct']:
                    return vales
