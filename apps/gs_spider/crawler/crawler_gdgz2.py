import json
import requests
import urllib3
from requests.adapters import HTT<PERSON>dapter
from loguru import logger
import ddddocr
import re
from typing import Union
import time
import random
from bs4 import BeautifulSoup

urllib3.disable_warnings()

from libs.env import get_stack_info
from entity.eventlog import Eventlog, SpiderCode
from apps.gs_spider.crawler.crawler import Crawler, CrawlerTask, MyException


class GDGZCrawlerTask(CrawlerTask):

    def __init__(self, eventlog: Eventlog):
        self.search = eventlog.selector.get_info('keyword')
        self.session = requests.session()
        self.session.proxies = {
            'http': 'http://************:30636',
            'https': 'http://************:30636'
        }
        self.session.mount('http://', HTTPAdapter(max_retries=1))
        self.session.mount('https://', HTTPAdapter(max_retries=1))
        super().__init__(eventlog)


class GDGZCrawler2(Crawler):
    def __init__(self, name: str, **kwargs):
        self.headers = {
            "Origin": "https://portal.scjgj.gz.gov.cn",
            "Referer": "https://portal.scjgj.gz.gov.cn/aiccips/GZpublicity/toSearch",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.timeout = 5
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        super().__init__(name, task_cls=GDGZCrawlerTask, **kwargs)

    def do_crawl(self):
        task: GDGZCrawlerTask = self.get_crawler_task()
        eventlog = task.eventlog

        if task.search is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.debug(f'搜索为空')
                eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            if '连续失败' in e.message:
                logger.debug(f'{e.message}')
                eventlog.spider_code = SpiderCode.FAIL
            task.pages = {}
            return
        except Exception as e:
            raise e

    def crawl_(self, task: GDGZCrawlerTask):
        pages = {}
        captcha = self.pass_capcha(task)
        url = "https://portal.scjgj.gz.gov.cn/aiccips/GZpublicity/showEnt"
        data = {"entName": "", "regNo": "", "uniSCID": task.search, "code": captcha, "exactFlag": "0"}
        response = self.request(task, 'POST', url, data=data, name='获取entNo参数')
        entNo = re.search(r'GZpublicity/GZpublicityList\?entNo=(.*?)&regOrg=&service=entInfo&code=', response)
        if not entNo:
            raise MyException('搜索为空')
        entNo = entNo.group(1)
        logger.info(f'entNo: {entNo}')

        captcha = self.pass_capcha(task)

        url = "https://portal.scjgj.gz.gov.cn/aiccips/GZpublicity/GZpublicityList"
        params = {"entNo": entNo, "regOrg": "", "service": "entInfo", "code": captcha}
        response = self.request(task, 'GET', url, params=params, name='商事登记基本信息')

        soup = BeautifulSoup(response, 'lxml')
        trs = soup.select('tr')
        key, value = [], []
        for tr in trs:
            list_ = list(tr.stripped_strings)
            key.extend(list_[0::2])
            value.extend(list_[1::2])
        data = dict(zip(key, value))
        logger.info(data)
        pages['base_info.txt'] = json.dumps(data, ensure_ascii=False)

        return pages

    def pass_capcha(self, task: GDGZCrawlerTask):
        captcha = ''
        for _ in range(5):
            url = "https://portal.scjgj.gz.gov.cn/aiccips/VerifyCode/getCode"
            params = {"random": str(round(random.random(), 16))}
            response = self.request(task, 'GET', url, params=params, name='获取验证码图片', toraw=True)
            captcha = self.ocr.classification(response.content)
            logger.info(f"captcha: {captcha}")

            url = "https://portal.scjgj.gz.gov.cn/aiccips/VerifyCode/checkCodeGz"
            params = {"entName": "", "regNo": "", "text3": task.search, "code": captcha}
            res: dict = self.request(task, 'GET', url, params=params, name='check_code', tojson=True)
            logger.info(res)
            if res['flag'] == '1':
                break
        if not captcha:
            raise MyException('验证码连续失败')
        return captcha

    def request(self, task: GDGZCrawlerTask, method: str, url: str, headers: dict = None, params: dict = None, data: dict = None,
                json: dict = None, path: str = None, name: str = '', tojson=False, toraw=False) -> Union[dict, str, requests.Response]:
        for i in range(10):
            response = None
            try:
                a = time.time()
                response = task.session.request(**{'method': method, 'url': url, 'data': data, 'headers': headers if headers else self.headers,
                                                   'verify': False, 'timeout': self.timeout, 'params': params, 'json': json})
                status = response.status_code
                if status != 200:
                    logger.warning(f'{name} {i} --> {status}')
                    del task.session.cookies['proxyBase']
                    continue
                logger.success(f'{name} {i} --> {status} {time.time() - a:.2f}s')
                if toraw:
                    return response
                if name not in ['获取entNo参数']:
                    logger.success(f'{name} --> {response.status_code}')
                    a = re.sub(r'[\r\n\t]', '', response.text)
                    logger.info(f'{name} --> {a}')
                if tojson:
                    return response.json()
                return response.text
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'continue{i} exception: {e}')
                del task.session.cookies['proxyBase']
                continue
            except Exception as e:
                status = response.status_code if response else "空"
                text = response.text if response else "空"
                logger.warning(f'continue-{i} 状态码：{status} res: {text} exception: {e}')
                del task.session.cookies['proxyBase']
                continue
        raise MyException('接口连续失败')
