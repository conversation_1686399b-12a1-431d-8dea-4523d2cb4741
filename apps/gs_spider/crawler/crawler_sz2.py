import re
import json
from requests.adapters import HTT<PERSON><PERSON>pter
import requests
import time
from requests import Session
from loguru import logger
import urllib3
from ddddocr import DdddOcr
from typing import Union
from requests import Response
from datetime import datetime
import pytz
from bs4 import BeautifulSoup
import random
import cchardet
from clients.redis._redis import Redis

from libs.env import get_stack_info
from entity.eventlog import Eventlog, SpiderCode
from apps.gs_spider.crawler.crawler import Crawler, CrawlerTask, MyException

urllib3.disable_warnings()


class SZCrawlerTask(CrawlerTask):

    def __init__(self, eventlog: Eventlog):
        self.search = eventlog.selector.get_info('keyword')
        self.rs = {
            'url_md5': 'ca561ff3982af02ab63bdd2f473b5ced',
            'new': True,
            # 'path': '/outer/entEnt/listDetail.do'
        }
        self.session = requests.session()
        # self.session.proxies = {
        #     'http': 'http://10.99.138.95:30636',
        #     'https': 'http://10.99.138.95:30636'
        # }
        self.session.mount('http://', HTTPAdapter(max_retries=1))
        self.session.mount('https://', HTTPAdapter(max_retries=1))
        super().__init__(eventlog)


class SZCrawler2(Crawler):
    def __init__(self, name: str, **kwargs):
        self.headers = {
            "User-Agent": 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/114.0.0.0 Safari/537.36'
        }
        self.timeout = 50
        self.ocr_ddd = DdddOcr(show_ad=False)
        self.home = 'https://amr.sz.gov.cn/xyjggs.webui/XYJGGS/List.aspx?view=info'
        self.captcha = "https://amr.sz.gov.cn/xyjggs.webui/XYJGGS/VerificationCode.aspx"
        self.captcha_verify = 'https://amr.sz.gov.cn/xyjggs.webui/XYJGGS/List.aspx/YZKeyCode'
        self.search = "https://amr.sz.gov.cn/xyjggs.webui/XYJGGS/Ajax/Ajax.ashx"
        self.detail = "https://amr.sz.gov.cn/xyjggs.webui/XYJGGS/Detail.aspx"
        self.redis_client = Redis(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com', password="3lvadmpiSj61ge", db=0)
        super().__init__(name, task_cls=SZCrawlerTask, **kwargs)

    def do_crawl(self):
        task: SZCrawlerTask = self.get_crawler_task()
        eventlog = task.eventlog

        if task.search is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.debug(f'搜索为空')
                eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            if '连续失败' in e.message:
                logger.debug(f'{e.message}')
                eventlog.spider_code = SpiderCode.FAIL
            task.pages = {}
            return
        except Exception as e:
            raise e

    def crawl_(self, task: SZCrawlerTask):
        pages = dict()
        self.request(task, self.home, 'GET', name='home', toRaw=True)

        company_info, investor, staff, abnormal_info = {}, [], [], []
        ENTID = ''
        ENTID_cache = self.redis_client.hget('gdsz_new_company_ENTID', task.search)
        if ENTID_cache:
            ENTID = ENTID_cache

        for _ in range(10):
            try:
                if not ENTID:
                    img = self.request(task, self.captcha, 'GET', name='captcha', toRaw=True).content
                    verify = self.verify(img)
                    res = self.request(task, self.captcha_verify, 'POST', json={'code': verify}, name='captcha_verify')
                    if res['d'] == 'true':
                        data = {"action": "getSSDJBList", "keyword": task.search, "PageIndex": "1", "code": verify, "a": self.get_current_time()}
                        self.request(task, self.home, 'GET', name='home', toRaw=True)
                        res: dict = self.request(task, self.search, 'POST', data=data, name='search')
                        ENTID = res['Data']['Items'][0]['ENTID']
                        params = {"id": ENTID, "view": "info"}
                        self.request(task, self.home, 'GET', name='home', toRaw=True)
                        res = self.request(task, self.detail, 'GET', params=params, name='detail', toText=True)
                        company_info, investor, staff, abnormal_info = self.parse(res)
                        break
                else:
                    params = {"id": ENTID, "view": "info"}
                    res = self.request(task, self.detail, 'GET', params=params, name='detail', toText=True)
                    company_info, investor, staff, abnormal_info = self.parse(res)
                    break
            except:
                continue
        if not company_info:
            raise MyException('连续失败')

        pre_name = task.eventlog.selector.info.get('name', '')
        if pre_name and company_info['企业名称'] != pre_name:
            raise MyException('搜索为空')

        logger.info(f'{task.search} --> company_info: {company_info}')
        logger.info(f'{task.search} --> investor: {investor}')
        logger.info(f'{task.search} --> staff: {staff}')
        logger.info(f'{task.search} --> abnormal_info: {abnormal_info}')
        pages['base_info.txt'] = json.dumps(company_info, ensure_ascii=False)
        pages['investor_info.txt'] = json.dumps(investor, ensure_ascii=False)
        pages['staff_info.txt'] = json.dumps(staff, ensure_ascii=False)
        pages['abnormal_info.txt'] = json.dumps(abnormal_info, ensure_ascii=False)
        return pages

    def request(self, task: SZCrawlerTask, url: str, method: str, params: dict = None, data: dict = None, json: dict = None,
                path: str = '', name: str = '', toRaw=False, toText=False) -> Union[dict, Response, str]:
        for _ in range(10):
            response = None
            try:
                self.set_cookies(task)
                a = time.time()
                response = task.session.request(**{'method': method, 'url': url, 'data': data, 'headers': self.headers, 'json': json,
                                                   'params': params, 'verify': False, 'timeout': self.timeout, 'proxies': self.get_long_proxy()})
                logger.info(f'{task.search} --> {name} - {response.status_code} - time:{time.time() - a}')
                if name == 'search' and response.status_code == 500:
                    logger.error(f"{name} --> html {response.status_code}-{response.text} 重试")
                    raise MyException('搜索失败')
                if response.status_code == 404:
                    raise MyException('404')
                if response.status_code in [206, 500]:
                    del task.session.cookies['proxyBase']
                    logger.error(f"{name}-{_ + 1} --> html {response.status_code}-{response.text[:300]} 重试")
                    continue
                if response.status_code == 412:
                    task.rs.update({'html': response.text})
                    continue
                if toRaw:
                    return response
                encoding = cchardet.detect(response.content)['encoding']
                response.encoding = encoding
                if toText:
                    if name == 'detail' and '您查询的过于频繁，请稍后再查' in response.text:
                        del task.session.cookies['proxyBase']
                        continue
                    return response.text
                logger.info(f"{task.search} --> {path.split('/')[-1]}:{name}({_ + 1}) --> {response.json()}")
                if name == 'search' and not response.json().get('IsSuccessed', False):
                    raise MyException('搜索失败')
                return response.json()
            except (requests.exceptions.ConnectionError, requests.exceptions.ProxyError, requests.exceptions.Timeout) as e:
                logger.warning(f"{task.search} --> {path.split('/')[-1]}:{name}({_ + 1}) --> 代理问题 重试：{e}")
                del task.session.cookies['proxyBase']
            except MyException as e:
                raise e
            except requests.exceptions.JSONDecodeError as e:
                logger.info(f'{task.search}-{_ + 1} -->{e} --> {name} --> {response.text}')
                raise Exception('json解析错误')
            except Exception as e:
                html = response.text if response else ''
                status_code = response.status_code if response else ''
                logger.error(f"{task.search} --> {name}-{status_code} - html: {html} 未知错误：{get_stack_info()}")
                del task.session.cookies['proxyBase']
        raise MyException('接口连续失败')

    @staticmethod
    def set_cookies(task: SZCrawlerTask):
        if 'html' in task.rs:
            res = requests.post('http://10.98.47.31:19081/get_cookie', data=task.rs, timeout=5).json()
            task.session.cookies.set('EKyd12pLzdcZP', res['cookie'], domain='amr.sz.gov.cn')

    def verify(self, img: bytes) -> str:
        temp = self.ocr_ddd.classification(img)
        logger.info(f'识别结果：{temp}')
        left, right = re.findall(f'(\d+)', temp)
        operator = re.search(r'\d+([乘加减除])\d+', temp).group(1).replace('乘', '*').replace('加', '+').replace('减', '-').replace('除', '/')
        result = eval(f'{left}{operator}{right}')
        logger.info(f'计算结果：{result}')
        return str(result)

    @staticmethod
    def get_current_time():
        tz = pytz.timezone('Asia/Shanghai')
        current_time = datetime.now(tz)
        formatted_time = current_time.strftime('%a %b %d %Y %H:%M:%S GMT%z (中国标准时间)')
        return formatted_time

    @staticmethod
    def parse(html):
        soup = BeautifulSoup(html, 'lxml')
        tables = soup.find_all('table')
        info, investor, staff, abnormal_info = {}, [], [], []
        for table in tables:
            title = table.find_all('tr')[0].text.strip()
            if title == '基本信息':
                trs = table.find_all('tr')
                for tr in trs[1:]:
                    temp = list(tr.stripped_strings)
                    if len(temp) <= 3:
                        info[temp[0]] = temp[1]
                    if len(temp) == 4:
                        info[temp[0]] = temp[1]
                        info[temp[2]] = temp[3]
            if title in ['股东信息', '合伙人信息']:
                trs = table.find_all('tr')
                for tr in trs[2:]:
                    temp = list(tr.stripped_strings)
                    investor.append({'name': temp[0], '认缴出资额': temp[1]})
            if title == '成员信息':
                trs = table.find_all('tr')
                for tr in trs[2:]:
                    temp = list(tr.stripped_strings)
                    staff.append({'name': temp[0], 'position': temp[1]})
            if title == '经营异常信息':
                trs = table.find_all('tr')
                for tr in trs[2:]:
                    temp = []
                    for e in tr:
                        temp.append(e.text.strip() if e.text.strip() else '')
                    if len(temp) > 4:
                        abnormal_info.append(temp)
        return info, investor, staff, abnormal_info

    @staticmethod
    def get_long_proxy():
        res = requests.get('http://*************:8015/long-proxy')
        proxy = random.choice(res.text.split('\r\n'))
        return {
            'http': f'http://{proxy}',
            'https': f'http://{proxy}'
        }
