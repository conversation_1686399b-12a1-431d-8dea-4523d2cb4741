import requests
import urllib3
from requests.adapters import HTTPAdapter
from loguru import logger
from PIL import Image
from io import BytesIO
import ddddocr
import re

urllib3.disable_warnings()

from libs.env import get_stack_info
from entity.eventlog import Eventlog, SpiderCode
from apps.gs_spider.crawler.crawler import <PERSON>rawler, CrawlerTask, MyException


class GDGZCrawlerTask(CrawlerTask):

    def __init__(self, eventlog: Eventlog):
        self.search = eventlog.selector.get_info('keyword')
        self.session = requests.session()
        self.session.proxies = {
            'http': 'http://10.99.138.95:30636',
            'https': 'http://10.99.138.95:30636'
        }
        self.session.mount('http://', HTTPAdapter(max_retries=1))
        self.session.mount('https://', HTTPAdapter(max_retries=1))
        super().__init__(eventlog)


class GDGZCrawler(Crawler):
    def __init__(self, name: str, **kwargs):
        self.headers = {
            "User-Agent": 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/114.0.0.0 Safari/537.36'
        }
        self.timeout = 50
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        super().__init__(name, task_cls=GDGZCrawlerTask, **kwargs)

    def do_crawl(self):
        task: GDGZCrawlerTask = self.get_crawler_task()
        eventlog = task.eventlog

        if task.search is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return

        try:
            url = "https://air.scjgj.gz.gov.cn/cancelEasy/commonquery/getBusinessLicenceImg"
            params = {"uniscid": task.search}
            for _ in range(10):
                try:
                    response = task.session.get(url, headers=self.headers, params=params, timeout=self.timeout, verify=False)
                    if response.status_code != 200:
                        continue
                    image = Image.open(BytesIO(response.content))
                    image_time = image.crop((1590, 1280, 1920, 1320))
                    image_data = BytesIO()
                    image_time.save(image_data, format='PNG')
                    date = self.ocr.classification(image_data.getvalue())
                    logger.info(f'{task.search} 核准日期 {date}')
                    if not re.search('\d{4}年\d{1,2}月\d{1,2}日', date):
                        raise MyException('搜索为空')
                    task.pages['核准日期'] = date
                    return
                except Exception as e:
                    logger.error(f'请求失败 {e}')
                    continue
            raise MyException('连续失败')
        except MyException as e:
            if e.message == '搜索为空':
                logger.debug(f'搜索为空')
                eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            if '连续失败' in e.message:
                logger.debug(f'{e.message}')
                eventlog.spider_code = SpiderCode.FAIL
            task.pages = {}
            return
        except Exception as e:
            raise e
