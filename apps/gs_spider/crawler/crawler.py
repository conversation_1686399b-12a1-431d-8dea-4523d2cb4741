# encoding=utf8

import logging
from abc import abstractmethod
from typing import Dict
from threading import Lock, current_thread

from entity.eventlog import Eventlog
from libs.dt import cur_ts_sec
from apps.gs_spider.utils.obs_manager import OBSManager

logger = logging.getLogger(__name__)


# Crawler的每个线程holder一个CrawlerTask，对应一个eventlog
class CrawlerTask(object):
    def __init__(self, eventlog: Eventlog):
        # print(f'Initializing CrawlerTask with eventlog: {eventlog}, cache: {cache}')  # 添加打印语句
        self.eventlog: Eventlog = eventlog
        self.event_id = self.eventlog.event_id
        self.pages: Dict[str, str] = dict()


class Crawler(object):
    def __init__(self, name: str, task_cls=CrawlerTask, page_bak_count: int = 3):
        self.name = name  # the spider's name
        self.task_cls = task_cls
        self.page_bak_count = page_bak_count
        self.obs_manager = OBSManager()
        self.tasks: Dict[int, task_cls] = dict()
        self.tasks_lock = Lock()

    def crawl(self, eventlog: Eventlog) -> Eventlog:
        logger.info(f'==== BEGIN {eventlog}')

        cache_obs_path = f'page/{eventlog.selector.inst_name}/{eventlog.selector.word}/cache.json'
        eventlog.spider.spider_data['cache'] = self.obs_manager.download_cache(cache_obs_path)

        with self.tasks_lock:
            tid = current_thread().ident
            self.tasks[tid] = self.task_cls(eventlog)

        eventlog.spider.receive_ts = cur_ts_sec()
        self.do_crawl()
        task = self.get_crawler_task()

        # send responses to OBS
        page_ts = self.obs_manager.upload_pages(
            pages=task.pages,
            bak_count=self.page_bak_count,
            base_dir=f'page/{eventlog.selector.inst_name}/{eventlog.selector.word}',
        )
        self.obs_manager.upload_cache(
            cache=eventlog.spider.spider_data['cache'],
            bak_count=1,
            cache_base_dir=f'page/{eventlog.selector.inst_name}/{eventlog.selector.word}/cache.json',
        )
        if page_ts > 0:
            eventlog.spider.spider_data['page_ts'] = page_ts

        return eventlog

    @abstractmethod
    def do_crawl(self):
        pass

    def get_crawler_task(self):
        with self.tasks_lock:
            tid = current_thread().ident
            if tid not in self.tasks:
                raise RuntimeError(f'not task tid={tid}')
            return self.tasks[tid]


class MyException(Exception):

    def __init__(self, message):
        self.message = message
        super().__init__(self.message)
