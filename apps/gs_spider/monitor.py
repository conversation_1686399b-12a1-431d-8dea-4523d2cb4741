from datetime import datetime, timedelta
import schedule
import time
from dao.octopus.solver_minute_count import SolverMinuteCountDao
from libs.env import ConstantProps
from libs.feishu import send_feishu_message


# open_id_dict = {
#     '史安琪': 'ou_51e8bc246873e0f4c31176484dc6f72b',
#     '丁良益': 'ou_cd3c38e542a51827dd48c3655b545c5c',
#     '于志国': 'ou_66775216bebe1d65e8d9226de6b70ace',
#     '王帮旭': 'ou_8502bcf8edd9fc542defdea8492bc64e',
# }

class Monitor:
    def __init__(self):
        self.SolverMinuteCountDao = SolverMinuteCountDao(**ConstantProps.PROPS_GS_TEST)
        self.url = 'https://open.feishu.cn/open-apis/bot/v2/hook/fa593330-a4ee-40c1-936f-447892785924'
        # threshold 为半小时额定最低抓取量
        self.config = {
            ('company', 'XA'): {'owner': '王帮旭', 'threshold': 16161 // 5, 'lasttime': None},
            ('company', 'bj'): {'owner': '丁良益', 'threshold': 73117 // 48 // 10, 'lasttime': None},
            ('company', 'js'): {'owner': '史安琪', 'threshold': 19237 // 48 // 10, 'lasttime': None},
            ('company', 'gdsz'): {'owner': '丁良益', 'threshold': 30000 // 48 // 10, 'lasttime': None},
            ('company', 'gd'): {'owner': '史安琪', 'threshold': 1329666 // 48 // 10, 'lasttime': None},
            ('company', 'tj'): {'owner': '丁良益', 'threshold': 207065 // 48 // 10, 'lasttime': None},
            ('company', 'jx'): {'owner': '丁良益', 'threshold': 190101 // 48 // 10, 'lasttime': None},
            ('company', 'hlj'): {'owner': '史安琪', 'threshold': 13792 // 48 / 10, 'lasttime': None},
            ('company', 'han'): {'owner': '史安琪', 'threshold': 684690 // 48 // 10, 'lasttime': None},
            ('company', 'gz'): {'owner': '史安琪', 'threshold': 22211 // 48 // 10, 'lasttime': None},
            ('company', 'sh'): {'owner': '丁良益', 'threshold': 118978 // 48 // 10, 'lasttime': None},
            ('company', 'fjxm'): {'owner': '丁良益', 'threshold': 14685 // 48 // 10, 'lasttime': None},
            ('company', 'gdzh'): {'owner': '丁良益', 'threshold': 45482 // 48 // 10, 'lasttime': None},
            # ('org', 'npo'): {'owner': '金辰星', 'threshold': 78238},
            # ('hk', 'default'): {'owner': '金辰星', 'threshold': 216400},
        }

    def run(self):
        try:
            end_time = datetime.now()  # 当前时间
            start_time = end_time - timedelta(minutes=30)  # 30分钟前
            start_timestamp = start_time.timestamp()
            end_timestamp = end_time.timestamp()
            for item, value in self.config.items():
                threshold = value['threshold']
                args = (start_timestamp // 60, end_timestamp // 60, item[0], item[1])
                result = self.SolverMinuteCountDao.select(*args)

                if int(result) < threshold:
                    if value['lasttime'] is None:
                        send_feishu_message(ats=[value['owner']],
                                            text=f'{item[0]}-{item[1]} 在过去半个小时内抓取量低于 {threshold}/0.5h, 现抓取量为{result}',
                                            url=self.url)
                        self.config[item]['lasttime'] = end_timestamp
                    elif end_timestamp - value['lasttime'] >= 21600:
                        send_feishu_message(ats=[value['owner']],
                                            text=f'{item[0]}-{item[1]} 在过去半个小时内抓取量低于 {threshold}/0.5h, 现抓取量为{result}',
                                            url=self.url)
                        self.config[item]['lasttime'] = end_timestamp

        except Exception as e:
            send_feishu_message(ats=['丁良益'], text=f'监控程序要挂啦!! - {e}', url=self.url)

    def main(self):
        schedule.every(10).minutes.do(self.run)
        while True:
            schedule.run_pending()
            time.sleep(10)


if __name__ == '__main__':
    Monitor().main()
    # Monitor().run()