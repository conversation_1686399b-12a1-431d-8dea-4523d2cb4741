# -*- coding: utf-8 -*-

import logging
import json
from flask import Flask, jsonify, request
from apps.gs_correction_api.controller import Controller

app = Flask('gs_correction_api')
controller = Controller()
logger = logging.getLogger(__file__)


@app.route('/say', methods=['POST'])
def say():
    response = controller.say(json.loads(request.data.decode()))
    return jsonify(response.dump())


@app.route('/gs_correct_data', methods=['POST'])
def gs_correct_data():
    response = controller.gs_correct_data(json.loads(request.data.decode()))
    return jsonify(response.dump())


if __name__ == '__main__':
    from libs.log2 import setup_logger
    logger = setup_logger()
    app.run(debug=True, port=9310)
