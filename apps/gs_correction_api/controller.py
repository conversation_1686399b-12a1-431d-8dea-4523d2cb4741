# -*- coding: utf-8 -*-

import time
import logging
from typing import Dict, Any, List, Tuple
from datetime import datetime
from clients.mysql_client import MySQLClient
from libs.env import get_props_mysql
logger = logging.getLogger(__name__)


class ResponseData(object):
    def __init__(self, param_dict=None):
        self.param_dict = param_dict
        self.code = 0
        self.message = 'success'
        self.data = {}
        self.ts = time.time()

    def set_data(self, key, value):
        self.data[key] = value

    def get_data(self, key):
        return self.data.get(key, None)

    def set_code_message(self, code, message):
        self.code = code
        self.message = message

    def dump(self) -> dict:
        ts = time.time() - self.ts
        d = {
            'code': self.code,
            'message': self.message,
            'data': self.data,
            'ts': ts,
        }
        logger.info('params=%s ret=%s', self.param_dict, d)
        return d


class Controller(object):
    dimension_code_map = {
        '1002000100': {
            'active': 1,
            'keys': [
                ('gid', 'org_id'),
                ('title', 'ed'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds110',
            'table': 'data_listed_company.balance_sheet_ns',
        },  # 二期 财务数据-资产负债表 唐杰成
        '1002000101': {
            'active': 1,
            'keys': [
                ('gid', 'org_id'),
                ('title', 'ed'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds110',
            'table': 'data_listed_company.cash_flow_statement_ns',
        },  # 二期 财务数据-现金流量 唐杰成
        '1002000102': {
            'active': 1,
            'keys': [
                ('gid', 'org_id'),
                ('title', 'ed'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds110',
            'table': 'data_listed_company.income_statement_ns',
        },  # 二期 财务数据-利润表 唐杰成

        '1002003': {
            'active': 1,
            'keys': [
                ('branchCid', 'id'),
                ('cid', 'parent_id'),
            ],
            'update_key_value': ('parent_id', 0),
            'mysql_instance': 'mysql.tyc.gs.hw_gsxt_outer_rw',
            'table': 'prism.company',
        },  # 二期 分支机构
        '1002004': {
            'active': 1,
            'keys': [
                ('cid', 'id'),
                ('parentCid', 'parent_id'),
            ],
            'update_key_value': ('parent_id', 0),
            'mysql_instance': 'mysql.tyc.gs.hw_gsxt_outer_rw',
            'table': 'prism.company',
        },  # 二期 总公司
        '1012001': {
            'active': 1,
            'keys': [
                ('uuid', 'uuid'),
            ],
            'update_key_value': ('isdelete', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds107',
            'table': 'data_judicial.company_law_judicial_case_detail',
        },  # 二期 司法案件 薛秋雨,

        '1012003': {
            'active': 1,
            'keys': [
                ('id', 'id'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds108',
            'table': 'data_judicial_risk.court_notices',
        },  # 二期 开庭公告 张鑫明,
        '1012004': {
            'active': 1,
            'keys': [
                ('caseNo', 'caseNo'),
                ('content', 'content'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds108',
            'table': 'data_judicial_risk.send_announcement',
        },  # 二期 送达公告 张晓永,
        '1012005': {
            'active': 1,
            'keys': [
                ('uuid', 'uuid'),
            ],
            'update_key_value': ('isdelete', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds103',
            'table': 'data_lawsuit.company_lawsuit',
        },  # 二期 裁判文书 唐杰成,
        '1012006': {
            'active': 1,
            'keys': [
                ('id', 'id'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds108',
            'table': 'data_judicial_risk.court_register',
        },  # 二期 立案信息 张晓永,
        '1061022': {
            'active': 1,
            'keys': [
                ('caseNo', 'case_no'),
                ('caseDate', 'filing_date'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds108',
            'table': 'data_judicial_risk.court_register',
        },  # 二期 历史立案信息 张晓永

        '1012007': {
            'active': 1,
            'keys': [
                ('id', 'id'),
            ],
            'update_key_value': ('deleted', 1),
            'history_key_value': ('status', 1),  # optional default is update_key_value, used when set history
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds108',
            'table': 'data_judicial_risk.zhixinginfo_restrict',
        },  # 二期 限制消费令 薛秋雨,
        '1012009': {
            'active': 1,
            'keys': [
                ('caseId', 'id'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds108',
            'table': 'data_judicial_risk.zhixinginfo_restricted_outbound',
        },  # 二期 限制出境 张鑫明,
        '1012010100': {
            'active': 1,
            'keys': [
                ('mainId', 'id'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds108',
            'table': 'data_judicial_risk.zhixinginfo_evaluate_result',
        },  # 二期 询价评估结果 薛秋雨,
        '1012010101': {
            'active': 1,
            'keys': [
                ('id', 'id'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds108',
            'table': 'data_judicial_risk.zhixinginfo_evaluate',
        },  # 二期 询价评估结果 薛秋雨,

        '1022002': {
            'active': 1,
            'keys': [
                ('main_id', 'id'),
                ('taxpayer_name', 'taxpayer_name'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds109',
            'table': 'data_business_risk.tax_contravention',
        },  # 二期 税收违法 金炜,
        '10220021': {
            'active': 1,
            'keys': [
                ('main_id', 'id'),
            ],
            'update_key_value': ('deletetd', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds109',
            'table': 'data_business_risk.improper_taxpayer',
        },  # 二期 税收违法 金炜,

        '1022005': {
            'active': 1,
            'keys': [
                ('caseNo', 'case_no'),
                ('startDate', 'start_date'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds109',
            'table': 'data_business_risk.court_arbitration_notices',
        },  # 二期 劳动仲裁-开庭公告 张鑫明,
        '1022006': {
            'active': 1,
            'keys': [
                ('caseNo', 'case_no'),
                ('startDate', 'start_date'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds109',
            'table': 'data_business_risk.court_arbitration_send_announcement',
        },  # 二期 劳动仲裁-送达公告 张鑫明,
        '1022007': {
            'active': 1,
            'keys': [
                ('id', 'id'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds109',
            'table': 'data_business_risk.land_mortgage',
        },  # 二期 土地抵押 张晓永,
        '1022009': {
            'keys': [
                ('id', 'id'),
            ],
            'update_key_value': ('deleted', 9),
            'history_key_value': ('deleted', 1),  # optional default is update_key_value, used when set history
            'rest_condition': 'deleted in (0, 1, 9)',
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds109',
            'table': 'data_business_risk.company_mortgage_info',
        },  # 二期 动产抵押 金炜,
        '1022010': {
            'keys': [
                ('gid', 'current_id'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.other.rds237',
            'table': 'judicial.business_disputes_data',
        },  # 二期 经营纠纷提示 赵杰
        '1022011': {
            'keys': [
                ('id', 'id'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds109',
            'table': 'data_business_risk.environmental_penalties',
        },  # 二期 环保处罚 张晓永
        '1022012': {
            'keys': [
                ('companyNames', 'company_names'),
                ('publishDate', 'publish_date'),
            ],
            'update_key_value': ('is_deleted', 1),
            'rest_condition': 'is_deleted = 0',
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds109',
            'table': 'data_business_risk.product_recall',
        },  # 二期 产品召回 薛秋雨
        '1022013': {
            'keys': [
                # ('gid', 'gid'),
                # ('punishNumber', 'punish_number'),
                ('id', 'id'),
            ],
            'update_key_value': ('state', 2),
            'history_key_value': ('state', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds109',
            'table': 'data_business_risk.company_administrative_punishment',
        },  # 二期 行政处罚 金炜
        '1022016': {
            'keys': [
                ('billNum', 'bill_num'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds109',
            'table': 'data_business_risk.public_notice',
        },  # 二期 公示催告 张鑫明

        '1022018': {
            'keys': [
                # ('name', 'name'),
                # ('publishDate', 'publish_date'),
                # ('taxCategory', 'tax_category'),
                # ('ownTaxBalance', 'own_tax_balance'),
                ('id', 'id'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds109',
            'table': 'data_business_risk.company_own_tax',
        },  # 二期 欠税公告 唐杰成
        '1032000': {
            'keys': [
                ('uuid', 'uuid'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds104',
            'table': 'data_bid.company_bid',
        },  # 二期 招投标 薛秋雨
        '1032001': {
            'keys': [
                ('detailId', 'id'),
                ('source', 'source'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.property_trans',
        },  # 二期 资产交易 张晓永
        '1032002': {
            'keys': [
                ('id', 'id'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.ranking_list_detail',
        },  # 二期 上榜榜单 张鑫明
        '1032004': {
            'keys': [
                ('companyName', 'company_name'),
                ('webInfoPath', 'url_Path'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds110',
            'table': 'data_development.company_employment',
        },  # 二期 招聘信息 唐杰成
        '1032006': {
            'keys': [
                ('companyName', 'land_use_right_person'),
                ('landArea', 'land_area'),
                ('landLocation', 'land_location'),
                ('publicationDate', 'publication_date_clean'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.land_publicity',
        },  # 二期 地块公示 张晓永
        '1032007': {
            'keys': [
                ('id', 'id'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.land_transfer',
        },  # 二期 地块公示 张晓永
        '1032008': {
            'keys': [
                ('checkNo', 'check_no'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.spcjsac_check',
        },  # 二期 食品安全 张晓永
        '1032009': {
            'keys': [
                ('docid', 'docid'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.rds485',
            'table': 'data_news.yuqing_ins',
        },  # 二期 新闻舆情 金炜
        '1032010': {
            'keys': [
                # ('gid', 'gid'),
                # ('licenceNumber', 'license_number'),
                ('id', 'id'),
            ],
            'update_key_value': ('state', 2),
            'history_key_value': ('state', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.company_administrative_license',
        },  # 二期 行政许可 金炜
        '1032011': {
            'keys': [
                ('publicNum', 'publicNum'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.wechat_public_num',
        },  # 二期 微信公众号 唐杰成
        '1032012': {
            'keys': [
                ('companyName', 'publisher_name'),
                ('bondNum', 'bond_num'),
                ('bondName', 'bond_name'),
            ],
            'update_key_value': ('is_delete', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.bond',
        },  # 二期 债券信息 唐杰成
        '1032013': {
            'keys': [
                ('companyName', 'name'),
                ('idNumber', 'id_number'),
                ('year', 'year'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.tax_credit',
        },  # 二期 税务评级 金炜
        '1032014': {
            'keys': [
                ('name', 'name'),
            ],
            'update_key_value': ('isdelete', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.sina_weibo',
        },  # 二期 微博 唐杰成
        '1032015': {
            'keys': [
                ('id', 'id'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.land_result_announcement',
        },  # 二期 购地信息 张晓永
        '1032016': {
            'keys': [
                ('gid', 'graph_id'),
                ('taxpayerQualificationType', 'qualification_type'),
            ],
            'update_key_value': ('deleted', 1),
            'rest_condition': 'valid_date is null ',
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.common_taxpayer',
        },  # 二期 纳税人资质 张晓永
        '**********': {
            'keys': [
                ('gid', 'graph_id'),
                ('accountNum', 'account_num'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.douyin_info',
        },  # 二期 抖音 金炜
        '**********': {
            'keys': [
                ('gid', 'graph_id'),
                ('accountNum', 'account_num'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.kuaishou_info',
        },  # 二期 快手 金炜
        '1032020': {
            'keys': [
                ('gid', 'client_graphId'),
                ('supplier_name', 'supplier_name'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds110',
            'table': 'data_listed_company.supply',
        },  # 二期 供应商 唐杰成
        '1032021': {
            'keys': [
                ('gid', 'graph_id'),
                ('md5Code', 'md5_code'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds110',
            'table': 'data_listed_company.announcement_relationship',
        },  # 二期 公告研报 唐杰成
        '1032022': {
            'keys': [
                ('id', 'id'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.appbk_info',
        },  # 二期 产品信息 张鑫明
        '1032023': {
            'keys': [
                ('gid', 'supplier_graphId'),
                ('client_name', 'client_name'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds110',
            'table': 'data_listed_company.supply',
        },  # 二期 客户 唐杰成
        '1042000': {
            'keys': [
                ('id', 'id'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds110',
            'table': 'data_development.qimingpian_history_rongzi',
        },  # 二期 融资历程 金炜
        '1041000': {
            'keys': [
                ('reg_no', 'reg_no'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds110',
            'table': 'data_development.amac_manager',
        },  # 二期 私募基金 鑫明
        '1042001': {
            'keys': [
                ('companyName', 'company_name'),
                ('name', 'name'),
                ('mapDescId', 'map_desc_id'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds110',
            'table': 'data_development.qimingpian_team_member',
        },  # 二期 核心团队 金炜
        '1042002': {
            'keys': [
                ('companyName', 'company_name'),
                ('yewu', 'yewu'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds110',
            'table': 'data_development.qimingpian_product',
        },  # 二期 企业业务 金炜
        '1042004': {
            'keys': [
                ('companyName', 'company_name'),
                ('jingpinProductId', 'jingpin_product_id'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds110',
            'table': 'data_development.qimingpian_jingpin_map',
        },  # 二期 竞品信息 金炜
        '1042005': {
            'keys': [
                ('companyName', 'company_name'),
                ('jigou_name', 'jigou_name'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds110',
            'table': 'data_development.qimingpian_jigou_basic',
        },  # 二期 投资机构 金炜
        '1052000': {
            'keys': [
                ('case_no', 'case_no'),
                ('publish_time', 'publish_time'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds105',
            'table': 'data_trademark.tm_lawsuit',
        },  # 二期 商标文书 金炜
        '1052001': {
            'keys': [
                ('regnum', 'regNum'),
            ],
            'update_key_value': ('isdelete', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_patent.copyright_reg',
        },  # 二期 软件著作权 金炜
        '1052002': {
            'keys': [
                ('regnum', 'regNum'),
            ],
            'update_key_value': ('isDeleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_patent.copyright_works',
        },  # 二期 作品著作权 张晓永

        '1011000': {
            'active': 1,
            'keys': [
                # ('zname', 'iname'),
                # ('caseCode', 'caseCode'),
                ('id', 'id'),
            ],
            'update_key_value': ('deleted', 1),
            'history_key_value': ('status', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds108',
            'table': 'data_judicial_risk.dishonestinfo',
        },  # '失信被执行人 秋雨',
        '1011001': {
            'active': 1,
            'keys': [
                # ('pname', 'pname'),
                # ('caseCode', 'caseCode'),
                ('id', 'id'),
            ],
            'update_key_value': ('deleted', 1),
            'history_key_value': ('status', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds108',
            'table': 'data_judicial_risk.zhixinginfo',
        },  # '被执行人 秋雨',
        '1011002': {
            'active': 1,
            'keys': [
                ('id', 'id'),
                # ('cid', 'company_id'),
                # ('executedPerson', 'executed_person'),
                # ('executeNoticeNum', 'execute_notice_num'),
                # ('equityAmount', 'equity_amount'),
                # ('executiveCourt', 'executive_court'),
                # ('type', 'ts_type'),
            ],
            'rest_condition': 'deleted in (0, 1, 9)',
            # 'update_key_value': ('deleted', 9),
            'update_key_value': ('deleted', 9),  # used when delete
            'history_key_value': ('deleted', 1),  # optional default is update_key_value, used when set history
            'mysql_instance': 'mysql.tyc.gs.hw_gsxt_outer_rw',
            'table': 'prism.company_judicial_assistance_info',
        },  # '股权冻结',
        '1011003': {
            'active': 1,
            'keys': [
                ('contentMd5', 'content_md5'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds108',
            'table': 'data_judicial_risk.court_announcement',
        },  # '法院公告 晓永',
        '1011004': {
            'active': 1,
            'keys': [
                # ('respondent', 'respondent'),
                # ('caseNo', 'caseNo'),
                ('uuid', 'uuid'),
            ],
            'update_key_value': ('isdelete', 1),
            'history_key_value': ('status', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds108',
            'table': 'data_judicial_risk.company_law_bankruptcy',
        },  # '破产案件 金炜',
        '1011005': {
            'active': 1,
            'keys': [
                ('zname', 'zname'),
                ('caseCode', 'caseCode'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds108',
            'table': 'data_judicial_risk.zhixinginfo_finalcase',
        },  # '终本案件 秋雨',
        '1021000': {
            'active': 1,  # 是否启用
            'keys': [
                # ('cid', 'company_id'),
                # ('iprCertificateNum', 'ipr_certificate_num'),
                # ('pledgeRegPeriod', 'pledge_reg_period'),
                # ('iprType', 'ipr_type'),
                ('id', 'id'),
            ],
            'update_key_value': ('deleted', 9),
            'history_key_value': ('deleted', 1),  # optional default is update_key_value, used when set history
            'rest_condition': 'deleted in (0, 1, 9)',
            'mysql_instance': 'mysql.tyc.gs.hw_gsxt_outer_rw',
            'table': 'prism.company_ipr_pledge_reg_info_entpub',
        },  # '知识产权出质',
        '1021001': {
            'active': 1,  # 是否启用
            'keys': [
                # ('cid', 'company_id'),
                # ('regDate', 'reg_date'),
                # ('pledgorStr', 'pledgor'),
                # ('equityAmount', 'equity_amount'),
                # ('pledgeeStr', 'pledgee'),
                # ('putDate', 'put_date'),
                ('id', 'id'),
            ],
            'update_key_value': ('deleted', 9),
            'history_key_value': ('deleted', 1),  # optional default is update_key_value, used when set history
            'rest_condition': 'deleted in (0, 1, 9)',
            'mysql_instance': 'mysql.tyc.gs.hw_gsxt_outer_rw',
            'table': 'prism.company_equity_info',
        },  # '股权出质',
        '1021007': {
            'active': 1,  # 是否启用
            'keys': [
                ('cid', 'company_id'),
                ('putReason', 'put_reason'),
                ('putDate', 'put_date'),
            ],
            'update_key_value': ('deleted', 9),
            'history_key_value': ('deleted', 1),  # optional default is update_key_value, used when set history
            'rest_condition': 'deleted in (0, 1, 9)',
            'mysql_instance': 'mysql.tyc.gs.hw_gsxt_outer_rw',
            'table': 'prism.company_abnormal_info',
        },  # '经营异常'
        '1021008': {
            'active': 1,  # 是否启用
            'keys': [
                ('cid', 'company_id'),
                ('recordDate', 'liquidating_group_record_date'),
            ],
            'update_key_value': ('deleted', 9),
            'history_key_value': ('deleted', 1),  # optional default is update_key_value, used when set history
            'rest_condition': 'deleted in (0, 1, 9)',
            'mysql_instance': 'mysql.tyc.gs.hw_gsxt_outer_rw',
            'table': 'prism.company_cancel_record_and_announcement_info',
        },  # '注销备案'
        '1021009': {
            'active': 1,  # 是否启用
            'keys': [
                ('cid', 'company_id'),
                ('manager', 'manager'),
                ('member', 'member'),
            ],
            'update_key_value': ('deleted', 9),
            'history_key_value': ('deleted', 1),  # optional default is update_key_value, used when set history
            'rest_condition': 'deleted in (0, 1, 9)',
            'mysql_instance': 'mysql.tyc.gs.hw_gsxt_outer_rw',
            'table': 'prism.company_liquidating_info',
        },  # '清算信息', 
        '1021010': {
            'active': 1,  # 是否启用
            'keys': [
                ('cid', 'company_id'),
                ('putDate', 'put_date'),
                ('putReason', 'put_reason'),
            ],
            'update_key_value': ('deleted', 9),  # deleted_his ?
            'history_key_value': ('deleted', 1),  # optional default is update_key_value, used when set history
            'rest_condition': 'deleted in (0, 1, 9)',
            'mysql_instance': 'mysql.tyc.gs.hw_gsxt_outer_rw',
            'table': 'prism.company_illegal_info',
        },  # '严重违法', 
        '1021011': {
            'active': 1,  # 是否启用
            'keys': [
                ('cid', 'company_id'),
                ('announcement_end_date', 'announcement_end_date'),
            ],
            'update_key_value': ('deleted', 9),
            'history_key_value': ('deleted', 1),  # optional default is update_key_value, used when set history
            'rest_condition': 'deleted in (0, 1, 9)',
            'mysql_instance': 'mysql.tyc.gs.hw_gsxt_outer_rw',
            'table': 'prism.company_brief_cancel_announcement_info',
        },  # '简易注销', 
        '1031000': {
            'active': 1,  # 是否启用
            'keys': [
                ('cid', 'company_id'),
                ('checkPlanNum', 'check_plan_num'),
                ('checkTaskNum', 'check_task_num'),
                ('checkDepartment', 'check_department'),
                ('checkType', 'check_type'),
            ],
            'update_key_value': ('deleted', 9),
            'history_key_value': ('deleted', 1),  # optional default is update_key_value, used when set history
            'rest_condition': 'deleted in (0, 1, 9)',
            'mysql_instance': 'mysql.tyc.gs.hw_gsxt_outer_rw',
            'table': 'prism.company_double_random_check_info',
        },  # '双随机抽查',
        '1031002': {
            'active': 1,
            'keys': [
                ('gid', 'graph_id'),
                ('caseNo', 'caseNo'),
            ],
            'update_key_value': ('is_deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.franchise_information',
        },  # '商业特许经营 杰成',
        '1031003': {
            'active': 1,  # 是否启用
            'keys': [
                ('companyName', 'company_name'),
            ],
            'update_key_value': ('delete', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.company_customs_business_credit',
        },  # '进出口信用 鑫明',
        '1031005': {
            'active': 1,
            'keys': [
                ('companyName', 'company_name'),
                ('licenseNumber', 'license_number'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_experience_situation.company_tele_communication_license',
        },  # '电信许可 晓永',
        '1031006': {
            'active': 0,  # 没有deleted字段
            'keys': [
                ('cid', 'company_id'),
                ('checkOrg', 'check_org'),
                ('checkType', 'check_type'),
                ('checkDate', 'check_date'),
            ],
            'update_key_value': ('deleted', 9),
            'history_key_value': ('deleted', 1),  # optional default is update_key_value, used when set history
            'rest_condition': 'deleted in (0, 1, 9)',
            'mysql_instance': 'mysql.tyc.gs.hw_gsxt_outer_rw',
            'table': 'prism.company_check_info',
        },  # '抽查检查',
        '1051000': {
            'active': 1,
            'keys': [
                ('reg_no', 'reg_no'),
                ('int_cls', 'int_cls'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds105',
            'table': 'data_trademark.tm_info',
        },  # '商标信息 秋雨',
        '1051001': {
            'active': 1,
            'keys': [
                ('appnumber', 'appnumber'),
                ('pubnumber', 'pubnumber'),
            ],
            'update_key_value': ('isDeleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds106',
            'table': 'data_patent.ent_patent_info_20170730',
        },  # '专利信息 金纬',
        '1051002': {
            'active': 1,
            'keys': [
                ('companyName', 'company_name'),
                ('ym', 'ym'),
                ('liscense', 'wz_baxh'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_property.company_icp_record_all_final',
        },  # '网站备案 鑫明',
        '1061009': {
            'active': 1,
            'keys': [
                ('companyName', 'company_name'),
                ('ym', 'ym'),
                ('liscense', 'wz_baxh'),
            ],
            'update_key_value': ('deleted', 1),
            'mysql_instance': 'mysql.tyc.zx.zhuanxiang_rds111',
            'table': 'data_property.company_icp_record_history',
        },  # '历史网站备案 鑫明',
    }

    def __init__(self):
        self.now = datetime.now()
        super(Controller, self).__init__()

    def say(self, param: Dict[str, Any]) -> ResponseData:
        response = ResponseData(param_dict=param)
        response.set_data('now', self.now)
        for key, value in param.items():
            if isinstance(value, str) and not key.startswith('_'):
                response.set_data(key, value)
        return response

    def gs_correct_data(self, param: Dict[str, Any]) -> ResponseData:
        response = ResponseData(param_dict=param)

        cid, gid = param.get('companyId'), param.get('companyGid')
        dimension_code = param.get('dimensionCode')
        operate_lst = param.get('operateDataList')

        if not isinstance(cid, int):
            response.set_code_message(1, 'companyId类型错误')
            return response
        if not isinstance(gid, int):
            response.set_code_message(1, 'companyGid类型错误')
            return response
        if dimension_code not in self.dimension_code_map:
            response.set_code_message(1, 'dimensionCode未查询到')
            return response
        dimension_item = self.dimension_code_map[dimension_code]
        if not dimension_item.get('active', 1):
            response.set_code_message(1, 'dimension处于关闭状态')
            return response
        table_name: str = dimension_item['table']
        keys: List[Tuple[str, str]] = dimension_item['keys']
        if not isinstance(operate_lst, list):
            response.set_code_message(1, 'operateDataList类型错误')
            return response
        mysql_client = MySQLClient(**get_props_mysql(dimension_item['mysql_instance']))
        if not mysql_client:
            response.set_code_message(1, f'mysql实例信息无效{dimension_item["mysql_instance"]}')
            return response
        rest_condition = dimension_item.get('rest_condition', '')

        operate_result_lst = []
        response.set_data('operateResultList', operate_result_lst)
        for operate_item in operate_lst:
            if not isinstance(operate_item, dict):
                response.set_code_message(1, 'operateDataList成员类型错误')
                return response
            task_id = operate_item.get('taskId')
            if not isinstance(task_id, str):
                operate_result_lst.append({
                    "taskId": task_id,
                    "result": "失败",
                    "detail": f'taskId错误',
                })
                continue
            operate_type = operate_item.get('operateType')
            if operate_type == 'DELETE':
                update_key, update_value = dimension_item.get('update_key_value')
            elif operate_type == 'HISTORY':
                update_key, update_value = dimension_item.get('history_key_value') or dimension_item.get('update_key_value')
            else:
                continue

            union_key = operate_item.get('unionKey')
            if not isinstance(union_key, dict):
                operate_result_lst.append({
                    "taskId": task_id,
                    "result": "失败",
                    "detail": f'unionKey错误',
                })
                continue

            # if 'source_table' in union_key:
            #     db_name, tb_name = table_name.split('.')
            #     table_name = db_name + '.' + union_key['source_table']
            #     logger.info(f'change table_name to {table_name}')

            table_params: List[Tuple[str, str]] = []

            item_done = False
            for operate_key, table_key in keys:
                if operate_key == 'cid':
                    table_params.append((table_key, str(cid)))
                    continue
                if operate_key not in union_key:
                    operate_result_lst.append({
                        "taskId": task_id,
                        "result": "失败",
                        "detail": f'key={operate_key}不存在',
                    })
                    item_done = True
                    break
                operate_value = union_key[operate_key]
                if isinstance(operate_value, int) or isinstance(operate_value, float):
                    operate_value = str(operate_value)
                if not isinstance(operate_value, str):
                    operate_result_lst.append({
                        "taskId": task_id,
                        "result": "失败",
                        "detail": "unionKey错误",
                    })
                    item_done = True
                    break
                table_params.append((table_key, operate_value))
            if item_done:
                continue
            logger.info(f'param={param} operate_result_lst={operate_result_lst}')
            if len(table_params) == 0:
                operate_result_lst.append({
                    "taskId": task_id,
                    "result": "失败",
                    "detail": "唯一键获取失败",
                })
                continue
            where_sql = ' and '.join(f'{table_key}=%s' for table_key, operate_value in table_params)
            if rest_condition:
                where_sql += f' and {rest_condition}'
            ret = list(mysql_client.select_many(
                sql=f'select * from {table_name} where {where_sql}',
                args=list(x[1] for x in table_params)
            ))
            ret_size = len(ret) if isinstance(ret, list) else 0
            if ret_size != 1:
                operate_result_lst.append({
                    "taskId": task_id,
                    "result": "失败",
                    "detail": f"未定位到唯一条目数据 {ret_size}",
                })
                continue
            dim_item = ret[0]
            if 'id' not in dim_item:
                response.set_code_message(2, f'id字段 不在查询结果中 {dimension_item}')
                return response
            if update_key not in dim_item:
                response.set_code_message(2, f'{update_key} 不在查询结果中 {dimension_item}')
                return response
            if dim_item[update_key] == update_value:
                operate_result_lst.append({
                    "taskId": task_id,
                    "result": "成功",
                    "detail": "已屏蔽的数据重复操作",
                })
                continue
            update_key_renamed = '`delete`' if update_key == 'delete' else update_key
            ret = mysql_client.execute(
                sql=f'update {table_name} set {update_key_renamed}=%s where id=%s limit 1',
                args=(update_value, dim_item['id']),
            )
            if ret == 1:
                operate_result_lst.append({
                    "taskId": task_id,
                    "result": "成功",
                    "detail": f"{dim_item[update_key]} -> {update_value}",
                })
            else:
                operate_result_lst.append({
                    "taskId": task_id,
                    "result": "失败",
                    "detail": "执行更新操作失败",
                })
        return response
