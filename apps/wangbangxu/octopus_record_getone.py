# encoding=utf8
import json
import re
import os.path
from collections import Counter
from datetime import datetime, timedelta, date
from libs.dt import to_datetime, datetime2str
from libs.log2 import setup_logger
from clients.obs_client import OBSClient
from entity.eventlog import Eventlog

logger = setup_logger()


cid_list = [int(x) for x in open('octopus_record_getone_report.txt', 'r').readlines()]

eventlog_obs_client = OBSClient(inst_name='obs.tyc_dyn', bucket_name='jindi-oss-gsxt-eventlog')
page_obs_client = OBSClient(inst_name='obs.tyc_dyn', bucket_name='jindi-oss-gsxt')
counter = Counter()


def get_social_data(cid: int):
    if os.path.exists(f'octopus_record_getone_report_data/{cid}.json'):
        report_social_data = open(f'octopus_record_getone_report_data/{cid}.json', 'rb').read()
        report_social_ts = open(f'octopus_record_getone_report_data/{cid}.ts.txt', 'r').read()
    else:
        for report_year_prefix in page_obs_client.list(f'company/xa/report/{cid}/report_', recursive=False):
            # report_base_path = f'company/xa/report/{cid}/report_{year}/baseinfoUrl.json'
            report_social_path = f'{report_year_prefix}/annSocsecinfoUrl.json'
            report_social_data, report_social_ts = page_obs_client.get_content(report_social_path, with_ts=True)
            if report_social_data:
                f = open(f'octopus_record_getone_report_data/{cid}.json', 'wb')
                f.write(report_social_data)
                f.close()
                f = open(f'octopus_record_getone_report_data/{cid}.ts.txt', 'w')
                f.write(datetime2str(report_social_ts))
                f.close()
                break
        else:
            return None, b''
    return report_social_ts, report_social_data


def main():
    for row_id, cid in enumerate(cid_list):
        path = f'octopus/company/0/{cid}/'
        scheduling = False
        for eventlog_file in eventlog_obs_client.list(prefix=path):
            mo = re.search(r'octopus_(\w+)-(\w+)-(\w+)-(\w+)-(\d+)-(\d)\.json', eventlog_file)
            if not mo:
                continue
            entry_or_clue, item_name, word, inst_name, ts, status = mo.groups()
            if inst_name != 'XA':
                continue
            if to_datetime(int(ts)) < datetime(year=2024, month=5, day=21, hour=17, minute=40):
                continue
            if status != '9':
                # logger.info(f'{ts} {cid} status={status}')
                scheduling = True
                continue
            ts = datetime2str(to_datetime(int(ts)))
            eventlog_data = eventlog_obs_client.get(eventlog_file)
            eventlog = Eventlog.model_validate_json(eventlog_data)
            reason = eventlog.selector.reason
            if eventlog.crawler_code == 11:
                # logger.info(f'{row_id} {cid} {ts} {reason} SEARCH EMPTY')
                print(cid, -1)
                counter['SEARCH_EMPTY'] += 1
                break
            report_social_ts, report_social_data = get_social_data(cid)
            report_social = json.loads(report_social_data or '{}')
            social_dict = report_social.get('data', [{}])[0]
            social_num = max(social_dict.get('so210', 0), social_dict.get('so110', 0), social_dict.get('so310', 0), social_dict.get('so410', 0))
            #logger.info(f'{row_id} {cid} {ts} {reason} {report_social_ts} {social_num}')
            print(cid, social_num)
            counter['ok'] += 1
            break
        else:
            if scheduling:
                counter['scheduling'] += 1
            else:
                counter['no_scheduling'] += 1
            # logger.info(f'{row_id} {cid} scheduling={scheduling}')
            print(cid, -1)

    logger.info(counter)


if __name__ == '__main__':
    main()