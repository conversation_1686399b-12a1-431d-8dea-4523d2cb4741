# encoding=utf8
import json
import re
from typing import Generator
from clients.obs_client import OBSClient
from dataclasses import dataclass
import bs4
from bs4 import Tag
import execjs
from libs.log2 import setup_logger
from gslib.id_center import id_center_query
from dao.company import CompanyGraph, CompanyGraphDao
from dao.investors.equity_ratio import EquityRatioDao, EquityRatio


logger = setup_logger()
company_graph_dao = CompanyGraphDao()
er_dao = EquityRatioDao()

pygs_dir = '/Users/<USER>/PycharmProjects/git/pygs-work-parent/'

page_obs_client = OBSClient(inst_name='obs.tyc_dyn', bucket_name='jindi-oss-gsxt')


@dataclass
class InvestorData:
    name: str
    gid: int = 0


def get_investors_qcc(use_qcc) -> Generator[InvestorData, None, None]:
    if not use_qcc:
        return
    qcc_detail_html_file = f'{pygs_dir}/apps/wangbangxu/gsxt_tools/investor_cmp_qcc.html'
    soup = bs4.BeautifulSoup(markup=open(qcc_detail_html_file, 'r', encoding='utf8'), features='lxml')

    scripts = soup.findAll('script')

    init_scripts = """
    var window = {}, document={}; 
    document.scripts = [
    {
        "parentNode": {
            removeChild: (a) => null
        }
    }
    ];
    """
    for script in scripts:
        script: Tag
        try:
            ctx = execjs.compile(init_scripts + script.text)
            data = ctx.eval('window.__INITIAL_STATE__')
            if isinstance(data, dict):
                break
        except execjs.ProgramError as e:
            logger.warning(f'error exec {script.text} e={e}')
    else:
        logger.warning('no found data')
        exit(1)

    company: dict = data['company']['companyDetail']
    partners = company['Partners']
    credit_code = company['CreditCode']

    for partner in partners:
        partner_name = partner['StockName']
        ent_type, gid = id_center_query(name=partner_name, use_graph_id=True)
        # logger.info(f'debug {partner_name} {gid}')
        yield InvestorData(name=partner_name, gid=gid)


def get_investor_er(gid) -> Generator[InvestorData, None, None]:
    for er in er_dao.get_many(value=gid, field='company_graph_id'):
        er: EquityRatio
        if er.source == 100:
            continue
        yield InvestorData(name=er.shareholder_name, gid=er.shareholder_graph_id)


def get_investor_obs(cid) -> Generator[InvestorData, None, None]:
    shareholder_name_info = {}
    for path in page_obs_client.list(f'company/xa/{cid}/shareholderUrl', recursive=True):
        if not re.search(r'shareholderUrl(_\d+)?\.json', path):
            continue
        content = page_obs_client.get_content(path).decode()
        logger.info(f'CONTENT {content}')
        shareholder_list = json.loads(content)['data']
        for shareholder_dict in shareholder_list:
            inv = shareholder_dict['inv']
            shareholder_name_info.setdefault(inv, [])
            inv_id = shareholder_dict['invId']
            detail_check = shareholder_dict['detailCheck']
            inv_detail = page_obs_client.get_content(f'company/xa/{cid}/shareholderUrl_item_{inv_id}.json')
            shareholder_name_info[inv].append((path, inv_id, detail_check, inv_detail))
            if len(shareholder_name_info[inv]) > 1:
                for v_i in shareholder_name_info[inv]:
                    logger.info(f'DUP {inv} >>> {v_i}')
            else:
                ent_type, gid = id_center_query(name=inv, use_graph_id=True)
                yield InvestorData(name=inv, gid=gid)


def investor_cmp(cid: int, use_qcc=False):
    cg: CompanyGraph = company_graph_dao.get_by_cid(cid)
    assert cg
    investors_qcc = dict((investor.gid, investor) for investor in get_investors_qcc(use_qcc))
    logger.info(f'investors_qcc SIZE {len(investors_qcc)} {investors_qcc}')
    investors_er = dict((investor.gid, investor) for investor in get_investor_er(cg.cgid))
    logger.info(f'investors_er  SIZE {len(investors_er)} {investors_er}')
    investors_obs = dict((investor.gid, investor) for investor in get_investor_obs(cg.cid))
    logger.info(f'investors_obs SIZE {len(investors_obs)} {investors_obs}')

    for investor in investors_er | investors_obs | investors_qcc:
        if investor not in investors_er:
            logger.warning(f'investors_er MISS {investors_obs.get(investor)} {investors_qcc.get(investor)}')
        if investor not in investors_obs:
            logger.warning(f'investors_obs MISS {investors_er.get(investor)} {investors_qcc.get(investor)}')
        if investor not in investors_qcc and use_qcc:
            logger.warning(f'investors_qcc MISS {investors_er.get(investor)} {investors_obs.get(investor)}')


def main():
    investor_cmp(cid=2636650753, use_qcc=True)


if __name__ == '__main__':
    main()
