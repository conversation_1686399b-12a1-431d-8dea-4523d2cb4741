import json
import re
from typing import Dict
import argparse
import csv
import time
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor
from gslib.schedule import immediately_gs
from dao.company import Company, CompanyDao
from dao.reports.annual_report import AnnualReportDao, AnnualReport
from dao.reports.annual_report_brief import AnnualReportBriefDao, AnnualReportBrief
from clients.redis.redis_queue import RedisQueue
from gslib.msv import msv_query_base_info
from libs.dt import to_date
from dao.company_history_name import CompanyHistoryName, CompanyHistoryNameDao
from dao.deps.mysql_dao import MySQLDao


logger = setup_logger()
fs = dict()
process_code_stat = Counter()
company_dao = CompanyDao()


# 三无公司更新程序 参考红杰的交接文档：https://b3sh6jivuw.feishu.cn/docx/doxcn1fNlUxsKq8BFZE91ZNGikd
# - 示例：select * from partnership_shield where company_id=42195 ; 找到直接删除
# - 屏蔽规则： ['property1', 'reg_number', 'legal_person_name', 'reg_location',
# - 'estiblish_time', 'business_scope', 'reg_institute', 'reg_status', 'approved_time', 'reg_capital']
# - company表这些字段都为空值时


class PartnershipShieldDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_INNER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'internal.partnership_shield')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', None)
        super().__init__(**kwargs)


dao = PartnershipShieldDao()


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    NOT_COMPANY = auto()
    NEED_DEL = auto()



@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


def process(tc: TaskContext):
    cid = int(tc.inputs['company_id'])
    # logger.info(f'cid={cid}')
    c: Company = company_dao.get(cid)
    if not c:
        tc.code = ProcessCode.NOT_COMPANY
        return
    ok = False
    if len(c.credit_code or '') == 18:
        ok = True
    if len(c.reg_number or '') == 15:
        ok = True

    if len(c.legal_name or '') >= 2:
        ok = True
    if c.establish_date is not None:
        ok = True
    if c.last_crawled_time is not None:
        ok = True
    if ok:
        tc.code = ProcessCode.NEED_DEL
        ret = dao.delete(tc.inputs['id'])
        logger.info(f'del inputs={tc.inputs} ret={ret}')


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code != ProcessCode.INITIAL:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool:
        while True:
            for did, d in enumerate(dao.scan(start=0, scan_key='id')):
                d['did'] = did
                tc = TaskContext(inputs=d)
                future: Future = worker_pool.submit(process, tc)
                fs[future] = tc
                future.add_done_callback(callback_fn)
            logger.info('sleep for next round.')
            time.sleep(3600)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='sanwu_company_update.py')
    ap.add_argument('--worker-num', type=int, default=1)

    main(ap.parse_args())
