SELECT SUM(CASE
    WHEN (`id` IS NULL
        OR `id` = ""
        OR `id` = "null") THEN
    1
    ELSE 0 END) AS `id_null_count`, SUM(CASE
    WHEN (`company_id` IS NULL
        OR `company_id` = ""
        OR `company_id` = "null") THEN
    1
    ELSE 0 END) AS `company_id_null_count`, SUM(CASE
    WHEN (`report_year` IS NULL
        OR `report_year` = ""
        OR `report_year` = "null") THEN
    1
    ELSE 0 END) AS `report_year_null_count`, SUM(CASE
    WHEN (`company_name` IS NULL
        OR `company_name` = ""
        OR `company_name` = "null") THEN
    1
    ELSE 0 END) AS `company_name_null_count`, SUM(CASE
    WHEN (`credit_code` IS NULL
        OR `credit_code` = ""
        OR `credit_code` = "null") THEN
    1
    ELSE 0 END) AS `credit_code_null_count`, SUM(CASE
    WHEN (`reg_number` IS NULL
        OR `reg_number` = ""
        OR `reg_number` = "null") THEN
    1
    ELSE 0 END) AS `reg_number_null_count`, SUM(CASE
    WHEN (`phone_number` IS NULL
        OR `phone_number` = ""
        OR `phone_number` = "null") THEN
    1
    ELSE 0 END) AS `phone_number_null_count`, SUM(CASE
    WHEN (`postcode` IS NULL
        OR `postcode` = ""
        OR `postcode` = "null") THEN
    1
    ELSE 0 END) AS `postcode_null_count`, SUM(CASE
    WHEN (`postal_address` IS NULL
        OR `postal_address` = ""
        OR `postal_address` = "null") THEN
    1
    ELSE 0 END) AS `postal_address_null_count`, SUM(CASE
    WHEN (`email` IS NULL
        OR `email` = ""
        OR `email` = "null") THEN
    1
    ELSE 0 END) AS `email_null_count`, SUM(CASE
    WHEN (`manage_state` IS NULL
        OR `manage_state` = ""
        OR `manage_state` = "null") THEN
    1
    ELSE 0 END) AS `manage_state_null_count`, SUM(CASE
    WHEN (`employee_num` IS NULL
        OR `employee_num` = ""
        OR `employee_num` = "null") THEN
    1
    ELSE 0 END) AS `employee_num_null_count`, SUM(CASE
    WHEN (`total_assets` IS NULL
        OR `total_assets` = ""
        OR `total_assets` = "null") THEN
    1
    ELSE 0 END) AS `total_assets_null_count`, SUM(CASE
    WHEN (`total_equity` IS NULL
        OR `total_equity` = ""
        OR `total_equity` = "null") THEN
    1
    ELSE 0 END) AS `total_equity_null_count`, SUM(CASE
    WHEN (`total_sales` IS NULL
        OR `total_sales` = ""
        OR `total_sales` = "null") THEN
    1
    ELSE 0 END) AS `total_sales_null_count`, SUM(CASE
    WHEN (`total_profit` IS NULL
        OR `total_profit` = ""
        OR `total_profit` = "null") THEN
    1
    ELSE 0 END) AS `total_profit_null_count`, SUM(CASE
    WHEN (`prime_bus_profit` IS NULL
        OR `prime_bus_profit` = ""
        OR `prime_bus_profit` = "null") THEN
    1
    ELSE 0 END) AS `prime_bus_profit_null_count`, SUM(CASE
    WHEN (`retained_profit` IS NULL
        OR `retained_profit` = ""
        OR `retained_profit` = "null") THEN
    1
    ELSE 0 END) AS `retained_profit_null_count`, SUM(CASE
    WHEN (`total_tax` IS NULL
        OR `total_tax` = ""
        OR `total_tax` = "null") THEN
    1
    ELSE 0 END) AS `total_tax_null_count`, SUM(CASE
    WHEN (`total_liability` IS NULL
        OR `total_liability` = ""
        OR `total_liability` = "null") THEN
    1
    ELSE 0 END) AS `total_liability_null_count`
FROM ods.ods_prism1_annual_report_df
WHERE pt=20250327
        AND (`dw_is_del` = 0
        OR `dw_is_del` IS NULL)