# encoding=utf8

import csv
from libs.log2 import setup_logger
from dao.octopus.item import ItemDao

logger = setup_logger()
item_dao = ItemDao(name='company', is_clue=False)


def process(cid: int):
    ret = item_dao.set_info(word=str(cid), do_update=False, report_2022_miss_social_ts=0)
    logger.info(f'set info cid={cid} ret={ret}')


def main():
    with open('2022年报缺少社保信息.csv', 'r') as f:
        reader = csv.DictReader(f)
        for lid, d in enumerate(reader):
            cid = int(d['company.id'])
            logger.info(f'cid={cid}')
            process(cid)


if __name__ == '__main__':
    main()
    # process(25618697)
