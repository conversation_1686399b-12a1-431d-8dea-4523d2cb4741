from csv import DictWriter
from dao.deps.mysql_dao import MySQLDao
from dao.company import CompanyDao, Company, CompanyGraphDao, CompanyGraph
from libs.env import ConstantProps

# logger = setup_logger()
from resx.log import setup_logger


class ListingDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_ZX_RDS110.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'data_listed_company.company_bond_plates')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', None)
        super().__init__(**kwargs)


def main():
    list_dao = ListingDao(batch_size=1000)
    company_dao = CompanyDao()
    company_graph_dao = CompanyGraphDao()
    writer = DictWriter(open('output.csv', 'w'), fieldnames=['did', 'cid', 'gid', 'name', 'count'])
    writer.writeheader()
    for did, d in enumerate(list_dao.get_many(value='港股', field='type')):
        cid = d['company_id']
        c: Company = company_dao.get(value=cid, field='id')
        # logger.info(f'{c}')
        if not c:
            logger.warning(f'not c {cid}')
            continue
        cg: CompanyGraph = company_graph_dao.get(value=c.cid, field='company_id')
        if not cg:
            logger.warning(f'no cg for c {c}')
            continue
        c_ref: Company = company_dao.get(value=str(cid), field='property2')
        if c_ref is None:
            pass
        elif c_ref.source_flag.startswith('http'):
            pass
        elif c_ref.source_flag.startswith('hk'):
            c = c_ref

        c_names = list(company_dao.get_many(value=c.name, field='name'))
        if c.source_flag.startswith('http') and c.credit_code is None:
            logger.info(f'{did} {cid} {cg.cgid}  {c.name} {len(c_names)}')
            writer.writerow({
                'did': did,
                'cid': cid,
                'gid': cg.cgid,
                'name': c.name,
                'count': len(c_names),
            })


if __name__ == '__main__':
    main()
