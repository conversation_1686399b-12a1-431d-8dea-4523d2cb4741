--
--select t2.gid, t2024.establish_date,
--(select distinct gid from
--(
--select gid from test.wbx_0314_y2024
--union select gid from test.wbx_0314_y2023
--union select gid from test.wbx_0314_y2022
--)t1
--)t2
--left join test.wbx_0314_y2024 t2024 on t2.gid = t2024.gid
--left join test.wbx_0314_y2023 t2023 on t2.gid = t2023.gid
--left join test.wbx_0314_y2022 t2022 on t2.gid = t2022.gid;

--2024年全年，发生了注册地址变更的制造业企业，从东部向中部、从东部向西部、从中部向西部分别迁移多少家

select t2023.province2, t2024.province2, count(1)
from test.wbx_0314_y2024 t2024 join test.wbx_0314_y2023 t2023  on t2024.gid = t2023.gid
where t2024.province2!='未知' and t2023.province2!='未知' group by t2023.province2, t2024.province2


