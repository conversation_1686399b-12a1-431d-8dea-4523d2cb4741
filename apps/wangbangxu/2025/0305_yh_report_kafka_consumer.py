# encoding=utf8

import argparse
import json
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.concurrent import BoundedExecutor
from libs.env import get_stack_info
from libs.env import ConstantProps, get_env_prop
from clients.kafka_client import KafkaConsumerClient
from gslib.id_center import id_center_query, EntityType
from gslib.schedule import immediately_gs
from dao.octopus.item import ItemDao
from dao.company import CompanyDao, Company
from dao.reports.annual_report import AnnualReportDao, AnnualReport

octopus_item_dao = ItemDao(name='company', is_clue=False)
company_dao = CompanyDao(batch_size=1000)
annual_report_dao = AnnualReportDao()

fs = dict()
ret_code_counter = Counter()


def callback_fn(f: Future):
    c = fs[f]
    del fs[f]
    try:
        ret = f.result()
        ret_code_counter[ret.get('code', '其他')] += 1
    except Exception as e:
        logger.info(f'error process {c}  e={e} {get_stack_info()}')
        ret_code_counter['异常'] += 1
    if sum(ret_code_counter.values()) % 1000 == 0:
        logger.info(f'ret_code_counter {ret_code_counter}')
        ret_code_counter.clear()


def process(part_id, kafka_inst, topic):
    client = KafkaConsumerClient(**get_env_prop(kafka_inst), kafka_topic=topic, group_id='group_id_wbx')
    f = open(f'0305_yh.{part_id}.txt', 'w')
    for data in client.read():
        f.write(f'{data}\n')


def main(args):
    logger.info(f'{args}')
    with BoundedExecutor(max_workers=8) as process_pool:
        for _ in range(8):
            process_pool.submit(process, _, args.kafka_inst, args.topic)


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='')
    ap.add_argument('--kafka-inst', type=str, default='kafka.tyc.channel_out1', help='kafka集群')
    ap.add_argument('--topic', type=str, default='yh_company_annual_report', help='kafka topic')

    logger = setup_logger()
    main(ap.parse_args())
    # test()
