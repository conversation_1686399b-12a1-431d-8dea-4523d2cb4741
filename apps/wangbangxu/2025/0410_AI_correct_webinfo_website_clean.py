import csv
import os
import re
from typing import List
os.environ['POD_ENV'] = 'online'
from resx.log import setup_logger
from resx.multi_task import MultiTask, TaskContext
from biz_utils.entity.report.annual_report import AnnualReport, AnnualReportDao
from biz_utils.entity.report.report_guarantee import ReportGuarantee, ReportGuaranteeDao
from biz_utils.entity.report.report_equity_change import ReportEquityChange, ReportEquityChangeDao
from biz_utils.entity.report.report_webinfo import ReportWebInfo, ReportWebInfoDao
from biz_utils.id_center import id_center_query, EntityType
from biz_utils.credit_code import credit_code_valid
from biz_utils.reg_number import reg_number_valid

logger = setup_logger()
report_dao = AnnualReportDao()
report_guarantee_dao = ReportGuaranteeDao()
report_webinfo_dao = ReportWebInfoDao()


def is_valid_url(url):
    pattern = re.compile(
        r'^(https?://)?'               # http:// 或 https://（可选）
        r'([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}'  # 域名，如 www.example.com
        r'(:\d+)?'                     # 端口（可选）
        r'(/[\w\-./?%&=]*)?$',         # 路径和参数（可选）
        re.IGNORECASE
    )
    return pattern.match(url) is not None


class ThisMultiTask(MultiTask):
    CANNOT_CORRECT = 'CANNOT_CORRECT'

    def __init__(self):
        super().__init__(worker_num=8, task_key='report_webinfo.id')

    def get_task(self):
        with open('0410_AI_correct_webinfo_website_clean.in.csv', 'r') as f:
            reader = csv.DictReader(f)
            for did, d in enumerate(reader):
                # if did < 0:
                #     continue
                # if did > 0+20:
                #     break
                yield d

    def process(self, tc: TaskContext):
        d = tc.item
        iid = d['report_webinfo.id']
        item: ReportWebInfo = report_webinfo_dao.get_by_id(iid)
        ok1 = is_valid_url(item.website)
        website = item.website.replace('：', ':').replace('．', '.').replace('／', '/')
        ok2 = is_valid_url(website)
        logger.info(f'{item.website} {ok1} {ok2}')
        if (not ok1) and ok2:
            item.website = website
            report_webinfo_dao.save_by_id(item)
            tc.code = self.CODE_SUCCESS
            return
        tc.code = self.CANNOT_CORRECT


ThisMultiTask().run()
# ThisMultiTask().process(TaskContext(iid=0, item={'report.report_id': 3057857581, }))
