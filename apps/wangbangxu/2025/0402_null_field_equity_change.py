import csv
import os
from typing import List
os.environ['POD_ENV'] = 'online'
from resx.log import setup_logger
logger = setup_logger()

from resx.multi_task import MultiTask, TaskContext
from biz_utils.entity.report.report_equity_change import ReportEquityChange, ReportEquityChangeDao
from biz_utils.id_center import id_center_query
dao = ReportEquityChangeDao()


class ThisMultiTask(MultiTask):
    def __init__(self):
        super().__init__(worker_num=4, task_key='report_equity_change.report_id')

    def get_task(self):
        with open('0402_null_field_equity_change.2.csv', 'r') as f:
            reader = csv.DictReader(f)
            for did, d in enumerate(reader):
                # if did > 100:
                #     break
                yield d

    def process(self, tc: TaskContext):
        d = tc.item
        report_id = d['report_equity_change.report_id']
        items: List[ReportEquityChange] = list(dao.get_many(annualreport_id=report_id))

        changed = False
        for item in items:
            if item.investor_type and item.investor_id:
                continue
            item.investor_type, item.investor_id = id_center_query(name=item.investor_name)
            changed = True
        if not changed:
            tc.code = self.CODE_DUP
            return

        ret = dao.save_group(items)
        logger.info(f'Processing report_id={report_id} ret={ret}')
        # 删除额外的空行
        for null_item in dao.get_many(annualreport_id=report_id, investor_id=None):
            dao.delete_by_id(null_item.id)
        tc.code = self.CODE_SUCCESS


ThisMultiTask().run()
# ThisMultiTask().process(TaskContext(iid=0, item={'report_equity_change.report_id': 2593950252, }))
