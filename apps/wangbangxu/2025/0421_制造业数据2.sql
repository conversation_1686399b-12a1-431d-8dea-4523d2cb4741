--以制造业和计算机、通信和其他电子设备制造业企业（去除个体工商户）为基本范围，分别计算：
--1、2015、2018、2024年，全国各省存续企业数量；
--2、2015、2018、2024年，全国各省新增企业数量；
--3、2015、2018、2024年，发生了跨省注册地址变更的企业，各省之前迁移企业数量；
--4、2015、2018、2024年，各省新注册成立的企业，统计当时的大股东所属来源省份数量；

with  area_code_mapping_0 as (select * from ods.ods_prism_area_code_district_df where pt = '20250101' and dw_is_del = 0 and version='V2020' and deleted=0),
area_code_mapping as (
    select distinct province_code as area_code, province_abbr as province, null as city, null as district from area_code_mapping_0 where length(province_code)=8
    union select distinct city_code as area_code, province_abbr as province, city, null as district from area_code_mapping_0 where length(city_code)=8
    union select distinct district_code as area_code, province_abbr as province, city, district from area_code_mapping_0 where length(district_code)=8 and district!=''
),
sub_company as (
    select id as sub_id, unified_social_credit_code as sub_code, deleted as sub_deleted, 'npo' as sub_type, company_id as cid from ods.ods_prism1_npo_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, unified_social_credit_code as sub_code, deleted as sub_deleted, 'neworg' as sub_type, company_id as cid from ods.ods_prism_organization_info_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, creditcode as sub_code, is_lawfirm as sub_deleted, 'lawfirm' as sub_type, company_id as cid from ods.ods_data_judicial_risk_law_firm_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, unified_social_credit_code as sub_code, 0 as sub_deleted, 'institution' as sub_type, company_id as cid from ods.ods_company_base_institution_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, company_num as sub_code, deleted as sub_deleted, 'hk' as sub_type, company_id as cid from ods.ods_prism1_company_hk_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, code as sub_code, deleted as sub_deleted,'tw' as sub_type, company_id as cid from ods.ods_prism1_company_tw_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, creditCode as sub_code, deleted as sub_deleted, 'foundation' as sub_type, company_id as cid from ods.ods_prism1_foundation_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
),
company_branch as (select c1.gid as gid, c2.gid as parent_gid, c1.cancel_date from (select * from company where parent_id > 0 and sub_type = 'gsxt_gongsi') c1 inner join (select * from company where parent_id = 0 and sub_type = 'gsxt_gongsi') c2 on c1.parent_id = c2.cid),
report_brief as (select company_id as cid, * from ods.ods_prism_annual_report_brief_df where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0),
report as (select id as report_id, company_id as cid, report_year, manage_state, phone_number, postcode, postal_address, email, credit_code, company_name, createtime as create_time from ods.ods_prism1_annual_report_df where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0),
report_investor as (select annual_report_id as report_id, investor_id, investor_type, investor_name, CAST(regexp_extract(subscribe_amount, '([0-9]*\\.?[0-9]+)', 0) AS DOUBLE) AS sub_amount from ods.ods_prism1_report_shareholder_df where pt = ${pt} and dw_is_del = 0),
report_social as (select annaulreport_id as report_id, * from ods.ods_prism1_report_social_security_info_df where pt = ${pt} and dw_is_del = 0),
report_equity_change as (select id, annualreport_id as report_id, investor_name,ratio_before,ratio_after,date(change_time) as change_date,investor_id,investor_type from ods.ods_prism1_report_equity_change_info_df where pt = ${pt} and dw_is_del = 0),
report_guarantee as (select id, annualreport_id as report_id, creditor,obligor,credito_type,credito_amount,credito_term,guarantee_term,guarantee_way,guarantee_scope from ods.ods_prism1_report_out_guarantee_info_df where pt = ${pt} and dw_is_del = 0),
report_webinfo as (select annualreport_id as report_id, * from ods.ods_prism1_report_webinfo_df where pt = ${pt} and dw_is_del = 0),
partnership_info as (select company_gid as gid, executive as executive_name, executive_gid, executive_type from ods.ods_prism_company_partnership_info_df where pt = ${pt} and dw_is_del = 0 and deleted = 0),
company_staff as (select company_id as cid, staff_id as staff_cid, staff_type_name as position from ods.ods_prism1_company_staff_df where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0),
company_staff_sort as (select company_gid as gid, name as staff_name, if(type=1, 2, 1) as staff_type, position as positions, human_gid as staff_gid, create_time from ods.ods_prism_company_staff_sort_df where pt = ${pt} and dw_is_del = 0 and company_gid % ${gid_mod} = 0),
equity_ratio as (select company_graph_id as gid, shareholder_graph_id as sh_gid, shareholder_name as sh_name, shareholder_type as sh_type, `percent` as ratio, source from ods.ods_prism1_equity_ratio_df where pt = ${pt} and dw_is_del = 0 and company_graph_id % ${gid_mod} = 0 and deleted = 0),
change_info as (select company_id as cid, renamed_change_item as change_item, content_before as cont_before, content_after as cont_after, to_date(change_time) as change_date from ods.ods_company_change_company_change_info_clean_df where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0 and deleted = 0),
history_name_info as (select company_gid as gid, company_name as his_name, start_time, end_time from ods.ods_prism_company_history_name_df where pt = ${pt} and dw_is_del = 0 and company_gid % ${gid_mod} = 0),
brief_cancel_info as (select company_id as cid, investor_commitment_oss_path as oss_path, deleted from ods.ods_prism1_company_brief_cancel_announcement_info_df where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0),
abnormal_info as (select company_id as cid, to_date(put_date) as put_date, put_reason, to_date(remove_date) as remove_date, deleted from ods_prism1_company_abnormal_info_df where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0),
patent_info as (select view_tmp.gid as gid, patent_type, count(1) as patent_count from ods.ods_intellectual_property_info_company_patent_basic_info_index_df lateral view explode(split(company_ids, ';')) view_tmp as gid where pt = ${pt} and dw_is_del = 0 and nvl(company_ids, '') != '' group by view_tmp.gid, patent_type),
company as (
    select c.id as cid, base, name, to_date(estiblish_time) as esta_date, to_date(approved_time) as appr_date, cast(c.property1 as string) credit_code, c.reg_status,
        legal_person_name as legal_name, cast(c.reg_number as string) reg_number, c.crawledtime, nvl(parent_id, 0) as parent_id, c.reg_institute, c.company_org_type, c.from_time, c.to_time, c.property2 as cid_ref,
        if(c.reg_status regexp '销', nvl(other.remove_date, to_date(c.crawledtime)), '2099-12-31') as end_date,
        cg.gid,
        area_code_mapping.province,
        base.company_type as base_company_type,
        cate.cate_1 as cate_1,
        cate.cate_2 as cate_2,
        (case
            when (property2 is not null and property2 != '') then 'prop2'
            when source_flag regexp 'qyxy' then
                (case
                    when nvl(c.property1, '') = '' and nvl(c.reg_number, '') = '' then 'kongke'
                    when c.company_org_type regexp '个体' then 'gsxt_geti'
                    when nvl(c.company_org_type, '') == '' and name not regexp '公司|合伙' then 'gsxt_geti'
                    when property1 regexp '^93' then 'gsxt_nonghe'
                    else 'gsxt_gongsi'
                end)
            when source_flag regexp '^npo_foundation' then 'foundation'
            when source_flag regexp '^npo' then 'npo'
            when source_flag regexp '^hk' then 'hk'
            when source_flag regexp '^tw' then 'tw'
            when source_flag regexp '^institution' then 'institution'
            when source_flag regexp '^lawfirm' then 'lawfirm'
            when source_flag regexp '^org' then 'neworg'
            else 'others' end
        )sub_type
    from (select id as cid, * from ods.ods_prism1_company_df where pt = ${pt}  and dw_is_del = 0 and id % ${cid_mod} = 0) c
    inner join (select graph_id as gid, company_id as cid, pt from ods.ods_prism1_company_graph_df where pt = ${pt} and dw_is_del = 0 and deleted = 0 and graph_id % ${gid_mod} = 0 and company_id % ${cid_mod} = 0) cg on cg.cid = c.cid
    inner join (select * from ods.ods_company_base_company_index_df where pt = ${pt} and dw_is_del = 0 and company_id % ${gid_mod} = 0 and id % ${cid_mod} = 0)base on base.id = c.cid
    -- inner join (select * from ods.ods_search_card_enterprise_search_basic_info_df where pt = ${pt} and is_deleted = 0 and company_id % ${gid_mod} = 0)search on cg.gid = search.company_id
    -- left join ( select biz_type_id, company_gid from ods.ods_prism_company_biz_type_df where pt = ${pt}  and dw_is_del = 0 and deleted = 0 and company_gid % ${gid_mod} = 0) biz on cg.gid = biz.company_gid
    -- left join (select id as cid, reg_capital_amount*1e-6 as reg_capital_amount, reg_capital_currency, actual_capital_amount*1e-6 as actual_capital_amount, company_org_type, reg_status, social_security_staff_num, alias from ods.ods_prism1_company_clean_info_df where pt = ${pt} and dw_is_del = 0 and id % ${cid_mod} = 0) clean on c.id = clean.cid
    left join (select id as cid, to_date(nvl(cancel_date, revoke_date)) as remove_date from ods.ods_prism1_company_other_info_df where pt = ${pt} and dw_is_del = 0 and id % ${cid_mod} = 0 and nvl(cancel_date, revoke_date) is not null) other on c.id = other.cid
    inner join area_code_mapping on base.business_areacode_00 = area_code_mapping.area_code
    left join (select cate_1, cate_2, cate_3, category_code_3 from ods.ods_prism_company_category_all_code_v2017_df where pt = ${pt} and dw_is_del = 0)cate on base.industry_national_std_lv3_code = cate.category_code_3
),

--4、2015、2018、2024年，各省新注册成立的企业，统计当时的大股东所属来源省份数量；
-- 以年报计算
--company1 as (select cid, province,
--    if(cate_2 = '计算机、通信和其他电子设备制造业', 1, 0) as is_computer_zzy,
--    year(esta_date) as esta_year
--from company where sub_type = 'gsxt_gongsi'
--    and base_company_type in (1, 10, 11)
--    and cate_1 = '制造业'
--    and province not in ('澳门', '香港', '台湾')
--    and year(esta_date) in (2015, 2018, 2024)
--)
--
--select company_report_first_investor.province, investor_company.province,
--    company_report_first_investor.esta_year,
--    company_report_first_investor.is_computer_zzy,
--    count(1)
--from (
--    select * from (
--        SELECT company_report_investors.*, ROW_NUMBER() OVER (PARTITION BY cid ORDER BY sub_amount DESC) AS rank FROM
--        (
--            select company1.*, report_investor.sub_amount, report_investor.investor_type, report_investor.investor_id
--            from company1
--                join report on company1.cid = report.cid and report.report_year = company1.esta_year
--                join report_investor on report.report_id = report_investor.report_id
--        ) company_report_investors
--    )  company_report_investors_rank
--    where rank = 1 and investor_type = 2
--) company_report_first_investor
--join company investor_company on company_report_first_investor.investor_id = investor_company.cid
--group by company_report_first_investor.province, investor_company.province, company_report_first_investor.esta_year, company_report_first_investor.is_computer_zzy

--3、2015、2018、2024年，发生了跨省注册地址变更的企业，各省之前迁移企业数量；
--  由于历史年份，无法使用 历史登记机关等数据，调整使用变更记录提取, 分析变更记录变更项为：登记机关变更、管辖单位变更;
-- update: 通过变更记录导出来的数据和实际差别较大， 我看了下数据情况，像江西、陕西省会把登记机关变更写到变更记录， 其他省反而不这么记录， 所以提取出来的数据失真比较严重。另外一点重要的，跨省迁移一般没写到变更记录中……。
-- update：调整为获取2020-2024数据
company_reg_inst_timeline_a as ( -- 登记机关历史时间线  中间表
    select company_id as cid, reg_institute, to_date(create_time) as ts, version from ods.ods_history_history_company_reg_institute_df
        where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0
),
company_reg_inst_timeline as ( -- 登记机关历史时间线  只有当前数据，即1个version的数据不产出
    select company.cid as cid, company.reg_institute, to_date(crawledtime) as ts, (max_version+1) as version from
    (
        select cid, max(version) as max_version from company_reg_inst_timeline_a  group by cid
    ) company_reg_inst_max_version inner join company on company.cid = company_reg_inst_max_version.cid
    union select * from company_reg_inst_timeline_a
),
area_code_count_over_reg_inst as ( -- 登记机关到area_code的映射 中间表
  select reg_institute, substr(credit_code, 3, 6) as area_code, COUNT(1) AS num from company
    where sub_type = 'gsxt_gongsi' and reg_status regexp '存续|在业|在营|开业' and length(credit_code) = 18
    group by reg_institute, substr(credit_code, 3, 6) having COUNT(1) > 100 -- 分组小于100的脏数据
),
reg_inst_2_area_code as ( -- 登记机关到area_code的映射
  select reg_institute, area_code from (
      SELECT reg_institute, area_code, num, ROW_NUMBER() OVER (PARTITION BY reg_institute ORDER BY num DESC) AS rank FROM area_code_count_over_reg_inst
  ) area_code_count_over_reg_inst_rank
  where rank = 1 and length(reg_institute) > 1
),
reg_inst_2_province as ( -- 登记机关到省份的映射
  select reg_institute, province from reg_inst_2_area_code join area_code_mapping
    on substr(reg_inst_2_area_code.area_code, 1, 2)=substr(area_code_mapping.area_code, 3, 2) and city is null and district is null
)
select province1, province2, change_year, is_computer_zzy, count(1) as c from
(
    select company.cid, province_from.province as province1, province_to.province as province2,
    if(cate_2 = '计算机、通信和其他电子设备制造业', 1, 0) as is_computer_zzy,
    year(timeline_from.ts) as change_year
from company_reg_inst_timeline timeline_from
    join company_reg_inst_timeline timeline_to on timeline_from.cid = timeline_to.cid and timeline_from.version + 1 = timeline_to.version
    join reg_inst_2_province province_from on timeline_from.reg_institute = province_from.reg_institute
    join reg_inst_2_province province_to on timeline_to.reg_institute = province_to.reg_institute
    join company on company.cid = timeline_to.cid
where company.sub_type = 'gsxt_gongsi'
    and company.base_company_type in (1, 10, 11)
    and company.cate_1 = '制造业'
    and province_from.province not in ('澳门', '香港', '台湾')
    and province_to.province not in ('澳门', '香港', '台湾')
    and province_from.province != province_to.province
    and year(timeline_from.ts) in (2020, 2021, 2022, 2023, 2024)
)t group by province1, province2, change_year, is_computer_zzy

-- 通过年报取数的代码留存
--select province1, province2, is_computer_zzy, count(change_2015)c2015, count(change_2018)c2018, count(change_2024)c2024 from
--(
--    select change_info_2.cid, province_from.province as province1, province_to.province as province2,
--    if(cate_2 = '计算机、通信和其他电子设备制造业', 1, 0) as is_computer_zzy,
--    if(year(change_date) = 2015, 1, null) as change_2015,
--    if(year(change_date) = 2018, 1, null) as change_2018,
--    if(year(change_date) = 2024, 1, null) as change_2024
--from
--    (
--        select cid, to_date(change_date)change_date, cont_before, cont_after from change_info where change_item in ('登记机关变更', '管辖单位变更')
--    )change_info_2
--    join reg_inst_2_province province_from on change_info_2.cont_before = province_from.reg_institute
--    join reg_inst_2_province province_to on change_info_2.cont_after = province_to.reg_institute
--    join company on company.cid = change_info_2.cid
--where company.sub_type = 'gsxt_gongsi'
--    and company.base_company_type in (1, 10, 11)
--    and company.cate_1 = '制造业'
--    and company.province not in ('澳门', '香港', '台湾')
--)t group by province1, province2, is_computer_zzy

-- 2、2015、2018、2024年，全国各省新增企业数量；
--select province, is_computer_zzy, count(esta_2015)c2015, count(esta_2018)c2018, count(esta_2024)c2024 from
--(select province, if(cate_2 = '计算机、通信和其他电子设备制造业', 1, 0) as is_computer_zzy,
--    if(year(esta_date)=2015, 1, null) as esta_2015,
--    if(year(esta_date)=2018, 1, null) as esta_2018,
--    if(year(esta_date)=2024, 1, null) as esta_2024,
--    cid
--from company where sub_type = 'gsxt_gongsi'
--    and base_company_type in (1, 10, 11)
--    and cate_1 = '制造业'
--    and province not in ('澳门', '香港', '台湾')
--)t group by province, is_computer_zzy

-- 1、2015、2018、2024年，全国各省存续企业数量；
--select province, is_computer_zzy, count(alive_2015)c2015, count(alive_2018)c2018, count(alive_2024)c2024 from
--(select province, if(cate_2 = '计算机、通信和其他电子设备制造业', 1, 0) as is_computer_zzy,
--    if(esta_date<'2016-01-01' and end_date > '2016-01-01', 1, null) as alive_2015,
--    if(esta_date<'2019-01-01' and end_date > '2019-01-01', 1, null) as alive_2018,
--    if(esta_date<'2025-01-01' and end_date > '2025-01-01', 1, null) as alive_2024,
--    cid
--from company where sub_type = 'gsxt_gongsi'
--    and base_company_type in (1, 10, 11)
--    and cate_1 = '制造业'
--    and province not in ('澳门', '香港', '台湾')
--)t group by province, is_computer_zzy