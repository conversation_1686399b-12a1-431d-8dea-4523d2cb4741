import csv
import os
from typing import List
os.environ['POD_ENV'] = 'online'
from resx.log import setup_logger
logger = setup_logger()

from resx.multi_task import MultiTask, TaskContext
from biz_utils.entity.report.report_investment import ReportInvestment, ReportInvestmentDao
from biz_utils.id_center import id_center_query, EntityType
from biz_utils.entity.company import Company, CompanyDao
from biz_utils.credit_code import credit_code_valid
from biz_utils.reg_number import reg_number_valid

dao = ReportInvestmentDao()
company_dao = CompanyDao()


class ThisMultiTask(MultiTask):
    CODE_ID_CENTER_MISS = 'CODE_ID_CENTER_MISS'

    def __init__(self):
        super().__init__(worker_num=8, task_key='company.cid')

    def get_task(self):
        with open('0402_null_field_org_type.csv', 'r') as f:
            reader = csv.DictReader(f)
            for did, d in enumerate(reader):
                # if did > 10:
                #     break
                yield d

    def process(self, tc: TaskContext):
        d = tc.item
        econ_kind1 = d['enterprise1.econ_kind']
        econ_kind2 = d['enterprise2.econ_kind']
        c: Company = company_dao.get_by_id(d['company.cid'])
        if not c or len(c.company_org_type or '') > 0:
            tc.code = self.CODE_DUP
            return
        econ_kind = None
        if econ_kind1 and econ_kind1 != 'NULL':
            econ_kind = econ_kind1
        if econ_kind2 and econ_kind2 != 'NULL':
            econ_kind = econ_kind2
        if not econ_kind:
            tc.code = self.CODE_EXCLUDE
            return
        logger.info(f'{c.cid} {c.name} {c.company_org_type} {econ_kind}')
        c.company_org_type = econ_kind
        company_dao.save_by_id(c)
        tc.code = self.CODE_SUCCESS


ThisMultiTask().run()
# hisMultiTask().process(TaskContext(iid=0, item={'report_id': 3046045631, }))
