from gslib.id_center import id_center_query, EntityType
from gslib.credit_code import credit_code_valid
from dao.company import company_dao, CompanyGraphDao
import csv
import requests

cgd = CompanyGraphDao()
writer = csv.DictWriter(f=open('0205_农业农村部数据导出需求.output.csv', 'w'), fieldnames=['name', 'credit_code', 'gid', 'rule'])

with open('0205_农业农村部数据导出需求.input.txt') as f:
    for sid, s in enumerate(f):
        if sid % 100 == 0:
            print(f'process {sid}')
        tokens = s.strip().split('\t')
        input_name, input_credit_code = None, None
        name = None
        credit_code = None
        gid = None
        rule = None
        if len(tokens) == 1:
            input_name = tokens[0]
        elif len(tokens) == 2:
            input_name, input_credit_code = tokens

        if credit_code_valid(input_credit_code):
            et, eid = id_center_query(credit_no=input_credit_code)
            if et == EntityType.ORG:
                c = company_dao.get(eid)
                cg = cgd.get_by_cid(eid)
                if cg:
                    name = c.name
                    credit_code = c.credit_code
                    gid = cg.cgid
                    rule = 'by_credit_code'
        if not name:
            et, eid = id_center_query(name=input_name)
            if et == EntityType.ORG:
                c = company_dao.get(eid)
                cg = cgd.get_by_cid(eid)
                if cg:
                    name = c.name
                    credit_code = c.credit_code
                    gid = cg.cgid
                    rule = 'by_name'

        if not name:
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,hi;q=0.7',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Content-Type': 'application/json',
                'Origin': 'https://www.tianyancha.com',
                'Pragma': 'no-cache',
                'Referer': 'https://www.tianyancha.com/',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-site',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
                'X-TYCID': '5e5acc109a9811ef9150a7fba5238e03',
                'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'version': 'TYC-Web',
            }
            params = {
                '_': '1738724505780',
            }

            json_data = {
                'keyword': input_name,
            }

            response = requests.post(
                'https://capi.tianyancha.com/cloud-tempest/search/suggest/company/main',
                params=params,
                headers=headers,
                json=json_data,
            )

            if response.status_code == 200:
                data = response.json()
                if data['state'] == 'ok' and len(data['data']['companySuggestList']) > 0:
                    gid = data['data']['companySuggestList'][0]['graphId']
                    name = data['data']['companySuggestList'][0]['comName']
                    credit_code = data['data']['companySuggestList'][0]['taxCode']
                    rule = 'by_search'
        writer.writerow({'name': name, 'credit_code': credit_code, 'gid': gid, 'rule': rule})