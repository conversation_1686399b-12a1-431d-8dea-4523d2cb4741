from datetime import date, datetime, timedelta
from resx.hive_client import HiveClient
from resx.log import setup_logger

logger = setup_logger()

tasks = [
    ('0407_发改委数据需求_8办理注销登记的企业数量（市）.sql', 'by_month', '20250401', True),  # or by_season
    ('0407_发改委数据需求_8办理注销登记的企业数量（区）.sql', 'by_month', '20250401', True),  # or by_season
    ('0407_发改委数据需求_9企业存活率（市）.sql', 'by_season', '20250401', False),  # or by_season
    ('0407_发改委数据需求_9企业存活率（区）.sql', 'by_season', '20250401', False),  # or by_season
    ('0407_发改委数据需求_10存量外资企业数量（市）.sql', 'by_month', '20250401', False),
    ('0407_发改委数据需求_10存量外资企业数量（区）.sql', 'by_month', '20250401', False),
    ('0407_发改委数据需求_12外资企业迁出地（市）.sql', 'by_month', '20250401', True),
    ('0407_发改委数据需求_12外资企业迁出地（区）.sql', 'by_month', '20250401', True),
    ('0407_发改委数据需求_13迁出京外资企业迁往区域情况.sql', 'by_month', '20250401', True),
    ('0407_发改委数据需求_15外资企业迁入地（区）.sql', 'by_month', '20250401', True),
    ('0407_发改委数据需求_15外资企业迁入地（市）.sql', 'by_month', '20250401', True),
    ('0407_发改委数据需求_16迁入京外资企业来源区域情况.sql', 'by_month', '20250101', True),
    ('0407_发改委数据需求_18办理注销外资企业数量（市）.sql', 'by_month', '20250101', True),
    ('0407_发改委数据需求_18办理注销外资企业数量（区）.sql', 'by_month', '20250101', True),
    ('0407_发改委数据需求_19外资企业存活率（市）.sql', 'by_season', '20250101', False),
    ('0407_发改委数据需求_19外资企业存活率（区）.sql', 'by_season', '20250101', False),
]

client = HiveClient()


def get_last_pt(s, mode):  # 20241001
    dt = datetime.strptime(s, '%Y%m%d')
    dt -= timedelta(days=1)
    if mode == 'by_month':
        return dt.strftime('%Y%m01')
    elif mode == 'by_season':
        dt = dt.replace(month=(dt.month + 2) // 3 * 3 - 2)  # 1 ~ 3 -> 1  4 ~ 6 -> 4  7 ~ 9 -> 7 10 ~ 12 -> 10
        return dt.strftime('%Y%m01')
    else:
        raise ValueError(f"Unknown by_mode: {mode}")


for task in tasks:
    sql_file, by_mode, pt_done, ab_mode = task
    pt_b = get_last_pt(datetime.now().strftime('%Y%m%d'), by_mode)
    while pt_b > pt_done:
        if ab_mode:
            pt_a = f'{int(pt_b[:4])}0101' if pt_b[4:] != '0101' else f'{int(pt_b[:4])-1}0101'
            logger.info(f'sql_file={sql_file} {pt_a} {pt_b}')
            pt_args = {'pt_a': pt_a, 'pt_b': pt_b}
        else:
            logger.info(f'sql_file={sql_file} {pt_b}')
            pt_args = {'pt': pt_b}
        for row in client.run(hql=open(sql_file, 'r').read(), **pt_args, cid_mod=1, gid_mod=1):
            logger.info(f'{sql_file} pt={pt_b} row={row}')
        pt_b = get_last_pt(pt_b, by_mode)


# if __name__ == '__main__':
#     ret = get_last_pt('20250202', 'by_season')
#     print(ret)
