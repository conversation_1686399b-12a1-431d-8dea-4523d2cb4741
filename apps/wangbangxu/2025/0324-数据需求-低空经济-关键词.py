import csv
from pyhive import hive
from resx.log import setup_logger

logger = setup_logger()


keywords_info = {
    '低空物理基础设施': {
        #'设计规划': '低空经济咨询规划、低空规划、低空施工、低空智能融合基础设施建、通用机场选址、通用机场可行性研究'.split('、'),
        #'起降平台': '起降平台、机场、跑道、停机坪、固定翼飞机起降机场、水上机场、虚拟机场、起降点、起降地、航空港、航空枢纽、无人机备降、无人机停放'.split('、'),
        #'飞行器充（换）电': '飞行器充电、飞行器换电、航空器充电、航空器换电'.split('、'),
        #'中转站': '飞行中转站、飞行中继'.split('、'),
        '货物/乘客平台': '无人机装载、无人机卸货、无人机货物投放、候机楼、候机大厅'.split('、'),
        '加氢站': '飞行器加氢站、航空器加氢站'.split('、'),
    },

    '低空信息基础设施': {
        '通信': '无人机数据链、C2链路、无人机卫星通信、空天地互连互通、空中基站、无人机通信保障、机载航空电子系统、无人机频谱管理'.split('、'),
        '导航': '高精度定位、导航芯片、高精度GNSS、GPS、定位系统、北斗导航、BDS、三维空间信息、三维可视化、三维激光扫描仪、地基增强系统GBAS、星基增强系统SBAS、UWB、室内定位技术、雷达、惯性导航、融合导航、激光雷达、定向天线、发射机、接收机、天线控制器、显示器和照相装置、电子计算机和图像传输、机载多模导航、导航算法、雷达信号处理机'.split('、'),
        '监视': 'ADS-B、广播式自动相关监视系统、一次雷达、二次雷达、一/二次雷达、无线信标、光电探测、通感遥一体、空天一体、低空监视装备、全域安全监测'.split('、'),
        '气象服务': '微波大气遥感设备、小尺度天气系统、气象雷达、风温廓线雷达、四维高分辨率数值气象预报系统'.split('、'),
        '空中交通管理': '空中交通服务、空中交通流量管理、空域管理、空中交通管制、飞行情报服务、告警服务、低空交通管理系统、弹性航线调度、空地一体全域空间覆盖、多对象群组化追踪控制、低空空域只能管控系统、飞行控制系统、低空接入控制、航线规划机调度系统、低空通信系统、巡航信息共享、禁飞区、限飞区、适飞区、航线、航路、飞行航线、飞行航路'.split('、'),
    },
    '低空飞行器基础材料': {
        '轻量化材料': '飞行器机构材料、化学纤维、碳纤维、聚丙烯腈、预浸料、复材质部件、碳纤维复合材料、PEEK（工程塑料）、PTFE（聚四氟乙烯）、陶瓷材料、陶瓷基材、氧化铝陶瓷、氮化硅陶瓷、碳化硅陶瓷、铁合金、航空铝材、玻璃纤维、玻纤、合金材料、工程塑料、特种橡胶'.split('、'),
        '环境适应材料': '耐高温材料、耐低温材料、防腐蚀材料、聚酰亚胺、PI、耐磨材料、耐振动材料、耐腐蚀材料、防静电材料'.split('、'),
    },
    '低空飞行器关键部件及系统': {
        '轮胎': '航空轮胎、无人机轮胎、飞行汽车轮胎'.split('、'),
        '机身/机翼/螺旋桨/结构件': '机身、机翼、旋翼、螺旋桨、固定翼、无人机结构件'.split('、'),
        '芯片/板卡/传感器': '飞行器数据处理、飞行数据存储、飞行影像处理、飞行器电路板、定位芯片、距离传感器、视觉传感器、激光雷达、毫米波雷达、气象雷达、无人机雷达、人眼安全铒玻璃激光器、人眼安全激光测距机、多光谱光电综合系统、红外热成像仪、夜视追踪、热源检测、滤波电感、陀螺仪'.split('、'),
        '载荷': '传感器、载荷秤、投放器、相机载荷、红外传感器载荷、激光雷达载荷、气象传感器载荷、可见光载荷、红外热像仪、无人机降落伞、无人机探照灯'.split('、'),
        '动力系统': '航空电池、航空发动机、无人机电池、无人机螺旋桨、无人机发动机'.split('、'),
        '航电系统': '无人机导航、直升机导航、无人机通信、直升机通信、无人机遥感、飞行空域、电子围栏、巡航定位装置、ADS-B/广播式自动相关监视系统、无人机环境感知技术、无人机轨迹感知、无人机航线修正、卫星通信服务、地理遥感信息服务、卫星遥感应用系统集成、卫星导航服务'.split('、'),
        '飞控系统': '飞控系统、飞行控制系统、无人机自动驾驶、无人机飞行指引、电传飞行控制、侧向航迹、无人机自动着陆、无人机地形跟随、无人机颤振抑制、无人机机动载荷、无人机自动返航、控制飞行姿态、无人机避障'.split('、'),
    },
    '低空飞行综合应用': {
        '农业': '植保无人机、农业无人机、无人机环境监测、气象探测无人机、侦查无人机、无人机航测、农药喷洒无人机、投饵无人机、无人机人工降雨'.split('、'),
        '旅游观光': '低空旅游、空中游览、直升机飞行体验、低空娱乐、航空运动、无人机表演、无人机灯光秀、无人机VR、无人机航拍、观光eVTOL、空中广告'.split('、'),
        '物流': '无人机外卖、无人机仓库管理、货运无人机、空中快递、无人机快递、无人机物流'.split('、'),
        '公共服务': '无人机巡检、货运无人机、照明无人机、消防无人机、无人机探照、无人机广播、无人机护林、无人机电力巡检、无人机管线巡检、城市空中交通/UAM、无人机预警系统'.split('、'),
        '警务消防': '消防无人机、无人机巡检、空中执法、无人机执法、警用无人机'.split('、'),
        '军事': '侦查无人机、货运无人机、照明无人机'.split('、'),
        '科研': '无人机航测、无人机环境监测、气象型无人机'.split('、'),
    },
    '低空飞行保障服务': {
        '维修测试': '无人机维修、无人机改装、无人机升级、低空产品综合检测、无人机防御系统、无人机检测、无人机信息安全、无人机环境试验'.split('、'),
        '认证': '无人机检测认证服务、无人机认证'.split('、'),
        '培训': '飞行培训、驾驶员培训、无人机技术推广、执照培训、无人机科技体验、无人机执照、民用无人驾驶航空器操控员执照、操控员执照、CAAC、中国民航飞行员执照、AOPA、国际航空器拥有者协会无人机驾驶证、UTC、无人驾驶航空器系统操作手合格证、ASFC、遥控航空模型飞行员执照、“1+X”证书、UOM、轻型民用无人驾驶航空器安全操控理论培训合格证'.split('、'),
        '租赁': '无人机租赁、直升机租赁'.split('、'),
    },
}


def hive_run(filename: str, re_str: str, batch_size=10000):
    hive_conn = hive.Connection(host="************", port=10000, username="wangbangxu",)  # password='Tyc0011__1'
    cursor = hive_conn.cursor()
    hql = open('0324-数据需求-低空经济-1命中集合.sql', 'r').read()
    hql = hql.replace('${hivevar:pt}', '20250324')
    hql = hql.replace('${hivevar:cid_mod}', '1')
    hql = hql.replace('${hivevar:gid_mod}', '1')
    hql = hql.replace('${hivevar:keywords}', f'"{re_str}"')

    logger.info(f'begin execute hql={hql}')
    cursor.execute(hql)
    logger.info(f'finish execute hql={hql}')
    writer = csv.writer(open(filename, 'w'))
    # cursor.description
    # [('base', 'STRING_TYPE', None, None, None, None, True), ('id', 'BIGINT_TYPE', None, None, None, None, True)]
    writer.writerow(x[0] for x in cursor.description)

    for i in range(10000):
        rows = cursor.fetchmany(size=batch_size)
        logger.info(f'filename={filename} fetch {i} {len(rows)} rows')
        for row in rows:
            writer.writerow(row)
        if len(rows) < batch_size:
            break
    cursor.close()
    hive_conn.close()


for k1, v1 in keywords_info.items():
    for k2, v2 in v1.items():
        st = set()
        for k3 in v2:
            st.add(k3)
        k11 = k1.replace('/', '')
        k22 = k2.replace('/', '')
        hive_run(f'0324/{k11}_{k22}.csv', '|'.join(st))
