import csv
import os
import re
from typing import List
os.environ['POD_ENV'] = 'online'
from resx.log import setup_logger
from resx.multi_task import MultiTask, TaskContext
from biz_utils.entity.report.annual_report import AnnualReport, AnnualReportDao
from biz_utils.id_center import id_center_query, EntityType
from biz_utils.credit_code import credit_code_valid
from biz_utils.reg_number import reg_number_valid

logger = setup_logger()
dao = AnnualReportDao()


def is_valid_email(email):
    pattern = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
    return re.match(pattern, email) is not None


class ThisMultiTask(MultiTask):
    CANNOT_CORRECT = 'CANNOT_CORRECT'

    def get_task(self):
        with open('0409_AI_correct_report_email.in.csv', 'r') as f:
            reader = csv.DictReader(f)
            for did, d in enumerate(reader):
                # if did > 20:
                #     break
                yield d

    def process(self, tc: TaskContext):
        d = tc.item
        report_id = d['report.report_id']
        email = d['report.email']
        report: AnnualReport = dao.get_by_id(report_id)
        assert report
        ok1 = is_valid_email(email)
        email1 = email.replace('。', '.').replace('..', '.').strip('.')
        ok2 = is_valid_email(email1)
        # logger.info(f'report={report.email} {ok1} {ok2}')

        changed = False
        if ok1:
            tc.code = self.CODE_DUP
        elif not ok2:
            tc.code = self.CANNOT_CORRECT
        else:
            if report.email != email1:
                report.email = email1
                dao.save(report)
                tc.code = self.CODE_SUCCESS


ThisMultiTask(worker_num=8, task_key='report.report_id').run()
# ThisMultiTask().process(TaskContext(iid=0, item={'report.report_id': 3090433278, 'report.email': 'hgmj@vip。163.com'}))
