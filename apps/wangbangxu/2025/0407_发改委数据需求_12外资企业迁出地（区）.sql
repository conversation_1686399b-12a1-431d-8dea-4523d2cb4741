with  area_code_mapping_0 as (select * from ods.ods_prism_area_code_district_df where pt = '20250101' and dw_is_del = 0 and version='V2020' and deleted=0),
area_code_mapping as (
    select distinct province_code as area_code, province_abbr as province, null as city, null as district from area_code_mapping_0 where length(province_code)=8
    union select distinct city_code as area_code, province_abbr as province, city, null as district from area_code_mapping_0 where length(city_code)=8
    union select distinct district_code as area_code, province_abbr as province, city, district from area_code_mapping_0 where length(district_code)=8 and district!=''
),
sub_company as (
    select id as sub_id, unified_social_credit_code as sub_code, deleted as sub_deleted, 'npo' as sub_type, company_id as cid from ods.ods_prism1_npo_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, unified_social_credit_code as sub_code, deleted as sub_deleted, 'neworg' as sub_type, company_id as cid from ods.ods_prism_organization_info_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, creditcode as sub_code, is_lawfirm as sub_deleted, 'lawfirm' as sub_type, company_id as cid from ods.ods_data_judicial_risk_law_firm_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, unified_social_credit_code as sub_code, 0 as sub_deleted, 'institution' as sub_type, company_id as cid from ods.ods_company_base_institution_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, company_num as sub_code, deleted as sub_deleted, 'hk' as sub_type, company_id as cid from ods.ods_prism1_company_hk_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, code as sub_code, deleted as sub_deleted,'tw' as sub_type, company_id as cid from ods.ods_prism1_company_tw_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, creditCode as sub_code, deleted as sub_deleted, 'foundation' as sub_type, company_id as cid from ods.ods_prism1_foundation_df where pt = ${pt} and dw_is_del = 0 and company_id is not null
),
company_branch as (select c1.gid as gid, c2.gid as parent_gid, c1.cancel_date from (select * from company where parent_id > 0 and sub_type = 'gsxt_gongsi') c1 inner join (select * from company where parent_id = 0 and sub_type = 'gsxt_gongsi') c2 on c1.parent_id = c2.cid),
report_brief as (select company_id as cid, report_year, published_date from ods.ods_prism_annual_report_brief_df where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0),
report as (select id as report_id, company_id as cid, report_year, manage_state, phone_number, postcode, postal_address, email, createtime as create_time from ods.ods_prism1_annual_report_df where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0),
report_investor as (select annual_report_id as report_id, investor_id, investor_type, investor_name from ods.ods_prism1_report_shareholder_df where pt = ${pt} and dw_is_del = 0),
report_social as (select annaulreport_id as report_id, endowment_insurance, unemployment_insurance, medical_insurance, employment_injury_insurance, maternity_insurance from ods.ods_prism1_report_social_security_info_df where pt = ${pt} and dw_is_del = 0),
partnership_info as (select company_gid as gid, executive as executive_name, executive_gid, executive_type from ods.ods_prism_company_partnership_info_df where pt = ${pt} and dw_is_del = 0 and deleted = 0),
company_staff as (select company_id as cid, staff_id as staff_cid, staff_type_name as position from ods.ods_prism1_company_staff_df where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0),
company_staff_sort as (select company_gid as gid, name as staff_name, if(type=1, 2, 1) as staff_type, position as positions, human_gid as staff_gid, create_time from ods.ods_prism_company_staff_sort_df where pt = ${pt} and dw_is_del = 0 and company_gid % ${gid_mod} = 0),
equity_ratio as (select company_graph_id as gid, shareholder_graph_id as sh_gid, shareholder_name as sh_name, shareholder_type as sh_type, `percent` as ratio, source from ods.ods_prism1_equity_ratio_df where pt = ${pt} and dw_is_del = 0 and company_graph_id % ${gid_mod} = 0 and deleted = 0),
change_info as (select company_id as cid, renamed_change_item as change_item, content_before as cont_before, content_after as cont_after, to_date(change_time) as change_date from ods.ods_company_change_company_change_info_clean_df where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0 and deleted = 0),
history_name_info as (select company_gid as gid, company_name as his_name, start_time, end_time from ods.ods_prism_company_history_name_df where pt = ${pt} and dw_is_del = 0 and company_gid % ${gid_mod} = 0),
brief_cancel_info as (select company_id as cid, investor_commitment_oss_path as oss_path, deleted from ods.ods_prism1_company_brief_cancel_announcement_info_df where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0),
abnormal_info as (select company_id as cid, to_date(put_date) as put_date, put_reason, to_date(remove_date) as remove_date, deleted from ods_prism1_company_abnormal_info_df where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0),
patent_info as (select view_tmp.gid as gid, patent_type, count(1) as patent_count from ods.ods_intellectual_property_info_company_patent_basic_info_index_df lateral view explode(split(company_ids, ';')) view_tmp as gid where pt = ${pt} and dw_is_del = 0 and nvl(company_ids, '') != '' group by view_tmp.gid, patent_type),
company as (
    select c.id as cid, base, name, to_date(estiblish_time) as esta_date, to_date(approved_time) as appr_date, cast(c.property1 as string) credit_code, c.reg_status,
        legal_person_name as legal_name, cast(c.reg_number as string) reg_number, c.crawledtime, nvl(parent_id, 0) as parent_id, c.reg_institute, c.company_org_type, c.from_time, c.to_time, c.property2 as cid_ref,
        -- if(c.reg_status regexp '销', nvl(other.remove_date, to_date(c.crawledtime)), null) as end_date,
        cg.gid,
         (case
            when c.reg_institute regexp '北京经济技术开发区' then '北京市'
            when province regexp '北京' then '北京市'
            when province regexp '上海' then '上海市'
            when city regexp '杭州市|厦门市|深圳市|广州市|苏州市' then city
            else '其他' end
        )city2,
        (case
                when reg_institute regexp '北京经济技术开发区' then '经开区'
                when province regexp '北京'  then district
                when province regexp '上海'  then '默认'
                when city regexp '杭州市|厦门市|深圳市|广州市|苏州市' then '默认'
                else '其他' end
        )district2,
        c.pt,
        (case
            when (property2 is not null and property2 != '') then 'prop2'
            when source_flag regexp 'qyxy' then
                (case
                    when nvl(c.property1, '') = '' and nvl(c.reg_number, '') = '' then 'kongke'
                    when c.company_org_type regexp '个体' then 'gsxt_geti'
                    when nvl(c.company_org_type, '') == '' and name not regexp '公司|合伙' then 'gsxt_geti'
                    when property1 regexp '^93' then 'gsxt_nonghe'
                    else 'gsxt_gongsi'
                end)
            when source_flag regexp '^npo_foundation' then 'foundation'
            when source_flag regexp '^npo' then 'npo'
            when source_flag regexp '^hk' then 'hk'
            when source_flag regexp '^tw' then 'tw'
            when source_flag regexp '^institution' then 'institution'
            when source_flag regexp '^lawfirm' then 'lawfirm'
            when source_flag regexp '^org' then 'neworg'
            else 'others' end
        )sub_type
    from (select id as cid, * from ods.ods_prism1_company_df where pt in (${pt_a}, ${pt_b})  and dw_is_del = 0 and id % ${cid_mod} = 0) c
    inner join (select graph_id as gid, company_id as cid, pt from ods.ods_prism1_company_graph_df where pt in (${pt_a}, ${pt_b}) and dw_is_del = 0 and deleted = 0 and graph_id % ${gid_mod} = 0 and company_id % ${cid_mod} = 0) cg on cg.cid = c.cid and cg.pt = c.pt
    inner join (select * from ods.ods_company_base_company_index_df where pt in (${pt_a}, ${pt_b}) and dw_is_del = 0 and company_id % ${gid_mod} = 0 and id % ${cid_mod} = 0)base on base.id = c.cid and base.pt = c.pt
    -- inner join (select * from ods.ods_search_card_enterprise_search_basic_info_df where pt = ${pt} and is_deleted = 0 and company_id % ${gid_mod} = 0)search on cg.gid = search.company_id
    -- left join ( select biz_type_id, company_gid from ods.ods_prism_company_biz_type_df where pt = ${pt}  and dw_is_del = 0 and deleted = 0 and company_gid % ${gid_mod} = 0) biz on cg.gid = biz.company_gid
    -- left join (select id as cid, reg_capital_amount*1e-6 as reg_capital_amount, reg_capital_currency, actual_capital_amount*1e-6 as actual_capital_amount, company_org_type, reg_status, social_security_staff_num, alias from ods.ods_prism1_company_clean_info_df where pt = ${pt} and dw_is_del = 0 and id % ${cid_mod} = 0) clean on c.id = clean.cid
    -- left join (select id as cid, to_date(nvl(cancel_date, revoke_date)) as remove_date from ods.ods_prism1_company_other_info_df where pt = ${pt} and dw_is_del = 0 and id % ${cid_mod} = 0 and nvl(cancel_date, revoke_date) is not null) other on c.id = other.cid
    inner join area_code_mapping on base.business_areacode_00 = area_code_mapping.area_code
    -- left join (select cate_1, cate_2, cate_3, category_code_3 from ods.ods_prism_company_category_all_code_v2017_df where pt = ${pt} and dw_is_del = 0)cate on base.industry_national_std_lv3_code = cate.category_code_3
)
select c_a.district2 as district2, count(1) from company c_a join company c_b
    on c_a.pt=${pt_a} and c_b.pt=${pt_b} and c_a.cid = c_b.cid
where c_b.sub_type = 'gsxt_gongsi'
AND c_b.company_org_type regexp '外' AND c_b.company_org_type regexp '资' and c_b.company_org_type not regexp '分'
and c_a.city2 != c_b.city2
group by c_a.district2
