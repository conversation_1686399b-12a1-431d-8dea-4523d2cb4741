--set hivevar:dt_now = date_format(from_unixtime(unix_timestamp()), 'yyyy-MM-dd');
--set hivevar:pt = date_format(date_sub(${hivevar:dt_now}, 1), 'yyyyMMdd');
--set hivevar:cid_mod = 1;
--set hivevar:gid_mod = 1;
--set hivevar:keywords = "{re_str}";
with  area_code_mapping_0 as (select * from ods.ods_prism_area_code_district_df where pt = ${hivevar:pt} and dw_is_del = 0 and version='V2020' and deleted=0),
area_code_mapping as (
    select distinct province_code as area_code, province as province, null as city, null as district from area_code_mapping_0
    union select distinct city_code as area_code, province as province, city, null as district from area_code_mapping_0
    union select distinct district_code as area_code, province as province, city, district from area_code_mapping_0
),
company_base as (
    select
        company_base_t.register_capital_amt*1e-6 as reg_capital,
        -- enterprise_search.reg_capital as reg_capital,
        to_date(company_base_t.establish_date) as establish_date,
        -- org_type,
        register_addr,
        province,
        city,
        district,
        area_code,
        cate_1,
        cate_2,
        cate_3,
        category_code_3,
        rongzhi_round,
        company_base_t.id as cid,
        company_base_t.company_id as gid,
        social_security_headcount,
        company_type,
        org_type,
        company_registation_status,
        enterprise_scale,
        company_name
    from (select * from ods.ods_company_base_company_index_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id % ${hivevar:gid_mod} = 0) company_base_t
    left join area_code_mapping on company_base_t.business_areacode_00 = area_code_mapping.area_code
    left join (select cate_1, cate_2, cate_3, category_code_3 from ods.ods_prism_company_category_all_code_v2017_df where pt = ${hivevar:pt} and dw_is_del = 0) cate_info_t on company_base_t.industry_national_std_lv3_code = cate_info_t.category_code_3
    left join (select * from ods.ods_search_card_enterprise_search_basic_info_df where pt = ${hivevar:pt} and is_deleted = 0 and company_id % ${hivevar:gid_mod} = 0) enterprise_search on company_base_t.company_id = enterprise_search.company_id
),
sub_company as (
    select id as sub_id, unified_social_credit_code as sub_code, deleted as sub_deleted, 'npo' as sub_type, company_id as cid from ods.ods_prism1_npo_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, unified_social_credit_code as sub_code, deleted as sub_deleted, 'neworg' as sub_type, company_id as cid from ods.ods_prism_organization_info_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, creditcode as sub_code, is_lawfirm as sub_deleted, 'lawfirm' as sub_type, company_id as cid from ods.ods_data_judicial_risk_law_firm_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, unified_social_credit_code as sub_code, 0 as sub_deleted, 'institution' as sub_type, company_id as cid from ods.ods_company_base_institution_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, company_num as sub_code, deleted as sub_deleted, 'hk' as sub_type, company_id as cid from ods.ods_prism1_company_hk_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, code as sub_code, deleted as sub_deleted,'tw' as sub_type, company_id as cid from ods.ods_prism1_company_tw_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id is not null
    union select id as sub_id, creditCode as sub_code, deleted as sub_deleted, 'foundation' as sub_type, company_id as cid from ods.ods_prism1_foundation_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id is not null
),
company_branch as (select c1.gid as gid, c2.gid as parent_gid, c1.cancel_date from (select * from company where parent_id > 0 and sub_type = 'gsxt_gongsi') c1 inner join (select * from company where parent_id = 0 and sub_type = 'gsxt_gongsi') c2 on c1.parent_id = c2.cid),
report_brief as (select company_id as cid, report_year, published_date from ods.ods_prism_annual_report_brief_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id % ${hivevar:cid_mod} = 0),
report as (select id as report_id, company_id as cid, report_year, manage_state, phone_number, postcode, postal_address, email, createtime as create_time from ods.ods_prism1_annual_report_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id % ${hivevar:cid_mod} = 0),
report_investor as (select annual_report_id as report_id, investor_id, investor_type, investor_name from ods.ods_prism1_report_shareholder_df where pt = ${hivevar:pt} and dw_is_del = 0),
report_social as (select annaulreport_id as report_id, endowment_insurance, unemployment_insurance, medical_insurance, employment_injury_insurance, maternity_insurance from ods.ods_prism1_report_social_security_info_df where pt = ${hivevar:pt} and dw_is_del = 0),
partnership_info as (select company_gid as gid, executive as executive_name, executive_gid, executive_type from ods.ods_prism_company_partnership_info_df where pt = ${hivevar:pt} and dw_is_del = 0 and deleted = 0),
company_staff as (select company_id as cid, staff_id as staff_cid, staff_type_name as position from ods.ods_prism1_company_staff_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id % ${hivevar:cid_mod} = 0),
company_staff_sort as (select company_gid as gid, name as staff_name, if(type=1, 2, 1) as staff_type, position as positions, human_gid as staff_gid, create_time from ods.ods_prism_company_staff_sort_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_gid % ${hivevar:gid_mod} = 0),
equity_ratio as (select company_graph_id as gid, shareholder_graph_id as sh_gid, shareholder_name as sh_name, shareholder_type as sh_type, `percent` as ratio, source from ods.ods_prism1_equity_ratio_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_graph_id % ${hivevar:gid_mod} = 0 and deleted = 0),
change_info as (select company_id as cid, renamed_change_item as change_item, content_before as cont_before, content_after as cont_after, to_date(change_time) as change_date from ods.ods_company_change_company_change_info_clean_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id % ${hivevar:cid_mod} = 0 and deleted = 0),
history_name_info as (select company_gid as gid, company_name as his_name, start_time, end_time from ods.ods_prism_company_history_name_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_gid % ${hivevar:gid_mod} = 0),
brief_cancel_info as (select company_id as cid, investor_commitment_oss_path as oss_path, deleted from ods.ods_prism1_company_brief_cancel_announcement_info_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id % ${hivevar:cid_mod} = 0),
abnormal_info as (select company_id as cid, to_date(put_date) as put_date, put_reason, to_date(remove_date) as remove_date, deleted from ods_prism1_company_abnormal_info_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_id % ${hivevar:cid_mod} = 0),
patent_info as (select view_tmp.gid as gid, patent_type, count(1) as patent_count from ods.ods_intellectual_property_info_company_patent_basic_info_index_df lateral view explode(split(company_ids, ';')) view_tmp as gid where pt = ${hivevar:pt} and dw_is_del = 0 and nvl(company_ids, '') != '' group by view_tmp.gid, patent_type),
park_info as (select park_rel.gid as gid, park_base.park_name as park_name from (select park_id, company_graph_id as gid from ods.ods_prism_qianzhan_park_relation_df where pt = ${hivevar:pt} and dw_is_del = 0 and company_graph_id % ${hivevar:gid_mod} = 0 and deleted=0) park_rel join (select id, park_name from ods.ods_prism_qianzhan_park_df where pt = ${hivevar:pt} and dw_is_del = 0 and deleted=0) park_base on park_rel.park_id = park_base.id),

company as (
    select c.id as cid, base, name, to_date(estiblish_time) as esta_date, to_date(approved_time) as appr_date, cast(c.property1 as string) credit_code, c.reg_status,
        legal_person_name as legal_name, cast(c.reg_number as string) reg_number, c.crawledtime, nvl(parent_id, 0) as parent_id, c.reg_institute, c.company_org_type, c.from_time, c.to_time, c.property2 as cid_ref,
        c.business_scope,
        c.reg_location,
        -- if(c.reg_status regexp '销', nvl(other.remove_date, to_date(c.crawledtime)), null) as end_date,
        cg.gid,
        (case
            when (property2 is not null and property2 != '') then 'prop2'
            when source_flag regexp 'qyxy' then
                (case
                    when nvl(c.property1, '') = '' and nvl(c.reg_number, '') = '' then 'kongke'
                    when c.company_org_type regexp '个体' then 'gsxt_geti'
                    when nvl(c.company_org_type, '') == '' and name not regexp '公司|合伙' then 'gsxt_geti'
                    when property1 regexp '^93' then 'gsxt_nonghe'
                    else 'gsxt_gongsi'
                end)
            when source_flag regexp '^npo_foundation' then 'foundation'
            when source_flag regexp '^npo' then 'npo'
            when source_flag regexp '^hk' then 'hk'
            when source_flag regexp '^tw' then 'tw'
            when source_flag regexp '^institution' then 'institution'
            when source_flag regexp '^lawfirm' then 'lawfirm'
            when source_flag regexp '^org' then 'neworg'
            else 'others' end
        )sub_type
    from (select id as cid, * from ods.ods_prism1_company_df where pt = ${hivevar:pt} and dw_is_del = 0 and id % ${hivevar:cid_mod} = 0) c
    inner join (select graph_id as gid, company_id as cid from ods.ods_prism1_company_graph_df where pt = ${hivevar:pt} and dw_is_del = 0 and deleted = 0 and graph_id % ${hivevar:gid_mod} = 0 and company_id % ${hivevar:cid_mod} = 0) cg on cg.cid = c.cid
--     left join ( select biz_type_id, company_gid from ods_prism_company_biz_type_df where pt = ${hivevar:pt}  and dw_is_del = 0 and deleted = 0 and company_gid % ${hivevar:gid_mod} = 0) biz on cg.gid = biz.company_gid
--     left join (select id as cid, reg_capital_amount*1e-6 as reg_capital_amount, reg_capital_currency, actual_capital_amount*1e-6 as actual_capital_amount, company_org_type, reg_status, social_security_staff_num, alias from ods.ods_prism1_company_clean_info_df where pt = ${hivevar:pt} and dw_is_del = 0 and id % ${hivevar:cid_mod} = 0) clean on c.id = clean.cid
--     left join (select id as cid, to_date(nvl(cancel_date, revoke_date)) as remove_date from ods.ods_prism1_company_other_info_df where pt = ${hivevar:pt} and dw_is_del = 0 and id % ${hivevar:cid_mod} = 0 and nvl(cancel_date, revoke_date) is not null) other on c.id = other.cid
),

-- select t.cid, company_base.gid, t.name, company_base.province, company_base.city, park_info.park_name, t.esta_date, company_base.reg_capital,
-- company_base.social_security_headcount, company_base.company_type, company_base.org_type, company_base.company_registation_status, company_base.enterprise_scale,
-- t.reg_location, t.business_scope, company_base.cate_1, company_base.cate_2, company_base.cate_3

-- from
-- (select *,
--     if(name regexp '旋翼|复合翼|固定翼|倾转旋翼|伞翼|扑翼|型号合格证|生产许可证|适航认证|飞行服务站' or business_scope regexp '旋翼|复合翼|固定翼|倾转旋翼|伞翼|扑翼|型号合格证|生产许可证|适航认证|飞行服务站', 1, 0) as tag1,
--     if(name regexp '单旋翼|双旋翼|多旋翼|倾转旋翼|型号合格证|生产许可证|适航认证' or business_scope regexp '单旋翼|双旋翼|多旋翼|倾转旋翼|型号合格证|生产许可证|适航认证', 1, 0) as tag2,
--     if(name regexp '多旋翼|复合翼|倾转旋翼|型号合格证|生产许可证|适航认证|飞行汽车' or business_scope regexp '多旋翼|复合翼|倾转旋翼|型号合格证|生产许可证|适航认证|飞行汽车', 1, 0) as tag3
-- from company where sub_type regexp 'gsxt_gongsi')t
-- join company_base on t.cid = company_base.cid
-- left join park_info on company_base.gid = park_info.gid

-- where length(t.credit_code) == 18 and (tag1 == 1 or tag2 == 1 or tag3 == 1)


-- 第一个查询 专利
total_gids as (
select gid, concat_ws(',', collect_set(match_reason)) AS match_reasons
 from (
SELECT
    get_json_object(json_da, '$.graph_id') as gid, '专利' as match_reason
FROM
    ods.ods_patent_company_patent_basic_info_df
    LATERAL VIEW EXPLODE(split(regexp_replace(
        regexp_replace(applicantnamenow, '\\[|\\]', ''),
        '\\}\\, \\{',
        '\\}\\;\\{'
    ), '\\;')) split_data AS json_da
WHERE
    pt = ${hivevar:pt}
    AND isdeleted = 0
    AND title RLIKE ${hivevar:keywords}
GROUP BY
    json_da
UNION
-- 第二个查询 企业简介
SELECT
    cid as gid, '企业简介' as match_reason
FROM
    dwt_offline.dwt_company_abs_df
WHERE
    pt = ${hivevar:pt}
    AND deleted = 0
    AND abstracts RLIKE ${hivevar:keywords}
GROUP BY
    cid
UNION
-- 第三个查询 项目品牌
SELECT
    graph_id as gid, '项目品牌' as match_reason
FROM
    ods.ods_prism_qimingpian_qimingpian_product_df
WHERE
    pt = ${hivevar:pt}
    AND is_deleted = 0
    AND CONCAT(yewu, intro) RLIKE ${hivevar:keywords}
GROUP BY
    graph_id
UNION
select gid, '企业名称' as match_reason from company where  sub_type != 'kongke' and name regexp ${hivevar:keywords}
UNION
select gid, '经营范围' as match_reason from company where  sub_type != 'kongke' and business_scope regexp ${hivevar:keywords}
)t group by gid
)


select company_base.gid as `公司ID`,
    match_reasons as `命中原因`,
    company_base.company_name as `公司名称`,
    company_base.province as `省份`,
    company_base.city as `市区`,
    park_info.park_name as `所在园区`,
    company_base.establish_date as `成立时间`,
    company_base.reg_capital as `注册资本`,
    company_base.social_security_headcount as `参保人数`,
    (case
        when company_base.company_type = 2 then '香港'
        when company_base.company_type = 3 then '台湾'
        when company_base.company_type = 4 then '事业单位'
        when company_base.company_type = 5 then '律所'
        when company_base.company_type = 6 then '社会组织'
        when company_base.company_type = 7 then '基金会'
        when company_base.company_type = 8 then '机构'
        when company_base.company_type = 9 then '工商来源个体工商户'
        when company_base.company_type = 10 then '工商来源个人独资企业'
        when company_base.company_type = 11 then '工商来源合伙制企业'
        when company_base.company_type = 1 then '工商来源其它普通公司'
        else '未知' end
    ) as `机构类型`,
    company_base.org_type as `企业类型`,
    company_base.company_registation_status as `登记信息`,
    company_base.enterprise_scale as `公司规模`,
    company_base.register_addr as `注册地址`,
    company.business_scope as `经营范围`,
    company_base.cate_1 as `行业一级分类`,
    company_base.cate_2 as `行业二级分类`,
    company_base.cate_3 as `行业三级分类`,
    prod.product                                 AS `品牌信息`,
    yuqing.COUNT                                 AS `新闻舆情（条数）`,
    patent_type.cou_zl                           AS `专利数申请量（2024年至今）`,
    patent_type.num_invention_patents            AS `专利分布-发明专利占比（%）`,
    patent_type.num_utility_model_patents        AS `专利分布-实用新型专利占比（%）`,
    patent_type.num_design_patents               AS `专利分布-外观专利占比（%）`,
    CASE WHEN w6.haveRz THEN TRUE ELSE FALSE END AS `融资情况`
from total_gids
inner join company on company.gid = total_gids.gid
inner join company_base on company.gid = company_base.gid
left join park_info on company_base.gid = park_info.gid
left join (
    -- 项目品牌
    SELECT graph_id,
           product,
           TRUE as haveProduct
    FROM ods.ods_prism_qimingpian_qimingpian_product_df
    WHERE pt = ${hivevar:pt}
      AND dw_is_del = 0
      AND is_deleted = 0
      AND graph_id <> 0
    GROUP BY graph_id, product) prod ON company.gid = prod.graph_id
LEFT JOIN (
    -- 专利
    SELECT t.cid,
           COUNT(1)                                                     AS cou_zl,
           sum(CASE WHEN t.pat_type = '1' THEN 1 ELSE 0 END) / COUNT(1) AS num_invention_patents,
           sum(CASE WHEN t.pat_type = '2' THEN 1 ELSE 0 END) / COUNT(1) AS num_utility_model_patents,
           sum(CASE WHEN t.pat_type = '3' THEN 1 ELSE 0 END) / COUNT(1) AS num_design_patents
    FROM (SELECT ROW_NUMBER() OVER (PARTITION BY cid, app_number ORDER BY id) AS row_num,
                 cid,
                 app_number,
                 pat_type
          FROM (select * from dwt_offline.dwt_company_patent_new_df where pt = ${hivevar:pt})patent_t
                   LATERAL VIEW EXPLODE(split(cids, ';')) split_data AS cid
          WHERE deleted = 0
            AND cids != ''
            AND YEAR(app_date) >= 2024
            AND cids IS NOT NULL) t
    WHERE t.row_num = 1
    GROUP BY t.cid) patent_type ON company.gid = patent_type.cid
LEFT JOIN (
    -- 新闻舆情
    SELECT cid,
           COUNT(*) AS COUNT
    FROM (SELECT split(SUBSTR(cids, 2, LENGTH(cids) - 2), ', ') AS cids
          FROM ods.ods_data_news_yuqing_ins_df
          WHERE pt = ${hivevar:pt}
            AND cids IS NOT NULL
            AND cids != '[]') AS TABLE_NAME
             LATERAL VIEW EXPLODE(cids) exploded_table AS cid
    GROUP BY cid) yuqing ON company.gid = yuqing.cid
LEFT JOIN (
    -- 融资数据
    SELECT graph_id,
           COUNT(1) AS cou,
           TRUE     as haveRz
    FROM dwd_offline.dwd_qimingpian_history_rongzi_df
    WHERE pt = ${hivevar:pt}
      AND dw_is_del = 0
      AND is_deleted = 0
      AND graph_id <> 0
    GROUP BY graph_id) w6 ON company.gid = w6.graph_id
where company.sub_type != 'kongke' and company.sub_type != 'prop2'
