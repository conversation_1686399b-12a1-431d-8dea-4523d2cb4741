import csv
import os
import re
from typing import List
os.environ['POD_ENV'] = 'online'
from resx.log import setup_logger
from resx.multi_task import MultiTask, TaskContext
from biz_utils.entity.report.annual_report import AnnualReport, AnnualReportDao
from biz_utils.entity.report.report_guarantee import ReportGuarantee, ReportGuaranteeDao
from biz_utils.id_center import id_center_query, EntityType
from biz_utils.credit_code import credit_code_valid
from biz_utils.reg_number import reg_number_valid

logger = setup_logger()
report_dao = AnnualReportDao()
report_guarantee_dao = ReportGuaranteeDao()


class ThisMultiTask(MultiTask):
    CANNOT_CORRECT = 'CANNOT_CORRECT'

    def __init__(self):
        super().__init__(worker_num=8, task_key='report_guarantee.id')

    def get_task(self):
        with open('0409_AI_correct_guarantee_term_enum.in.csv', 'r') as f:
            reader = csv.DictReader(f)
            for did, d in enumerate(reader):
                # if did < 0:
                #     continue
                # if did > 0+2:
                #     break
                yield d

    def process(self, tc: TaskContext):
        d = tc.item
        iid = d['report_guarantee.id']
        item: ReportGuarantee = report_guarantee_dao.get_by_id(iid)
        assert item
        # 3=未约定 2=连带保证 1=一般保证
        changed = False
        if item.guarantee_term == '1':
            item.guarantee_term = '期限'
            changed = True
        if item.guarantee_term == '2':
            item.guarantee_term = '未约定'
            changed = True

        if not changed:
            tc.code = self.CODE_DUP
            return
        report_guarantee_dao.save_by_id(item)
        tc.code = self.CODE_SUCCESS


ThisMultiTask().run()
# ThisMultiTask().process(TaskContext(iid=0, item={'report.report_id': 3057857581, }))
