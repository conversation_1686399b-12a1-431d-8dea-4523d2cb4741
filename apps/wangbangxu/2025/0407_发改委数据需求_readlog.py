import re
import sys
from collections import defaultdict

# f = open('0407_发改委数据需求.log2', 'r')
# cities = ['北京市','上海市','杭州市','厦门市','深圳市','广州市','苏州市']
cities = ['东城区','西城区','朝阳区','海淀区','丰台区','石景山区','门头沟区','房山区','通州区','顺义区','昌平区','大兴区','怀柔区','平谷区','密云区','延庆区','经开区']
months = ['20250401', '20250301', '20250201', '20250101', '20241201', '20241101', '20241001', '20240901', '20240801', '20240701', '20240601', '20240501', '20240401', '20240301', '20240201'][::-1]
data = [['0'] * len(cities) for _ in months]
for line in sys.stdin:
    # INFO   MainThread 2025-04-08 17:08:33 0407_发改委数据需求.py:43 - pt=20240601 row={'city2': '北京市', '_c1': 14374}
    pat = r'pt=(\d+) row=(.+)'
    if match := re.search(pat, line):
        pt = match.group(1)
        row = eval(match.group(2))
        city_name = 'district2'  # city2 district2
        if city_name in row and row[city_name] in cities:
            c = cities.index(row[city_name])
            r = months.index(pt)
            data[r][c] = str(row['_c1'])

for row in data:
    print('\t'.join(row))
