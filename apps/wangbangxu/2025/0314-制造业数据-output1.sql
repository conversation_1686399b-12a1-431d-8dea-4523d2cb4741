--
--select t2.gid, t2024.establish_date,
--(select distinct gid from
--(
--select gid from test.wbx_0314_y2024
--union select gid from test.wbx_0314_y2023
--union select gid from test.wbx_0314_y2022
--)t1
--)t2
--left join test.wbx_0314_y2024 t2024 on t2.gid = t2024.gid
--left join test.wbx_0314_y2023 t2023 on t2.gid = t2023.gid
--left join test.wbx_0314_y2022 t2022 on t2.gid = t2022.gid;

-- 1、截止2022年度和2024年底，东部、中部和西部地区存续制造业企业数量；
--
select province2, count(1) from test.wbx_0314_y2022 where status regexp '存续|开业|在业' and province2 != '其他' group by province2;
select province2, count(1) from test.wbx_0314_y2024 where status regexp '存续|开业|在业' and province2 != '其他' group by province2;