import csv
import os
import re
from typing import List
os.environ['POD_ENV'] = 'online'
from resx.log import setup_logger
from resx.multi_task import MultiTask, TaskContext
from biz_utils.entity.report.annual_report import AnnualReport, AnnualReportDao
from biz_utils.entity.report.report_equity_change import ReportEquityChange, ReportEquityChangeDao

from biz_utils.id_center import id_center_query, EntityType
from biz_utils.credit_code import credit_code_valid
from biz_utils.reg_number import reg_number_valid

logger = setup_logger()
equity_change_dao = ReportEquityChangeDao()


def ok(s):
    if re.fullmatch(r'\d+(\.\d+)?', s):
        return 0 <= float(s) <= 100
    return False


class ThisMultiTask(MultiTask):

    def __init__(self):
        super().__init__(worker_num=8, task_key='report_equity_change.id')

    def get_task(self):
        with open('0409_AI_correct_equity_change_ratio.in.csv', 'r') as f:
            reader = csv.DictReader(f)
            for did, d in enumerate(reader):
                # if did > 2:
                #     break
                yield d

    def process(self, tc: TaskContext):
        d = tc.item
        iid = d['report_equity_change.id']
        item: ReportEquityChange = equity_change_dao.get_by_id(iid)
        assert item
        changed = False
        if ok(item.ratio_before):
            item.ratio_before += '%'
            changed = True
        if ok(item.ratio_after):
            item.ratio_after += '%'
            changed = True

        logger.info(f'item={item} {changed}')
        if not changed:
            tc.code = self.CODE_DUP
            return
        equity_change_dao.save(item)
        tc.code = self.CODE_SUCCESS


ThisMultiTask().run()
