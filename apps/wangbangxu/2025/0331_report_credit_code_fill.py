import csv
import os
import time

os.environ['POD_ENV'] = 'online'
from resx.log import setup_logger
from biz_utils.entity.report.annual_report import AnnualReportDao, AnnualReport
logger = setup_logger()
report_dao = AnnualReportDao()


with open('0331_report_credit_code_fill.csv', 'r') as f:
    reader = csv.DictReader(f)
    for did, d in enumerate(reader):
        if did % 100 == 0:
            logger.info(f'Processed {did} records')
            time.sleep(20)

        report_id = int(d['report.report_id'])
        credit_code = d['company.credit_code']
        report: AnnualReport = report_dao.get_by_id(report_id)
        if not report:
            logger.warning(f'Cannot find report with id {report_id}')
            continue
        if len(report.credit_code or '') == 18:
            logger.info(f'Credit code already exists for report {report_id}')
            continue
        report.credit_code = credit_code
        ret = report_dao.save(report)
        logger.info(f'ret={ret} for {d}')
