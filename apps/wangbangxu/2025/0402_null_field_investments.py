import csv
import os
from typing import List
os.environ['POD_ENV'] = 'online'
from resx.log import setup_logger
logger = setup_logger()

from resx.multi_task import MultiTask, TaskContext
from biz_utils.entity.report.report_investment import ReportInvestment, ReportInvestmentDao
from biz_utils.id_center import id_center_query, EntityType
from biz_utils.credit_code import credit_code_valid
from biz_utils.reg_number import reg_number_valid
dao = ReportInvestmentDao()


class ThisMultiTask(MultiTask):
    CODE_ID_CENTER_MISS = 'CODE_ID_CENTER_MISS'

    def __init__(self):
        super().__init__(worker_num=4, task_key='report_id')

    def get_task(self):
        with open('0402_null_field_investments.1.csv', 'r') as f:
            reader = csv.DictReader(f)
            for did, d in enumerate(reader):
                # if did > 2:
                #     break
                yield d

    def process(self, tc: TaskContext):
        d = tc.item
        report_id = d['report_id']
        items: List[ReportInvestment] = list(dao.get_many(annual_report_id=report_id))

        checked, changed = False, False
        for item in items:
            if item.outcompany_id:
                continue
            if credit_code_valid(item.credit_code):
                ent_type, eid = id_center_query(credit_no=item.credit_code)
                reason = 'by_credit'
            elif credit_code_valid(item.reg_num):
                ent_type, eid = id_center_query(credit_no=item.reg_num)
                reason = 'by_credit'
            elif reg_number_valid(item.reg_num):
                ent_type, eid = id_center_query(reg_number=item.reg_num)
                reason = 'by_reg_num'
            elif reg_number_valid(item.credit_code):
                ent_type, eid = id_center_query(reg_number=item.credit_code)
                reason = 'by_reg_num'
            else:
                ent_type, eid = id_center_query(name=item.outcompany_name)
                reason = 'by_name'
            checked = True
            if ent_type == EntityType.ORG:
                item.outcompany_id = eid
                logger.info(f'report_id={report_id} outcompany_id={item.outcompany_id} by {reason}')
                changed = True
        if not checked:
            tc.code = self.CODE_DUP
            return
        if not changed:
            tc.code = self.CODE_ID_CENTER_MISS
            return

        ret = dao.save_group(items)
        # 删除额外的空行
        for null_item in dao.get_many(annual_report_id=report_id, outcompany_id=None):
            dao.delete_by_id(null_item.id)
        tc.code = self.CODE_SUCCESS


# ThisMultiTask().run()
ThisMultiTask().process(TaskContext(iid=0, item={'report_id': 3046045631, }))
