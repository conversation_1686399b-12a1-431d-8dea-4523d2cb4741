# encoding=utf8

import re
import argparse
import time
from datetime import datetime, timedelta, date
from libs.dt import to_date, date2str
from libs.log2 import setup_logger
from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao
from dao.octopus.item import ItemDao

logger = setup_logger()


class BriefCancelInfoDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_brief_cancel_announcement_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', None)
        super().__init__(**kwargs)


class CancelRecordDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_cancel_record_and_announcement_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', None)
        super().__init__(**kwargs)

    def get_latest(self, limit=400000):
        sql = f'select * from {self.db_tb_name} order by id desc limit %s'
        for d in self.mysql_client.select_many(sql, args=(limit,)):
            yield d


octopus_item_dao = ItemDao(name='company', is_clue=False)
brief_cancel_dao = BriefCancelInfoDao()
cancel_record_dao = CancelRecordDao()


def main(args):
    logger.info(f'{args}')
    while True:
        time.sleep(3600 * 6)
        start = (datetime.now() - timedelta(days=90)).date()
        for d in brief_cancel_dao.scan(scan_key='announcement_end_date', start=start):
            announcement_term = d.get('announcement_term', '')
            announcement_end_date = d['announcement_end_date']
            cid = d['company_id']
            if not isinstance(announcement_term, str):
                announcement_term = ''
            mo = re.search(r'(\d{4}年\d{2}月\d{2}日)$', announcement_term)  # 2023年12月13日 - 2024年01月02日
            if mo:
                announcement_end_date1 = to_date(mo.group(1))
                if announcement_end_date1 != announcement_end_date:
                    logger.info(f'{announcement_end_date} --> {announcement_end_date1}')
                    announcement_end_date = announcement_end_date1

            ret = octopus_item_dao.set_info(word=str(cid), do_update=True, cancel_date=date2str(announcement_end_date))
            logger.info(f'OUTPUT {cid} {announcement_end_date} {ret}')

        for d in cancel_record_dao.get_latest():
            creditors_publish_to = d['creditors_publish_to']
            cid = d['company_id']
            if not isinstance(creditors_publish_to, date):
                continue
            ret = octopus_item_dao.set_info(word=str(cid), do_update=True, cancel_date=date2str(creditors_publish_to))
            logger.info(f'OUTPUT {cid} {creditors_publish_to} {ret}')


if __name__ == '__main__':
    from libs.log2 import setup_logger
    ap = argparse.ArgumentParser(description='octopus_company_cancel_predict.py')
    logger = setup_logger()
    main(ap.parse_args())
