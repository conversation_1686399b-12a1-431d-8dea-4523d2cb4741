# encoding=utf8
import csv
from typing import Tuple
import argparse
from collections import Counter
from libs.log2 import setup_logger

logger = setup_logger()


def get_city_district(area_code: str) -> Tuple[str, str]:
    if area_code.startswith('00110101'):
        return '北京', '东城'
    if area_code.startswith('00110102'):
        return '北京', '西城'
    if area_code.startswith('00110105'):
        return '北京', '朝阳'
    if area_code.startswith('00110106'):
        return '北京', '丰台'
    if area_code.startswith('00110107'):
        return '北京', '石景山'
    if area_code.startswith('00110108'):
        return '北京', '海淀'
    if area_code.startswith('00110109'):
        return '北京', '门头沟'
    if area_code.startswith('00110111'):
        return '北京', '房山'
    if area_code.startswith('00110112'):
        return '北京', '通州'
    if area_code.startswith('00110113'):
        return '北京', '顺义'
    if area_code.startswith('00110114'):
        return '北京', '昌平'
    if area_code.startswith('00110115'):
        return '北京', '大兴'
    if area_code.startswith('00110116'):
        return '北京', '怀柔'
    if area_code.startswith('00110117'):
        return '北京', '平谷'
    if area_code.startswith('00110118'):
        return '北京', '密云'
    if area_code.startswith('00110119'):
        return '北京', '延庆'
    if area_code.startswith('0011'):
        return '北京', '其他'
    if area_code.startswith('0031'):
        return '上海', ''
    if area_code.startswith('003301'):
        return '杭州', ''
    if area_code.startswith('003502'):
        return '厦门', ''
    if area_code.startswith('004403'):
        return '深圳', ''
    if area_code.startswith('004401'):
        return '广州', ''
    return '其他', ''


def main(args):
    logger.info(f'args={args}')

    move_out = {}  # pt -> count

    pts = [
        '20221001', '20221101', '20221201',
        '20230101', '20230201', '20230301', '20230401', '20230501', '20230601', '20230701', '20230801', '20230901', '20231001', '20231101', '20231201',
        '20240101', '20240201', '20240301', '20240401', '20240501', '20240601', '20240701', '20240801', '20240901', '20241001', '20241101',
    ]
    cities = []
    for pt in pts[:]:
        move_out[pt] = Counter()
        logger.info(f'process pt={pt}')
        with open(f'./data/area_code_change.{pt}.csv', 'r') as f:
            reader = csv.DictReader(f=f)
            for did, d in enumerate(reader):
                area_code_from: str = d['area_code_from']
                area_code_to: str = d['area_code_to']
                cid: int = int(d['cid'])
                city_from, district_from = get_city_district(area_code_from)
                city_to, district_to = get_city_district(area_code_to)
                if city_from != city_to:
                    move_out[pt][city_from + district_from] += 1
                    if city_from + district_from not in cities:
                        cities.append(city_from + district_from)
                    if city_from == '北京' and district_from == '其他':
                        logger.info(f'{cid} {area_code_from} {area_code_to}')

    for pt in sorted(move_out.keys()):
        s = ' '.join(f'{city}:{move_out[pt][city]}' for city in cities)
        logger.info(s)


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='数据统计')
    main(ap.parse_args())
