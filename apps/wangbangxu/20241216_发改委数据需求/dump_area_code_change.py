# encoding=utf8
from pyhive import hive
import csv
import argparse
from libs.log2 import setup_logger

logger = setup_logger()


def main(args):
    logger.info(f'args={args}')
    pts = [
        '20221001', '20221101', '20221201',
        '20230101', '20230201', '20230301', '20230401', '20230501', '20230601', '20230701', '20230801', '20230901', '20231001', '20231101', '20231201',
        '20240101', '20240201', '20240301', '20240401', '20240501', '20240601', '20240701', '20240801', '20240901', '20241001', '20241101', '20241201',
    ]
    batch_size = 10000
    for pts_idx in range(len(pts) - 1):
        pt1, pt2, cid_mod = pts[pts_idx], pts[pts_idx + 1], 1
        dump_file_name = f'./data/area_code_change.{pt1}.csv'
        hive_conn = hive.Connection(
            host="************",
            port=10000,
            username="wangbangxu",
            # password='Tyc0011__1',
        )

        cursor = hive_conn.cursor()

        cursor.execute(f"""
        with 
        area_code_1 as (
            select company_id as cid, areacode_00 as areacode
            from ods.ods_prism_company_area_code_df where pt={pt1} and dw_is_del = 0 and company_id % {cid_mod} = 0
        ),
        area_code_2 as (
            select company_id as cid, areacode_00 as areacode
            from ods.ods_prism_company_area_code_df where pt={pt2} and dw_is_del = 0 and company_id % {cid_mod} = 0
        )
        select area_code_1.cid as cid,
            area_code_1.areacode as area_code_from,
            area_code_2.areacode as area_code_to
            from area_code_1 join area_code_2 on area_code_1.cid = area_code_2.cid
            where area_code_1.areacode != area_code_2.areacode
        """)

        writer = csv.writer(open(dump_file_name, 'w'))
        # cursor.description
        # [('base', 'STRING_TYPE', None, None, None, None, True), ('id', 'BIGINT_TYPE', None, None, None, None, True)]
        writer.writerow(x[0] for x in cursor.description)

        while True:
            rows = cursor.fetchmany(size=batch_size)
            logger.info(f'pt1={pt1} fetch {len(rows)} rows')
            for row in rows:
                writer.writerow(row)
            if len(rows) < batch_size:
                break
        cursor.close()
        hive_conn.close()


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='dump出所有area_code的变更信息')
    main(ap.parse_args())
