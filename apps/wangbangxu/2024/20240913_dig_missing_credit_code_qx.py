import json
import re
from typing import Dict
import argparse
import csv
import time
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor
from gslib.schedule import immediately_gs
from dao.company import Company, CompanyDao
from dao.reports.annual_report import AnnualReportDao, AnnualReport
from dao.reports.annual_report_brief import AnnualReportBriefDao, AnnualReportBrief
from clients.redis.redis_queue import RedisQueue
from gslib.msv import msv_query_base_info
from libs.dt import to_date
from gslib.id_center import id_center_query, EntityType
from gslib.credit_code import credit_code_valid


logger = setup_logger()
fs = dict()
process_code_stat = Counter()
company_dao = CompanyDao(max_write_per_minute=600)


redis_queue = RedisQueue(name='normal_schedule_queue', max_length=1000000, **ConstantProps.PROPS_GS_REDIS_ONLINE)


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    BAD_COMPANY = auto()
    ALREADY_DONE_COMPANY = auto()
    BAD_CREDIT_CODE = auto()
    CREDIT_CODE_SEEN = auto()

    OK = auto()


@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


def process(tc: TaskContext):
    cid = int(tc.inputs['t1.cid'])

    company: Company = company_dao.get(cid)
    if not company or company.prop2 is not None:
        tc.code = ProcessCode.BAD_COMPANY
        return

    if len(company.credit_code or '') == 18:
        tc.code = ProcessCode.ALREADY_DONE_COMPANY
        return

    credit_code = tc.inputs['t2.credit_no']
    if not credit_code_valid(credit_code):
        tc.code = ProcessCode.BAD_CREDIT_CODE
        return

    ttp, tid = id_center_query(credit_no=credit_code)
    if ttp != EntityType.UNSET:
        tc.code = ProcessCode.CREDIT_CODE_SEEN
        if ttp != EntityType.ORG:
            logger.warning(f'bad entity type {ttp.name} {cid} {credit_code}')
            tc.code = ProcessCode.BAD_CREDIT_CODE
            return
        logger.info(f'CREDIT_CODE_SEEN cid={cid} cid2={tid}')
        return

    tc.code = ProcessCode.OK
    company.credit_code = credit_code
    ret = company_dao.mysql_client.execute('update prism.company set property1=%s where id=%s limit 1', args=(credit_code, company.cid))
    logger.info(f'OUTPUT {cid} credit_code={credit_code} ret={ret}')


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code != ProcessCode.INITIAL:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)
        for did, d in enumerate(reader):
            if did < args.input_csv_lid_min:
                continue
            if did >= args.input_csv_lid_max != -1:
                break
            d['did'] = did
            tc = TaskContext(inputs=d)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多线程处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='20240913_dig_missing_credit_code_qx.input.1p', help='')
    ap.add_argument('--input-csv-lid-min', type=int, default=-1)
    ap.add_argument('--input-csv-lid-max', type=int, default=10)
    ap.add_argument('--worker-num', type=int, default=1)

    main(ap.parse_args())

    # _d = {
    #     'did': 0,
    #     'cf_cid': 121141506,
    #
    # }
    # tc = TaskContext(inputs=_d)
    # process(tc)
    # logger.info(f'tc={tc}')
    #
