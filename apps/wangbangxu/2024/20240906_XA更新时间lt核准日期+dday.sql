-- select * from  ods.ods_prism1_company_df where pt in ('20240818','20240101','20230101','20220101') and id in ('*********') ;
-- select * from  ods.ods_prism1_npo_df where pt in ('20240705','20240704','20240703','20230101','20220101') and id in ('506748') ;
-- PASS Tyc0011__1


with company as (
    select c.id as cid,
        cg.gid,
        base,
        name,
        legal_person_name as legal_name,
        to_date(estiblish_time) as esta_date,
        to_date(approved_time) as appr_date,
        cast(c.property1 as string) credit_code,
        cast(c.reg_number as string) reg_number,
        c.crawledtime,
        nvl(parent_id, 0) as parent_id,
        c.reg_institute,
        c.reg_status,
        c.company_org_type,
        c.from_time,
        c.to_time,
        c.property2 as cid_ref,
--        c.business_scope,
--        clean.reg_status,
--        clean.reg_capital_amount,
--        clean.company_org_type,
--        clean.alias,
--        biz.biz_type_id,
--        if(clean.reg_status regexp '销', nvl(other.remove_date, to_date(c.crawledtime)), null) as end_date,
        (case
            when (property2 is not null and property2 != '') then 'prop2'
            when source_flag regexp 'qyxy' then
                (case
                    when nvl(c.property1, '') = '' and nvl(c.reg_number, '') = '' then 'kongke'
                    when c.company_org_type regexp '个体' then 'gsxt_geti'
                    when nvl(c.company_org_type, '') == '' and name not regexp '公司|合伙' then 'gsxt_geti'
                    when property1 regexp '^93' then 'gsxt_nonghe'
                    else 'gsxt_gongsi'
                end)
            when source_flag regexp '^npo_foundation' then 'foundation'
            when source_flag regexp '^npo' then 'npo'
            when source_flag regexp '^hk' then 'hk'
            when source_flag regexp '^tw' then 'tw'
            when source_flag regexp '^institution' then 'institution'
            when source_flag regexp '^lawfirm' then 'lawfirm'
            when source_flag regexp '^org' then 'neworg'
            else 'others' end
        )sub_type
    from (
        select
            id as cid,
            *
        from ods.ods_prism1_company_df
        where pt = ${pt}
            and dw_is_del = 0
            and id % ${cid_mod} = 0
    ) c
    inner join (
        select
            graph_id as gid,
            company_id as cid
        from ods.ods_prism1_company_graph_df
        where pt = ${pt}
            and dw_is_del = 0
            and deleted = 0
            and graph_id % ${gid_mod} = 0
            and company_id % ${cid_mod} = 0
    ) cg on cg.cid = c.cid
--     left join (
--         select
--            biz_type_id,
--            company_gid
--         from ods_prism_company_biz_type_df
--         where pt = ${pt}
--            and dw_is_del = 0
--            and deleted = 0
--            and company_gid % ${gid_mod} = 0
--     ) biz on cg.gid = biz.company_gid
--     left join (
--         select
--            id as cid,
--            reg_capital_amount*1e-6 as reg_capital_amount,
--            reg_capital_currency,
--            actual_capital_amount*1e-6 as actual_capital_amount,
--            company_org_type,
--            reg_status,
--            social_security_staff_num,
--            alias
--         from ods.ods_prism1_company_clean_info_df
--         where pt = ${pt}
--            and dw_is_del = 0
--            and id % ${cid_mod} = 0
--     ) clean on c.id = clean.cid
--     left join (
--         select
--            id as cid,
--            to_date(nvl(cancel_date, revoke_date)) as remove_date
--        from ods.ods_prism1_company_other_info_df
--        where pt = ${pt}
--            and dw_is_del = 0
--            and id % ${cid_mod} = 0
--            and nvl(cancel_date, revoke_date) is not null
--     ) other on c.id = other.cid
),
area_code_mapping_0 as (
    select *
    from ods.ods_prism_area_code_district_df
    where pt = ${pt} 
        and dw_is_del = 0
        and version='V2020'
        and deleted=0
),
area_code_mapping as (
    select distinct
    province_code as area_code,
        province as province,
        null as city,
        null as district
    from area_code_mapping_0
    union
    select distinct
        city_code as area_code,
        province as province,
        city,
        null as district
    from area_code_mapping_0
    union
    select distinct
        district_code as area_code,
        province as province,
        city,
        district
    from area_code_mapping_0
),
company_base as (
    select
        company_base_t.register_capital_amt*1e-6 as reg_capital,
        -- enterprise_search.reg_capital as reg_capital,
        to_date(company_base_t.establish_date) as establish_date,
        -- org_type,
        -- register_addr,
        province,
        city,
        district,
        area_code,
        cate_1,
        cate_2,
        cate_3,
        category_code_3,
        rongzhi_round,
        scientific_company,
        company_base_t.id as cid,
        company_base_t.company_id as gid,
        company_name
    from (
        select *
        from ods.ods_company_base_company_index_df
        where pt = ${pt}
            and dw_is_del = 0
            and company_id % ${gid_mod} = 0
    ) company_base_t
    left join area_code_mapping
        on company_base_t.business_areacode_00 = area_code_mapping.area_code
    left join (
        select
            cate_1,
            cate_2,
            cate_3,
            category_code_3
        from ods.ods_prism_company_category_all_code_v2017_df
        where pt = ${pt}
            and dw_is_del = 0
    ) cate_info_t
        on company_base_t.industry_national_std_lv3_code = cate_info_t.category_code_3
    -- left join (
    --     select * from
    --     ods.ods_search_card_enterprise_search_basic_info_df where pt = ${pt} and is_deleted = 0 and company_id % ${gid_mod} = 0
    -- ) enterprise_search on company_base_t.company_id = enterprise_search.company_id
),
sub_company as (
    (
        select
            id as sub_id,
            unified_social_credit_code as sub_code,
            deleted as sub_deleted,
            'npo' as sub_type,
            company_id as cid
        from ods.ods_prism1_npo_df
        where pt = ${pt}
            and dw_is_del = 0
            and company_id is not null
    ) union (
        select
            id as sub_id,
            unified_social_credit_code as sub_code,
            deleted as sub_deleted,
            'neworg' as sub_type,
            company_id as cid
        from ods.ods_prism_organization_info_df
        where pt = ${pt}
            and dw_is_del = 0
            and company_id is not null
    ) union (
        select
            id as sub_id,
            creditcode as sub_code,
            is_lawfirm as sub_deleted,
            'lawfirm' as sub_type,
            company_id as cid
        from ods.ods_data_judicial_risk_law_firm_df
        where pt = ${pt}
            and dw_is_del = 0
            and company_id is not null
    ) union (
        select
            id as sub_id,
            unified_social_credit_code as sub_code,
            0 as sub_deleted,
            'institution' as sub_type,
            company_id as cid
        from ods.ods_company_base_institution_df
        where pt = ${pt}
            and dw_is_del = 0
            and company_id is not null
    ) union (
        select
            id as sub_id,
            company_num as sub_code,
            deleted as sub_deleted,
            'hk' as sub_type,
            company_id as cid
        from ods.ods_prism1_company_hk_df
        where pt = ${pt}
            and dw_is_del = 0
            and company_id is not null
    ) union (
        select
            id as sub_id,
            code as sub_code,
            deleted as sub_deleted,
            'tw' as sub_type,
            company_id as cid
        from ods.ods_prism1_company_tw_df
        where pt = ${pt}
            and dw_is_del = 0
            and company_id is not null
    ) union (
        select
            id as sub_id,
            creditCode as sub_code,
            deleted as sub_deleted,
            'foundation' as sub_type,
            company_id as cid
        from ods.ods_prism1_foundation_df
        where pt = ${pt}
            and dw_is_del = 0
            and company_id is not null
    )
),
company_investor_path_count as (
    select
        company_id as gid,
        count(if(shareholder_entity_type=1, 1, null)) as company_investor_count,
        count(if(shareholder_entity_type=2, 1, null)) as human_investor_count
    from
        ods.ods_prism_shareholder_path_ratio_path_company_new_all_df
    where pt = ${pt} and dw_is_del = 0 and company_id % ${gid_mod} = 0
        and is_end = 1  -- 路径终点
        and shareholder_id != cast(company_id as string)  -- 股东自己的 排除个体户等
    group by company_id
),
company_investor_path as (
    select
        company_id as gid,
        shareholder_entity_type as sh_type,
        shareholder_id as sh_id,
        shareholder_name as sh_name,
        investment_ratio_total
    from
        ods.ods_prism_shareholder_path_ratio_path_company_new_all_df
    where pt = ${pt}
        and dw_is_del = 0
        and company_id % ${gid_mod} = 0
        and is_end = 1  -- 路径终点
        and shareholder_id != cast(company_id as string)  -- 股东自己的 排除个体户等
),
company_branch as (
    select
        c1.gid as gid,
        c2.gid as parent_gid,
        c1.cancel_date
    from (
        select *
        from company
        where parent_id > 0
            and sub_type = 'gsxt_gongsi'
    ) c1 inner join (
        select *
        from company
        where parent_id = 0
            and sub_type = 'gsxt_gongsi'
    ) c2 on c1.parent_id = c2.cid
    union
    select
        gid as gid,
        gid as parent_gid,
        cancel_date
        from company
        where parent_id = 0
            and sub_type = 'gsxt_gongsi' -- not branch
),
report_brief as (
    select
        company_id as cid,
        report_year,
        published_date -- 2020年05月12日
    from
        ods.ods_prism_annual_report_brief_df
    where
        pt = ${pt}
        and dw_is_del = 0
        and company_id % ${cid_mod} = 0
),
report as (
    select
        id as report_id,
        company_id as cid,
        report_year,
        manage_state
    from
        ods.ods_prism1_annual_report_df
    where
        pt = ${pt}
        and dw_is_del = 0
        and company_id % ${cid_mod} = 0
),
report_investor as (
    select
        annual_report_id as report_id,
        investor_id,
        investor_type,
        investor_name
    from
        ods.ods_prism1_report_shareholder_df
    where
        pt = ${pt}
        and dw_is_del = 0
),
company_max_report_year as ( -- 公司的最大年报日期
    select cid, max(report_year) as max_report_year from report group by cid
),
company_max_report_year_2 as ( -- 公司的最大年报日期（有股东的）
    select
        report.cid as cid,
        max(report.report_year) as max_report_year
    from
        report
    join report_investor
        on report.report_id = report_investor.report_id
    group by
        report.cid
),
patent_info as (
    select view_tmp.gid as gid,
    patent_type,   -- 1: 发明专利
    count(1) as patent_count
    from ods.ods_intellectual_property_info_company_patent_basic_info_index_df
    lateral view explode(split(company_ids, ';')) view_tmp as gid
    where pt = ${pt} and dw_is_del = 0 and nvl(company_ids, '') != ''
    group by view_tmp.gid, patent_type
),
partnership_info as (
    select
        company_gid as gid,
        executive as executive_name,
        executive_gid,
        executive_type,
        deleted
    from
        ods.ods_prism_company_partnership_info_df
    where
        pt = ${pt}
        and dw_is_del = 0
),
company_staff as (
    select
        company_id as cid,
        staff_id as staff_cid,
        staff_type_name as position
    from ods.ods_prism1_company_staff_df
    where pt = ${pt}
        and dw_is_del = 0
        and company_id % ${cid_mod} = 0
),
company_staff_sort as (
    select
        company_gid as gid,
        name as staff_name,
        if(type=1, 2, 1) as staff_type,
        position as positions,
        human_gid as staff_gid,
        create_time
    from ods.ods_prism_company_staff_sort_df
    where pt = ${pt}
        and dw_is_del = 0
        and company_gid % ${gid_mod} = 0
),
equity_ratio as (
    select
        company_graph_id as gid,
        shareholder_graph_id as sh_gid,
        shareholder_name as sh_name,
        shareholder_type as sh_type,
        `percent` as ratio, source
    from ods.ods_prism1_equity_ratio_df
    where pt = ${pt}
        and dw_is_del = 0
        and company_graph_id % ${gid_mod} = 0
        and deleted = 0
),
change_info as (
    select
        company_id as cid,
        renamed_change_item as change_item,
        content_before as cont_before,
        content_after as cont_after,
        to_date(change_time) as change_date
    from ods.ods_company_change_company_change_info_clean_df
    where pt = ${pt}
        and dw_is_del = 0
        and company_id % ${cid_mod} = 0
        and deleted = 0
),
history_name_info as (
    select
        company_gid as gid,
        company_name as his_name,
        start_time,
        end_time
    from ods.ods_prism_company_history_name_df
    where pt = ${pt}
        and dw_is_del = 0
        and company_gid % ${gid_mod} = 0
),
brief_cancel_info as (
    select
        company_id as cid,
        investor_commitment_oss_path as oss_path,
        deleted
    from ods.ods_prism1_company_brief_cancel_announcement_info_df
    where pt = ${pt}
        and dw_is_del = 0
        and company_id % ${cid_mod} = 0
),
abnormal_info as (
    select
        company_id as cid,
        to_date(put_date) as put_date,
        put_reason,
        to_date(remove_date) as remove_date,
        deleted
    from ods_prism1_company_abnormal_info_df
    where pt = ${pt}
        and dw_is_del = 0
        and company_id % ${cid_mod} = 0
)
 

-- 20240906_XA更新时间<核准日期+dday.sql  appr_change_recall
select  LEAST(GREATEST(t.diff_date, -1), 30), count(*) from
(
    select
        cf_cid,
        datediff(from_unixtime(cast(cf_1_msv_last_time as bigint) div 1000), to_date(cf_approved_time)) as diff_date
    from ods.ods_ns_online_company_base_info
    where pt='20240819'
        and cf_source = 'XA'
        and cf_from_time = '~&^'
        and cf_credit_code regexp '^91'
        and cf_reg_status  regexp '存续|在业|在营|开业'
)t group by LEAST(GREATEST(t.diff_date, -1), 30)

-- 重复的空壳名称
-- company_new as (
--     select regexp_replace(name, '[.,!?:;\'"\\()\\[\\]{}<>@#$%^&*\\-_=+~，。！？：；‘’“”（）《》【】「」…＝￥＊◎]', '') as name_hanzi, name, cid, gid
--     from company
--     where sub_type regexp 'gsxt|kongke' 
-- )

-- select c1.name_hanzi , count(*) from company_new c1 
--     inner join company_new c2
--         on c1.name_hanzi = c2.name_hanzi and c1.cid < c2.cid and length(c1.name_hanzi) > 4
--     group by c1.name_hanzi having count(*) >= 3;
    
-- select regexp_replace('ask1&*^%)()（）*&&……', '[.,!?:;\'"\\()\\[\\]{}<>@#$%^&*\\-_=+~，。！？：；‘’“”（）《》【】「」…]', '');

-- -- 工商指非工商数据统计
-- select * from company c join company c_ref
--     on c.cid_ref = cast(c_ref.cid as string) and c_ref.sub_type not regexp 'gsxt|kongke|prop2' and c.sub_type = 'prop2'
    
--  t as (
--     select company.*, brief_cancel_info.oss_path from brief_cancel_info 
--         join company 
--         on company.cid=brief_cancel_info.cid 
--             and company.sub_type regexp 'gsxt' 
--             and brief_cancel_info.deleted=0
-- )
-- select * from t t1 join t t2
--     on t1.oss_path=t2.oss_path 
--         and  t1.cid < t2.cid


-- -- 分月企业数量统计
-- select date_format(t.group_date, 'yyyyMM'), count(1) from
-- (
--     select *, GREATEST(end_date, to_date('2020-12-31')) as group_date from company
--     where sub_type regexp 'gsxt'
--     and end_date is not null
-- )t
-- group by date_format(t.group_date, 'yyyyMM');


-- company_old as (select * from ods.ods_prism1_company_df where pt = '20220101' and dw_is_del = 0)
-- select company.*, company_old.reg_status
-- from  company
--     left join company_old on company_old.id = company.cid
-- where company.sub_type regexp 'gsxt'
-- and company.reg_status regexp '吊销' and company.reg_status regexp '未注销'
-- and nvl(company_old.reg_status, '存续') not regexp '销' -- 2022年初已经是注销

-- 有股东的最大年报 股东id为0的
-- select * from company
--     join report on company.cid = report.cid
--     -- join company_max_report_year_2 on company.cid = company_max_report_year_2.cid and report.report_year = company_max_report_year_2.max_report_year
--     join report_investor on report.report_id = report_investor.report_id
-- where company.sub_type = 'gsxt_gongsi'
-- and company.org_type regexp '股份有限公司'
-- and company.org_type regexp '非上市'
-- and company.org_type not regexp '分'
-- and nvl(report_investor.investor_id, -1) = 0;

-- 最新年报无股东的
-- select * from company
--     join report on company.cid = report.cid
--     join company_max_report_year on company.cid = company_max_report_year.cid and report.report_year = company_max_report_year.max_report_year
--     left join report_investor on report.report_id = report_investor.report_id
-- where company.sub_type = 'gsxt_gongsi'
-- and company.org_type regexp '股份有限公司'
-- and company.org_type regexp '非上市'
-- and company.org_type not regexp '分'
-- and report_investor.report_id is null;

-- 年报股东id为0
-- select report.cid, report_investor.investor_name, count(1) as cnt from report_investor
-- join report on report.report_id = report_investor.report_id
-- join company on report.cid = company.cid
-- where report_investor.investor_id = 0 and company.sub_type = 'gsxt_gongsi'
-- group by report.cid, report_investor.investor_name

-- 年报股东为空壳的
-- select report.cid, report_investor.investor_name, count(1) as cnt from report_investor
-- join report on report.report_id = report_investor.report_id
-- join company on report.cid = company.cid
-- join company as company2 on report_investor.investor_id = company2.cid
-- where company.sub_type = 'gsxt_gongsi'
-- and report_investor.investor_id > 0 and investor_type = 2 and company2.sub_type = 'kongke'
-- group by report.cid, report_investor.investor_name

-- select substr(t.credit_code, 1, 2), count(*) from
-- (select upper(credit_code) as credit_code from company where length(credit_code) = 18 and sub_type != 'prop2' group by credit_code)t
-- group by substr(t.credit_code, 1, 2)

-- patent_info as (
--     select view_tmp.gid as gid,
--     patent_type,   -- 1: 发明专利
--     count(1) as patent_count
--     from ods.ods_intellectual_property_info_company_patent_basic_info_index_df
--     lateral view explode(split(company_ids, ';')) view_tmp as gid
--     where pt = ${pt} and dw_is_del = 0 and nvl(company_ids, '') != ''
--     group by view_tmp.gid, patent_type
-- ),

-- 股东数量超过50个
-- select * from
-- (select company.gid, max(source) as source1, min(source) as source2, count(1) as cnt from company
--     join equity_ratio on equity_ratio.gid = company.gid
-- where equity_ratio.source != 100 and company.sub_type regexp 'gsxt'
-- group by company.gid having count(1) >= 50)t
-- left join company company2 on company2.gid=t.gid
-- 无股东数据的
-- select company.* from company
--     left join equity_ratio on equity_ratio.gid = company.gid
-- where equity_ratio.gid is null and
-- company.sub_type regexp 'gsxt_gongsi'
-- and reg_status not regexp '销|迁'
-- and org_type not regexp '分|所有制|代表机构|合作社|办事处|集体|集团|国有经营单位'
-- and parent_id = 0
-- and crawledtime > '2020-01-01'

-- 每个group取其中一条
-- select t.gid, t.source
-- from (
--     select gid,
--           source,
--           row_number() over (partition by gid order by sh_gid) as rn
--     FROM equity_ratio
-- ) t
-- WHERE t.rn = 1;

-- select * from company where credit_code regexp '^9[1-3]33' and length(credit_code)=18 and sub_type regexp 'gsxt' and esta_date >= '2024-06-01';
-- select esta_date, credit_code  from company where length(credit_code)=18 and sub_type regexp 'gsxt' and  esta_date >= '2024-05-01' and esta_date < '2024-06-01';


-- select distinct credit_code, esta_date from company where length(credit_code)=18;
-- select deleted, count(*) from ods_prism1_company_double_random_check_result_info_df where pt='20240601' and dw_is_del = 0  group by deleted


-- select source_flag, count(1) as total from (
-- select case when source_flag like '%http://qyxy.baic.gov.cn%' then 'baic'
--     when source_flag like '%org%' then 'org'
--     when source_flag like '%npo%' then 'npo'
--     when source_flag like '%institution%' then 'institution'
--     else 'kong' end as source_flag
-- from ods.ods_prism1_company_df where pt = ${pt} and dw_is_del = 0 and (reg_status is null or reg_status = '' )
-- and (reg_number is  not null or reg_number <> '' ) and  (estiblish_time   is not null or estiblish_time <> '' )
-- and (legal_person_name is not null or legal_person_name <> '' ) and (property1  IS NOT NULL AND property1 <> '') and (property2  IS  NULL or property2 = '')
-- and dw_is_del=0 )temp
-- group by source_flag;

-- select distinct t1.credit_code from
--     (select credit_code, gid  from company where credit_code regexp '^914419' and esta_date between '2024-07-01' and '2024-07-10')t1
--     join (select gid from equity_ratio where sh_type = 1)t2 on t1.gid = t2.gid;

-- select * from (
-- select company_new.id as cid, 'move' as tp, company_old.reg_location as addr from  -- 通过登记机关来判断
--     (select * from ods.ods_prism1_company_df where pt = '20240701' and dw_is_del = 0 and id % ${cid_mod} = 0 and reg_institute regexp '北京.*通州') company_new
-- inner join
--     (select * from ods.ods_prism1_company_df where pt = '20240301' and dw_is_del = 0 and id % ${cid_mod} = 0 and reg_institute not regexp '通州') company_old
-- on company_new.id = company_old.id
--  union
--     (select id as cid, 'establish' as tp, reg_location as addr  -- 通过地址来判断
--     from ods.ods_prism1_company_df where pt = '20240701' and dw_is_del = 0 and id % ${cid_mod} = 0
--     and (reg_location regexp '北京.*通州' or reg_institute regexp '北京.*通州') and estiblish_time >= '2024-03-01')
-- union
-- (select company.id as cid, 'report' as tp, postal_address as addr from  -- 通过地址来判断
--     (select company_id as cid, postal_address from ods.ods_prism1_annual_report_df where pt = '20240701' and dw_is_del = 0
--     and company_id % ${cid_mod} = 0 and report_year = '2023' and postal_address regexp '北京.*通州'
--     )report inner join (
--         select * from ods.ods_prism1_company_df where pt = '20240701' and dw_is_del = 0 and id % ${cid_mod} = 0 and reg_location not regexp '通州'
--     )company on report.cid = company.id
-- )
-- ) t inner join company on t.cid = company.cid

-- select * from origindb_offline.origindb_prism1_big_shareholder_hi where pt in (20240706,20240713,20240715) and hh=23 and company_graph_id=31353524;
-- select * from origindb_offline.origindb_prism1_big_shareholder_hi where pt=20240711 and hh=23 and company_graph_id=*********;



-- 20240716 河北种植业数据导出
-- select province, city, district, esta_year, count(1), sum(reg_capital) from
-- (
--     select date_format(GREATEST(esta_date, to_date('2012-12-31')), 'yyyy') as esta_year, province, city, district, company_base.reg_capital from company
--     inner join company_base on company.gid = company_base.gid
--     where sub_type regexp 'gsxt' and esta_date < '2024-01-01' and company_base.cate_2 = '农业'
-- )t
-- group by province, city, district, esta_year;

-- sub_company相关统计 ---------------------------------------------------------------------------------------
-- 缺失sub_company表信息的
-- select * from company
--     left join sub_company on sub_company.sub_type = company.sub_type and sub_company.cid = company.cid
-- where company.sub_type not regexp 'gsxt' and company.sub_type != 'kongke'
-- and sub_company.sub_id is null;
-- select * from ods.ods_prism1_company_hk_df where pt = ${pt}  and id='1004055';  表有缺失数据

-- GS115 经营期限to_time 无固定期限更新
-- select * from company where company.sub_type = 'gsxt_gongsi' and credit_code regexp '^913502' and to_time is null and reg_status not regexp '销';

-- GS116 更加详细的企业类型
-- select * from company where company.sub_type = 'gsxt_gongsi' and company_org_type = '有限责任公司' and reg_status not regexp '销' and base='cq';

-- 空壳追查 cid: 2692124708  gid: 7049741798
-- select * from ods.ods_prism1_company_df where pt = '20240720' and parent_id='2692124708'  -- 总公司
-- select * from ods.ods_prism1_equity_ratio_df where pt = '20240720' and shareholder_graph_id='7049741798'  -- 股东
-- select * from ods.ods_prism1_report_shareholder_df where (pt = '20240720' or  pt = '20240718') and  annual_report_id in (3159825200, 2912248336, 2970469686) -- 年报股东
-- select * from ods.ods_company_all_count_hbase_merged_df where pt = '20240725' and company_id='7049741798'  -- dim count

-- select ent_type, count(*) from ods.ods_gsxt_company_supplement_df where pt = ${pt} and dw_is_del = 0 and company_id % ${cid_mod} = 0 group by ent_type;

-- 年报年份与企业成立日期矛盾的
-- select * from report join company on report.cid = company.cid
-- where company.sub_type regexp 'gsxt' and report.report_year < year(company.esta_date)
-- and (company.sub_type = 'gsxt_gongsi' and company.credit_code regexp '^92')

-- 20240819_2023年年报补充抓取
-- select * from company
--     left join report on company.cid = report.cid and report.report_year = 2023
--     inner join (
--         select distinct cid from abnormal_info where 
--             put_reason regexp '年度报告'
--             and put_date > '2024-01-01'
--             and remove_date < '2024-09-01'
--     )abnormal on company.cid = abnormal.cid
-- where
-- report.report_id is null 
-- and year(company.esta_date) < 2024 
-- and company.sub_type regexp 'gsxt_gongsi'
-- and company.reg_status regexp '存续|在业|在营|开业'
-- and credit_code not regexp '^9144' 

-- 20240820_sz非核变更新登记机关
-- select * from company where
-- company.sub_type regexp 'gsxt_gongsi' and company.reg_status regexp '存续|在业|在营|开业'
-- and credit_code regexp '^9144' and char_length(reg_institute) < 4

-- select credit_code, reg_number, esta_date from company where esta_date between '2024-01-01' and '2024-07-30' and length(credit_code) = 18 and credit_code not regexp '^9'

-- -- 20240820_xa_jyqx_repair
-- select cf_cid from (
--     select cf_cid from ods.ods_ns_online_company_base_info
--     where pt='20240819'
--     and cf_source = 'XA' 
--     and cf_from_time = '~&^'
--     and cf_credit_code regexp '^91'
--     and cf_reg_status  regexp '存续|在业|在营|开业'
-- )t join company on t.cf_cid = company.cid
-- where (company.from_time is not null and to_date(company.from_time) != company.esta_date) or (company.to_time is not null)



-- DATA124 gs_history_name_missing
-- select * from 
-- (
--     select 
--     cid,
--     change_date,
--     change_item,
--     REPLACE(REPLACE(cont_before, ')', '）'), '(', '（') as cont_before,
--     REPLACE(REPLACE(cont_after,')', '）'), '(', '（') as cont_after
--     from change_info 
--     where year(change_date) = 2022 and (change_item='名称' or change_item regexp '^名称变更' or change_item='公司名称变更')
-- ) change_t
-- inner join (
--     select * from company where sub_type regexp 'gsxt_gongsi'
--     -- and cid in ('11467487', '*********', '*********', '90098334', '58758252')
-- ) company_t 
--     on company_t.cid = change_t.cid 
--         and change_t.cont_after = company_t.name  -- 公司当前名称=变更记录后的名称
-- left join history_name_info his_name_t2  
--     on his_name_t2.gid = company_t.gid 
--         and his_name_t2.his_name = change_t.cont_before  -- 变更前=曾用名
-- where 
--     his_name_t2.gid is null


-- 