from datetime import datetime
import json
import re
from typing import Dict, List
import argparse
import csv
import time
from datetime import timedelta
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor

from gslib.schedule import immediately_gs

from dao.company import Company
from dao.company import CompanyDao
from dao.company import CompanyGraph
from dao.company import CompanyGraphDao
from dao.reports.annual_report import AnnualReport
from dao.reports.annual_report import AnnualReportDao
from dao.reports.annual_report_brief import AnnualReportBrief
from dao.reports.annual_report_brief import AnnualReportBriefDao
from dao.key_person.history_company_staff import HistoryCompanyStaff
from dao.key_person.history_company_staff import HistoryCompanyStaffDao
from dao.key_person.company_staff import CompanyStaff
from dao.key_person.company_staff import CompanyStaffDao
from dao.company_hk import CompanyHk
from dao.company_hk import CompanyHkDao
from clients.obs_client import OBSClient
from clients.redis.redis_queue import RedisQueue
from gslib.msv import msv_query_base_info, msv_query_list_dim
from libs.dt import to_date, to_datetime
from gslib.id_center import id_center_query, EntityType
from gslib.credit_code import credit_code_valid


logger = setup_logger()
fs = dict()
process_code_stat = Counter()

redis_queue = RedisQueue(name='normal_schedule_queue', max_length=1000000, **ConstantProps.PROPS_GS_REDIS_ONLINE)
obs_client = OBSClient(bucket_name='jindi-oss-gsxt')
company_dao = CompanyDao(max_write_per_minute=3000)
company_graph_dao = CompanyGraphDao()
history_company_staff_dao = HistoryCompanyStaffDao()
company_staff_dao = CompanyStaffDao()
company_hk_dao = CompanyHkDao()


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    DUP = auto()  # 已经生效过的
    EXCLUDE = auto()  # 非目标集合
    OK = auto()  # 本次目标集合


@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


def process(tc: TaskContext):
    cid = int(tc.inputs['tyc_company.id'])

    company: Company = company_dao.get(cid)
    assert company

    s = json.dumps({
        'companyId': str(cid),
        'companyName': company.name,
        'creditCode': company.credit_code,
        'dims': ['annualreport2', ],
        'scheduleReason': '0303_yh_report',
        'score': 5,
    }, ensure_ascii=False)
    if redis_queue.redis.zcard(redis_queue.name) > 500000:
        logger.warning(f'redis sleep for length ')
        time.sleep(5.0)
    ret = redis_queue.redis.zadd(
        redis_queue.name,
        {s: 100},
    )
    logger.info(f'OUTPUT {cid} {company.credit_code} ret={ret}')


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code not in [ProcessCode.INITIAL, ProcessCode.EXCLUDE]:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)
        for did, d in enumerate(reader):
            if did < args.input_csv_lid_min:
                continue
            if did >= args.input_csv_lid_max != -1:
                break
            d['did'] = did
            tc = TaskContext(inputs=d)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多线程处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='/Users/<USER>/Downloads/query-hive-518086', help='')
    ap.add_argument('--input-csv-lid-min', type=int, default=-1)
    ap.add_argument('--input-csv-lid-max', type=int, default=-1)
    ap.add_argument('--worker-num', type=int, default=8)

    main(ap.parse_args())
