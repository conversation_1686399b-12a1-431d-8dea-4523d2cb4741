import re
from typing import Dict
import argparse
import csv
import time
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info
from libs.concurrent import BoundedExecutor
from dao.company import CompanyGraph, CompanyGraphDao, Company, CompanyDao
from dao.human import HumanGraph, HumanGraphDao
from dao.investors.equity_ratio import EquityRatioDao, EquityRatio
from gslib.msv import msv_query_list_dim
from gslib.schedule import immediately_gs

logger = setup_logger()
fs = dict()
process_code_stat = Counter()

company_graph_dao = CompanyGraphDao()
human_graph_dao = HumanGraphDao()
company_dao = CompanyDao()
equity_ratio_dao = EquityRatioDao()


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    BAD_CG = auto()
    ER_INFO_SAME = auto()


@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


def process(tc: TaskContext):
    gid = int(tc.inputs['eq.company_graph_id'])
    cg: CompanyGraph = company_graph_dao.get(value=gid, field='graph_id')
    if not cg or cg.deleted != 0:
        tc.code = ProcessCode.BAD_CG
        return
    c: Company = company_dao.get(cg.cid)
    if not c:
        tc.code = ProcessCode.BAD_CG
        return
    if c.credit_code.startswith('93'):
        tc.code = ProcessCode.BAD_CG
        return
    sh_gids = set(er.shareholder_graph_id for er in equity_ratio_dao.get_many(value=gid, field='company_graph_id'))
    logger.info(f'{gid} >> {sh_gids}')

    msv_investors = msv_query_list_dim(
        cid=cg.cid,
        table_name='company_investor',
        ts=-1,
        source='gsxt_page',
    )
    logger.info(f'msv_investors {msv_investors}')
    msv_sh_gids = set()
    for msv_investor in msv_investors:
        investor_id = msv_investor['investor_id']
        investor_type = msv_investor['investor_type']
        sh_gid = 0
        if investor_type == 1:
            g = human_graph_dao.get_by_hid(investor_id)
            if g:
                sh_gid = g.hgid
        elif investor_type == 2:
            g = company_graph_dao.get_by_cid(investor_id)
            if g:
                sh_gid = g.cgid
        if sh_gid:
            msv_sh_gids.add(sh_gid)
    logger.info(f'{gid} >> {msv_sh_gids}')
    if msv_sh_gids == sh_gids:
        tc.code = ProcessCode.ER_INFO_SAME
        return
    immediately_gs(c.credit_code, reason='0718_drop_ch_investor_repair', interval_sec=1)


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code != ProcessCode.INITIAL:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)
        for did, d in enumerate(reader):
            if did < args.input_csv_lid_min:
                continue
            if did >= args.input_csv_lid_max != -1:
                break
            d['did'] = did
            tc = TaskContext(inputs=d)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多线程处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='20240718_gids', help='')
    ap.add_argument('--input-csv-lid-min', type=int, default=-1)
    ap.add_argument('--input-csv-lid-max', type=int, default=10)
    ap.add_argument('--worker-num', type=int, default=1)

    main(ap.parse_args())
    #
    # _d = {
    #     'did': 0,
    #     'company.name': 'name',
    #     'eq.company_graph_id': 3010578845,
    #     'company.credit_code': '91542233MAC694EK7T',
    #     'company.esta_date': '2009-03-02',
    # }
    # tc = TaskContext(inputs=_d)
    # process(tc)
    # logger.info(f'tc={tc}')
    #
