from typing import Optional
import csv
import time
from enum import Enum
from concurrent.futures import Future
import argparse
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info
from libs.concurrent import BoundedExecutor
from dao.company import CompanyDao, Company
from gslib.msv import msv_query_base_info
from resx.log import setup_logger

logger = setup_logger()

fs = dict()
processed = set()
process_code_stat = Counter()
company_dao = CompanyDao()


class ProcessCode(Enum):
    OK = '正常'
    DUP = '重复数据'
    EXCEPTION = '抛出异常'
    ERROR = '错误'


def at_haidian(d: dict) -> bool:
    reg_location = d.get('reg_location')
    reg_institute = d.get('reg_institute')
    if reg_institute:
        return '海淀' in reg_institute
    return '海淀区' in reg_location


def process(
        d: dict,
        writer: Optional[csv.DictWriter] = None,
) -> ProcessCode:
    cid = int(d['enterprise_search_basic.cid'])
    gid = int(d['enterprise_search_basic.gid'])
    c: Company = company_dao.get(cid)
    if not c:
        logger.warning(f'not company for {cid}')
        return ProcessCode.ERROR
    xa_version_list = msv_query_base_info(cid)
    bj_version_list = msv_query_base_info(cid, source='bjold')
    version_list = sorted(xa_version_list + bj_version_list, key=lambda x: x['approved_time'], reverse=True)
    version_list_by_appr = []
    for version in version_list:
        if len(version_list_by_appr) == 0 or version_list_by_appr[-1].get('approved_time') != version.get('approved_time'):
            version_list_by_appr.append(version)

    if len(version_list_by_appr) <= 1:
        logger.warning(f'bad  version_list_by_appr  {cid}  size={len(version_list_by_appr)}')
        return ProcessCode.ERROR

    for vid in range(1, len(version_list_by_appr)):
        version_to, version_from = version_list_by_appr[vid-1], version_list_by_appr[vid]
        if not at_haidian(version_from) and at_haidian(version_to):
            move_type = '迁入'
            location_from = version_from.get('reg_location')
            location_to = version_to.get('reg_location')
            move_date = version_to.get('approved_time')
            break
        if at_haidian(version_from) and not at_haidian(version_to):
            move_type = '迁出'
            location_from = version_from.get('reg_location')
            location_to = version_to.get('reg_location')
            move_date = version_to.get('approved_time')
            break
    else:
        logger.warning(f'bad xa_version data {cid} xa_version_list={version_list_by_appr}')
        return ProcessCode.ERROR

    output = {
        'url': f'https://www.tianyancha.com/company/{gid}',
        'cid': cid,
        'name': c.name,
        'code': c.credit_code,
        'move_type': move_type,
        'move_date': move_date,
        'location_from': location_from,
        'location_to': location_to,
    }
    logger.info(f'OUTPUT {output}')
    if writer:
        writer.writerow(output)
    return ProcessCode.OK


def callback_fn(f: Future):
    c = fs[f]
    del fs[f]
    try:
        ret: ProcessCode = f.result()
        process_code_stat[ret] += 1
    except Exception as e:
        logger.info(f'error process {c}  e={e} {get_stack_info()}')
        process_code_stat[ProcessCode.EXCEPTION] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
        open(f'{args.input_csv_name}.output.csv', 'w') as fout,
    ):
        reader = csv.DictReader(fin)
        writer = csv.DictWriter(
            f=fout,
            fieldnames=['url', 'cid', 'name', 'code', 'move_type', 'move_date', 'location_from', 'location_to'],
        )
        writer.writeheader()

        for did, d in enumerate(reader):
            # if did > 10:
            #     break
            d['did'] = did
            future: Future = worker_pool.submit(process, d, writer)
            fs[future] = d
            future.add_done_callback(callback_fn)
        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多任务处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='query-hive-437195', help='')
    ap.add_argument('--worker-num', type=int, default=4, help='')

    main(ap.parse_args())
    # process({'enterprise_search_basic.cid': '172151', 'enterprise_search_basic.gid': '7628730'}, None)
