import json
import re
from typing import Dict, List
import argparse
import csv
import time
from enum import Enum, auto
from pydantic import Field
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor
from gslib.schedule import immediately_gs
from dao.company import Company, CompanyDao
from dao.reports.annual_report import AnnualReportDao, AnnualReport
from dao.reports.annual_report_brief import AnnualReportBriefDao, AnnualReportBrief
from clients.redis.redis_queue import RedisQueue
from gslib.msv import msv_query_base_info
from libs.dt import to_date
from dao.company_history_name import CompanyHistoryName, CompanyHistoryNameDao
from dao.deps.mysql_dao import MySQLDao, BaseEntity


logger = setup_logger()
fs = dict()
process_code_stat = Counter()
company_dao = CompanyDao()


class PartnershipInfo(BaseEntity):
    id: int = Field(default=0)
    company_id: int
    executive: str
    executive_id: int
    executive_type: int
    represen: str
    represen_id: int
    deleted: int


class PartnershipInfoDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_partnership_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', PartnershipInfo)
        super().__init__(**kwargs)


partnership_info_dao = PartnershipInfoDao()


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()


@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


def process(tc: TaskContext):
    cid = tc.inputs["cid"]
    logger.info(f'PROCESS {cid}')
    items: List[PartnershipInfo] = list(partnership_info_dao.get_many(value=cid, field='company_id'))

    for item_a in items:
        if item_a.deleted != 0:
            continue
        del_a = False
        for item_b in items:
            if item_b.deleted != 0:
                continue
            if item_a.id > item_b.id and item_a.executive_id == item_b.executive_id:
                logger.info(f'{cid} DEL {item_a.id} by {item_b.id}')
                del_a = True
        if del_a:
            partnership_info_dao.delete(item_a.id)


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code != ProcessCode.INITIAL:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool:
        seen_cid_set = set()
        for line in open('20240830_remove_dup_executives.input.csv', 'r'):
            cid = int(line.strip())
            if cid in seen_cid_set:
                continue
            seen_cid_set.add(cid)
            tc = TaskContext(inputs={'iid': 0, 'cid': cid})
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)
        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='20240830_remove_dup_executives.py')
    ap.add_argument('--worker-num', type=int, default=4)

    main(ap.parse_args())
    # process(TaskContext(inputs={'iid': 0, 'cid': 193249369}))
