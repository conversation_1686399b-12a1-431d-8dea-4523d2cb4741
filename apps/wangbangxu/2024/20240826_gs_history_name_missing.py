import json
import re
from typing import Dict
import argparse
import csv
import time
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor
from gslib.schedule import immediately_gs
from dao.company import Company, CompanyDao
from dao.reports.annual_report import AnnualReportDao, AnnualReport
from dao.reports.annual_report_brief import AnnualReportBriefDao, AnnualReportBrief
from clients.redis.redis_queue import RedisQueue
from gslib.msv import msv_query_base_info
from libs.dt import to_date
from dao.company_history_name import CompanyHistoryName, CompanyHistoryNameDao

logger = setup_logger()
fs = dict()
process_code_stat = Counter()
company_dao = CompanyDao()
company_history_name_dao = CompanyHistoryNameDao(max_write_per_minute=1200)


redis_queue = RedisQueue(name='normal_schedule_queue', max_length=100000, **ConstantProps.PROPS_GS_REDIS_ONLINE)
#
# records: List[HkCompanyNameRecords] = list(self.hk_company_name_records_dao.get_many(value=item_id, field='hk_id'))
#
# if len(records) == 0:
#     return
#
# cg: CompanyGraph = self.company_graph_dao.get_by_cid(cid)
# if not cg:
#     return
#
# records.sort(key=lambda x: x.change_time)
#
# new_his_obj_list: List[CompanyHistoryName] = []
# for i in range(0, len(records) - 1):
#     his_obj_dict = dict(company_gid=cg.cgid, company_name=f'{records[i].history_name_cn} {records[i].history_name_en}')
#     if records[i].change_time:
#         his_obj_dict['start_time'] = f'{records[i].change_time.year}-{records[i].change_time.month:02d}'
#     if records[i + 1].change_time:
#         his_obj_dict['end_time'] = f'{records[i + 1].change_time.year}-{records[i + 1].change_time.month:02d}'
#
#     his_obj: CompanyHistoryName = CompanyHistoryName.from_dict(his_obj_dict)
#     if not his_obj:
#         continue
#     new_his_obj_list.append(his_obj)
#
# self.company_history_name_dao.save_by_group(
#     items=new_his_obj_list,
#     group_fields=['company_gid', ],
#     key_fields=['company_name', ],
#     save_mode=0,
#     group_values=[cg.cgid, ],
# )

class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    BAD_CONT_NAME = auto()

    OK = auto()


@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


def process(tc: TaskContext):
    gid = int(tc.inputs['company_t.gid'])
    change_date = tc.inputs['change_t.change_date']
    cont_name = tc.inputs['change_t.cont_before']
    name = tc.inputs['company_t.name']
    esta_date = tc.inputs['company_t.esta_date']

    if len(cont_name) <= 4 or '*' in cont_name or re.search(r'名称|字号|\s', cont_name) or cont_name == name:
        tc.code = ProcessCode.BAD_CONT_NAME
        return

    start_date, end_date = esta_date[:7], change_date[:7]
    target_item = None
    for item in company_history_name_dao.get_many(field='company_gid', value=gid):
        item: CompanyHistoryName
        if item.company_name == cont_name:
            target_item = item
        else:
            if item.start_time > start_date:
                start_date = item.start_time
            if start_date < item.end_time < end_date:
                start_date = item.end_time
    logger.info(f'target_item {target_item}')
    logger.info(f'start_date {start_date} end_date={end_date}')

    if target_item is None:
        target_item = CompanyHistoryName(
            company_gid=gid,
            company_name=cont_name,
            start_time=start_date,
            end_time=end_date
        )
    else:
        target_item.start_time = start_date
        target_item.end_time = end_date
    changed, insert, item_id = company_history_name_dao.save_by_cmp(
        item=target_item,
        fields=['company_gid', 'company_name'],
    )
    logger.info(f'gid={gid} name={name} change_date={change_date} cont_name={cont_name} {insert} {item_id}')


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code != ProcessCode.INITIAL:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)
        for did, d in enumerate(reader):
            if did < args.input_csv_lid_min:
                continue
            if did >= args.input_csv_lid_max != -1:
                break
            d['did'] = did
            tc = TaskContext(inputs=d)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多线程处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='20240826_gs_history_name_missing.input', help='')
    ap.add_argument('--input-csv-lid-min', type=int, default=11)
    ap.add_argument('--input-csv-lid-max', type=int, default=21)
    ap.add_argument('--worker-num', type=int, default=1)

    main(ap.parse_args())
    #
    # _d = {
    #     'did': 0,
    #     'company.name': 'name',
    #     'eq.company_graph_id': 3010578845,
    #     'company.credit_code': '91542233MAC694EK7T',
    #     'company.esta_date': '2009-03-02',
    # }
    # tc = TaskContext(inputs=_d)
    # process(tc)
    # logger.info(f'tc={tc}')
    #
