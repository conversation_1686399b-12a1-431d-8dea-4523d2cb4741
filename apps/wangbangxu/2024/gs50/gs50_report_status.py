import csv
import time
from enum import Enum
from concurrent.futures import Future
import argparse
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info
from libs.concurrent import BoundedExecutor
from apps.wangbangxu.qx_eid import QxbCodeEidDao, QxbCodeEid
from dao.qxb.report_detail import ReportDetailDao, ReportDetail
from dao.reports.annual_report import AnnualReportDao, AnnualReport
from dao.company import CompanyDao, Company
logger = setup_logger()

fs = dict()
processed = set()
process_code_stat = Counter()
qx_code_eid_dao = QxbCodeEidDao()
qx_report_dao = ReportDetailDao()
report_dao = AnnualReportDao()
company_dao = CompanyDao()


class ProcessCode(Enum):
    OK = '正常'
    DUP = '重复数据'
    EXCEPTION = '抛出异常'
    ERROR = '错误'


def process(
        d: dict,
) -> ProcessCode:
    report_year = d['report.report_year']
    cid = int(d['report.cid'])

    company: Company = company_dao.get(cid)
    if not company or len(company.credit_code or '') != 18:
        logger.warning(f'not cid from company {cid}')
        return ProcessCode.ERROR

    qx_eid: QxbCodeEid = qx_code_eid_dao.get(value=company.credit_code, field='credit_code')
    if not qx_eid:
        logger.warning(f'not eid for {company.credit_code}')
        return ProcessCode.ERROR

    qx_report: ReportDetail = qx_report_dao.get_by_eid_year(qx_eid.eid, report_year)
    if not qx_report:
        logger.warning(f'not qx_report for {company.credit_code} {qx_eid.eid} {report_year}')
        return ProcessCode.ERROR

    report: AnnualReport = report_dao.get_ex(values=[cid, report_year], fields=['company_id', 'report_year'])
    if not report:
        logger.warning(f'no brief for {cid} {report_year}')
        return ProcessCode.ERROR
    report.manage_state = qx_report.status
    ret = report_dao.save_by_cmp(report, fields=['company_id', 'report_year'])
    logger.info(f'{qx_report.status} -> {report.manage_state} {ret}')
    return ProcessCode.OK


def callback_fn(f: Future):
    c = fs[f]
    del fs[f]
    try:
        ret: ProcessCode = f.result()
        process_code_stat[ret] += 1
    except Exception as e:
        logger.info(f'error process {c}  e={e} {get_stack_info()}')
        process_code_stat[ProcessCode.EXCEPTION] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)

        for did, d in enumerate(reader):
            # if did > 10:
            #     break
            d['did'] = did
            future: Future = worker_pool.submit(process, d)
            fs[future] = d
            future.add_done_callback(callback_fn)
        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多任务处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='gs50', help='')
    ap.add_argument('--worker-num', type=int, default=4, help='')

    main(ap.parse_args())
    # process({'enterprise_search_basic.cid': '172151', 'enterprise_search_basic.gid': '7628730'}, None)
