import re
from typing import Dict
import argparse
import csv
import time
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info
from libs.concurrent import BoundedExecutor
from dao.company import CompanyGraph, CompanyGraphDao, Company, CompanyDao
from dao.npo.npo import Npo, NpoDao
from dao.organization_info import OrganizationInfo, OrganizationInfoDao
from dao.qxb.enterprise import Enterprise, EnterpriseDao
from apps.wangbangxu.qx_eid import QxbCodeEid, QxbCodeEidDao


logger = setup_logger()
fs = dict()
process_code_stat = Counter()
company_graph_dao = CompanyGraphDao()
npo_dao = NpoDao()
org_dao = OrganizationInfoDao()
company_dao = CompanyDao()
qxb_code_eid_dao = QxbCodeEidDao()
qx_enterprise_dao = EnterpriseDao()


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    NOT_C = auto()
    NOT_TASK = auto()
    NOT_CG = auto()

    NPO_NO_DETAIL = auto()
    NPO_CREDIT_BAD_ORG_0 = auto()
    NPO_CREDIT_BAD_ORG_1 = auto()
    NPO_SF_TO_ORG = auto()
    NPO_OTHERS = auto()

    ORG_NO_DETAIL = auto()

    NO_QX = auto()
    ORG_QX_UPDATE = auto()
    NPO_QX_UPDATE = auto()


@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


mig_npo_org_fp = open('mig_npo_org.txt', 'w')
npo_org_new_fp = open('npo_org_new.txt', 'w')


def process(tc: TaskContext):
    cid = int(tc.inputs['ods_prism1_company_df.id'])
    c: Company = company_dao.get(cid)
    if not c:
        tc.code = ProcessCode.NOT_C
        return
    if len(c.reg_status or '') > 0:
        tc.code = ProcessCode.NOT_TASK
        return
    sf = c.source_flag or ''
    if 'npo' not in sf and 'org' not in sf:
        tc.code = ProcessCode.NOT_TASK
        return

    cg: CompanyGraph = company_graph_dao.get_by_cid(cid)
    if not cg:
        tc.code = ProcessCode.NOT_CG
        return

    if 'npo' in sf:
        npo: Npo = npo_dao.get(cid, field='company_id')
        if not npo:
            tc.code = ProcessCode.NPO_NO_DETAIL
            return
        if re.match(r'8', npo.unified_social_credit_code) and len(npo.unified_social_credit_code) == 18:
            org: OrganizationInfo = org_dao.get(npo.unified_social_credit_code, field='unified_social_credit_code')
            if org and org.company_id:
                if cid == org.company_id:
                    tc.code = ProcessCode.NPO_SF_TO_ORG
                    company_dao.update_source_flag(cid=c.cid, name=c.name, prefix='org')
                    # 然后再按照org处理一次
                    return
                tc.code = ProcessCode.NPO_CREDIT_BAD_ORG_1
                mig_npo_org_fp.write(f'{cid} {cg.cgid} {org.company_id} {org.company_gid}\n')
                return
            else:
                tc.code = ProcessCode.NPO_CREDIT_BAD_ORG_0
                npo_org_new_fp.write(f'{cid} {cg.cgid} {npo.unified_social_credit_code}\n')
                return
        qxb_code_eid: QxbCodeEid = qxb_code_eid_dao.get(value=cid, field='cid')
        if not qxb_code_eid or not qxb_code_eid.eid:
            tc.code = ProcessCode.NO_QX
            return
        qx_enterprise: Enterprise = qx_enterprise_dao.get(value=qxb_code_eid.eid, field='eid')
        if not qx_enterprise:
            tc.code = ProcessCode.NO_QX
            return
        tc.context['qx_status'] = qx_enterprise.reg_status
        if npo.status == qx_enterprise.reg_status:
            npo.status = qx_enterprise.reg_status + ' '
        else:
            npo.status = qx_enterprise.reg_status

        npo_dao.save_by_cmp(item=npo, fields=['id', ])
        tc.code = ProcessCode.NPO_QX_UPDATE

    elif 'org' in sf:
        org: OrganizationInfo = org_dao.get(value=cid, field='company_id')
        if not org:
            tc.code = ProcessCode.ORG_NO_DETAIL
            return

        qxb_code_eid: QxbCodeEid = qxb_code_eid_dao.get(value=cid, field='cid')
        if not qxb_code_eid or not qxb_code_eid.eid:
            tc.code = ProcessCode.NO_QX
            return
        qx_enterprise: Enterprise = qx_enterprise_dao.get(value=qxb_code_eid.eid, field='eid')
        if not qx_enterprise:
            tc.code = ProcessCode.NO_QX
            return
        tc.context['qx_status'] = qx_enterprise.reg_status
        if org.reg_status == qx_enterprise.reg_status:
            org.reg_status = qx_enterprise.reg_status + ' '
        else:
            org.reg_status = qx_enterprise.reg_status
        org_dao.save_by_cmp(item=org, fields=['id', ])
        tc.code = ProcessCode.ORG_QX_UPDATE


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code != ProcessCode.INITIAL:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)
        for did, d in enumerate(reader):
            if did < args.input_csv_lid_min:
                continue
            if did >= args.input_csv_lid_max != -1:
                break
            d['did'] = did
            tc = TaskContext(inputs=d)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多线程处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='input.npo', help='')
    ap.add_argument('--input-csv-lid-min', type=int, default=-1)
    ap.add_argument('--input-csv-lid-max', type=int, default=-1)
    ap.add_argument('--worker-num', type=int, default=8)

    main(ap.parse_args())

    #
    # _d = {
    #     'did': 0,
    #     'company.name': 'name',
    #     'company.cid': '2445056223',
    #     'company.credit_code': '91542233MAC694EK7T',
    #     'company.esta_date': '2009-03-02',
    # }
    # logger.info(f'ret={process(_d)} d={_d}')
