import csv
import time
from enum import Enum
from concurrent.futures import Future
import argparse
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor
from dao.company import CompanyDao, Company
from dao.deps.mysql_dao import MySQLDao
from gslib.msv import msv_query_base_info
from libs.dt import to_datetime

logger = setup_logger()

fs = dict()
processed = set()
process_code_stat = Counter()
company_dao = CompanyDao()


class ProcessCode(Enum):
    OK = '正常'
    DUP = '重复数据'
    EXCEPTION = '抛出异常'
    ERROR = '错误'


def process(
        d: dict,
) -> ProcessCode:
    cid = int(d['company.cid'])
    name = d['company.name']

    msv = msv_query_base_info(cid=cid)
    if len(msv) == 0:
        return ProcessCode.OK
    msv_cur = msv[0]
    status = msv_cur.get('reg_status', '')
    if '销' in status:
        return ProcessCode.OK
    if all('销' not in x.get('reg_status') for x in msv):
        return ProcessCode.OK
    msv_last_time = to_datetime(msv_cur.get('msv_last_time'))

    logger.info(f'msv={status} name={name} {msv_last_time} {msv_cur}')

    return ProcessCode.OK


def callback_fn(f: Future):
    c: dict = fs[f]
    del fs[f]
    try:
        ret: ProcessCode = f.result()
        process_code_stat[ret] += 1
    except Exception as e:
        logger.info(f'error process {c}  e={e} {get_stack_info()}')
        process_code_stat[ProcessCode.EXCEPTION] += 1
    # c.setdefault('canceled', '-')
    # c.setdefault('abnormal', '-')
    # writer.writerow(c)
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)

        for did, d in enumerate(reader):
            # if did > 1000:
            #     break
            d['did'] = did
            future: Future = worker_pool.submit(process, d)
            fs[future] = d
            future.add_done_callback(callback_fn)
        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多任务处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='gs86', help='')
    ap.add_argument('--worker-num', type=int, default=4, help='')

    main(ap.parse_args())
    #
    # _d = {
    #     'did': 0,
    #     'company.name': 'name',
    #     'company.cid': '*********',
    # }
    # logger.info(f'ret={process(_d)} d={_d}')
