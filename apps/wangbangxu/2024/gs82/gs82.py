import csv
import time
from enum import Enum
from concurrent.futures import Future
import argparse
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor
from dao.company import CompanyDao, Company
from dao.deps.mysql_dao import MySQLDao
from gslib.id_center import id_center_query, EntityType
logger = setup_logger()

fs = dict()
processed = set()
process_code_stat = Counter()
company_dao = CompanyDao()
company_abnormal_dao = MySQLDao(
    **ConstantProps.PROPS_GS_OUTER_RW,
    db_tb_name='prism.company_abnormal_info',
)


writer = csv.DictWriter(
    f=open('gs82.out.csv', 'w'),
    fieldnames=['did', 'name', 'name_clean', 'canceled', 'abnormal'],
)
writer.writeheader()


class ProcessCode(Enum):
    OK = '正常'
    DUP = '重复数据'
    EXCEPTION = '抛出异常'
    ERROR = '错误'

name_mapping = {
    '优炫软件股份有限公司': '北京优炫软件股份有限公司',
    '酷我音乐': '北京酷我科技有限公司',
    '盛威时代科技集团': '盛威时代科技股份有限公司',
    '融360集团公司': '北京融世纪信息技术有限公司',
    '北京一亩田网络科技有限公司': '北京一人一亩田网络科技有限公司',
    '北京文通科技有限公司党支部': '北京文通科技有限公司',
    '北京飞天诚信科技股份有限公司': '飞天诚信科技股份有限公司',
    '国双科技': '北京国双科技有限公司',
    '天目科技': '北京天目科技有限公司',
    '千里日成': '北京国双千里科技有限公司',
    '麦迪斯顿（北京）医疗信息科技邮箱公司': '麦迪斯顿（北京）医疗科技有限公司',
    '北京豪腾嘉科技有限公司': '北京豪腾嘉科科技有限公司',
    '福建省天泉教育科技有限公司北京分公司': '福建天泉教育科技有限公司北京分公司',
    '北京迪威特科科技有限公司': '北京迪威特科技有限公司',
    '北京信恒创科发展有限公司': '北京信恒创科技发展有限公司',
    '北京市北京勤实信息技术有限公司': '北京勤实信息技术有限公司',
    '曙光数据基础设施基础设施（北京）股份有限公司': '曙光数据基础设施创新技术（北京）股份有限公司',
    '北京旭日佳业网络技术邮箱公司': '北京旭日佳业网络技术有限公司',
    '北京元生华网软件 有限公司': '北京元生华网软件有限公司',
    '北京选过客信息技术股份有限公司': '北京炫果壳信息技术股份有限公司',
    '优视米': '北京优视米网络科技有限公司',
    '得到APP': '北京思维造物信息科技股份有限公司',
    '北京市时代星盟科技股份有限公司': '北京时代星盟科技股份有限公司',
    '道明伟业': '道明伟业（北京）网络信息科技有限公司',
    '博易创为': '博易创为（北京）数字传媒股份有限公司',
    '安捷智合': '北京安捷智合科技有限公司',
    '省广聚合（北京）数字技术有限公司': '省广聚合（北京）数字技术有限公司',
    '北京华创方舟科技集体有限公司': '北京华创方舟科技集团有限公司',
    'VIPKID在线少儿英语': '北京大米科技有限公司',
    '中科砚云科技有限公司': '北京中科砚云科技有限公司',
    '小米科技有限责任公司（小米集团）': '小米科技有限责任公司',
    '人大数媒体科技（北京）有限公司': '人大数媒科技（北京）有限公司',
    '北京华萍科技集团有限公司': '北京华苹科技集团有限公司',
    '北京世纪好未来教育科技有限公司（好未来）': '北京世纪好未来教育科技有限公司',
    '北京趣拿软件科技有限公司（去哪儿网）': '北京趣拿软件科技有限公司',
    '北京嘀嘀无限科技发展有限公司（滴滴）': '北京嘀嘀无限科技发展有限公司',
    '北京便利蜂连锁商业有限公司（便利蜂）': '北京便利蜂连锁商业有限公司',
    '作业帮教育科技(北京)有限公司（作业帮）': '作业帮教育科技（北京）有限公司',
    '北京佳讯飞鸿电器股份有限公司': '北京佳讯飞鸿电气股份有限公司',
    '北京淘友天下科技发展有限公司（脉脉）': '北京淘友天下科技发展有限公司',
    '北京多牛互动传媒股份有限公司（人人网）': '北京多牛互动传媒股份有限公司',
    '北京金堤科技有限公司（天眼查）': '北京金堤科技有限公司',
    '北京多氪信息科技有限公司（36氪）': '北京多氪信息科技有限公司',
    '百度公司': '北京百度网讯科技有限公司',
    '新浪公司': '新浪网技术（中国）有限公司',
    '搜狐公司': '北京搜狐新媒体信息技术有限公司',
    '网易传媒': '网易传媒科技（北京）有限公司',
    '易车公司': '北京易车互联信息技术有限公司',
    '第一视频': '第一视频集团有限公司',
    '多点': '多点生活（中国）网络科技有限公司',
    '中科讯飞互联(北京)信息科技有限公司': '科大讯飞（北京）有限公司',
}


def process(
        d: dict,
) -> ProcessCode:
    name = d['name']
    name_clean = name_mapping.get(name, name)
    d['name_clean'] = name_clean
    ent_type, eid = id_center_query(name=name_clean)
    if ent_type != EntityType.ORG:
        logger.info(f'bad name {name} {name_clean}')
        return ProcessCode.ERROR
    c: Company = company_dao.get(eid)
    if not c or not c.establish_date:
        logger.info(f'empty name {name} {name_clean}')
        return ProcessCode.ERROR
    d['name_clean'] = c.name
    d['canceled'] = '是' if '销' in c.reg_status else '否'
    d['abnormal'] = '是' if company_abnormal_dao.get_ex(values=[eid, 0], fields=['company_id', 'deleted']) else '否'
    return ProcessCode.OK


def callback_fn(f: Future):
    c: dict = fs[f]
    del fs[f]
    try:
        ret: ProcessCode = f.result()
        process_code_stat[ret] += 1
    except Exception as e:
        logger.info(f'error process {c}  e={e} {get_stack_info()}')
        process_code_stat[ProcessCode.EXCEPTION] += 1
    c.setdefault('canceled', '-')
    c.setdefault('abnormal', '-')
    writer.writerow(c)
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)

        for did, d in enumerate(reader):
            # if did > 100:
            #     break
            d['did'] = did
            future: Future = worker_pool.submit(process, d)
            fs[future] = d
            future.add_done_callback(callback_fn)
        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多任务处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='gs82', help='')
    ap.add_argument('--worker-num', type=int, default=4, help='')

    main(ap.parse_args())

    # _d = {
    #     'name': '宁夏凯捷装饰设计有限公司',
    #     'did': 0,
    # }
    # logger.info(f'ret={process(_d)} d={_d}')
