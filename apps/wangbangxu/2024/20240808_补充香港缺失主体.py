import re
from typing import Dict
import argparse
import csv
import time
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info
from libs.concurrent import BoundedExecutor
from dao.company import CompanyGraph, CompanyGraphDao, Company, CompanyDao
from dao.human import HumanGraph, HumanGraphDao
from dao.investors.equity_ratio import EquityRatioDao, EquityRatio
from gslib.schedule import immediately_gs
from dao.company_hk import CompanyHkDao
from apps.octopus.utils.entry_manager import EntryManager, OctopusConfManager
logger = setup_logger()
fs = dict()
process_code_stat = Counter()
company_hk_dao = CompanyHkDao()
entry_mgr = EntryManager(conf_manager=OctopusConfManager(json_conf_name='hk.brno.json'))

f = open('20240808_补充香港缺失主体.miss.txt', 'a')


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()
    BAD_CG = auto()
    DUP = auto()


@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


def process(tc: TaskContext):
    brno = tc.inputs['BRN']
    if company_hk_dao.get(value=brno, field='br_num'):
        # logger.info(f'exists {brno}')
        tc.code = ProcessCode.DUP
        return
    f.write(brno + '\n')


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code != ProcessCode.INITIAL:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)
        for did, d in enumerate(reader):
            if did < args.input_csv_lid_min:
                continue
            if did >= args.input_csv_lid_max != -1:
                break
            d['did'] = did
            tc = TaskContext(inputs=d)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多线程处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='20240808_补充香港缺失主体.company', help='')
    ap.add_argument('--input-csv-lid-min', type=int, default=330140)
    ap.add_argument('--input-csv-lid-max', type=int, default=-1)
    ap.add_argument('--worker-num', type=int, default=8)

    main(ap.parse_args())
    #
    # _d = {
    #     'did': 0,
    #     'company.name': 'name',
    #     'eq.company_graph_id': 3010578845,
    #     'company.credit_code': '91542233MAC694EK7T',
    #     'company.esta_date': '2009-03-02',
    # }
    # tc = TaskContext(inputs=_d)
    # process(tc)
    # logger.info(f'tc={tc}')
    #
