import csv
from datetime import date
import time
import json
from enum import Enum
from concurrent.futures import Future
import argparse
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info
from libs.concurrent import BoundedExecutor
from clients.obs_client import OBSClient
from libs.dt import to_datetime, to_date
from gslib.schedule import immediately_gs
from gslib.credit_code import credit_code_valid
from dao.company import CompanyDao, Company
from entity.eventlog import Eventlog, SpiderCode

logger = setup_logger()

fs = dict()
processed = set()
process_code_stat = Counter()
company_dao = CompanyDao()
obs_client = OBSClient()


class ProcessCode(Enum):
    OK = '正常'
    DUP = '重复数据'
    EXCEPTION = '抛出异常'
    ERROR = '错误'
    IGNORE_CREDIT_CODE = '排除逻辑-统一信用代码'
    IGNORE_ESTA_DATE = '排除逻辑-成立日期'
    DONE_SEARCH_EMPTY = '完成-搜索无结果'
    DONE_SUCCESS = '完成-股东已抓取成功'

    TODO_CODE_NOT_0 = '抓取-无股东数据'


SCHEDULE_REASON = '0628_missing_investor'


def process(
        d: dict,
) -> ProcessCode:
    cid = int(d['company.cid'])
    task_id = cid

    # gid = int(d['company.gid'])
    credit_code = d['company.credit_code']
    if not credit_code_valid(credit_code) or credit_code[0] != '9':
        logger.info(f'task_id={task_id} bad credit_code {d}')
        return ProcessCode.IGNORE_CREDIT_CODE
    esta_date_s = d['company.esta_date']
    esta_date = to_date(esta_date_s)
    if not esta_date or esta_date < date(year=2000, month=1, day=1) or esta_date > date(year=2024, month=6, day=17):
        logger.info(f'task_id={task_id} bad esta_date {d}')
        return ProcessCode.IGNORE_ESTA_DATE

    need_crawl = True
    for path, modify_dt in obs_client.list2(f'octopus/company/0/{cid}/octopus_entry-company-{cid}-XA'):
        # octopus_entry-company-2849048-bj-1710296634-9.json
        if not path.endswith('9.json') or modify_dt < to_datetime('2024-06-01 00:00:00'):
            continue
        data = obs_client.get(path)
        if SCHEDULE_REASON not in data:
            continue
        eventlog: Eventlog = Eventlog.from_dict(json.loads(data))
        if eventlog.selector.reason != SCHEDULE_REASON:
            continue
        logger.info(f'task_id={task_id} eventlog={data}')
        if eventlog.spider_code == SpiderCode.SEARCH_EMPTY:
            logger.info(f'task_id={task_id} DONE_SEARCH_EMPTY')
            need_crawl = False
            break
        dim_code = eventlog.parser.get('dim_code', {}).get('shareholder', -1)
        if dim_code == 0:
            logger.info(f'task_id={task_id} DONE_SUCCESS')
            need_crawl = False
            break
        # elif dim_code > 0:
        #     logger.info(f'task_id={task_id} code={dim_code} TODO_CODE_NOT_0')
        #     immediately_gs(credit_code=credit_code, reason='0628_missing_investor', interval_sec=1)
        #     return ProcessCode.TODO_CODE_NOT_0
        # else:
        #     logger.info(f'task_id={task_id} bad code eventlog={data}')
    if not need_crawl:
        logger.info(f'task_id={task_id} already DONE')
        return ProcessCode.DONE_SUCCESS
    logger.info(f'task_id={task_id} need crawl')
    immediately_gs(credit_code=credit_code, reason='0628_missing_investor', interval_sec=1)
    return ProcessCode.OK


def callback_fn(f: Future):
    c: dict = fs[f]
    del fs[f]
    try:
        ret: ProcessCode = f.result()
        process_code_stat[ret] += 1
    except Exception as e:
        logger.info(f'error process {c}  e={e} {get_stack_info()}')
        process_code_stat[ProcessCode.EXCEPTION] += 1
    # c.setdefault('canceled', '-')
    # c.setdefault('abnormal', '-')
    # writer.writerow(c)
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)

        for did, d in enumerate(reader):
            if did < 100:
                continue
            if did > 200:
                break
            d['did'] = did
            future: Future = worker_pool.submit(process, d)
            fs[future] = d
            future.add_done_callback(callback_fn)
        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多任务处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='gs92', help='')
    ap.add_argument('--worker-num', type=int, default=4, help='')

    # main(ap.parse_args())

    _d = {
        'did': 0,
        'company.name': 'name',
        'company.cid': '2445056223',
        'company.credit_code': '91542233MAC694EK7T',
        'company.esta_date': '2009-03-02',
    }
    logger.info(f'ret={process(_d)} d={_d}')
