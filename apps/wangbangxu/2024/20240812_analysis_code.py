from collections import Counter
from gslib.credit_code import credit_code_incr

MAX_GAP = 50


class CodeInfo:
    def __init__(self, s):
        code, dt = s
        self.cate = code[:2]
        self.area_code = code[0:8]
        self.org_code = code[8:16]
        self.org_code_seq = int(self.org_code[5:])
        self.org_code_head = self.org_code[:5]


# 52659001MJX964511R
seg_start = '0' * 18
seg_end = '0' * 18
area_code_set = set()
code_info_last = None

if __name__ == '__main__':
    code_lst = [('0' * 18, '')]
    with open('20240812_analysis_code.in.txt', 'r') as f:
        code_lst += [line_.strip().split() for line_ in f]
    code_lst += [('9' * 18, '')]

    start = 0
    for idx in range(1, len(code_lst)):
        continuous = True
        if CodeInfo(code_lst[idx]).org_code_head != CodeInfo(code_lst[idx - 1]).org_code_head:
            continuous = False
        if CodeInfo(code_lst[idx]).org_code_seq > CodeInfo(code_lst[idx - 1]).org_code_seq + MAX_GAP:
            continuous = False
        if not continuous:
            if idx - start > 50:
                # print(f'start {code_lst[start]} {code_lst[idx - 1]} {idx - start} ||| {code_lst[idx]}')
                area_code = Counter(CodeInfo(code_lst[i]).area_code for i in range(start, idx))
                cate = set(CodeInfo(code_lst[i]).cate for i in range(start, idx))
                # print(f'"{CodeInfo(code_lst[start]).org_code_head}": {dict(area_code)},')
                for incr in range(0, 3):
                    code_new = credit_code_incr(code_lst[idx-1][0], incr)
                    print(f'{code_new}')
            start = idx

