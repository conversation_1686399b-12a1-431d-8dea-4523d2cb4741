from typing import Optional
import csv
import time
from enum import Enum
from queue import Queue
from concurrent.futures import Future
import argparse
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info
from libs.concurrent import BoundedExecutor
from dao.company import CompanyDao, Company, CompanyGraphDao, CompanyGraph
from dao.company_hk import CompanyHk, CompanyHkDao
from dao.investors.equity_ratio import EquityRatioDao, EquityRatio
from gslib.id_center import id_center_query, EntityType

logger = setup_logger()

fs = dict()
processed = set()
process_code_stat = Counter()
company_dao = CompanyDao()
company_hk_dao = CompanyHkDao()
company_graph_dao = CompanyGraphDao()
er_dao = EquityRatioDao()


class ProcessCode(Enum):
    OK = '正常'
    DUP = '重复数据'
    EXCEPTION = '抛出异常'
    ERROR = '错误'


def process(
        d: dict,
        writer: Optional[csv.DictWriter] = None,
        stat_writer: Optional[csv.DictWriter] = None,
) -> ProcessCode:
    name, code = d['name'], d['code']

    # if name in processed:
    #     logger.warning(f'重复数据 {d}')
    #     return ProcessCode.DUP
    # processed.add(name)

    if len(code) == 18:
        ent_type, cid = id_center_query(credit_no=code)
        if ent_type != EntityType.ORG:
            logger.warning(f'bad company {code} {ent_type}')
            return ProcessCode.ERROR
    elif len(code) == 8:
        company_hk: CompanyHk = company_hk_dao.get(value=code, field='br_num')
        if not company_hk or company_hk.company_id is None:
            logger.warning(f'bad company_hk {code} {company_hk}')
            return ProcessCode.ERROR
        cid = company_hk.company_id
    else:
        ent_type, cid = id_center_query(name=name, type=EntityType.ORG)
        if ent_type != EntityType.ORG:
            logger.warning(f'bad company {code} {name}')
            return ProcessCode.ERROR

    logger.info(f'cid = {cid}')
    company_graph: CompanyGraph = company_graph_dao.get_by_cid(cid)
    if not company_graph:
        logger.warning(f'bad company_graph {cid}')
        return ProcessCode.ERROR
    q = Queue()
    q.put((company_graph.cgid, 1.0))

    count = 0
    while not q.empty():
        gid, percent = q.get()
        for er in er_dao.get_many(value=gid, field='shareholder_graph_id'):
            er: EquityRatio
            if er.source == 100:
                continue
            if er.percent is None:
                continue
            percent_new = percent * er.percent
            if percent_new >= 0.5:
                logger.info(f'OUTPUT {name} {company_graph.cgid} {er.company_name} from {er.shareholder_name} {percent_new}')
                if writer:
                    writer.writerow({
                        'name': name,
                        'code': code,
                        'gid': company_graph.cgid,
                        'investee_name': er.company_name,
                        'investee_gid': er.company_graph_id,
                        'percent': percent_new,
                        'did': d['did'],
                    })
                count += 1
                q.put((er.company_graph_id, percent_new))
    if stat_writer:
        stat_writer.writerow({
            'name': name,
            'code': code,
            'count': count,
            'did': d['did'],
        })
    return ProcessCode.OK


def callback_fn(f: Future):
    c = fs[f]
    del fs[f]
    try:
        ret: ProcessCode = f.result()
        process_code_stat[ret] += 1
    except Exception as e:
        logger.info(f'error process {c}  e={e} {get_stack_info()}')
        process_code_stat[ProcessCode.EXCEPTION] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
        open(f'{args.input_csv_name}.output.csv', 'w') as fout,
        open(f'{args.input_csv_name}.stat.csv', 'w') as fout2,

    ):
        reader = csv.DictReader(fin, delimiter='\t')
        writer = csv.DictWriter(
            f=fout,
            fieldnames=['did', 'name', 'code', 'gid', 'investee_name', 'investee_gid', 'percent'],
        )
        writer.writeheader()
        stat_writer = csv.DictWriter(
            f=fout2,
            fieldnames=['did', 'name', 'code', 'count'],
        )
        stat_writer.writeheader()
        for did, d in enumerate(reader):
            # if did > 10:
            #     break
            d['did'] = did
            future: Future = worker_pool.submit(process, d, writer, stat_writer)
            fs[future] = d
            future.add_done_callback(callback_fn)
        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多任务处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='company.input', help='')
    ap.add_argument('--worker-num', type=int, default=4, help='')

    main(ap.parse_args())
    # process({'name': '国灿能源（北京）有限公司', 'code': '91110109MACKERU32P'}, None)
