import csv
import time
from enum import Enum
from concurrent.futures import Future
import argparse
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info
from libs.concurrent import BoundedExecutor
from dao.company import CompanyDao, Company

logger = setup_logger()

fs = dict()
processed = set()
process_code_stat = Counter()
company_dao = CompanyDao(max_write_per_minute=3000)


class ProcessCode(Enum):
    OK = '正常'
    DUP = '重复数据'
    EXCEPTION = '抛出异常'
    ERROR = '错误'


def process(d: dict) -> ProcessCode:
    cid = int(d['cid'])

    if cid in processed:
        logger.warning(f'重复数据 {d}')
        return ProcessCode.DUP
    processed.add(cid)

    c: Company = company_dao.get(cid)
    if not c:
        logger.warning(f'no company {cid}')
        return ProcessCode.ERROR

    ret = company_dao.mysql_client.execute(
        sql=f'update {company_dao.db_tb_name} set reg_capital=%s where id=%s limit 1',
        args=(None, cid),
    )
    if ret != 1:
        logger.warning(f'ret != 1 {cid}')
        return ProcessCode.ERROR
    return ProcessCode.OK


def callback_fn(f: Future):
    c = fs[f]
    del fs[f]
    try:
        ret: ProcessCode = f.result()
        process_code_stat[ret] += 1
    except Exception as e:
        logger.info(f'error process {c}  e={e} {get_stack_info()}')
        process_code_stat[ProcessCode.EXCEPTION] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(args.input_csv_name, 'r') as f
    ):
        reader = csv.DictReader(f)
        for did, d in enumerate(reader):
            if did > 300:
                break
            future: Future = worker_pool.submit(process, d)
            fs[future] = d
            future.add_done_callback(callback_fn)
        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多任务处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='gs54.input.sample.csv', help='')
    ap.add_argument('--worker-num', type=int, default=4, help='')

    main(ap.parse_args())
