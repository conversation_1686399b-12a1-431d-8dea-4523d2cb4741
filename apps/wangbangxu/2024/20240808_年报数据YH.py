import re
from typing import Dict
import argparse
import csv
import time
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info
from libs.concurrent import BoundedExecutor
from dao.company import CompanyGraph, CompanyGraphDao, Company, CompanyDao
from dao.human import HumanGraph, HumanGraphDao
from dao.investors.equity_ratio import EquityRatioDao, EquityRatio
from gslib.schedule import immediately_gs
from dao.company_hk import CompanyHkDao
from apps.octopus.utils.entry_manager import EntryManager, OctopusConfManager
from dao.reports.annual_report import AnnualReport, AnnualReportDao

logger = setup_logger()
fs = dict()
process_code_stat = Counter()
company_dao = CompanyDao()
annual_report_dao = AnnualReportDao(max_write_per_minute=3000)

yh_seen = set()
with open('20240808_年报数据YH.yh.input.csv', 'r') as f:
    reader = csv.DictReader(f)
    for did, d in enumerate(reader):
        if did % 10000 == 0:
            logger.info(f'yh read {did}')
        d: Dict
        credit_code = d['credit_code']
        reg_number = d['reg_number']
        report_year_lst = d['report_year_list'].split('|')
        code = credit_code or reg_number
        for report_year in report_year_lst:
            yh_seen.add((code, report_year))
logger.info(f'yh_seen size {len(yh_seen)}')

# f_out = open('20240808_年报数据YH.csv', 'w')


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    SUCCESS = auto()



@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


def process(tc: TaskContext):
    report_year = tc.inputs['report.report_year']
    credit_code = tc.inputs['company.credit_code']
    reg_number = tc.inputs['company.reg_number']
    reg_status = '销' in tc.inputs['company.reg_status']
    cid = tc.inputs['company.cid']
    # c: Company = company_dao.get(cid)
    # mo = re.search(r'(\d+\.?\d*)', c.reg_capital or '')
    # reg_capital = mo.group(1) if mo else ''
    # if not c:
    #     tc.code = ProcessCode.BAD_CG
    #     return
    code = credit_code or reg_number
    seen = (code, report_year) in yh_seen
    #logger.info(f'code={code} report_year={report_year} seen={seen}')
    # f_out.write(f'{code}\t{report_year}\t{reg_status}\t{reg_capital}\t{seen}\n')
    if not seen:
        report: AnnualReport = annual_report_dao.get_ex(values=[cid, report_year], fields=['company_id', 'report_year'])
        if report:
            ret = annual_report_dao.delete(report.id)
            logger.info(f'ret={ret} report {report.to_json()}')
            tc.code = ProcessCode.SUCCESS


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code != ProcessCode.INITIAL:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)
        for did, d in enumerate(reader):
            if did < args.input_csv_lid_min:
                continue
            if did >= args.input_csv_lid_max != -1:
                break
            d['did'] = did
            tc = TaskContext(inputs=d)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多线程处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='20240808_年报数据YH.tyc.input', help='')
    ap.add_argument('--input-csv-lid-min', type=int, default=-1)
    ap.add_argument('--input-csv-lid-max', type=int, default=-1)
    ap.add_argument('--worker-num', type=int, default=1)

    main(ap.parse_args())
    #
    # _d = {
    #     'did': 0,
    #     'company.name': 'name',
    #     'eq.company_graph_id': 3010578845,
    #     'company.credit_code': '91542233MAC694EK7T',
    #     'company.esta_date': '2009-03-02',
    # }
    # tc = TaskContext(inputs=_d)
    # process(tc)
    # logger.info(f'tc={tc}')
    #
