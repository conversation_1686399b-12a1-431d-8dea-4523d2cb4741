from datetime import datetime
import json
import re
from typing import Dict, List
import argparse
import csv
import time
from datetime import timedelta
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor

from gslib.schedule import immediately_gs

from dao.company import Company
from dao.company import CompanyDao
from dao.company import CompanyGraph
from dao.company import CompanyGraphDao
from dao.reports.annual_report import AnnualReport
from dao.reports.annual_report import AnnualReportDao
from dao.reports.annual_report_brief import AnnualReportBrief
from dao.reports.annual_report_brief import AnnualReportBriefDao
from dao.key_person.history_company_staff import HistoryCompanyStaff
from dao.key_person.history_company_staff import HistoryCompanyStaffDao
from dao.key_person.company_staff import CompanyStaff
from dao.key_person.company_staff import CompanyStaffDao
from dao.company_hk import CompanyHk
from dao.company_hk import CompanyHkDao
from clients.obs_client import OBSClient
from clients.redis.redis_queue import RedisQueue
from gslib.msv import msv_query_base_info, msv_query_list_dim
from libs.dt import to_date, to_datetime
from gslib.id_center import id_center_query, EntityType
from gslib.credit_code import credit_code_valid
from dao.reports.report_shareholder import ReportShareholderDao, ReportShareholder
from dao.company_clean_info import CompanyCleanInfoDao, CompanyCleanInfo
from dao.company_staff_sort import CompanyStaffSort, CompanyStaffSortDao
from dao.investors.equity_ratio import EquityRatioDao,  EquityRatio
from dao.company_change_info import  CompanyChangeInfo, CompanyChangeInfoDao
company_graph_dao = CompanyGraphDao()
company_dao = CompanyDao()
company_clean_info_dao = CompanyCleanInfoDao()
company_staff_sort_dao = CompanyStaffSortDao()
equity_ratio_dao = EquityRatioDao()
company_change_info_dao = CompanyChangeInfoDao()

names = []
fout = open('20241226_保险公司数据导出.output.txt', 'w')

with open('20241226_保险公司数据导出.txt', 'r') as f:
    for line in f:
        s = line.strip()
        if len(s) > 0:
            names.append(s)

for name in names[:3111]:
    ent_type, eid = id_center_query(name=name)
    if ent_type != EntityType.ORG:
        gid = 0
        cid = 0
    else:
        cg = company_graph_dao.get_by_cid(eid)
        if not cg:
            gid = 0
            cid = 0
        else:
            gid = cg.cgid
            cid = eid
    if cid == 0 or gid == 0:
        # print('-')
        continue
    # company: Company = company_dao.get(cid)
    # company_clean_info: CompanyCleanInfo = company_clean_info_dao.get(cid)
    # if not company_clean_info:
    #     print('-')
    #     continue
    # print(company_clean_info.reg_capital)

    # ans = '-'
    # for staff in company_staff_sort_dao.get_many(field='company_gid', value=gid):
    #     staff: CompanyStaffSort
    #     if '董事长' in staff.position:
    #         ans = staff.name
    #         break
    # print(ans)
    # for staff in company_staff_sort_dao.get_many(field='company_gid', value=gid):
    #     staff: CompanyStaffSort
    #     print('\t'.join([name, staff.name, staff.position]))
    #
    # for c in company_dao.get_many(field='parent_id', value=cid):
    #     c: Company
    #     fout.write('\t'.join([name, c.name]) + '\n')

    for c in company_change_info_dao.get_many(field='company_id', value=cid):
        c: CompanyChangeInfo
        if re.search(r'高级管理人员|监事|董事|经理', c.change_item):
            content_before = c.content_before.replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
            content_after = c.content_after.replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')

            print('\t'.join([name, content_before, content_after]))
