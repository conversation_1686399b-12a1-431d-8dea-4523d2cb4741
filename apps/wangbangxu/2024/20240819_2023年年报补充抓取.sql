--SELECT *
FROM ods.ods_prism1_company_df
WHERE pt IN ('20240617','20240601','20240101','20230101','20220101')
		AND id IN ('156198', '*********') ; --SELECT *
FROM ods.ods_prism1_npo_df
WHERE pt IN ('20240705','20240704','20240703','20230101','20220101')
		AND id IN ('506748') ;
WITH company AS
    (SELECT c.id AS cid,
		 -- cg.gid,
		 base,
		 name,
		 legal_person_name AS legal_name,
		 to_date(estiblish_time) AS esta_date,
		 to_date(approved_time) AS appr_date,
		 cast(c.property1 AS string) credit_code,
		 cast(c.reg_number AS string) reg_number,
		 c.crawledtime,
		 nvl(parent_id,
		 0) AS parent_id,
		 c.reg_institute,
		 c.reg_status,
		 c.company_org_type,
		 c.to_time,
		 -- c.business_scope,
		 -- clean.reg_status,
		 -- clean.reg_capital_amount,
		 -- clean.company_org_type,
		 -- clean.alias,
		 -- biz.biz_type_id,
		 -- if(clean.reg_status regexp '销', nvl(other.remove_date, to_date(c.crawledtime)), null) AS end_date, (case
    	WHEN (property2 is NOT null
    		AND property2 != '') THEN
    	'prop2'
    	WHEN source_flag regexp 'qyxy' THEN
    	(case
    	WHEN nvl(c.property1, '') = ''
    		AND nvl(c.reg_number, '') = '' THEN
    	'kongke'
    	WHEN c.company_org_type regexp '个体' THEN
    	'gsxt_geti'
    	WHEN nvl(c.company_org_type, '') == ''
    		AND name NOT regexp '公司|合伙' THEN
    	'gsxt_geti'
    	WHEN property1 regexp '^93' THEN
    	'gsxt_nonghe'
    	ELSE 'gsxt_gongsi' end)
    	WHEN source_flag regexp '^npo_foundation' THEN
    	'foundation'
    	WHEN source_flag regexp '^npo' THEN
    	'npo'
    	WHEN source_flag regexp '^hk' THEN
    	'hk'
    	WHEN source_flag regexp '^tw' THEN
    	'tw'
    	WHEN source_flag regexp '^institution' THEN
    	'institution'
    	WHEN source_flag regexp '^lawfirm' THEN
    	'lawfirm'
    	WHEN source_flag regexp '^org' THEN
    	'neworg'
    	ELSE 'others'
    	END )sub_type
    FROM
        (SELECT id AS cid,
		 *
        FROM ods.ods_prism1_company_df
        WHERE pt = ${pt}
        		AND dw_is_del = 0
        		AND id % ${cid_mod} = 0 ) c --
        INNER JOIN ( --SELECT -- graph_id AS gid,
		 -- company_id AS cid --
        FROM ods.ods_prism1_company_graph_df --
        WHERE pt = ${pt} --
        		AND dw_is_del = 0 --
        		AND deleted = 0 --
        		AND graph_id % ${gid_mod} = 0 --
        		AND company_id % ${cid_mod} = 0 -- ) cg
        	ON cg.cid = c.cid --
        LEFT JOIN ( --SELECT -- biz_type_id,
		 -- company_gid --
        FROM ods_prism_company_biz_type_df --
        WHERE pt = ${pt} --
        		AND dw_is_del = 0 --
        		AND deleted = 0 --
        		AND company_gid % ${gid_mod} = 0 -- ) biz
        	ON cg.gid = biz.company_gid --
        LEFT JOIN ( --SELECT -- id AS cid,
		 -- reg_capital_amount*1e-6 AS reg_capital_amount,
		 -- reg_capital_currency,
		 -- actual_capital_amount*1e-6 AS actual_capital_amount,
		 -- company_org_type,
		 -- reg_status,
		 -- social_security_staff_num,
		 -- alias --
        FROM ods.ods_prism1_company_clean_info_df --
        WHERE pt = ${pt} --
        		AND dw_is_del = 0 --
        		AND id % ${cid_mod} = 0 -- ) clean
        	ON c.id = clean.cid --
        LEFT JOIN ( --SELECT -- id AS cid,
		 -- to_date(nvl(cancel_date,
		 revoke_date)) AS remove_date --
        FROM ods.ods_prism1_company_other_info_df --
        WHERE pt = ${pt} --
        		AND dw_is_del = 0 --
        		AND id % ${cid_mod} = 0 --
        		AND nvl(cancel_date, revoke_date) is NOT NULL -- ) other
        	ON c.id = other.cid ), area_code_mapping_0 AS
        (SELECT *
        FROM ods.ods_prism_area_code_district_df
        WHERE pt = ${pt} a nd dw_is_del = 0
        		AND version='V2020'
        		AND deleted=0 ), area_code_mapping AS
        (SELECT DISTINCT province_code AS area_code,
		 province AS province,
		 NULL AS city,
		 NULL AS district
        FROM area_code_mapping_0
        UNION
        SELECT DISTINCT city_code AS area_code,
		 province AS province,
		 city,
		 NULL AS district
        FROM area_code_mapping_0
        UNION
        SELECT DISTINCT district_code AS area_code,
		 province AS province,
		 city,
		 district
        FROM area_code_mapping_0 ), company_base AS
        (SELECT company_base_t.register_capital_amt*1e-6 AS reg_capital,
		 -- enterprise_search.reg_capital AS reg_capital,
		 to_date(company_base_t.establish_date) AS establish_date,
		 -- org_type,
		 -- register_addr,
		 province,
		 city,
		 district,
		 area_code,
		 cate_1,
		 cate_2,
		 cate_3,
		 category_code_3,
		 rongzhi_round,
		 scientific_company,
		 company_base_t.id AS cid,
		 company_base_t.company_id AS gid,
		 company_name
        FROM
            (SELECT *
            FROM ods.ods_company_base_company_index_df
            WHERE pt = ${pt}
            		AND dw_is_del = 0
            		AND company_id % ${gid_mod} = 0 ) company_base_t
            LEFT JOIN area_code_mapping
            	ON company_base_t.business_areacode_00 = area_code_mapping.area_code
            LEFT JOIN
                (SELECT cate_1,
		 cate_2,
		 cate_3,
		 category_code_3
                FROM ods.ods_prism_company_category_all_code_v2017_df
                WHERE pt = ${pt}
                		AND dw_is_del = 0 ) cate_info_t
                	ON company_base_t.industry_national_std_lv3_code = cate_info_t.category_code_3 --
                LEFT JOIN ( --SELECT *
                FROM -- ods.ods_search_card_enterprise_search_basic_info_df
                WHERE pt = ${pt}
                		AND is_deleted = 0
                		AND company_id % ${gid_mod} = 0 -- ) enterprise_search
                	ON company_base_t.company_id = enterprise_search.company_id ), sub_company AS (
                    (SELECT id AS sub_id,
		 unified_social_credit_code AS sub_code,
		 deleted AS sub_deleted,
		 'npo' AS sub_type, company_id AS cid
                    FROM ods.ods_prism1_npo_df
                    WHERE pt = ${pt}
                    		AND dw_is_del = 0
                    		AND company_id is NOT NULL )
                    UNION
                        ::~(SELECT id AS sub_id,
		 unified_social_credit_code AS sub_code,
		 deleted AS sub_deleted,
		 'neworg' AS sub_type, company_id AS cid
                        FROM ods.ods_prism_organization_info_df
                        WHERE pt = ${pt}
                        		AND dw_is_del = 0
                        		AND company_id is NOT NULL )
                        UNION
                            ::~(SELECT id AS sub_id,
		 creditcode AS sub_code,
		 is_lawfirm AS sub_deleted,
		 'lawfirm' AS sub_type, company_id AS cid
                            FROM ods.ods_data_judicial_risk_law_firm_df
                            WHERE pt = ${pt}
                            		AND dw_is_del = 0
                            		AND company_id is NOT NULL )
                            UNION
                                ::~(SELECT id AS sub_id,
		 unified_social_credit_code AS sub_code,
		 0 AS sub_deleted,
		 'institution' AS sub_type, company_id AS cid
                                FROM ods.ods_company_base_institution_df
                                WHERE pt = ${pt}
                                		AND dw_is_del = 0
                                		AND company_id is NOT NULL )
                                UNION
                                    ::~(SELECT id AS sub_id,
		 company_num AS sub_code,
		 deleted AS sub_deleted,
		 'hk' AS sub_type, company_id AS cid
                                    FROM ods.ods_prism1_company_hk_df
                                    WHERE pt = ${pt}
                                    		AND dw_is_del = 0
                                    		AND company_id is NOT NULL )
                                    UNION
                                        ::~(SELECT id AS sub_id,
		 code AS sub_code,
		 deleted AS sub_deleted,
		 'tw' AS sub_type, company_id AS cid
                                        FROM ods.ods_prism1_company_tw_df
                                        WHERE pt = ${pt}
                                        		AND dw_is_del = 0
                                        		AND company_id is NOT NULL )
                                        UNION
                                            ::~(SELECT id AS sub_id,
		 creditCode AS sub_code,
		 deleted AS sub_deleted,
		 'foundation' AS sub_type, company_id AS cid
                                            FROM ods.ods_prism1_foundation_df
                                            WHERE pt = ${pt}
                                            		AND dw_is_del = 0
                                            		AND company_id is NOT NULL ) ), company_investor_path_count AS
                                            (SELECT company_id AS gid,
		 count(if(shareholder_entity_type=1,
		 1,
		 null)) AS company_investor_count,
		 count(if(shareholder_entity_type=2,
		 1,
		 null)) AS human_investor_count
                                            FROM ods.ods_prism_shareholder_path_ratio_path_company_new_all_df
                                            WHERE pt = ${pt}
                                            		AND dw_is_del = 0
                                            		AND company_id % ${gid_mod} = 0
                                            		AND is_end = 1 -- 路径终点
                                            		AND shareholder_id != cast(company_id AS string) -- 股东自己的 排除个体户等
                                            GROUP BY  company_id ), company_investor_path AS
                                            (SELECT company_id AS gid,
		 shareholder_entity_type AS sh_type,
		 shareholder_id AS sh_id,
		 shareholder_name AS sh_name,
		 investment_ratio_total
                                            FROM ods.ods_prism_shareholder_path_ratio_path_company_new_all_df
                                            WHERE pt = ${pt}
                                            		AND dw_is_del = 0
                                            		AND company_id % ${gid_mod} = 0
                                            		AND is_end = 1 -- 路径终点
                                            		AND shareholder_id != cast(company_id AS string) -- 股东自己的 排除个体户等 ), company_branch AS
                                            (SELECT c1.gid AS gid,
		 c2.gid AS parent_gid,
		 c1.cancel_date
                                            FROM
                                                (SELECT *
                                                FROM company
                                                WHERE parent_id > 0
                                                		AND sub_type = 'gsxt_gongsi' ) c1
                                                INNER JOIN
                                                    (SELECT *
                                                    FROM company
                                                    WHERE parent_id = 0
                                                    		AND sub_type = 'gsxt_gongsi' ) c2
                                                    	ON c1.parent_id = c2.cid
                                                    UNION
                                                    SELECT gid AS gid,
		 gid AS parent_gid,
		 cancel_date
                                                    FROM company
                                                    WHERE parent_id = 0
                                                    		AND sub_type = 'gsxt_gongsi' -- NOT branch ), report_brief AS
                                                    (SELECT company_id AS cid,
		 report_year,
		 published_date -- 2020年05月12日
                                                    FROM ods.ods_prism_annual_report_brief_df
                                                    WHERE pt = ${pt}
                                                    		AND dw_is_del = 0
                                                    		AND company_id % ${cid_mod} = 0 ), report AS
                                                    (SELECT id AS report_id,
		 company_id AS cid,
		 report_year,
		 manage_state
                                                    FROM ods.ods_prism1_annual_report_df
                                                    WHERE pt = ${pt}
                                                    		AND dw_is_del = 0
                                                    		AND company_id % ${cid_mod} = 0 ), report_investor AS
                                                    (SELECT annual_report_id AS report_id,
		 investor_id,
		 investor_type,
		 investor_name
                                                    FROM ods.ods_prism1_report_shareholder_df
                                                    WHERE pt = ${pt}
                                                    		AND dw_is_del = 0 ),
		 company_max_report_year AS ( -- 公司的最大年报日期SELECT cid,
		 max(report_year) AS max_report_year
                                                    FROM report
                                                    GROUP BY  cid ),
		 company_max_report_year_2 AS ( -- 公司的最大年报日期（有股东的）SELECT report.cid AS cid,
		 max(report.report_year) AS max_report_year
                                                    FROM report
                                                    JOIN report_investor
                                                    	ON report.report_id = report_investor.report_id
                                                    GROUP BY  report.cid ), patent_info AS
                                                    (SELECT view_tmp.gid AS gid,
		 patent_type,
		 -- 1: 发明专利 count(1) AS patent_count
                                                    FROM ods.ods_intellectual_property_info_company_patent_basic_info_index_df lateral view explode(split(company_ids, ';')) view_tmp AS gid
                                                    WHERE pt = ${pt}
                                                    		AND dw_is_del = 0
                                                    		AND nvl(company_ids, '') != ''
                                                    GROUP BY  view_tmp.gid, patent_type ), partnership_info AS
                                                    (SELECT company_gid AS gid,
		 executive AS executive_name,
		 executive_gid,
		 executive_type,
		 deleted
                                                    FROM ods.ods_prism_company_partnership_info_df
                                                    WHERE pt = ${pt}
                                                    		AND dw_is_del = 0 ), company_staff AS
                                                    (SELECT company_id AS cid,
		 staff_id AS staff_cid,
		 staff_type_name AS position
                                                    FROM ods.ods_prism1_company_staff_df
                                                    WHERE pt = ${pt}
                                                    		AND dw_is_del = 0
                                                    		AND company_id % ${cid_mod} = 0 ), company_staff_sort AS
                                                    (SELECT company_gid AS gid,
		 name AS staff_name,
		 if(type=1,
		 2,
		 1) AS staff_type,
		 position AS positions,
		 human_gid AS staff_gid,
		 create_time
                                                    FROM ods.ods_prism_company_staff_sort_df
                                                    WHERE pt = ${pt}
                                                    		AND dw_is_del = 0
                                                    		AND company_gid % ${gid_mod} = 0 ), equity_ratio AS
                                                    (SELECT company_graph_id AS gid,
		 shareholder_graph_id AS sh_gid,
		 shareholder_name AS sh_name,
		 shareholder_type AS sh_type,
		 `percent` AS ratio,
		 source
                                                    FROM ods.ods_prism1_equity_ratio_df
                                                    WHERE pt = ${pt}
                                                    		AND dw_is_del = 0
                                                    		AND company_graph_id % ${gid_mod} = 0
                                                    		AND deleted = 0 ), change_info AS
                                                    (SELECT company_id AS cid,
		 renamed_change_item AS change_item,
		 content_before AS cont_before,
		 content_after AS cont_after,
		 to_date(change_time) AS change_date
                                                    FROM ods.ods_company_change_company_change_info_clean_df
                                                    WHERE pt = ${pt}
                                                    		AND dw_is_del = 0
                                                    		AND company_id % ${cid_mod} = 0
                                                    		AND deleted = 0 ), abnormal_info AS
                                                    (SELECT company_id AS cid,
		 to_date(put_date) AS put_date,
		 deleted
                                                    FROM ods_prism1_company_abnormal_info_df
                                                    WHERE pt = ${pt}
                                                    		AND dw_is_del = 0
                                                    		AND company_id % ${cid_mod} = 0 ) -- -- 分月企业数量统计 --SELECT date_format(t.group_date,
		 'yyyyMM'), count(1)
                                                FROM -- ( --SELECT *,
		 GREATEST(end_date,
		 to_date('2020-12-31')) AS group_date
                                                FROM company --
                                                WHERE sub_type regexp 'gsxt' --
                                                		AND end_date is NOT NULL -- )t --
                                            GROUP BY  date_format(t.group_date, 'yyyyMM'); -- company_old AS
                                            (SELECT *
                                            FROM ods.ods_prism1_company_df
                                            WHERE pt = '20220101'
                                            		AND dw_is_del = 0) --SELECT company.*,
		 company_old.reg_status --
                                        FROM company --
                                    LEFT JOIN company_old
                                	ON company_old.id = company.cid --
                            WHERE company.sub_type regexp 'gsxt' --
                		AND company.reg_status regexp '吊销'
    		AND company.reg_status regexp '未注销' --
		AND nvl(company_old.reg_status, '存续') NOT regexp '销' -- 2022年初已经是注销 -- 有股东的最大年报 股东id为0的 --SELECT *
FROM company --
JOIN report
	ON company.cid = report.cid -- --
JOIN company_max_report_year_2
	ON company.cid = company_max_report_year_2.cid
		AND report.report_year = company_max_report_year_2.max_report_year --
JOIN report_investor
	ON report.report_id = report_investor.report_id --
WHERE company.sub_type = 'gsxt_gongsi' --
		AND company.org_type regexp '股份有限公司' --
		AND company.org_type regexp '非上市' --
		AND company.org_type NOT regexp '分' --
		AND nvl(report_investor.investor_id,
		 -1) = 0; -- 最新年报无股东的 --SELECT *
FROM company --
JOIN report
	ON company.cid = report.cid --
JOIN company_max_report_year
	ON company.cid = company_max_report_year.cid
		AND report.report_year = company_max_report_year.max_report_year --
LEFT JOIN report_investor
	ON report.report_id = report_investor.report_id --
WHERE company.sub_type = 'gsxt_gongsi' --
		AND company.org_type regexp '股份有限公司' --
		AND company.org_type regexp '非上市' --
		AND company.org_type NOT regexp '分' --
		AND report_investor.report_id is null; -- 年报股东id为0 --SELECT report.cid,
		 report_investor.investor_name,
		 count(1) AS cnt
FROM report_investor --
JOIN report
	ON report.report_id = report_investor.report_id --
JOIN company
	ON report.cid = company.cid --
WHERE report_investor.investor_id = 0
		AND company.sub_type = 'gsxt_gongsi' --
GROUP BY  report.cid,
		 report_investor.investor_name -- 年报股东为空壳的 --SELECT report.cid,
		 report_investor.investor_name,
		 count(1) AS cnt
FROM report_investor --
JOIN report
	ON report.report_id = report_investor.report_id --
JOIN company
	ON report.cid = company.cid --
JOIN company AS company2
	ON report_investor.investor_id = company2.cid --
WHERE company.sub_type = 'gsxt_gongsi' --
		AND report_investor.investor_id > 0
		AND investor_type = 2
		AND company2.sub_type = 'kongke' --
GROUP BY  report.cid,
		 report_investor.investor_name --SELECT substr(t.credit_code,
		 1,
		 2),
		 count(*)
FROM --
    (SELECT upper(credit_code) AS credit_code
    FROM company
    WHERE length(credit_code) = 18
    		AND sub_type != 'prop2'
    GROUP BY  credit_code)t --
GROUP BY  substr(t.credit_code,
		 1,
		 2) -- patent_info AS ( --SELECT view_tmp.gid AS gid,
		 -- patent_type,
		 -- 1: 发明专利 -- count(1) AS patent_count --
FROM ods.ods_intellectual_property_info_company_patent_basic_info_index_df -- lateral view explode(split(company_ids, ';')) view_tmp AS gid --
WHERE pt = ${pt}
		AND dw_is_del = 0
		AND nvl(company_ids, '') != '' --
GROUP BY  view_tmp.gid,
		 patent_type -- ),
		 -- 股东数量超过50个 --SELECT *
FROM --
    (SELECT company.gid,
		 max(source) AS source1,
		 min(source) AS source2,
		 count(1) AS cnt
    FROM company --
    JOIN equity_ratio
    	ON equity_ratio.gid = company.gid --
    WHERE equity_ratio.source != 100
    		AND company.sub_type regexp 'gsxt' --
    GROUP BY  company.gid
    HAVING count(1) >= 50)t --
LEFT JOIN company company2
	ON company2.gid=t.gid -- 无股东数据的 --SELECT company.*
FROM company --
LEFT JOIN equity_ratio
	ON equity_ratio.gid = company.gid --
WHERE equity_ratio.gid is null
		AND -- company.sub_type regexp 'gsxt_gongsi' --
		AND reg_status NOT regexp '销|迁' --
		AND org_type NOT regexp '分|所有制|代表机构|合作社|办事处|集体|集团|国有经营单位' --
		AND parent_id = 0 --
		AND crawledtime > '2020-01-01' -- 每个group取其中一条 --SELECT t.gid,
		 t.source --
FROM ( --SELECT gid,
		 -- source,
		 -- row_number()
	OVER (partition by gid
ORDER BY  sh_gid) AS rn --
FROM equity_ratio -- ) t --
WHERE t.rn = 1; --SELECT *
FROM company
WHERE credit_code regexp '^9[1-3]33'
		AND length(credit_code)=18
		AND sub_type regexp 'gsxt'
		AND esta_date >= '2024-06-01'; --SELECT esta_date,
		 credit_code
FROM company
WHERE length(credit_code)=18
		AND sub_type regexp 'gsxt'
		AND esta_date >= '2024-05-01'
		AND esta_date < '2024-06-01'; --SELECT DISTINCT credit_code,
		 esta_date
FROM company
WHERE length(credit_code)=18; --SELECT deleted,
		 count(*)
FROM ods_prism1_company_double_random_check_result_info_df
WHERE pt='20240601'
		AND dw_is_del = 0
GROUP BY  deleted --SELECT source_flag,
		 count(1) AS total
FROM ( -- select
	CASE
	WHEN source_flag LIKE '%http://qyxy.baic.gov.cn%' THEN
	'baic' --
	WHEN source_flag LIKE '%org%' THEN
	'org' --
	WHEN source_flag LIKE '%npo%' THEN
	'npo' --
	WHEN source_flag LIKE '%institution%' THEN
	'institution' --
	ELSE 'kong'
	END AS source_flag --
FROM ods.ods_prism1_company_df
WHERE pt = ${pt}
		AND dw_is_del = 0
		AND (reg_status is null
		OR reg_status = '' ) --
		AND (reg_number is NOT null
		OR reg_number <> '' )
		AND (estiblish_time is NOT null
		OR estiblish_time <> '' ) --
		AND (legal_person_name is NOT null
		OR legal_person_name <> '' )
		AND (property1 IS NOT NULL
		AND property1 <> '')
		AND (property2 IS NULL
		OR property2 = '') --
		AND dw_is_del=0 )temp --
GROUP BY  source_flag; --SELECT DISTINCT t1.credit_code
FROM --
    (SELECT credit_code,
		 gid
    FROM company
    WHERE credit_code regexp '^914419'
    		AND esta_date
    	BETWEEN '2024-07-01'
    		AND '2024-07-10')t1 --
JOIN
    (SELECT gid
    FROM equity_ratio
    WHERE sh_type = 1)t2
	ON t1.gid = t2.gid; --SELECT *
FROM ( --SELECT company_new.id AS cid,
		 'move' AS tp, company_old.reg_location AS addr
FROM -- 通过登记机关来判断 --
    (SELECT *
    FROM ods.ods_prism1_company_df
    WHERE pt = '20240701'
    		AND dw_is_del = 0
    		AND id % ${cid_mod} = 0
    		AND reg_institute regexp '北京.*通州') company_new --
    INNER JOIN --
        (SELECT *
        FROM ods.ods_prism1_company_df
        WHERE pt = '20240301'
        		AND dw_is_del = 0
        		AND id % ${cid_mod} = 0
        		AND reg_institute NOT regexp '通州') company_old --
        	ON company_new.id = company_old.id --
        UNION
        --
            (SELECT id AS cid,
		 'establish' AS tp, reg_location AS addr -- 通过地址来判断 --
            FROM ods.ods_prism1_company_df
            WHERE pt = '20240701'
            		AND dw_is_del = 0
            		AND id % ${cid_mod} = 0 --
            		AND (reg_location regexp '北京.*通州'
            		OR reg_institute regexp '北京.*通州')
            		AND estiblish_time >= '2024-03-01') --
            UNION
            --
                (SELECT company.id AS cid,
		 'report' AS tp, postal_address AS addr
                FROM -- 通过地址来判断 --
                    (SELECT company_id AS cid,
		 postal_address
                    FROM ods.ods_prism1_annual_report_df
                    WHERE pt = '20240701'
                    		AND dw_is_del = 0 --
                    		AND company_id % ${cid_mod} = 0
                    		AND report_year = '2023'
                    		AND postal_address regexp '北京.*通州' -- )report
                    INNER JOIN ( --SELECT *
                    FROM ods.ods_prism1_company_df
                    WHERE pt = '20240701'
                    		AND dw_is_del = 0
                    		AND id % ${cid_mod} = 0
                    		AND reg_location NOT regexp '通州' -- )company
                    	ON report.cid = company.id -- ) -- ) t
                INNER JOIN company
            	ON t.cid = company.cid --SELECT *
        FROM origindb_offline.origindb_prism1_big_shareholder_hi
    WHERE pt IN (20240706,20240713,20240715)
		AND hh=23
		AND company_graph_id=31353524; --SELECT *
FROM origindb_offline.origindb_prism1_big_shareholder_hi
WHERE pt=20240711
		AND hh=23
		AND company_graph_id=*********; -- 20240716 河北种植业数据导出 --SELECT province,
		 city,
		 district,
		 esta_year,
		 count(1),
		 sum(reg_capital)
FROM -- ( --SELECT date_format(GREATEST(esta_date,
		 to_date('2012-12-31')), 'yyyy') AS esta_year, province, city, district, company_base.reg_capital
FROM company --
INNER JOIN company_base
	ON company.gid = company_base.gid --
WHERE sub_type regexp 'gsxt'
		AND esta_date < '2024-01-01'
		AND company_base.cate_2 = '农业' -- )t --
GROUP BY  province,
		 city,
		 district,
		 esta_year; -- sub_company相关统计 --------------------------------------------------------------------------------------- -- 缺失sub_company表信息的 --SELECT *
FROM company --
LEFT JOIN sub_company
	ON sub_company.sub_type = company.sub_type
		AND sub_company.cid = company.cid --
WHERE company.sub_type NOT regexp 'gsxt'
		AND company.sub_type != 'kongke' --
		AND sub_company.sub_id is null; --SELECT *
FROM ods.ods_prism1_company_hk_df
WHERE pt = ${pt}
		AND id='1004055'; 表有缺失数据 -- GS115 经营期限to_time 无固定期限更新 --SELECT *
FROM company
WHERE company.sub_type = 'gsxt_gongsi'
		AND credit_code regexp '^913502'
		AND to_time is null
		AND reg_status NOT regexp '销'; -- GS116 更加详细的企业类型 --SELECT *
FROM company
WHERE company.sub_type = 'gsxt_gongsi'
		AND company_org_type = '有限责任公司'
		AND reg_status NOT regexp '销'
		AND base='cq'; -- 空壳追查 cid: 2692124708 gid: 7049741798 --SELECT *
FROM ods.ods_prism1_company_df
WHERE pt = '20240720'
		AND parent_id='2692124708' -- 总公司 --SELECT *
FROM ods.ods_prism1_equity_ratio_df
WHERE pt = '20240720'
		AND shareholder_graph_id='7049741798' -- 股东 --SELECT *
FROM ods.ods_prism1_report_shareholder_df
WHERE (pt = '20240720'
		OR pt = '20240718')
		AND annual_report_id IN (3159825200,
		 2912248336,
		 2970469686) -- 年报股东 --SELECT *
FROM ods.ods_company_all_count_hbase_merged_df
WHERE pt = '20240725'
		AND company_id='7049741798' -- dim count --SELECT ent_type,
		 count(*)
FROM ods.ods_gsxt_company_supplement_df
WHERE pt = ${pt}
		AND dw_is_del = 0
		AND company_id % ${cid_mod} = 0
GROUP BY  ent_type; -- 年报年份与企业成立日期矛盾的 --SELECT *
FROM report
JOIN company
	ON report.cid = company.cid --
WHERE company.sub_type regexp 'gsxt'
		AND report.report_year < year(company.esta_date) --
		AND (company.sub_type = 'gsxt_gongsi'
		AND company.credit_code regexp '^92') -- 20240819_2023年年报补充抓取SELECT *
FROM company
LEFT JOIN report
	ON company.cid = report.cid
		AND report.report_year = 2023
LEFT JOIN
    (SELECT cid,
		 max(put_date) AS max_put_date
    FROM abnormal_info
    GROUP BY  cid )abnormal
	ON company.cid = abnormal.cid
WHERE report.report_id is null
		AND year(company.esta_date) < 2024
		AND company.sub_type regexp 'gsxt_gongsi'
		AND company.reg_status regexp '存续|在业|在营|开业'
		AND nvl(max_put_date, to_date('2020-01-01')) < '2024-01-01' --SELECT credit_code,
		 reg_number,
		 esta_date
FROM company
WHERE esta_date
	BETWEEN '2024-01-01'
		AND '2024-07-30'
		AND length(credit_code) = 18
		AND credit_code NOT regexp '^9'