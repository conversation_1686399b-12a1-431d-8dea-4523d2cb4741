import json
import re
from typing import Dict
import argparse
import csv
import time
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor
from gslib.schedule import immediately_gs
from dao.company import Company, CompanyDao
from dao.reports.annual_report import AnnualReportDao, AnnualReport
from dao.reports.annual_report_brief import AnnualReportBriefDao, AnnualReportBrief
from clients.redis.redis_queue import RedisQueue
from gslib.msv import msv_query_base_info
from libs.dt import to_date

logger = setup_logger()
fs = dict()
process_code_stat = Counter()
company_dao = CompanyDao()


redis_queue = RedisQueue(name='normal_schedule_queue', max_length=1000000, **ConstantProps.PROPS_GS_REDIS_ONLINE)


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()
    DUP = auto()

    BAD_COMPANY = auto()
    BAD_COMPANY_GEDU = auto()

    ALREADY_DONE_MSV = auto()
    ALREADY_DONE_COMPANY = auto()

    ERROR_MSV = auto()
    MSV_CHANGED = auto()
    CHECKED = auto()

    OK = auto()


@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


def process(tc: TaskContext):
    cid = int(tc.inputs['cf_cid'])

    company: Company = company_dao.get(cid)
    if not company or len(company.credit_code or '') != 18 or not company.establish_date:
        tc.code = ProcessCode.BAD_COMPANY
        return
    if '销' in (company.reg_status or ''):
        tc.code = ProcessCode.BAD_COMPANY
        return
    if '个人独资企业' == company.company_org_type:
        tc.code = ProcessCode.BAD_COMPANY_GEDU
        return

    tc.context['from_date'] = company.from_date
    tc.context['to_date'] = company.to_date

    if company.from_date != company.establish_date or company.to_date is not None:
        tc.code = ProcessCode.ALREADY_DONE_COMPANY
        return
    if to_date(company.last_crawled_time) >= to_date('2024-08-20'):
        tc.code = ProcessCode.CHECKED
        return

    msv_base_info_lst = msv_query_base_info(cid=cid)
    if len(msv_base_info_lst) == 0:
        tc.code = ProcessCode.ERROR_MSV
        return
    msv_base_info = msv_base_info_lst[0]
    if 'from_time' not in msv_base_info or 'to_time' not in msv_base_info:
        tc.code = ProcessCode.ERROR_MSV
        return
    if msv_base_info['from_time'] is not None or msv_base_info['to_time'] is not None:
        tc.code = ProcessCode.ALREADY_DONE_MSV
        return

    tc.code = ProcessCode.OK
    redis_queue.push(value=json.dumps({
        'companyId': str(cid),
        'companyName': company.name,
        'creditCode': company.credit_code,
        'dims': [],
        'scheduleReason': '0822_xa_jyqx_s1',
        'score': 5,
    }, ensure_ascii=False), realtime=True)  # 这里realtime反而是降低优先级
    logger.info(f'OUTPUT {cid} {company.credit_code}')


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code != ProcessCode.INITIAL:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)
        for did, d in enumerate(reader):
            if did < args.input_csv_lid_min:
                continue
            if did >= args.input_csv_lid_max != -1:
                break
            d['did'] = did
            tc = TaskContext(inputs=d)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多线程处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='20240820_xa_jyqx_repair.input.new', help='')
    ap.add_argument('--input-csv-lid-min', type=int, default=-1)
    ap.add_argument('--input-csv-lid-max', type=int, default=10000)
    ap.add_argument('--worker-num', type=int, default=4)

    main(ap.parse_args())
    #
    # _d = {
    #     'did': 0,
    #     'company.name': 'name',
    #     'eq.company_graph_id': 3010578845,
    #     'company.credit_code': '91542233MAC694EK7T',
    #     'company.esta_date': '2009-03-02',
    # }
    # tc = TaskContext(inputs=_d)
    # process(tc)
    # logger.info(f'tc={tc}')
    #
