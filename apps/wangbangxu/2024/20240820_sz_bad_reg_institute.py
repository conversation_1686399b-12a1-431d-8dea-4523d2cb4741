import json
import re
from typing import Dict
import argparse
import csv
import time
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor
from gslib.schedule import immediately_gs
from dao.reports.annual_report import AnnualReportDao, AnnualReport
from dao.reports.annual_report_brief import AnnualReportBriefDao, AnnualReportBrief
from clients.redis.redis_queue import RedisQueue
from gslib.msv import msv_query_base_info
from libs.dt import to_date
from dao.company import Company, CompanyDao

logger = setup_logger()
fs = dict()
process_code_stat = Counter()
report_dao = AnnualReportBriefDao()
company_dao = CompanyDao()

redis_queue = RedisQueue(name='normal_schedule_queue', max_length=1000000, **ConstantProps.PROPS_GS_REDIS_ONLINE)


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    NOT_COMPANY = auto()
    CHECKED = auto()

    ALREADY_DONE = auto()

    OK = auto()


@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


def process(tc: TaskContext):
    cid = int(tc.inputs['company.cid'])
    c: Company = company_dao.get(cid)
    if not c or not (c.credit_code or '').startswith('9144'):
        tc.code = ProcessCode.NOT_COMPANY
        return
    if len(c.reg_institute) > 3:
        tc.code = ProcessCode.ALREADY_DONE
        return
    if to_date(c.last_crawled_time) >= to_date('2024-08-20'):
        tc.code = ProcessCode.CHECKED
        return
    tc.code = ProcessCode.OK
    redis_queue.push(value=json.dumps({
        'companyId': str(cid),
        'companyName': tc.inputs['company.name'],
        'creditCode': c.credit_code,
        'dims': [],
        'scheduleReason': '0820_sz_bad_inst',
        'score': 5,
    }, ensure_ascii=False))
    logger.info(f'{cid} {c.credit_code}')


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code != ProcessCode.INITIAL:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)
        for did, d in enumerate(reader):
            if did < args.input_csv_lid_min:
                continue
            if did >= args.input_csv_lid_max != -1:
                break
            d['did'] = did
            tc = TaskContext(inputs=d)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多线程处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='20240820_sz_bad_reg_institute.input', help='')
    ap.add_argument('--input-csv-lid-min', type=int, default=601234)
    ap.add_argument('--input-csv-lid-max', type=int, default=601334)
    ap.add_argument('--worker-num', type=int, default=4)

    main(ap.parse_args())
    #
    # _d = {
    #     'did': 0,
    #     'company.name': 'name',
    #     'eq.company_graph_id': 3010578845,
    #     'company.credit_code': '91542233MAC694EK7T',
    #     'company.esta_date': '2009-03-02',
    # }
    # tc = TaskContext(inputs=_d)
    # process(tc)
    # logger.info(f'tc={tc}')
    #
