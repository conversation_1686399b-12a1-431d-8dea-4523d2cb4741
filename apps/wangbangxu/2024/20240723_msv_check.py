import json
from datetime import datetime
from libs.log2 import setup_logger
from apps.wangbangxu.multi_task import MultiTask, TaskContext
from dao.company import CompanyDao, Company
from gslib.msv import msv_query_base_info, msv_query_list_dim
from biz_utils.credit_code import credit_code_valid

logger = setup_logger()
dao = CompanyDao()


def process(tc: TaskContext):
    assert isinstance(tc.item, Company)
    if len(tc.item.credit_code or '') != 18:
        return
    if '公司' not in tc.item.name:
        return
    if '分' in (tc.item.company_org_type or ''):
        return
    if not tc.item.credit_code.startswith('91'):
        return

    msv_data = msv_query_list_dim(cid=tc.item.cid, table_name='company_investor', source='gsxt_page')
    # appr_dates = [x['approved_time'] for x in msv_data]
    logger.info(f'{tc.item.cid} {tc.item.name} {len(msv_data)} ')


tasks_iter = dao.scan(start='2018-07-01', scan_key='estiblish_time', total=10000)
MultiTask(tasks_iter=tasks_iter, process_fn=process, worker_num=4).run()
