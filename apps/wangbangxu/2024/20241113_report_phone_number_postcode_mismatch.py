from datetime import datetime
import json
import re
from typing import Dict, List
import argparse
import csv
import time
from datetime import timedelta
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor

from gslib.schedule import immediately_gs

from dao.company import Company
from dao.company import CompanyDao
from dao.company import CompanyGraph
from dao.company import CompanyGraphDao
from dao.reports.annual_report import AnnualReport
from dao.reports.annual_report import AnnualReportDao
from dao.reports.annual_report_brief import AnnualReportBrief
from dao.reports.annual_report_brief import AnnualReportBriefDao
from dao.key_person.history_company_staff import HistoryCompanyStaff
from dao.key_person.history_company_staff import HistoryCompanyStaffDao
from dao.key_person.company_staff import CompanyStaff
from dao.key_person.company_staff import CompanyStaffDao
from dao.company_hk import CompanyHk
from dao.company_hk import CompanyHkDao
from clients.obs_client import OBSClient
from clients.redis.redis_queue import RedisQueue
from gslib.msv import msv_query_base_info, msv_query_list_dim
from libs.dt import to_date, to_datetime
from gslib.id_center import id_center_query, EntityType
from gslib.credit_code import credit_code_valid


logger = setup_logger()
fs = dict()
process_code_stat = Counter()

redis_queue = RedisQueue(name='normal_schedule_queue', max_length=1000000, **ConstantProps.PROPS_GS_REDIS_ONLINE)
obs_client = OBSClient(bucket_name='jindi-oss-gsxt')
company_dao = CompanyDao(max_write_per_minute=3000)
company_graph_dao = CompanyGraphDao()
history_company_staff_dao = HistoryCompanyStaffDao()
company_staff_dao = CompanyStaffDao()
company_hk_dao = CompanyHkDao()
annual_report_dao = AnnualReportDao()

class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    DUP = auto()  # 已经生效过的
    EXCLUDE = auto()  # 非目标集合
    OK = auto()  # 本次目标集合

    NO_REPORT = auto()


@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


def process(tc: TaskContext):
    report_id = int(tc.inputs['report.report_id'])
    report: AnnualReport = annual_report_dao.get(report_id)
    if not report:
        tc.code = ProcessCode.NO_REPORT
        return

    if len(report.postcode or '') == 6:
        tc.code = ProcessCode.EXCLUDE
        return
    report.phone_number, report.postcode, report.postal_address = report.postal_address, report.phone_number, report.postcode
    ret = annual_report_dao.save_by_cmp(item=report, fields=['id', ])
    logger.info(f'OUTPUT {report.id}  ret={ret}')


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code not in [ProcessCode.INITIAL, ProcessCode.EXCLUDE]:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)
        for did, d in enumerate(reader):
            if did < args.input_csv_lid_min:
                continue
            if did >= args.input_csv_lid_max != -1:
                break
            d['did'] = did
            tc = TaskContext(inputs=d)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多线程处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='20241113_report_phone_number_postcode_mismatch', help='')
    ap.add_argument('--input-csv-lid-min', type=int, default=-1)
    ap.add_argument('--input-csv-lid-max', type=int, default=-1)
    ap.add_argument('--worker-num', type=int, default=8)

    main(ap.parse_args())

    # _d = {
    #     'did': 0,
    #     'c.cid': '2461030911',
    #
    # }
    # tc = TaskContext(inputs=_d)
    # process(tc)
    # logger.info(f'tc={tc}')

#
# def process4(tc: TaskContext):
#     cid = int(tc.inputs['t1.cid'])
#
#     company: Company = company_dao.get(cid)
#     if not company or company.prop2 is not None:
#         tc.code = ProcessCode.BAD_COMPANY
#         return
#
#     if len(company.credit_code or '') == 18:
#         tc.code = ProcessCode.ALREADY_DONE_COMPANY
#         return
#
#     credit_code = tc.inputs['t2.credit_no']
#     if not credit_code_valid(credit_code):
#         tc.code = ProcessCode.BAD_CREDIT_CODE
#         return
#
#     ttp, tid = id_center_query(credit_no=credit_code)
#     if ttp != EntityType.UNSET:
#         tc.code = ProcessCode.CREDIT_CODE_SEEN
#         if ttp != EntityType.ORG:
#             logger.warning(f'bad entity type {ttp.name} {cid} {credit_code}')
#             tc.code = ProcessCode.BAD_CREDIT_CODE
#             return
#         logger.info(f'CREDIT_CODE_SEEN cid={cid} cid2={tid}')
#         return
#
#     tc.code = ProcessCode.OK
#     company.credit_code = credit_code
#     ret = company_dao.mysql_client.execute('update prism.company set property1=%s where id=%s limit 1', args=(credit_code, company.cid))
#     logger.info(f'OUTPUT {cid} credit_code={credit_code} ret={ret}')
#
#
# def process3(tc: TaskContext):
#     cid = int(tc.inputs['cf_cid'])
#
#     company: Company = company_dao.get(cid)
#     if not company or len(company.credit_code or '') != 18 or not company.establish_date:
#         tc.code = ProcessCode.BAD_COMPANY
#         return
#     if '销' in (company.reg_status or ''):
#         tc.code = ProcessCode.BAD_COMPANY
#         return
#     if re.search(r'个人独资企业|集体', company.company_org_type):
#         tc.code = ProcessCode.BAD_COMPANY_GEDU
#         return
#
#     tc.context['from_date'] = company.from_date
#     tc.context['to_date'] = company.to_date
#
#     if company.from_date != company.establish_date or company.to_date is not None:
#         tc.code = ProcessCode.ALREADY_DONE_COMPANY
#         return
#     # if to_date(company.last_crawled_time) >= to_date('2024-08-20'):
#     #     tc.code = ProcessCode.CHECKED
#     #     return
#
#     msv_base_info_lst = msv_query_base_info(cid=cid)
#     if len(msv_base_info_lst) == 0:
#         tc.code = ProcessCode.ERROR_MSV
#         return
#     msv_base_info = msv_base_info_lst[0]
#     if 'from_time' not in msv_base_info or 'to_time' not in msv_base_info:
#         tc.code = ProcessCode.ERROR_MSV
#         return
#     if msv_base_info.get('msv_last_time', 0) > 1724084003000:
#         tc.code = ProcessCode.CHECKED
#         return
#     if msv_base_info['from_time'] is not None or msv_base_info['to_time'] is not None:
#         tc.code = ProcessCode.ALREADY_DONE_MSV
#         return
#
#     tc.code = ProcessCode.OK
#     redis_queue.push(value=json.dumps({
#         'companyId': str(cid),
#         'companyName': company.name,
#         'creditCode': company.credit_code,
#         'dims': [],
#         'scheduleReason': '0905_xa_jyqx',
#         'score': 5,
#     }, ensure_ascii=False), realtime=True)  # 这里realtime反而是降低优先级
#     logger.info(f'OUTPUT {cid} {company.credit_code}')
#
#
# def process2(tc: TaskContext):
#     br_num = tc.inputs['br_num']
#     if len(br_num) != 8:
#         tc.code = ProcessCode.BAD_BR_NUM
#         return
#
#     company_hk: CompanyHk = company_hk_dao.get(value=br_num, field='br_num')
#     if not company_hk:
#         tc.code = ProcessCode.BAD_NO_HK
#         return
#
#     if not company_hk.company_id:
#         tc.code = ProcessCode.BAD_HK_NO_CID
#         return
#
#     name_hk = company_hk.name_cn_s or company_hk.name_en
#     company_lst: List[Company] = list(company_dao.get_many(field='name', value=name_hk))
#     max_cid = 2743518550
#     cid_to_del = []
#     min_cid = max_cid
#     for company in company_lst:
#         # logger.info(f'{company}')
#         if company.cid > max_cid:
#             cid_to_del.append(company.cid)
#         else:
#             if re.search('hk_', company.source_flag) and (
#                     company.credit_code == br_num
#                     or company.source_flag.startswith('hk_'+company_hk.company_num)
#                     or company.source_flag.startswith('hk_' + br_num)
#             ):
#                 min_cid = company.cid
#
#     if min_cid == max_cid:
#         tc.code = ProcessCode.BAD_NO_TARGET_ITEM
#         return
#     if len(cid_to_del) == 0 and min_cid == company_hk.company_id:
#         tc.code = ProcessCode.DONE
#         return
#
#     for cid in cid_to_del:
#         company_dao.delete(cid)
#         cg: CompanyGraph = company_graph_dao.get_by_cid(cid)
#         if cg:
#             company_graph_dao.delete(cg.id)
#     company_hk.company_id = min_cid
#     company_hk_dao.save_by_cmp(item=company_hk, fields=['br_num'])
#     logger.info(f'size = {len(cid_to_del)} {len(company_lst)} {min_cid}')
#
#
# def process1(tc: TaskContext):
#     # ods_history_history_company_staff_df.id	ods_history_history_company_staff_df.company_id
#     # ods_history_history_company_staff_df.company_name
#     # ods_history_history_company_staff_df.staff_id	ods_history_history_company_staff_df.staff_name
#     # ods_history_history_company_staff_df.staff_type
#     # ods_history_history_company_staff_df.staff_type_name
#     # ods_history_history_company_staff_df.staff_salary	ods_history_history_company_staff_df.staff_stake_num
#     # ods_history_history_company_staff_df.staff_other_info	ods_history_history_company_staff_df.createtime
#     # ods_history_history_company_staff_df.change_time	ods_history_history_company_staff_df.update_time
#     # ods_history_history_company_staff_df.version	ods_history_history_company_staff_df.dw_update_time
#     # ods_history_history_company_staff_df.dw_is_del
#     # ods_history_history_company_staff_df.dw_opt_type
#     # ods_history_history_company_staff_df.pt
#     cid = int(tc.inputs['ods_history_history_company_staff_df.company_id'])
#     records: List[HistoryCompanyStaff] = list(history_company_staff_dao.get_many(value=cid, field='company_id'))
#     current: List[CompanyStaff] = list(company_staff_dao.get_many(value=cid, field='company_id'))
#     # for x in current:
#     #     logger.info(x)
#     # for record in records:
#     #     logger.info(record)
#     #
#
#     for version_t in range(1, 100):
#         staff_num = 0
#         bad_staff = False
#         for record in records:
#             if record.version == version_t:
#                 staff_num += 1
#                 if re.search(r'[εζδιηθΌΏΈΊΎΆ]', record.staff_name):
#                     bad_staff = True
#         if staff_num == 0:
#             break
#         if not bad_staff:
#             continue
#         if version_t == 1:
#             tc.code = ProcessCode.LUANMA_FIRST_VERSION
#             return
#
#         prv = set()
#         for record in records:
#             if record.version == version_t - 1:
#                 prv.add(record.staff_id)
#         nxt = set()
#         for record in records:
#             if record.version == version_t + 1:
#                 nxt.add(record.staff_id)
#         if len(nxt) == 0:
#             for record in current:
#                 nxt.add(record.staff_id)
#         logger.info(f'{prv} {nxt}')
#         if prv == nxt:
#             ids_for_del = []
#             for record in records:
#                 if record.version == version_t or record.version == version_t - 1:
#                     ids_for_del.append(record.id)
#             logger.info(f'{cid}  luanma at {version_t} ids_for_del={ids_for_del}')
#             for id_for_del in ids_for_del:
#                 history_company_staff_dao.delete(id_for_del)
#             tc.code = ProcessCode.LUANMA_FOUND
#             return
#     tc.code = ProcessCode.LUANMA_NO_FOUND


# # obs_client.get(f'company/xa/{cid}/keyPersonUrl.json')
    #
    # for page, ts in list(obs_client.list2(f'company/xa/{cid}/')):
    #     if 'keyPersonUrl' in page:
    #         text = obs_client.get_content(page)
    #         js = json.loads(text)
    #         for item in js['data']:
    #             # logger.info(f'{cid} {item}')
    #             if item['position_CN'] == '':
    #                 if (datetime.now() - ts).total_seconds() < 86400 * 14:
    #                     tc.code = ProcessCode.DUP
    #                     return
    #                 tc.code = ProcessCode.OK
    #                 s = json.dumps({
    #                     'companyId': str(cid),
    #                     'companyName': company.name,
    #                     'creditCode': company.credit_code,
    #                     'dims': [],
    #                     'scheduleReason': '0929_staff_no_position',
    #                     'score': 5,
    #                 }, ensure_ascii=False)
    #                 if redis_queue.redis.zcard(redis_queue.name) > 1000000:
    #                     logger.warning(f'redis sleep for length ')
    #                     time.sleep(5.0)
    #                 ret = redis_queue.redis.zadd(
    #                     redis_queue.name,
    #                     {s: 101},
    #                 )
    #                 logger.info(f'OUTPUT {cid} {company.credit_code} ret={ret}')
    #                 return
    #
    # tc.code = ProcessCode.EXCLUDE
    #
