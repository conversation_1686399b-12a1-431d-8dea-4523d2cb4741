import json
import re
from typing import Dict
import argparse
import csv
import time
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor
from dao.reports.annual_report_brief import AnnualReportBriefDao, AnnualReportBrief
from clients.redis.redis_queue import RedisQueue
from gslib.msv import msv_query_base_info
from libs.dt import to_date
from biz_utils.credit_code import credit_code_valid

logger = setup_logger()
fs = dict()
process_code_stat = Counter()
report_dao = AnnualReportBriefDao()

redis_queue = RedisQueue(name='normal_schedule_queue', max_length=2000000, **ConstantProps.PROPS_GS_REDIS_ONLINE)


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()
    DUP = auto()

    NO_MSV_TIME = auto()
    BAD_MSV_TIME = auto()
    NO_PUB_DATE_2022 = auto()
    BAD_PUB_DATE_2022 = auto()

    OK = auto()


@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


def process(tc: TaskContext):
    cid = int(tc.inputs['company.cid'])
    credit_code: str = tc.inputs['company.credit_code']
    esta_date = to_date(tc.inputs['company.esta_date'])
    # logger.info(f'cid={cid}')
    if len(credit_code) != 18 or credit_code.startswith('9144'):
        tc.code = ProcessCode.DUP
        return

    if esta_date.year <= 2022:
        report: AnnualReportBrief = report_dao.get_ex(fields=['company_id', 'report_year'], values=[cid, 2022])
        if not report:
            logger.warning(f'no publish date {tc.inputs}')
            tc.code = ProcessCode.NO_PUB_DATE_2022
            return
        pub_date = to_date(report.published_date)
        if pub_date > to_date('2023-07-01'):
            tc.code = ProcessCode.BAD_PUB_DATE_2022
            return

    msv_base_info_lst = msv_query_base_info(cid=cid)
    if len(msv_base_info_lst) == 0:
        tc.code = ProcessCode.BAD_MSV_TIME
        return
    msv_base_info = msv_base_info_lst[0]
    if 'msv_last_time' not in msv_base_info:
        tc.code = ProcessCode.NO_MSV_TIME
        return
    msv_last_time = to_date(msv_base_info['msv_last_time'])
    if msv_last_time < to_date('2023-01-01'):
        tc.code = ProcessCode.BAD_MSV_TIME
        return
    tc.code = ProcessCode.OK
    ret = redis_queue.push(value=json.dumps({
        'companyId': str(cid),
        'companyName': tc.inputs['company.name'],
        'creditCode': credit_code,
        'dims': ['annualreport2', ],
        'scheduleReason': '0819_report_repair',
        'score': 5,
    }, ensure_ascii=False))
    logger.info(f'{cid} {credit_code} {msv_last_time} ret={ret}')

    # immediately_gs(credit_code, reason='0819_report_repair', interval_sec=1)


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code != ProcessCode.INITIAL:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)
        for did, d in enumerate(reader):
            if did < args.input_csv_lid_min:
                continue
            if did >= args.input_csv_lid_max != -1:
                break
            d['did'] = did
            tc = TaskContext(inputs=d)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多线程处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='20240819_2023年年报补充抓取.input', help='')
    ap.add_argument('--input-csv-lid-min', type=int, default=-1)
    ap.add_argument('--input-csv-lid-max', type=int, default=-1)
    ap.add_argument('--worker-num', type=int, default=1)

    main(ap.parse_args())
    #
    # _d = {
    #     'did': 0,
    #     'company.name': 'name',
    #     'eq.company_graph_id': 3010578845,
    #     'company.credit_code': '91542233MAC694EK7T',
    #     'company.esta_date': '2009-03-02',
    # }
    # tc = TaskContext(inputs=_d)
    # process(tc)
    # logger.info(f'tc={tc}')
    #
