# encoding=utf8

import argparse
import time
from datetime import datetime, timedelta, date
from libs.dt import date2str
from libs.log2 import setup_logger
from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao
from dao.octopus.item import ItemDao
from dao.company import CompanyDao, Company
from gslib.id_center import id_center_query, EntityType
from resx.log import setup_logger

logger = setup_logger()


class GSNoticeDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_TEST.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.gs_notice')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', None)
        super().__init__(**kwargs)

    def get_latest(self, limit=400000):
        min_notice_from = datetime.now() - timedelta(days=45)
        max_notice_from = datetime.now() - timedelta(days=19)
        sql = f'select * from {self.db_tb_name} where notice_from between %s and %s limit %s'
        for d in self.mysql_client.select_many(sql, args=(min_notice_from, max_notice_from, limit)):
            yield d


company_dao = CompanyDao()
octopus_item_dao = ItemDao(name='company', is_clue=False)
gs_notice_dao = GSNoticeDao()


def process(d):
    notice_from, notice_to = d['notice_from'], d['notice_to']
    credit_code, notice_type = d['certificate_code'], d['type']
    did, name = d['id'], d['name']
    if notice_to < datetime(year=2000, month=1, day=1):
        notice_days = 45 if notice_type == 'reduce_capital_notice' else 20
        notice_to = notice_from + timedelta(days=notice_days)
    # if not (datetime.now() - timedelta(15) < notice_to < datetime.now()):
    #     return
    notice_to: datetime

    if len(credit_code) == 18:
        entity_type, cid = id_center_query(credit_no=credit_code)
        if entity_type != EntityType.ORG:
            logger.warning(f'get no cid credit_code={credit_code}')
            return
    else:
        entity_type, cid = id_center_query(name=name)
        if entity_type != EntityType.ORG:
            logger.warning(f'get no cid name={name}')
            return
    company: Company = company_dao.get(cid)
    if not company:
        return
    if notice_type == 'simple_notice':
        ret = octopus_item_dao.set_info(word=str(cid), do_update=True, cancel_date=date2str(notice_to.date()))
    elif notice_type == 'reduce_capital_notice':
        ret = octopus_item_dao.set_info(word=str(cid), do_update=True, reduce_cap_date=date2str(notice_to.date()))
    else:
        logger.warning(f'not handle type {notice_type}')
        return
    logger.info(f'{did} {notice_to.date()} {company.approved_date} {cid} {company.name} {notice_type} {ret}')


def main(args):
    # d = gs_notice_dao.get(14902)
    # process(d)
    # exit()

    logger.info(f'{args}')
    while True:
        for did, d in enumerate(gs_notice_dao.get_latest()):
            process(d)
        time.sleep(3600 * 6)


if __name__ == '__main__':
    from libs.log2 import setup_logger
    ap = argparse.ArgumentParser(description='octopus_company_gs_notice.py')
    logger = setup_logger()
    main(ap.parse_args())
