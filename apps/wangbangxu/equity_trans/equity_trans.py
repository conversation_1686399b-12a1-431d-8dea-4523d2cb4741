# encoding=utf8

import csv
from queue import Queue
import argparse
from typing import Dict
from pydantic import Field, conint, confloat
from libs.log2 import setup_logger
from dao.company import CompanyDao, Company, CompanyGraph, CompanyGraphDao
from dao.human import Human, HumanDao, HumanGraphDao, HumanGraph
from dao.investors.equity_ratio import EquityRatio, EquityRatioDao
from dao.investors.listed_shareholder import ListedShareholderDao
from entity.deps.entity import BaseEntity

logger = setup_logger()
company_graph_dao = CompanyGraphDao()
company_dao = CompanyDao()
human_graph_dao = HumanGraphDao()
human_dao = HumanDao()
equity_ratio_dao = EquityRatioDao()
listed_shareholder_dao = ListedShareholderDao()

EPS = 1e-9


class EquityNode(BaseEntity):
    gid: conint(ge=0)
    parent: str = Field(default='')
    level: int = Field(default=0)
    ratio: confloat(gt=-EPS, lt=1+EPS)
    exact: bool  # 是否是精确认缴

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def follow(self, min_equity_ratio: float, max_level: int):
        if self.gid == 0:
            # 空壳股东
            return False
        if self.ratio < min_equity_ratio:
            # 不follow小于1%的贡献链路
            return False
        if self.level >= max_level:
            return False
        return True


class EquityData(BaseEntity):
    gid: conint(ge=0)  # equity gid
    ratio_exact: confloat(gt=-EPS, lt=1+EPS) = Field(default=0)  # 认缴明确的股比部分
    ratio_ceiling: confloat(gt=-EPS, lt=1+EPS) = Field(default=0)  # 由于认缴未知的股比部分
    level: int = Field(default=999)  #
    parent: str = Field(default='')
    path_count: int = Field(default=0)  # 路径数量
    leaf: bool = Field(default=True)

    def update(self, equity_node: EquityNode):
        assert equity_node.gid == self.gid
        if equity_node.exact:
            self.ratio_exact += equity_node.ratio
        else:
            self.ratio_ceiling += equity_node.ratio
        if equity_node.level < self.level:
            self.level = equity_node.level
            self.parent = equity_node.parent
        self.path_count += 1


class EquitySheet(BaseEntity):
    gid0: conint(gt=0)
    data: Dict[int, EquityData] = Field(default_factory=dict)

    def update(self, equity_node: EquityNode):
        if equity_node.gid not in self.data:
            equity_data = EquityData(gid=equity_node.gid)
            self.data[equity_node.gid] = equity_data
        self.data[equity_node.gid].update(equity_node)

    def save(self, min_equity_ratio):
        with open(f'data/equity_trans.{self.gid0}.csv', 'w') as f:
            writer = csv.writer(f)
            writer.writerow(['企业名称', '登记状态', '股比(精确)', '股比(模糊)', '投资链路数量', '链路最小层级', '链路股东', 'leaf'])
            for graph_id, equity_data in self.data.items():
                if equity_data.ratio_exact + equity_data.ratio_ceiling < min_equity_ratio:
                    continue
                company_graph: CompanyGraph = company_graph_dao.get(field='graph_id', value=graph_id)
                if company_graph:
                    company: Company = company_dao.get(company_graph.cid)
                    if not company:
                        logger.warning(f'no company {graph_id}')
                        continue
                    name = company.name
                    status = company.reg_status
                else:
                    human_graph: HumanGraph = human_graph_dao.get(field='graph_id', value=graph_id)
                    if not human_graph:
                        logger.warning(f'no graph {graph_id}')
                        continue
                    human: Human = human_dao.get(human_graph.hid)
                    if not human:
                        logger.warning(f'no human {graph_id}')
                        continue
                    name = human.name
                    status = '-'
                writer.writerow([
                    name, status, equity_data.ratio_exact, equity_data.ratio_ceiling,
                    equity_data.path_count, equity_data.level, equity_data.parent, equity_data.leaf,
                ])


def process(gid0: int, min_equity_ratio: float, max_level: int, mode_investor: bool) -> bool:
    queue = Queue()
    node: EquityNode = EquityNode(gid=gid0, level=0, ratio=1, exact=True)
    queue.put(node)
    equity_sheet = EquitySheet(gid0=gid0)

    while not queue.empty():
        node = queue.get()
        field = 'company_graph_id' if mode_investor else 'shareholder_graph_id'

        for equity_ratio in equity_ratio_dao.get_many(value=node.gid, field=field):
            equity_ratio: EquityRatio
            if equity_ratio.deleted != 0:
                continue
            is_list_company = listed_shareholder_dao.is_listing_company(equity_ratio.company_graph_id)
            if (is_list_company and equity_ratio.source != 100) or (not is_list_company and equity_ratio.source == 100):
                logger.warning(f'bad listing status {equity_ratio}')
                continue

            if equity_ratio.percent is not None and equity_ratio.percent > EPS:  # 认缴已知  不改变exact的值
                ratio_new = node.ratio * equity_ratio.percent
                exact_new = node.exact
            else:
                ratio_new = node.ratio
                exact_new = False
                logger.warning(f'no percent equity_ratio {equity_ratio}')

            if mode_investor:
                gid_new = equity_ratio.shareholder_graph_id
                parent = equity_ratio.company_name
            else:
                gid_new = equity_ratio.company_graph_id
                parent = equity_ratio.shareholder_name

            if node.gid in equity_sheet.data:
                equity_sheet.data[node.gid].leaf = False

            node_new = EquityNode(gid=gid_new, parent=parent, level=node.level + 1, ratio=ratio_new, exact=exact_new)
            equity_sheet.update(node_new)

            if node_new.follow(min_equity_ratio, max_level):
                logger.debug(f'enqueue {node_new}')
                queue.put(node_new)
        logger.info(f'queue len {queue.qsize()}')

    equity_sheet.save(min_equity_ratio)
    return True


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='股东/对外投资穿透')
    ap.add_argument('--gid', type=int, default=3137174753, help='gid')
    ap.add_argument('--mode', type=str, default='investee', choices=['investee', 'investor', ])
    ap.add_argument('--min-equity-ratio', type=float, default=0.01, help='最小参与计算的股比')
    ap.add_argument('--max-level', type=int, default=3, help='最大穿透深度')
    ap_args = ap.parse_args()

    process(
        gid0=ap_args.gid,
        min_equity_ratio=ap_args.min_equity_ratio,
        max_level=ap_args.max_level,
        mode_investor=(ap_args.mode == 'investor'),
    )

    # 企查查  3469055311
    # 天眼查 2318455639
    # 中国机械工业集团有限公司 1190439
    # 福建圣农发展股份有限公司 241342986
    # 中国中车集团有限公司 3137569533
    # 中国铝业集团有限公司 3137174753
