# encoding=utf8

from typing import Optional, List
import argparse
from datetime import datetime, timedelta
from pydantic import Field
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from libs.log2 import setup_logger
from gslib.credit_code import credit_code_valid
from gslib.id_center import id_center_query
from gslib.gs_enum import EntityType
from clients.redis.redis_hash import RedisHash
from dao.qxb.enterprise import EnterpriseDao, Enterprise


logger = setup_logger()


class QxbCodeEid(BaseEntity):
    id: int
    credit_code: str
    eid: str
    cid: Optional[int] = Field(default=None)
    ts: Optional[datetime] = Field(default=None)


class QxbCodeEidDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_INNER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'internal.qxb_code_eid')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', QxbCodeEid)
        super().__init__(**kwargs)

    def write(self, credit_code, eid, cid) -> bool:
        ts = datetime.now()
        sql = f"""insert into {self.db_tb_name} (credit_code, eid, cid, ts) values(%s,%s,%s,%s) 
        on duplicate key update eid=%s, cid=%s, ts=%s"""
        ret = self.mysql_client.execute(sql, args=(credit_code, eid, cid, ts, eid, cid, ts))
        return ret == 1

    def get_many_for_update(self) -> List[QxbCodeEid]:
        ts = datetime.now() - timedelta(hours=24)
        items = []
        sql = f'select * from {self.db_tb_name} where cid is null and ts < %s limit %s'
        for d in self.mysql_client.select_many(sql, args=(ts, self.batch_size)):
            item = self._to_entity(d)
            if item is not None:
                items.append(item)
        return items


enterprise_dao = EnterpriseDao()
qxb_code_eid_dao = QxbCodeEidDao()


def process(items):
    logger.info(f'process total of {len(items)}')
    for item in items:
        item: Enterprise
        eid, credit_code = item.eid, item.credit_code
        if not credit_code_valid(credit_code):
            continue
        o: QxbCodeEid = qxb_code_eid_dao.get(value=item.credit_code, field='credit_code')
        if o and o.cid is not None:
            continue
        entity_type, entity_id = id_center_query(credit_no=credit_code)
        cid = entity_id if entity_type == EntityType.ORG else None
        ret = qxb_code_eid_dao.write(credit_code=credit_code, eid=eid, cid=cid)
        logger.info(f'credit_code_id write success credit_code={credit_code} ret={ret}')


def main(args):
    logger.info(f'args={args}')
    enterprise_offsets = RedisHash(
        db=2,
        name='code_eid_enterprise_offset',
        **ConstantProps.PROPS_GS_REDIS_ONLINE,
    )

    enterprise_dao.sharding_scan(
        process_fn=process,
        process_workers=args.process_num,
        scan_workers=args.dump_num,
        part_num=args.idx_num,
        redis_offset=enterprise_offsets,
        init_offset=datetime.now() - timedelta(days=args.start_days),
        infinite_wait_secs=600,
        scan_key='row_update_time',
    )


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='qx_eid.py')
    ap.add_argument('--dump-num', type=int, default=1, help='线程数')
    ap.add_argument('--start-days', type=int, default=90, help='row-update-time开始时间')
    ap.add_argument('--idx-num', type=int, default=256, help='只计算0-N的分片')
    ap.add_argument('--process-num', type=int, default=1, help='线程数')
    main(ap.parse_args())
