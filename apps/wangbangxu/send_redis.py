# encoding=utf8
import argparse
import sys
import time
from resx.config import CFG_REDIS_GS
from resx.func import split_parts
from resx.redis_types import Redis


def main():
    ap = argparse.ArgumentParser(description='send_redis.py')
    ap.add_argument('--db', type=int, required=True)
    ap.add_argument('--name', required=True)
    ap.add_argument('--fin', type=argparse.FileType('r'), default=sys.stdin)

    ap_args = ap.parse_args()
    print(f'ap_args={ap_args}')

    redis = Redis(**CFG_REDIS_GS, db=ap_args.db)

    for buf in split_parts(ap_args.fin, 100):
        values = dict((x.strip(), time.time()) for x in buf)
        ret = redis.zadd(ap_args.name, values)
        print(f'send values send={len(values)} ok={ret}')


if __name__ == '__main__':
    main()
