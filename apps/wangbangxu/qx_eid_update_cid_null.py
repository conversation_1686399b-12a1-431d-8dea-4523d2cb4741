# encoding=utf8
import time
from typing import List
import argparse
from resx.log import setup_logger
from gslib.id_center import id_center_query
from gslib.gs_enum import EntityType
from apps.wangbangxu.qx_eid import QxbCodeEid, QxbCodeEidDao

logger = setup_logger()
batch_size = 4000
qxb_code_eid_dao = QxbCodeEidDao(batch_size=batch_size)


def main(args):
    logger.info(f'args={args}')
    while True:
        items = qxb_code_eid_dao.get_many_for_update()
        logger.info(f'get {len(items)}')
        for item in items:
            entity_type, entity_id = id_center_query(credit_no=item.credit_code)
            cid = entity_id if entity_type == EntityType.ORG else None
            ret = qxb_code_eid_dao.write(credit_code=item.credit_code, eid=item.eid, cid=cid)
            logger.info(f'credit_code_id write credit_code={item.credit_code} cid={cid} ret={ret}')
        if len(items) < batch_size:
            time.sleep(30)


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='qx_eid_update_cid_null.py')
    main(ap.parse_args())
