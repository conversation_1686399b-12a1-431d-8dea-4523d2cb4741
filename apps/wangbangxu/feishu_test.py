import json

from libs.feishu import send_feishu_message_attachments
from libs.log2 import setup_logger

logger = setup_logger()

if __name__ == '__main__':
    # 工商抓取小队 oc_ff3954e7a265e2b30587f82d401d80f9
    # ret = send_feishu_message(ats=['王帮旭', '所有人'], text='我们123!!~@@')
    # ret = send_feishu_message(ats=['王帮旭', '所有人'], text='我们123!!~@@')
    pdf = open('/Users/<USER>/Documents/011002200711_39053825.pdf', 'rb').read()
    image = open('/Users/<USER>/Documents/WechatIMG372.jpg', 'rb').read()
    ret2 = send_feishu_message_attachments(chat_id='oc_ff3954e7a265e2b30587f82d401d80f9', ats=['王帮旭', '所有人'], text='我们123!!~@@', image=image, file=pdf, file_name='1.pdf')


