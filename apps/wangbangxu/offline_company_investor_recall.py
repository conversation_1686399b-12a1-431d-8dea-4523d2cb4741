from datetime import datetime
import json
import re
from typing import Dict, List
import argparse
import csv
import time
from datetime import timedelta
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter

import pydantic

from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor

from gslib.schedule import immediately_gs
from dao.deps.mysql_dao import MySQLDao
from dao.company import Company
from dao.company import CompanyDao
from dao.company import CompanyGraph
from dao.company import CompanyGraphDao
from dao.human import HumanGraph
from dao.human import HumanGraphDao
from dao.reports.annual_report import AnnualReport
from dao.reports.annual_report import AnnualReportDao
from dao.reports.annual_report_brief import AnnualReportBrief
from dao.reports.annual_report_brief import AnnualReportBriefDao
from dao.key_person.history_company_staff import HistoryCompanyStaff
from dao.key_person.history_company_staff import HistoryCompanyStaffDao
from dao.key_person.company_staff import CompanyStaff
from dao.key_person.company_staff import CompanyStaffDao
from dao.investors.equity_ratio import EquityRatioDao
from dao.investors.equity_ratio import EquityRatio
from dao.company_hk import CompanyHk
from dao.company_hk import CompanyHkDao
from clients.obs_client import OBSClient
from clients.redis.redis_queue import RedisQueue
from entity.deps.entity import BaseEntity
from gslib.msv import msv_query_base_info, msv_query_list_dim
from libs.dt import to_date, to_datetime
from gslib.id_center import id_center_query, EntityType
from gslib.credit_code import credit_code_valid
from entity.eventlog import Eventlog
from resx.log import setup_logger


logger = setup_logger()
fs = dict()
process_code_stat = Counter()

redis_queue = RedisQueue(name='normal_schedule_queue', max_length=1000000, **ConstantProps.PROPS_GS_REDIS_ONLINE)
obs_client = OBSClient(bucket_name='jindi-oss-gsxt-eventlog')
company_dao = CompanyDao(max_write_per_minute=3000)
company_graph_dao = CompanyGraphDao()
history_company_staff_dao = HistoryCompanyStaffDao()
company_staff_dao = CompanyStaffDao()
company_hk_dao = CompanyHkDao()
equity_ratio_dao = EquityRatioDao()
human_graph_dao = HumanGraphDao()

entry_dao = MySQLDao(
    db_tb_name='octopvs.entry_credit',
    pk_name='id',
    **ConstantProps.PROPS_GS_NG,
)


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    EXCLUDE_DEFAULT = auto()  # 非目标集合
    EXCLUDE_PROP2 = auto()  # 非目标集合
    EXCLUDE_NOT_GS = auto()  # 非目标集合
    EXCLUDE_BRANCH = auto()  # 非目标集合
    EXCLUDE_GETI = auto()  # 非目标集合
    EXCLUDE_KONGKE = auto()  # 非目标集合
    EXCLUDE_NO_CODE = auto()  # 非目标集合
    EXCLUDE_GRAPH = auto()  # graph.deleted

    DUP_FRESH = auto()  # 已经更新过的
    DUP_BAD_STATUS = auto()  # 非存续有股东
    DUP_SEARCH_EMPTY = auto()

    OK_BAD_STATUS = auto()  # 非存续 没有股东
    OK_NO_ER = auto()  # 存续 没有股东信息
    OK_NO_MSV_GSXT = auto()  # 总局没有股东信息
    OK_MSV_ER_DIFF = auto()  # 多源多版本gsxt_page和er不一致
    OK_MSV_NO_LAST_TIME = auto()  # 多源多版本gsxt_page太老
    OK_MSV_APPR_DIFF_DAY_0 = auto()  # 多源多版本gsxt_page太老
    OK_MSV_APPR_DIFF_DAY_1 = auto()  # 多源多版本gsxt_page太老

    DUP = auto()  # 没有发现问题


class TaskContext(BaseEntity):
    word: str
    code: ProcessCode = pydantic.Field(default=ProcessCode.INITIAL)
    context: Dict = pydantic.Field(default_factory=dict)
    ts: float = pydantic.Field(default_factory=time.time)


def process_core(company: Company, company_graph: CompanyGraph) -> ProcessCode:
    reg_status_ok = re.search('存续|在业|在营|开业', company.reg_status or '')

    ers: List[EquityRatio] = list(equity_ratio_dao.get_many(value=company_graph.cgid, field='company_graph_id'))

    if not reg_status_ok:
        if len(ers) > 0:
            return ProcessCode.DUP_BAD_STATUS
        else:
            return ProcessCode.OK_BAD_STATUS

    if len(ers) == 0:
        return ProcessCode.OK_NO_ER
    msv_investors = msv_query_list_dim(cid=company.cid, table_name='company_investor', source='gsxt_page')
    if len(msv_investors) == 0:
        return ProcessCode.OK_NO_MSV_GSXT

    seen_gids = dict()
    for er in ers:
        if er.deleted != 0:
            continue
        seen_gids[er.shareholder_graph_id] = {
            'name': er.shareholder_name,
            'er': True,
            'msv': False,
        }
    for msv_investor in msv_investors:
        msv_last_time = int(msv_investor.get('msv_last_time', '0'))
        if msv_last_time == 0:
            return ProcessCode.OK_MSV_NO_LAST_TIME
        msv_last_date = to_date(msv_last_time)
        diff_days = (msv_last_date - company.approved_date).days
        if diff_days == 1:
            return ProcessCode.OK_MSV_APPR_DIFF_DAY_1
        if diff_days < 1:
            return ProcessCode.OK_MSV_APPR_DIFF_DAY_0

        # logger.info(f'{msv_last_date} {company.approved_date}')

        if msv_investor['investor_type'] != 2:
            g: HumanGraph = human_graph_dao.get(field='human_id', value=msv_investor['investor_id'])
            inv_gid = g.hgid
        else:
            g: CompanyGraph = company_graph_dao.get(field='company_id', value=msv_investor['investor_id'])
            inv_gid = g.cgid

        if inv_gid not in seen_gids:
            seen_gids[inv_gid] = {
                'name': msv_investor['investor_name'],
                'er': False,
                'msv': True,
            }
        else:
            seen_gids[inv_gid]['msv'] = True

    for sh_gid in seen_gids:
        if not seen_gids[sh_gid]['er'] or not seen_gids[sh_gid]['msv']:
            logger.warning(f'error {seen_gids[sh_gid]}')
            return ProcessCode.OK_MSV_ER_DIFF

    return ProcessCode.DUP


def process(tc: TaskContext):
    if re.match(r'.{18}', tc.word):
        ent_type, cid = id_center_query(credit_no=tc.word)
        if ent_type != EntityType.ORG:
            tc.code = ProcessCode.EXCLUDE_DEFAULT
            return
    elif re.match(r'\d+', tc.word):
        cid = int(tc.word)
    else:
        tc.code = ProcessCode.EXCLUDE_DEFAULT
        return

    company: Company = company_dao.get(cid)
    if not company:
        tc.code = ProcessCode.EXCLUDE_DEFAULT
        return

    credit_code = company.credit_code or ''
    source_flag = company.source_flag or ''
    org_type = company.company_org_type or ''
    last_crawled_time = company.last_crawled_time or datetime.now()

    if company.prop2:
        tc.code = ProcessCode.EXCLUDE_PROP2
        return

    if not re.search('qyxy', source_flag):
        tc.code = ProcessCode.EXCLUDE_NOT_GS
        return

    if not company.establish_date or not company.reg_institute:
        tc.code = ProcessCode.EXCLUDE_KONGKE
        return
    if len(credit_code) != 18:
        tc.code = ProcessCode.EXCLUDE_NO_CODE
        return
    if credit_code[0] != '9' or credit_code[1] == '3':
        tc.code = ProcessCode.EXCLUDE_NOT_GS
        return
    if '个体' in org_type:
        tc.code = ProcessCode.EXCLUDE_GETI
        return
    if '分' in org_type or '办事处' in org_type:
        tc.code = ProcessCode.EXCLUDE_BRANCH
        return

    company_graph: CompanyGraph = company_graph_dao.get_by_cid(company.cid)
    if not company_graph:
        tc.code = ProcessCode.EXCLUDE_GRAPH
        return

    if last_crawled_time + timedelta(days=1) > datetime.now():
        tc.code = ProcessCode.DUP_FRESH
        return

    tc.code = process_core(company, company_graph)
    if tc.code.name.startswith('OK_'):
        # check search_empty
        crawler_code, ts = -1, None
        for path in obs_client.list(f'octopus/company/0/{cid}'):
            # octopus_entry-company-2849048-bj-1710296634-9.json
            if not path.endswith('9.json'):
                continue
            if '-XA-' not in path:
                continue
            data, ts1 = obs_client.get_content(path, with_ts=True)
            eventlog: Eventlog = Eventlog.from_dict(json.loads(data))
            if (ts is None or ts1 > ts) and ts1.date() > company.establish_date + timedelta(days=30):
                ts = ts1
                crawler_code = eventlog.crawler_code
        if crawler_code == 11:
            logger.info(f'search empty, set dup {cid} {credit_code}')
            tc.code = ProcessCode.DUP_SEARCH_EMPTY
    if tc.code.name.startswith('OK_'):
        s = json.dumps({
            'companyId': str(cid),
            'companyName': company.name,
            'creditCode': company.credit_code,
            'dims': [],
            'scheduleReason': 'INV_' + tc.code.name[3:],
            'score': 5,
        }, ensure_ascii=False)
        if redis_queue.redis.zcard(redis_queue.name) > 500000:
            logger.warning(f'redis sleep for length ')
            time.sleep(5.0)
        redis_queue.redis.zadd(
            redis_queue.name,
            {s: 100 - time.time()*1e-10},
        )
        logger.info(f'OUTPUT {cid} {company.credit_code}')


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code not in [ProcessCode.INITIAL, ProcessCode.INITIAL]:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    total = sum(process_code_stat.values())
    if total % 10000 == 0:
        for process_code in process_code_stat:
            count = process_code_stat[process_code]
            ratio = count / total
            logger.info(f'STAT {process_code} {count} {ratio:.4f}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    next_start_id = args.scan_start_id
    total = None if args.total == -1 else args.total
    with BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool:
        while True:
            for d in entry_dao.scan(start=next_start_id, scan_key='code1', total=total):
                credit_code = d['word']
                cid = json.loads(d['info'] or '{}').get('cid', 0)
                if cid != 0:
                    tc = TaskContext(word=str(cid))
                else:
                    tc = TaskContext(word=str(credit_code))
                future: Future = worker_pool.submit(process, tc)
                fs[future] = tc
                future.add_done_callback(callback_fn)
            next_start_id = 0
            if total is not None:
                break
        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='股比计算离线挖掘重算程序')
    ap.add_argument('--scan-start-id', type=str, default='000124902826')
    ap.add_argument('--total', type=int, default=2000)
    # ap.add_argument('--input-csv-name', type=str, default='20241009_investor_msv_xa_none', help='')
    # ap.add_argument('--input-csv-lid-min', type=int, default=-1)
    # ap.add_argument('--input-csv-lid-max', type=int, default=100)
    ap.add_argument('--worker-num', type=int, default=8)

    main(ap.parse_args())
#     cid_list = [2634690008,
# # 70019239,
# 2648799877,
# 9425409,
# 2460007467,
# 2425901400,
# 2604829549,
# 2473599036,]
#     for cid_ in cid_list:
#         tc = TaskContext(word=str(cid_))
#         process(tc)
#         logger.info(f'tc={tc}')
