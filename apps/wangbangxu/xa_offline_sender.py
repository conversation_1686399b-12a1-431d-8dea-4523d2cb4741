import json
import re
from typing import Dict
import argparse
import csv
import time
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor

from dao.company import Company, CompanyDao
from clients.redis.redis_queue import RedisQueue

logger = setup_logger()
fs = dict()
process_code_stat = Counter()

redis_queue = RedisQueue(name='normal_schedule_queue', max_length=1000000, **ConstantProps.PROPS_GS_REDIS_ONLINE)
company_dao = CompanyDao(max_write_per_minute=3000)


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    DUP = auto()  # 已经生效过的
    EXCLUDE = auto()  # 非目标集合
    OK = auto()  # 本次目标集合


@dataclass
class TaskContext(object):
    inputs: Dict
    send_reason: str
    cid_col_name: str
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


def process(tc: TaskContext):
    cid = int(tc.inputs[tc.cid_col_name])

    company: Company = company_dao.get(cid)
    if not company:
        tc.code = ProcessCode.EXCLUDE
        return
    if not re.fullmatch(r'9.{17}', company.credit_code or ''):
        tc.code = ProcessCode.EXCLUDE
        return

    s = json.dumps({
        'companyId': str(cid),
        'companyName': company.name,
        'creditCode': company.credit_code,
        'dims': [],
        'scheduleReason': tc.send_reason,
        'score': 5,
    }, ensure_ascii=False)
    if redis_queue.redis.zcard(redis_queue.name) > 500000:
        logger.warning(f'redis sleep for length ')
        time.sleep(5.0)
    ret = redis_queue.redis.zadd(redis_queue.name, {s: 100})
    logger.info(f'OUTPUT {cid} {company.credit_code} ret={ret}')


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code not in [ProcessCode.INITIAL, ProcessCode.EXCLUDE]:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')


def main(args):
    logger.info(f'args={args}')
    with BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool, open(f'{args.input_csv_name}.csv', 'r') as fin:
        reader = csv.DictReader(fin)
        for did, d in enumerate(reader):
            if did < args.lid_min:
                continue
            if did >= args.lid_max != -1:
                break
            d['did'] = did
            tc = TaskContext(inputs=d, send_reason=args.reason, cid_col_name=args.cid_col_name)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多线程处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, help='file name of data')
    ap.add_argument('-lid-min', type=int, default=-1)
    ap.add_argument('--lid-max', type=int, default=-1)
    ap.add_argument('--worker-num', type=int, default=8)
    ap.add_argument('--reason', type=str, required=True)
    ap.add_argument('--cid-col-name', type=str, required=True)

    main(ap.parse_args())
