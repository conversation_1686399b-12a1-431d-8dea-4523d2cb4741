
with company as (
    select c.id as cid,
        -- cg.gid,
        base,
        name,
        legal_person_name as legal_name,
        to_date(estiblish_time) as esta_date,
        to_date(approved_time) as appr_date,
        cast(c.property1 as string) credit_code,
        cast(c.reg_number as string) reg_number,
        c.crawledtime,
        nvl(parent_id, 0) as parent_id,
        c.reg_institute,
        c.reg_status,
        c.company_org_type,
        c.from_time,
        c.to_time,
        c.property2 as cid_ref,
        c.reg_capital,
--        c.business_scope,
--        clean.reg_status,
--        clean.reg_capital_amount,
        -- clean.actual_capital_amount,
--        clean.company_org_type,
--        clean.alias,
        -- biz.biz_type_id,
        if(c.reg_status regexp '销', nvl(other.remove_date, to_date(c.crawledtime)), null) as end_date,
        (case
            when (property2 is not null and property2 != '') then 'prop2'
            when source_flag regexp 'qyxy' then
                (case
                    when nvl(c.property1, '') = '' and nvl(c.reg_number, '') = '' then 'kongke'
                    when c.company_org_type regexp '个体' then 'gsxt_geti'
                    when nvl(c.company_org_type, '') == '' and name not regexp '公司|合伙' then 'gsxt_geti'
                    when property1 regexp '^93' then 'gsxt_nonghe'
                    else 'gsxt_gongsi'
                end)
            when source_flag regexp '^npo_foundation' then 'foundation'
            when source_flag regexp '^npo' then 'npo'
            when source_flag regexp '^hk' then 'hk'
            when source_flag regexp '^tw' then 'tw'
            when source_flag regexp '^institution' then 'institution'
            when source_flag regexp '^lawfirm' then 'lawfirm'
            when source_flag regexp '^org' then 'neworg'
            else 'others' end
        )sub_type
    from (
        select
            id as cid,
            *
        from ods.ods_prism1_company_df
        where pt = ${pt}
            and dw_is_del = 0
            and id % ${cid_mod} = 0
    ) c
    -- inner join (
    --     select
    --         graph_id as gid,
    --         company_id as cid
    --     from ods.ods_prism1_company_graph_df
    --     where pt = ${pt}
    --         and dw_is_del = 0
    --         and deleted = 0
    --         and graph_id % ${gid_mod} = 0
    --         and company_id % ${cid_mod} = 0
    -- ) cg on cg.cid = c.cid
    -- left join (
    --     select
    --         biz_type_id,
    --         company_gid
    --     from ods_prism_company_biz_type_df
    --     where pt = ${pt}
    --         and dw_is_del = 0
    --         and deleted = 0
    --         and company_gid % ${gid_mod} = 0
    -- ) biz on cg.gid = biz.company_gid
    -- left join (
    --     select
    --         id as cid,
    --         reg_capital_amount*1e-6 as reg_capital_amount,
    --         reg_capital_currency,
    --         actual_capital_amount*1e-6 as actual_capital_amount,
    --         company_org_type,
    --         reg_status,
    --         social_security_staff_num,
    --         alias
    --     from ods.ods_prism1_company_clean_info_df
    --     where pt = ${pt}
    --         and dw_is_del = 0
    --         and id % ${cid_mod} = 0
    -- ) clean on c.id = clean.cid
    left join (
        select
            id as cid,
            to_date(nvl(cancel_date, revoke_date)) as remove_date
        from ods.ods_prism1_company_other_info_df
        where pt = ${pt}
            and dw_is_del = 0
            and id % ${cid_mod} = 0
            and nvl(cancel_date, revoke_date) is not null
    ) other on c.id = other.cid
),
area_code_mapping_0 as (
    select *
    from ods.ods_prism_area_code_district_df
    where pt = ${pt}
        and dw_is_del = 0
        and version='V2020'
        and deleted=0
),
area_code_mapping as (
    select distinct
    province_code as area_code,
        province as province,
        null as city,
        null as district
    from area_code_mapping_0
    union
    select distinct
        city_code as area_code,
        province as province,
        city,
        null as district
    from area_code_mapping_0
    union
    select distinct
        district_code as area_code,
        province as province,
        city,
        district
    from area_code_mapping_0
),
area_code_mapping2 as (
    select
    province,
    (case
            when province regexp '北京市|上海市'  then province
            when city regexp '杭州市|厦门市|深圳市|广州市|苏州市' then city
            else '其他' end
    )city2,
    (case
            when province regexp '北京市'  then district
            when province regexp '上海市'  then '默认'
            when city regexp '杭州市|厦门市|深圳市|广州市|苏州市' then '默认'
            else '其他' end
    )district2,
    substr(area_code, 3) as area_code
    from area_code_mapping
)
select province, city2, district2, month_s, count(1) as num from
(
    select cid,
      if(reg_institute regexp '北京经济技术开发区', '北京市', province) as province,
    if(reg_institute regexp '北京经济技术开发区', '北京市', city2) as city2,
    if(reg_institute regexp '北京经济技术开发区', '经开区', district2) as district2,
     month_s from
    (
        select cid, substr(credit_code, 3, 6) as area_code, reg_institute,
        date_format(nvl(end_date, to_date('2099-01-01')), 'yyyyMM') as month_s
        from company where
            sub_type regexp 'gsxt_gongsi'
            and length(credit_code) = 18
            and year(esta_date)=2023
    ) t join area_code_mapping2 on t.area_code = area_code_mapping2.area_code
) t2 group by province, city2, district2, month_s
