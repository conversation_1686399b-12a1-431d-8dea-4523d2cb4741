# encoding=utf8

from typing import Optional
import re
import argparse
from datetime import datetime, timed<PERSON><PERSON>
from typing import List
from libs.log2 import setup_logger
from libs.dt import to_date
from libs.env import ConstantProps
from clients.redis.redis_hash import RedisHash
from dao.qxb.enterprise import EnterpriseDao, Enterprise
from dao.company_other_info import CompanyOtherInfoDao, CompanyOtherInfo
from apps.wangbangxu.qx_eid import QxbCodeEidDao, QxbCodeEid
from gslib.id_center import id_center_query, EntityType

logger = setup_logger()
redis_offset = RedisHash(db=3, name='channel_report:qx_company_other_info', **ConstantProps.PROPS_GS_REDIS_ONLINE)
enterprise_dao = EnterpriseDao()
company_other_info_dao = CompanyOtherInfoDao()
code_eid_dao = QxbCodeEidDao()

# revoke_reason  revoke_date 吊销
# logout_reason logout_date 注销
# undo_reason undo_date  撤销  QX没有


def get_cid(credit_code: str) -> Optional[int]:
    if not isinstance(credit_code, str) or len(credit_code) != 18:
        return None
    code_eid: QxbCodeEid = code_eid_dao.get(value=credit_code, field='credit_code')
    if code_eid and code_eid.cid:
        return code_eid.cid
    entity_type, cid = id_center_query(credit_no=credit_code)
    if entity_type == EntityType.ORG:
        return cid
    return None


def process(items: List[Enterprise]):
    for item in items:
        process_one(item)


def process_one(e: Enterprise):
    company_other_info_dict = {}
    if re.fullmatch(r'\d{4}-\d{2}-\d{2}', e.revoke_date or ''):
        revoke_date = to_date(e.revoke_date)
        if revoke_date:
            company_other_info_dict['revoke_date'] = revoke_date
    if e.revoke_reason:
        company_other_info_dict['revoke_reason'] = e.revoke_reason

    if re.fullmatch(r'\d{4}-\d{2}-\d{2}', e.logout_date or ''):
        logout_date = to_date(e.logout_date)
        if logout_date:
            company_other_info_dict['cancel_date'] = logout_date
    if e.logout_reason:
        company_other_info_dict['cancel_reason'] = e.logout_reason

    if len(company_other_info_dict) == 0:
        return

    cid = get_cid(e.credit_code)
    if not cid:
        logger.warning(f'no cid for {e.eid} {e.credit_code}')
        return
    company_other_info_dict['id'] = cid
    company_other_info: CompanyOtherInfo = company_other_info_dao.get(cid)
    if not company_other_info:
        ret = company_other_info_dao.insert_or_update(dup_keys=['id'], d=company_other_info_dict)
        logger.info(f'insert {company_other_info_dict} ret={ret}')
    else:
        for k, v in list(company_other_info_dict.items()):
            if k == 'id':
                continue
            v0 = getattr(company_other_info, k)
            if v0 != '' and v0 is not None:
                del company_other_info_dict[k]
        if len(company_other_info_dict) > 1:
            ret = company_other_info_dao.insert_or_update(dup_keys=['id'], d=company_other_info_dict)
            logger.info(f'update {company_other_info_dict} ret={ret}')
        else:
            logger.debug(f'no update {company_other_info_dict}')


def main(args):
    logger.info(f'{args}')

    if args.start_days == -1:
        row_update_time_default = datetime(year=2000, month=1, day=1)
    else:
        row_update_time_default = datetime.now() - timedelta(days=args.start_days)

    enterprise_dao.sharding_scan(
        process_fn=process,
        process_workers=args.process_num,
        scan_workers=args.dump_num,
        part_num=args.part_num,
        redis_offset=redis_offset,
        init_offset=row_update_time_default,
        infinite_wait_secs=600,
        scan_key='row_update_time',
    )


def test():
    eid = 'aa0b22c2-2301-46d0-878a-b6e9cdfc01f6'
    e = enterprise_dao.get(value=eid, field='eid')
    process_one(e)


if __name__ == '__main__':
    from libs.log2 import setup_logger
    # qx 渠道直接入库 到 company_other_info
    ap = argparse.ArgumentParser(description='qx_company_other_info.py')
    ap.add_argument('--dump-num', type=int, default=1, help='dump线程数')
    ap.add_argument('--process-num', type=int, default=1, help='处理线程数')
    ap.add_argument('--part-num', type=int, default=1, help='part num')
    ap.add_argument('--start-days', type=int, default=-1, help='首次运行 row-update-time开始时间')
    main(ap.parse_args())
    # test()
