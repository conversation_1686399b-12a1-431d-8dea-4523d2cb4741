import time
import random
from resx.log import setup_logger
from resx.hive_client import HiveClient
from biz_utils.schedule import gs_dispatch
logger = setup_logger()
hive_client = HiveClient()

# cat logs/octopus_solver.log.2025-04-16_19 | grep new_report_2024 | grep companyReport
# 每次均匀调度10万条，耗时1天+
while True:
    items = list(hive_client.run(hql=open('schedule_report_fresh_year.sql', 'r').read()))
    logger.info(f'total items={len(items)}')
    mod = max(1, len(items) // 100000) + random.randint(1, 99)
    items = items[::mod]
    for iid, item in enumerate(items):
        gs_dispatch(reason='new_report_2024', cid=item['company.cid'], dims=['annualreport_2024', ], immediately=False)
        time.sleep(1)
