import argparse
import json
from csv import DictWriter
from libs.log2 import setup_logger
from gslib.credit_code import credit_code_valid
from gslib.id_center import id_center_query
from clients.obs_client import OBSClient
from dao.company import CompanyDao, Company
from entity.eventlog import Eventlog

logger = setup_logger()
obs_client = OBSClient()
company_dao = CompanyDao()


def main(args):
    input_file = args.input
    with open(input_file, 'r') as f, open(f'{input_file}.output.csv', 'w') as f_out:
        writer = DictWriter(f_out, fieldnames=['no', 'credit_code', 'found', 'info', 'ab_info', 'valid_code', 'api_ret'])
        writer.writeheader()
        for line in f:
            if len(line) < 10:
                continue
            no, credit_code = line.strip().split()

            etype, cid = id_center_query(credit_no=credit_code)
            valid_code = credit_code_valid(credit_code)
            target = ''
            for path in obs_client.list(f'octopus/company/0/{cid}'):
                # octopus_entry-company-2849048-bj-1710296634-9.json
                if not path.endswith('9.json'):
                    continue
                data = obs_client.get(path)
                if all(reason not in data for reason in ['20240319_wx_xa_chk', '20240320_wx_xa_chk', 'platform']):
                    continue
                eventlog: Eventlog = Eventlog.from_dict(json.loads(data))
                if eventlog.selector.reason not in ['20240319_wx_xa_chk', '20240320_wx_xa_chk', 'platform']:
                    continue
                target = eventlog.parser.get('abInfo', '{}')
                break
            logger.info(f'{credit_code} {etype} {cid} {valid_code} {target}')

            info = ''
            if cid > 0:
                c: Company = company_dao.get(cid)
                if c:
                    info = f'{c.name}#{c.legal_name}#{c.establish_date}#{c.reg_status}#{c.reg_location}'

            writer.writerow({
                'no': no,
                'credit_code': credit_code,
                'found': cid,
                'valid_code': valid_code,
                'ab_info': target,
                'info': info,
            })

            # ret = requests.post(
            #     url='http://172.24.114.24/gsxt/v2/update/merge',
            #     json={
            #         "companySign": credit_code,
            #         "record": "20240320_wx_xa_chk",
            #         "channelType": 2,
            #         "channel": {
            #             "channelGroup": "20240320_wx_xa_chk",
            #             "credit_code": credit_code,
            #         }
            #     },
            #     verify=False,
            #     timeout=3,
            # )
            # logger.info(f'OUTPUT {ret.text}')
            # writer.writerow({
            #     'no': no,
            #     'credit_code': credit_code,
            #     'api_ret': ret.text,
            # })


if __name__ == '__main__':
    # qx 渠道直接入库 到 company_other_info
    ap = argparse.ArgumentParser(description='')
    ap.add_argument('--input', type=str, default='20240319_wx_xa_chk.input.23.txt')
    main(ap.parse_args())
