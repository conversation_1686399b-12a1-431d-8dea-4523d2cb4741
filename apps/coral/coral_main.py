# encoding=utf8
import time
import argparse
from concurrent.futures import Future
from libs.env import get_stack_info, get_pygs_dir
from libs.dt import cur_ts_sec
from libs.log2 import setup_logger
from apps.coral.core.spider import BaseSpider
from apps.coral.core.task import TaskCode
from apps.coral.core.conf import SpiderConf
from apps.coral.spiders import *


class Main(object):
    def __init__(self, args):
        logger.info(f'args={args}')

        conf_path = f'{get_pygs_dir()}/apps/coral/conf/{args.conf}.json'
        with open(conf_path, 'r') as fp:
            conf_text = fp.read()
            conf = SpiderConf.model_validate_json(conf_text)
            spider_class = globals()[conf.spider_class]
            self.spider: BaseSpider = spider_class(
                crawl_workers=conf.crawl_workers,
                **conf.spider_args,
            )
        self.crawler_futures = {}

    def run(self):
        for task in self.spider.generate_tasks():
            future: Future = self.spider.crawler.submit(self.spider.crawl, task)
            self.crawler_futures[future] = task
            future.add_done_callback(crawler_callback)

        while True:
            logger.info('main thread do nothing.')
            time.sleep(10)


def crawler_callback(f: Future):
    task = main.crawler_futures[f]
    del main.crawler_futures[f]
    try:
        f.result()
        if task.code == TaskCode.UNFILLED:
            task.code = TaskCode.OK
    except Exception as e:
        logger.info(f'error process {task}  e={e} {get_stack_info()}')
        task.code = TaskCode.EXCEPTION
    finally:
        task.done_ts = cur_ts_sec()
        logger.info(f'OUTPUT {task.model_dump_json()}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='多线程并发抓取框架，内置调度逻辑 简易版')
    ap.add_argument('--conf', type=str, default='china_npo_list', help='xxx.json')
    ap.add_argument('--backup-days', type=int, default=1, help='日志保存天数')
    ap_args = ap.parse_args()
    logger = setup_logger(
        use_file_log=True,
        app_name=f'coral.{ap_args.conf}',
        backup_count=ap_args.backup_days,
        rotate_mode='D',
    )
    main = Main(ap_args)
    main.run()
