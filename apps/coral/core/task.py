from __future__ import annotations
import logging
from enum import Enum
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class TaskCode(Enum):
    UNFILLED = -1
    OK = 0
    CRAWL_FAIL = 1
    PARSE_FAIL = 2
    EXCEPTION = 3


class Task(BaseModel):
    spider_name: str  # which spider run the task.
    params: dict  # the task detail data
    start_ts: int = Field(default=-1)
    done_ts: int = Field(default=-1)
    meta: dict
    code: TaskCode = Field(default=TaskCode.UNFILLED)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
