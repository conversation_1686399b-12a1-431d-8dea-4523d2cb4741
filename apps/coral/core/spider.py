from __future__ import annotations
from typing import Generator
from abc import abstractmethod
import logging
from libs.concurrent import BoundedExecutor
from apps.coral.core.task import Task

logger = logging.getLogger(__name__)


class BaseSpider(object):
    def __init__(
            self,
            crawl_workers: int,
    ):
        self.crawler = BoundedExecutor(max_workers=crawl_workers, thread_name_prefix='crawler')

    # 定义调度逻辑
    @abstractmethod
    def generate_tasks(self) -> Generator[Task, None, None]:
        pass

    # 定义抓取一个task， 抓取结果在task内部
    @abstractmethod
    def crawl(self, task: Task):
        pass
