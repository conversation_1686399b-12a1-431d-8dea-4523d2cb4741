import logging
import re
import time
from typing import Generator
from pydantic import BaseModel
from DrissionPage import ChromiumPage, ChromiumOptions
from libs.dt import cur_ts_sec
from clients.obs_client import OBSClient
from dao.npo.npo_suspected_illegal import NpoSuspectedIllegalDao, NpoSuspectedIllegal
from dao.npo.npo_canceled_illegal import NpoCanceledIllegalDao, NpoCanceledIllegal
from apps.coral.core.task import Task, TaskCode
from apps.coral.core.spider import BaseSpider

logger = logging.getLogger(__file__)


class ChinaNPOListTaskType(BaseModel):
    list_name: str


class ChinaNPOListTask(Task):
    params: ChinaNPOListTaskType

    def __init__(self, **kwargs):
        kwargs.setdefault('spider_name', 'china_npo_list')
        super().__init__(**kwargs)


class ChinaNPOListSpider(BaseSpider):
    def __init__(self, **kwargs):
        self.npo_suspected_illegal_dao = NpoSuspectedIllegalDao()
        self.npo_canceled_illegal_dao = NpoCanceledIllegalDao()
        self.obs = OBSClient(bucket_name='jindi-oss-gsxt')
        super().__init__(**kwargs)

    def generate_tasks(self) -> Generator[Task, None, None]:
        while True:
            for list_name in ['canceled_illegal', 'suspected_illegal', ]:
                yield ChinaNPOListTask.model_validate({
                    "spider_name": "china_npo_list",
                    "params": {
                        "list_name": list_name,
                    },
                    "start_ts": cur_ts_sec(),
                    "done_ts": -1,
                    "meta": {},
                })
                time.sleep(3600 * 12)

    def crawl(self, task: Task):
        logger.info(f'task={task}')
        if not isinstance(task, ChinaNPOListTask):
            task.code = TaskCode.CRAWL_FAIL
            return
        params: ChinaNPOListTaskType = task.params
        if params.list_name == 'suspected_illegal':
            url = 'https://xxgs.chinanpo.mca.gov.cn/gsxt/illeagalorg'
        elif params.list_name == 'canceled_illegal':
            url = 'https://xxgs.chinanpo.mca.gov.cn/gsxt/banillegalorg'
        else:
            task.code = TaskCode.CRAWL_FAIL
            logger.warning(f'bad list_name {params.list_name}')
            return

        chromium_options = ChromiumOptions(read_file=False)
        chromium_options.set_proxy('10.99.203.222:9981')
        chromium_options.headless()
        chromium_options.set_user_agent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36')
        page = ChromiumPage(addr_or_opts=chromium_options)
        page.get(url)

        screen_shot_count = 0
        item_count = 0
        while True:
            time.sleep(1.0)  # sleep 1.0 for page.ele
            row_elements = page.eles('tag:li@@class=list_li')
            logger.info(f'row_elements len={len(row_elements)}')

            screen_shot_id = 0
            for row_element in row_elements:
                logger.info(f'row_element {row_element.text}')
                if params.list_name == 'suspected_illegal':
                    name = row_element.text.strip()
                    patterns = [
                        r'(.*)（民政部登记有(.*)）',  # 中国人民和平发展基金会（民政部登记有中国和平发展基金会）
                        r'(.*)（(.*)系在民政部合法登记的社会组织）',  # 中华慈善总会华慈文旅公益基金运城网络公益协会（中华慈善总会系在民政部合法登记的社会组织）
                        r'(.*)（合法登记的为(.*)）',  # 中国物业管理服务协会（合法登记的为中国物业管理协会）
                        r'(.*)（我部登记有(.*)）',  # 中国对外文化交流发展协会（我部登记有中国对外文化交流协会）
                        r'([^（]*)（已劝散(.*)）',  # 珠海市青少年高尔夫培训中心（已劝散）
                    ]
                    for pattern in patterns:
                        mo = re.match(pattern, name)
                        if mo:
                            npo_name, similar_to_npo = mo.groups()
                            break
                    else:
                        npo_name, similar_to_npo = name, ''
                    suspected_illegal = NpoSuspectedIllegal(npo_name=npo_name, similar_to_npo=similar_to_npo)
                    item_count += 1
                    changed, inserted, item_id = self.npo_suspected_illegal_dao.save_by_cmp(
                        item=suspected_illegal,
                        fields=['npo_name', ],
                        ignore_fields=['screen_shot_id', ]
                    )
                    suspected_illegal = self.npo_suspected_illegal_dao.get(item_id)
                    if suspected_illegal.screen_shot_id == 0:
                        if screen_shot_id == 0:
                            screen_shot_count += 1
                            screen_shot_id = cur_ts_sec()
                        suspected_illegal.screen_shot_id = screen_shot_id
                        self.npo_suspected_illegal_dao.save_by_cmp(
                            item=suspected_illegal,
                            fields=['npo_name', ],
                        )
                else:
                    # 中国少儿艺术教育家协会（石家庄）（北京市民政部门取缔）
                    # 中国写作学会小学作文教育研究会（河北省民政部门取缔）（注：中国写作学会为合法社会组织）
                    name = row_element.text.strip()
                    mo = re.match(r'(.*)（(.*)取缔）', name)
                    if mo:
                        npo_name, canceled_by = mo.groups()
                    else:
                        npo_name, canceled_by = name, ''
                    canceled_illegal = NpoCanceledIllegal(npo_name=npo_name, canceled_by=canceled_by)
                    item_count += 1
                    changed, inserted, item_id = self.npo_canceled_illegal_dao.save_by_cmp(
                        item=canceled_illegal,
                        fields=['npo_name', ],
                        ignore_fields=['screen_shot_id', ]
                    )
                    canceled_illegal = self.npo_canceled_illegal_dao.get(item_id)
                    if canceled_illegal.screen_shot_id == 0:
                        if screen_shot_id == 0:
                            screen_shot_count += 1
                            screen_shot_id = cur_ts_sec()
                        canceled_illegal.screen_shot_id = screen_shot_id
                        self.npo_canceled_illegal_dao.save_by_cmp(
                            item=canceled_illegal,
                            fields=['npo_name', ],
                        )
            if screen_shot_id != 0:
                screenshot_bytes = page.get_screenshot(as_bytes='png', full_page=True)
                self.obs.put(f'page/china_npo_list/{screen_shot_id}.png', screenshot_bytes)
                time.sleep(1.0)  # sleep 1 second for different screenshot file name.
            next_page_element = page.ele('tag:li@@title=下一页')
            # next_page_element = page.ele('tag:li@@title=72')
            if next_page_element.attr('aria-disabled') == 'true':
                break
            next_page_element.click()
            logger.info(f'next page screen_shot_count={screen_shot_count} item_count={item_count}')
            # if item_count > 20:
            #     break
        task.meta['screen_shot_count'] = screen_shot_count
        task.meta['item_count'] = item_count
        page.close()
