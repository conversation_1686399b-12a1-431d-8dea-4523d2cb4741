import streamlit as st
from gslib.credit_code import credit_code_valid, credit_code_incr

st.title('数据编码')


text_in = st.text_area(label='输入多个统一信用代码 判断是否有效')
text_out = ''
for x in text_in.split():
    x = x.strip()
    text_out += f'{x} {credit_code_valid(x)}\n'
st.text_area(label='输出', value=text_out)

text_in = st.text_area(label='输入统一信用代码返回后续的20个')
text_out = ''
credit_code = text_in.strip()
for incr in range(1, 20):
    x2 = credit_code_incr(credit_code, incr)
    text_out += f'{x2}\n'
st.text_area(label='输出', value=text_out)
