import streamlit as st
from gslib.id_center import id_center_query

st.title('id中心查询')


# name: str = None,
#         credit_no: str = None,
#         reg_number: str = None,
#         source: str = 'pygs',
#         use_graph_id: bool = False,
#         all_match: bool = False,
#         type: EntityType = EntityType.UNSET,

name = st.text_input('名称')
credit_no = st.text_input('credit_no')
reg_number = st.text_input('reg_number')

result = id_center_query(
    name=(name or None),
    credit_no=(credit_no or None),
    reg_number=(reg_number or None),
)
st.text_area(label='输出', value=f'{result}')
