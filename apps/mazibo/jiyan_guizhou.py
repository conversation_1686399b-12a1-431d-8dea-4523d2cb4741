# -*- coding: UTF-8 -*-
'''
@Project ：pygs-work-parent 
@File    ：jiyan_guizhou.py
<AUTHOR>
@Date    ：2024/11/18 19:45 
'''
import json
import time
import uuid
import redis
import requests
from concurrent.futures import ThreadPoolExecutor
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
from loguru import logger

redis_server = redis.Redis(host='redis-2b0f46e5-545a-4c21-a4a4-053daff1ee7b.cn-north-4.dcs.myhuaweicloud.com',
                           port=6379, username='root', password='rdsnhgot0akp17Xq', db=8)


def requests_function_get(url, params=None, proxy=None, verify=False, cookies=None, headers=None):
    for _ in range(5):
        try:
            resp = requests.get(url, headers=headers, proxies=proxy, verify=verify, timeout=3,
                                params=params, cookies=cookies)
            if resp.status_code == 200:
                return resp
            else:
                continue
        except Exception:
            continue


def requests_function_post(url, headers=None, data=None, proxy=None, verify=False, json=None, cookies=None):
    for _ in range(5):
        try:
            resp = requests.post(url, headers=headers, json=json, timeout=30, proxies=proxy, verify=verify,
                                 data=data, cookies=cookies)
            if resp.status_code == 200 and resp.text:
                return resp
            elif resp.status_code == 501:
                return ""
            else:
                continue
        except Exception:
            continue


def verify_first_requests():
    headers = {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-language": "zh-CN,zh;q=0.9",
        "cache-control": "no-cache",
        "pragma": "no-cache",
        "priority": "u=0, i",
        "sec-ch-ua": "\"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"macOS\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
                      " (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
        "x-requested-with": "XMLHttpRequest"
    }
    url = "https://gsxt.amr.guizhou.gov.cn/registerValidate.jspx"
    params = {
        "t": str(time.time() * 1000)
    }
    resp = requests_function_get(url, headers=headers, params=params, proxy={
        'http': 'http://************:30636',
        'https': 'http://************:30636'
    }, verify=False)
    if resp:
        json_data = resp.json()
        return json_data["gt"], json_data["challenge"]
    else:
        return "", ""


def verify_second_requests(gt):
    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Pragma": "no-cache",
        "Sec-Fetch-Dest": "script",
        "Sec-Fetch-Mode": "no-cors",
        "Sec-Fetch-Site": "cross-site",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
                      " (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
        "sec-ch-ua": "\"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"macOS\""
    }
    url = "https://api.geetest.com/gettype.php"
    callback_str = "geetest_" + str(int(time.time() * 1000))
    params = {
        "gt": gt,
        "callback": callback_str
    }
    requests_function_get(url, headers=headers, params=params, proxy={
        'http': 'http://************:30636',
        'https': 'http://************:30636'
    }, verify=False)


def verify_third_requests(gt, challenge, refer):
    url = "http://*************:8778/get_validate_v3"
    data = {
        "gt": gt,
        "challenge": challenge,
        "refer": refer
    }
    resp = requests_function_post(url, json=data)
    if resp:
        json_data = resp.json()

        return json_data["challenge"], json_data["validate"]
    return "", ""


def prd_chanllenge_validate():
    queue_name = "jiyan_verify_guizhou_queue"
    while 1:
        gt, challenge = verify_first_requests()
        if gt and challenge:
            verify_second_requests(gt)
        challenge, validate = verify_third_requests(gt, challenge, "https://gsxt.amr.guizhou.gov.cn/")
        if challenge and validate:
            logger.info(challenge + ", " + validate)
            unique_id = str(uuid.uuid4())
            item = {
                "challenge": challenge,
                "validate": validate
            }
            json_data = json.dumps(item)
            current_time = int(time.time() * 1000)
            redis_server.zadd(queue_name, {unique_id: current_time})
            redis_server.setex(unique_id, 180, json_data)


with ThreadPoolExecutor(50) as t:
    for _ in range(50):
        t.submit(prd_chanllenge_validate)
