# -*- coding: UTF-8 -*-
"""
@Project ：pygs-work-parent 
@File    ：functionSet.py
<AUTHOR>
@Date    ：2024/11/13 19:32 
"""
import json
import time
import paramiko
import redis
import requests
from typing import Dict
from loguru import logger
from requests.exceptions import RequestException
from clients.mysql_client import MySQLClient
from kafka import KafkaConsumer, TopicPartition
import yaml
import os
import random

REDIS_POOL = redis.ConnectionPool(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com',
                                  port=6379, username='root', password='3lvadmpiSj61ge', db=8, max_connections=10)


def get_pygs_dir():
    cur_path = os.path.abspath(__file__)
    while True:
        new_path = os.path.dirname(cur_path)
        if new_path == cur_path:
            print('error get get_env_yml_path, at root path %s, no env.yaml? exit 1' % cur_path)
            exit(1)
        cur_path = new_path
        env_yml_path = os.path.join(cur_path, 'env.yml')
        if os.path.isfile(env_yml_path):
            return cur_path
        else:
            time.sleep(10)


class TypePortFunctionSet:
    def __init__(self, url: str, method: str, timeout: int, max_retries: int,
                 headers: Dict[str, str], data: Dict[str, str], params: Dict[str, str], threshold_val: int,
                 cur_val: int):
        self.url = url
        self.method = method
        self.timeout = timeout
        self.max_retries = max_retries
        self.headers = headers
        self.data = data
        self.params = params
        self.cur_val = cur_val
        self.threshold_val = threshold_val

    def send_request(self):
        for attempt in range(self.max_retries):
            try:
                if self.method == "GET":
                    try:
                        response = requests.get(self.url, params=self.params, headers=self.headers,
                                                timeout=self.timeout)
                        if response.status_code == 200:
                            self.cur_val = 1
                            return self.cur_val
                    except Exception as error_get:
                        logger.error(error_get)
                        self.cur_val = 0
                        return self.cur_val
                elif self.method == "POST":
                    try:
                        response = requests.head(self.url)
                        if response.status_code in (200, 405):
                            self.cur_val = 1
                            return self.cur_val
                    except Exception as error_post:
                        logger.error(error_post)
                        self.cur_val = 0
                        return self.cur_val

            except RequestException:
                if attempt == self.max_retries - 1:
                    self.cur_val = 0
                    return self.cur_val


class TypeProcessFunctionSet:
    def __init__(self, server_ip: str, process_name: str, threshold_val: int, cur_val: int):
        self.server_ip = server_ip
        self.process_name = process_name
        self.threshold_val = threshold_val
        self.cur_val = cur_val
        self.ssh_client_dict = {}
        self.client = self.get_ssh_client(self.server_ip)

    def get_ssh_client(self, ip):
        if ip not in self.ssh_client_dict:
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh_client.connect(ip, username='work', key_filename="work.pem")
            self.ssh_client_dict[ip] = ssh_client
        return self.ssh_client_dict[ip]

    def remote_ps(self):
        stdin, stdout, stderr = self.client.exec_command(f"ps -aux | grep {self.process_name} | grep -v grep | wc -l")
        result = stdout.readlines()
        self.cur_val = result[0].strip('\n')
        self.client.close()
        return self.cur_val


class TypeKafkaFunctionSet:
    def __init__(self, bootstrap_servers_name: str, topic_name: str, consumer_group: str,
                 threshold_val: int, cur_val: int):
        self.bootstrap_servers = self.load_config(bootstrap_servers_name)
        self.topic_name = topic_name
        self.consumer_group = consumer_group
        self.threshold_val = threshold_val
        self.cur_val = cur_val
        self.kafka_consumer_client = KafkaConsumer(
            bootstrap_servers=self.bootstrap_servers,
            enable_auto_commit=False,
            group_id=self.consumer_group,
        )

    @staticmethod
    def load_config(bootstrap_servers_name):
        with open(os.path.join(get_pygs_dir(), 'env.yml'), 'rb') as f:
            item = yaml.safe_load(f)
        server = item["kafka"]["tyc"][bootstrap_servers_name]["bootstrap_servers"]
        return server

    def kafka_lag(self):
        consumer = self.kafka_consumer_client
        partitions = consumer.partitions_for_topic(self.topic_name)
        if not partitions:
            logger.info(f"Topic '{self.topic_name}' 不存在或无分区！")
            return ""
        topic_partitions = [TopicPartition(self.topic_name, p) for p in partitions]
        consumer.assign(topic_partitions)
        end_offsets = consumer.end_offsets(topic_partitions)
        consumer_offsets = {tp: consumer.committed(tp) or 0 for tp in topic_partitions}
        total_lag = 0
        for tp in topic_partitions:
            log_end_offset = end_offsets[tp]
            consumer_offset = consumer_offsets.get(tp, 0)
            lag = log_end_offset - consumer_offset
            total_lag += lag
        self.cur_val = total_lag
        return self.cur_val


class TypeRedisFunctionSet:
    def __init__(self, redis_server_name: str, redis_db: int, queue_name: str, queue_type: str,
                 threshold_val: int, cur_val: int):
        self.redis_host, self.redis_password = self.load_config(redis_server_name)
        self.redis_cli = redis.Redis(host=self.redis_host, password=self.redis_password, port=6379,
                                     db=redis_db)
        self.queue_name = queue_name
        self.queue_type = queue_type
        self.threshold_val = threshold_val
        self.cur_val = cur_val

    @staticmethod
    def load_config(redis_server_name):
        with open(os.path.join(get_pygs_dir(), 'env.yml'), 'rb') as f:
            item = yaml.safe_load(f)
        if redis_server_name != "default":
            host = item["redis"]["tyc"][redis_server_name]["host"]
            password = item["redis"]["tyc"][redis_server_name]["password"]
        else:
            host = "localhost"
            password = None
        return host, password

    def redis_func(self):
        if self.queue_type == "list":
            self.cur_val = self.redis_cli.llen(self.queue_name)
            return self.cur_val

        elif self.queue_type == "zset":
            self.cur_val = self.redis_cli.zcard(self.queue_name)
            return self.cur_val


class TypeSpiderFunctionSet:
    def __init__(self, item_name: str, inst_name: str, time_scope: int, threshold_val: int, cur_val: int):
        self.item_name = item_name
        self.inst_name = inst_name
        self.time_scope = time_scope
        self.threshold_val = threshold_val
        self.cur_val = cur_val
        self.mysql_client = MySQLClient(
            host="0035c980965348f3b3ace8e7d92d2d0bin01.internal.cn-north-4.mysql.rds.myhuaweicloud.com",
            user="jdtest_d_gong_ddl",
            password="CoIfwlop8nnNJzAAOA",
            port=3306
        )

    def sql_func(self):
        time_scope = int(self.time_scope / 60)
        cal_sql = f"""
                SELECT SUM(count) AS total_sum
                FROM octopus.solver_minute_count
                WHERE minute BETWEEN FLOOR(UNIX_TIMESTAMP(NOW() - INTERVAL {time_scope} MINUTE) / 60) 
                                  AND FLOOR(UNIX_TIMESTAMP(NOW()) / 60)
                                  and item_name = "{self.item_name}"
                                  and inst_name = "{self.inst_name}"
                   """
        result = self.mysql_client.select(cal_sql)
        if result:
            result = result["total_sum"]
        else:
            result = 0
        return result


class TypeSpider2FunctionSet:
    def __init__(self, item_name: str, inst_name: str, time_scope: int, threshold_val: int, cur_val: int):
        self.item_name = item_name
        self.inst_name = inst_name
        self.time_scope = time_scope
        self.threshold_val = threshold_val
        self.cur_val = cur_val
        self.mysql_client = MySQLClient(
            host="0035c980965348f3b3ace8e7d92d2d0bin01.internal.cn-north-4.mysql.rds.myhuaweicloud.com",
            user="jdtest_d_gong_ddl",
            password="CoIfwlop8nnNJzAAOA",
            port=3306
        )

    def sql2_func(self):
        time_scope = int(self.time_scope / 60)
        cal_sql = f"""
                SELECT SUM(count_all) AS total_sum
                FROM octopvs.minute_stat_2
                WHERE stat_minute BETWEEN FLOOR(UNIX_TIMESTAMP(NOW() - INTERVAL {time_scope} MINUTE) / 60) 
                                  AND FLOOR(UNIX_TIMESTAMP(NOW()) / 60)
                                  and entry_name = "{self.item_name}"
                                  and inst_name = "{self.inst_name}"
                   """
        result = self.mysql_client.select(cal_sql)
        if result:
            result = result["total_sum"]
        else:
            result = 0
        return result


class TypePinFunctionSet:
    def __init__(self, key_name: str, calculation_type: str, threshold_val: int, cur_val: int, message: str,
                 monitoring_cycle: int):
        self.key_name = key_name
        self.calculation_type = calculation_type
        self.threshold_val = threshold_val
        self.cur_val = cur_val
        self.redis_cli = redis.Redis(connection_pool=REDIS_POOL)
        self.count_key = f"{self.key_name}:{self.calculation_type}"
        self.monitoring_cycle = monitoring_cycle
        self.message = message

    def cleanup_old_data(self):
        """清理过期数据"""
        current_time = int(time.time() * 1000)
        expire_time = current_time - (self.monitoring_cycle * 2 * 1000)

        cursor = 0
        while True:
            cursor, data = self.redis_cli.hscan(self.count_key, cursor, count=100)
            for key, value in data.items():
                try:
                    instance_data = json.loads(value)
                    new_data = [x for x in instance_data if int(x[0]) > expire_time]
                    if new_data:
                        self.redis_cli.hset(self.count_key, key, json.dumps(new_data))
                    else:
                        self.redis_cli.hdel(self.count_key, key)
                except json.JSONDecodeError:
                    # 处理损坏的数据
                    self.redis_cli.hdel(self.count_key, key)
            if cursor == 0:
                break

    def pin_func(self):
        """优化后的PIN检测函数"""
        current_time = int(time.time() * 1000)
        before_time = current_time - (self.monitoring_cycle * 1000)
        instance_set = set()

        # 使用HSCAN代替HGETALL
        cursor = 0
        while True:
            cursor, data = self.redis_cli.hscan(self.count_key, cursor, count=100)
            for key, value in data.items():
                try:
                    instance_data = json.loads(value)
                    if not instance_data:
                        continue

                    if self.calculation_type == "active":
                        # 只检查最后一条记录
                        if int(instance_data[-1][0]) >= before_time:
                            instance_set.add(key)
                    elif self.calculation_type == "count":
                        # 检查时间范围内的记录
                        if any(int(record[0]) >= before_time for record in instance_data):
                            instance_set.add(key)

                except (json.JSONDecodeError, IndexError, ValueError) as e:
                    logger.error(f"Error processing data for key {key}: {e}")
                    continue
            if cursor == 0:
                break

        # 定期清理过期数据
        if random.random() < 0.1:  # 10%的概率执行清理
            self.cleanup_old_data()

        self.cur_val = len(instance_set)
        return (self.cur_val,
                str(instance_set) if self.calculation_type == "count" else "")
