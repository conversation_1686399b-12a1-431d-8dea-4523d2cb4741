import threading
import queue
import time
from loguru import logger

class TaskScheduler:
    def __init__(self):
        self.lock = threading.Lock()
        self.task_queue = queue.Queue(maxsize=100)  # 限制队列大小
        self.task_wait_time_dict = {}
        self.tasks = {}
        self.function = None
        self.running = True

    def task_executor_thread_function(self):
        while self.running:
            try:
                if self.task_queue.qsize() == 0:
                    time.sleep(10)
                    continue

                logger.info(f"Current queue size: {self.task_queue.qsize()}")
                # 使用更长的超时时间
                func, task_item, task_id = self.task_queue.get(timeout=5)
                try:
                    func(**task_item)
                    with self.lock:
                        self.task_wait_time_dict[task_id] = task_item["monitoring_cycle"]
                except Exception as e:
                    logger.error(f"Task execution error: {e}")
            except queue.Empty:
                # 只处理队列为空的情况
                time.sleep(2)
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                time.sleep(1)

    def time_update_thread_function(self):
        while self.running:
            time.sleep(10)
            with self.lock:
                # 使用列表推导式优化遍历
                expired_tasks = [(task_id, self.tasks[task_id])
                                 for task_id, wait_time in self.task_wait_time_dict.items()
                                 if wait_time <= 2]

                # 批量更新和删除
                self.task_wait_time_dict = {
                    task_id: wait_time - 10
                    for task_id, wait_time in self.task_wait_time_dict.items()
                    if wait_time > 2
                }

            # 在锁外处理过期任务
            for task_id, task_item in expired_tasks:
                try:
                    self.task_queue.put((self.function, task_item, task_id), timeout=1)
                except Exception:
                    time.sleep(1)  # 队列满时短暂等待

    def consumer_queue(self, worker_nums):
        self.workers = []
        for _ in range(worker_nums):
            worker = threading.Thread(target=self.task_executor_thread_function, daemon=True)
            worker.start()
            self.workers.append(worker)

    def run(self, worker_nums=1):
        self.consumer_queue(worker_nums)
        self.time_update_thread = threading.Thread(target=self.time_update_thread_function, daemon=True)
        self.time_update_thread.start()

    def stop(self):
        """立即停止所有任务和线程"""
        logger.info("正在停止调度器...")

        # 1. 设置运行标志为 False
        self.running = False

        # 2. 清空任务队列
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
                self.task_queue.task_done()
            except queue.Empty:
                break

        # 3. 停止所有工作线程
        for worker in self.workers:
            if worker.is_alive():
                logger.info(f"正在停止工作线程: {worker.name}")
                worker.join(timeout=1)

        # 4. 停止时间更新线程
        if self.time_update_thread and self.time_update_thread.is_alive():
            logger.info("正在停止时间更新线程")
            self.time_update_thread.join(timeout=1)

        # 5. 清理资源
        self.workers.clear()
        self.task_wait_time_dict.clear()
        self.time_update_thread = None

        logger.info("调度器已完全停止")


# 示例任务函数
def example_task(monitoring_cycle):
    print(f"Task executed with monitoring_cycle: {monitoring_cycle}")


if __name__ == "__main__":
    ...


