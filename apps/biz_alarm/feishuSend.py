# -*- coding: UTF-8 -*-
"""
@Project ：pygs-work-parent 
@File    ：feishuSend.py
<AUTHOR>
@Date    ：2024/11/13 19:30 
"""
import json
import datetime
import requests
import urllib3
import redis
from loguru import logger

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
REDIS_POOL = redis.ConnectionPool(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com',
                                  port=6379, username='root', password='3lvadmpiSj61ge', db=8, max_connections=10)

class FeiShu:
    def __init__(self):
        self.record = ""
        self.load_record_file()
        self.is_send = False
        self.redis_cli = redis.Redis(connection_pool=REDIS_POOL)

    def load_record_file(self):
        with open("./logs/send_record.log", "r") as f:
            self.record = f.read()

    def send_feishu(self, values):
        open_id_dict = {
            '所有人': 'all',
            '史安琪': 'ou_51e8bc246873e0f4c31176484dc6f72b',
            '丁良益': 'ou_cd3c38e542a51827dd48c3655b545c5c',
            '于志国': 'ou_66775216bebe1d65e8d9226de6b70ace',
            '王帮旭': 'ou_8502bcf8edd9fc542defdea8492bc64e',
            '金炜': 'ou_4048a112b97d5e09408bbd5034f9dd96',
            '薛秋雨': 'ou_4654cf587d9fb496122152a81fc937c8',
            '张晓永': 'ou_98ecdf1cd534de8d8a6e605a424e6551',
            '张鑫明': 'ou_aaefa731a515cff950d77b414c65c3c9',
            '唐杰成': 'ou_db3fd038bbaae64836f212c6402793da',
            '潘仕江': 'ou_604d8012c65cc30ae641947f23d3071c',
            '杜波': 'ou_e5f08c8f224a6d548c9a3365b4e86cf9',
            '阮齐明': 'ou_e60e68b1cdfd3c5aa4b01955ad8009e9',
            '马子博': 'ou_9386a44adc9772882df0c5ee9ca0f897'
        }
        if not values.task_params.get("message", ""):
            prd_text = f"通用监控:\n" \
                       f"   任务名称: {values.task_name}\n"\
                       f"   检测周期: {values.monitoring_cycle} 秒\n" \
                       f"   报警周期: {values.monitor_time} 秒\n" \
                       f"   阈值: {values.task_params['threshold_val']}，当前值: {values.task_params['cur_val']}\n"
        else:
            prd_text = f"通用监控:\n" \
                       f"   任务名称: {values.task_name}\n" \
                       f"   检测周期: {values.monitoring_cycle} 秒\n" \
                       f"   报警周期: {values.monitor_time} 秒\n" \
                       f"   阈值: {values.task_params['threshold_val']}，当前值: {values.task_params['cur_val']}\n" \
                       f"   信息: {values.task_params['message']}"

        logger.info(prd_text)
        at_text = "".join(f"<at user_id=\"{open_id_dict[user_item]}\"></at>" for user_item in values.user)
        feishu_url = "https://open.feishu.cn/open-apis/bot/v2/hook/fa593330-a4ee-40c1-936f-447892785924"
        headers = {
            "Content-Type": "application/json"
        }
        feishu_data = {
            "msg_type": "text",
            "content": {
                "text": prd_text + at_text
            }
        }
        feishu_data = json.dumps(feishu_data, separators=(',', ':'))

        before_add_num = self.redis_cli.get("pygs_biz_alarm_send_feishu_"+values.task_name)
        if not before_add_num:
            self.redis_cli.setnx("pygs_biz_alarm_send_feishu_" + values.task_name, 1)
            self.redis_cli.expire("pygs_biz_alarm_send_feishu_"+values.task_name, values.monitor_time)
            self.is_send = True
        else:
            self.is_send = False

        # logger.info(prd_text)
        if prd_text and self.is_send:
            for _ in range(3):
                resp = requests.post(feishu_url, headers=headers, data=feishu_data, proxies={
                            'http': 'http://10.99.138.95:30636',
                            'https': 'http://10.99.138.95:30636'
                        }, verify=False)
                if resp.status_code == 200:
                    break
                else:
                    continue
