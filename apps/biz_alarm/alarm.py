# -*- coding: UTF-8 -*-
"""
@Project ：pygs-work-parent 
@File    ：alarm.py
<AUTHOR>
@Date    ：2024/11/13 16:11 
"""
import json
import time
import threading

from loguru import logger
from typing import Literal
from feishuSend import <PERSON>i<PERSON>hu
from functionSet import TypePortFunctionSet, TypeProcessFunctionSet, TypeKafkaFunctionSet,\
    TypeRedisFunctionSet, TypeSpiderFunctionSet, TypeSpider2FunctionSet, TypePinFunctionSet
from pydantic import BaseModel, Field, constr, conlist, conint, model_validator
from threading import Lock
from taskScheduler import TaskScheduler
from frequencyFlask import threading_flask_run, find_dict_with_keys

logger_lock = Lock()
feishu_instance = FeiShu()


class Task(BaseModel):
    task_name: constr(min_length=1) = Field(..., description="任务名称")
    user: conlist(
        Literal["马子博", "所有人", "史安琪", "丁良益", "于志国", "王帮旭", "金炜",
                "薛秋雨", "张晓永", "张鑫明", "唐杰成", "潘仕江", "杜波", "阮齐明"], min_length=1
    ) = Field(..., description="用户列表")
    task_type: Literal["port", "process", "kafka", "redis",
                       "spider", "spider2", "pin"] = Field(..., description="监控任务类型")
    monitoring_cycle: conint(ge=1) = Field(..., description="监控周期(秒)")
    monitor_time: conint(ge=1) = Field(..., description="监控报警周期(秒)")
    task_params: dict = Field(default_factory=dict, description="任务所需参数")
    is_active: bool = Field(False, description="任务是否激活")

    @model_validator(mode="after")
    def check_task_params_and_run(cls, values):
        task_type = values.task_type
        task_params = values.task_params
        if values.is_active:
            if task_type == "port":
                with logger_lock:
                    logger.info(f"[{values.task_name}] 接口检测开始...")
                task_params_instance = TaskParams1(**task_params)
                task_function = TypePortFunctionSet(**task_params_instance.model_dump())
                values.task_params['cur_val'] = int(task_function.send_request())
                if values.task_params['cur_val'] < values.task_params['threshold_val']:
                    feishu_instance.send_feishu(values)
                with logger_lock:
                    logger.info(f"[{values.task_name}] 当前接口状态为 {values.task_params['cur_val']}")
                    logger.info(f"[{values.task_name}] 接口检测完成!")

            elif task_type == "process":
                with logger_lock:
                    logger.info(f"[{values.task_name}] 进程检测开始...")
                task_params_instance = TaskParams2(**task_params)
                task_function = TypeProcessFunctionSet(**task_params_instance.model_dump())
                values.task_params["cur_val"] = int(task_function.remote_ps())
                if values.task_params['cur_val'] < values.task_params['threshold_val']:
                    feishu_instance.send_feishu(values)
                with logger_lock:
                    logger.info(f"[{values.task_name}] 当前进程状态为 {values.task_params['cur_val']}")
                    logger.info(f"[{values.task_name}] 进程检测完成!")

            elif task_type == "kafka":
                with logger_lock:
                    logger.info(f"[{values.task_name}] kafka-lag检测开始...")
                task_params_instance = TaskParams3(**task_params)
                task_function = TypeKafkaFunctionSet(**task_params_instance.model_dump())
                values.task_params['cur_val'] = task_function.kafka_lag()
                if values.task_params['cur_val'] > values.task_params['threshold_val']:
                    feishu_instance.send_feishu(values)
                with logger_lock:
                    logger.info(f"[{values.task_name}] 当前kafka-lag为 {values.task_params['cur_val']}")
                    logger.info(f"[{values.task_name}] kafka-lag检测完成!")

            elif task_type == "redis":
                with logger_lock:
                    logger.info(f"[{values.task_name}] redis-length检测开始...")
                task_params_instance = TaskParams4(**task_params)
                task_function = TypeRedisFunctionSet(**task_params_instance.model_dump())
                values.task_params['cur_val'] = task_function.redis_func()
                if values.task_params['cur_val'] > values.task_params['threshold_val']:
                    feishu_instance.send_feishu(values)
                with logger_lock:
                    logger.info(f"[{values.task_name}] 当前长度为 {values.task_params['cur_val']}")
                    logger.info(f"[{values.task_name}] redis-length检测完成!")

            elif task_type == "spider":
                with logger_lock:
                    logger.info(f"[{values.task_name}] spider抓取量检测开始...")
                task_params_instance = TaskParams5(**task_params)
                task_function = TypeSpiderFunctionSet(**task_params_instance.model_dump())
                values.task_params['cur_val'] = task_function.sql_func() if task_function.sql_func() else 0
                if values.task_params['cur_val'] < values.task_params['threshold_val']:
                    feishu_instance.send_feishu(values)
                with logger_lock:
                    logger.info(f"[{values.task_name}] 当前抓取量为 {values.task_params['cur_val']}")
                    logger.info(f"[{values.task_name}] spider抓取量检测完成!")

            elif task_type == "spider2":
                with logger_lock:
                    logger.info(f"[{values.task_name}] spider抓取量检测开始...")
                task_params_instance = TaskParams5(**task_params)
                task_function = TypeSpider2FunctionSet(**task_params_instance.model_dump())
                values.task_params['cur_val'] = task_function.sql2_func() if task_function.sql2_func() else 0
                if values.task_params['cur_val'] < values.task_params['threshold_val']:
                    feishu_instance.send_feishu(values)
                with logger_lock:
                    logger.info(f"[{values.task_name}] 当前抓取量为 {values.task_params['cur_val']}")
                    logger.info(f"[{values.task_name}] spider抓取量检测完成!")

            elif task_type == "pin":
                with logger_lock:
                    logger.info(f"[{values.task_name}] PIN检测开始...")
                task_params_instance = TaskParams6(**task_params)
                task_function = TypePinFunctionSet(**task_params_instance.model_dump(),
                                                    **{"monitoring_cycle":values.monitoring_cycle})
                values.task_params['cur_val'], values.task_params['message'] = task_function.pin_func()

                if values.task_params['calculation_type'] == "active":
                    if values.task_params['cur_val'] < values.task_params['threshold_val']:
                        feishu_instance.send_feishu(values)
                    with logger_lock:
                        logger.info(f"[{values.task_name}] 当前PIN数量为 {values.task_params['cur_val']}")
                        logger.info(f"[{values.task_name}] PIN数量检测完成!")
                elif values.task_params['calculation_type'] == "count":
                    if values.task_params['cur_val'] > values.task_params['threshold_val']:
                        feishu_instance.send_feishu(values)
                    with logger_lock:
                        logger.info(f"[{values.task_name}] 当前pin数量为 {values.task_params['cur_val']}")
                        logger.info(f"[{values.task_name}] PIN数量检测完成!")


class TaskParams1(BaseModel):
    url: constr(min_length=1) = Field(..., description="请求地址")
    method: Literal["GET", "POST"] = Field(..., description="请求方法")
    timeout: conint(ge=1) = Field(..., description="超时时间")
    max_retries: conint(ge=1) = Field(..., description="最大重试次数")
    threshold_val: conint(ge=0) = Field(..., description="阈值")
    cur_val: int = Field(0, description="当前值")

    # headers, data, params为可选字段
    headers: dict = Field(default_factory=dict, description="请求头信息")
    data: dict = Field(default_factory=dict, description="POST请求的数据")
    params: dict = Field(default_factory=dict, description="GET请求的查询参数")


class TaskParams2(BaseModel):
    server_ip: constr(min_length=1) = Field(..., description="服务器地址")
    process_name: constr(min_length=1) = Field(..., description="进程名称")
    threshold_val: conint(ge=0) = Field(..., description="阈值")
    cur_val: int = Field(0, description="当前值")


class TaskParams3(BaseModel):
    bootstrap_servers_name: Literal["gs", "gs_data", "gs_test",
                                    "canal_prod", "channel_out1", "channel_out2"] = Field(..., description="Kafka名称")
    topic_name: constr(min_length=1) = Field(..., description="topic名字")
    consumer_group: constr(min_length=1) = Field(..., description="消费者名字")
    threshold_val: conint(ge=0) = Field(..., description="阈值")
    cur_val: int = Field(0, description="当前值")


class TaskParams4(BaseModel):
    redis_server_name: Literal["default", "gs", "gs_test",
                               "zhuan"] = Field(..., description="Redis名称")
    redis_db: int = Field(..., description="db库")
    queue_name: constr(min_length=1) = Field(..., description="队列名称")
    queue_type: Literal["list", "zset"] = Field(..., description="队列类型")
    threshold_val: conint(ge=0) = Field(..., description="阈值")
    cur_val: int = Field(0, description="当前值")


class TaskParams5(BaseModel):
    item_name: Literal["company", "brno", "credit"] = Field(..., description="调度单元名称")
    inst_name: constr(min_length=1) = Field(..., description="调度实例名称")
    time_scope: conint(ge=300) = Field(..., description="时间范围, 不小于五分钟")
    threshold_val: conint(ge=0) = Field(..., description="阈值")
    cur_val: int = Field(0, description="当前值")


class TaskParams6(BaseModel):
    key_name: constr(min_length=1) = Field(..., description="键名")
    calculation_type: Literal["active", "count"] = Field(..., description="计算类型")
    threshold_val: conint(ge=0) = Field(..., description="阈值")
    cur_val: int = Field(0, description="当前值")
    message: str = Field("", description="返回信息")


def main(**data):
    try:
        Task(**data)
    except Exception as e:
        logger.error(e)


if __name__ == '__main__':
    with open('./alarmConfig.json', 'r') as f:
        tasks = json.load(f)["tasks"]

    key_name_list = find_dict_with_keys(tasks, {"key_name"})
    keys_list = [key["key_name"] for key in key_name_list]
    if len(keys_list) != len(set(keys_list)):
        logger.warning("配置文件:PIN检测配置存在重复键 key_name")
        exit()
    keys_list = [task["task_name"] for task in tasks]
    if len(keys_list) != len(set(keys_list)):
        logger.warning("配置文件:重复任务名，主键task_name")
        exit()

    # 线程池消费
    taskScheduler_instance = TaskScheduler()
    taskScheduler_instance.tasks = tasks
    taskScheduler_instance.function = main
    for task_id, task in enumerate(tasks):
        taskScheduler_instance.task_queue.put((main, task, task_id))

    taskScheduler_instance.run(worker_nums=8)

    # try:
    #     while True:
    #         time.sleep(1)
    #         # 增加资源监控
    #         if threading.active_count() > 100:  # 示例阈值
    #             logger.warning("Thread count too high")
    #         # 可以添加内存监控等
    # except KeyboardInterrupt:
    #     logger.info("Shutting down...")
    #     taskScheduler_instance.stop()
    # except Exception as e:
    #     logger.error(f"Main loop error: {e}")
    #     taskScheduler_instance.stop()

    # 在主循环中添加更多的监控和保护机制
    try:
        while True:
            time.sleep(30)  # 增加检查间隔
            thread_count = threading.active_count()
            logger.warning(f"Thread count: {thread_count}")
            if thread_count > 20:  # 设置合理的线程数阈值
                logger.warning(f"Thread count too high: {thread_count}")
                # 可以添加一些恢复机制，比如重启调度器
                taskScheduler_instance.stop()
                time.sleep(10)
                taskScheduler_instance = TaskScheduler()
                taskScheduler_instance.tasks = tasks
                taskScheduler_instance.function = main
                for task_id, task in enumerate(tasks):
                    taskScheduler_instance.task_queue.put((main, task, task_id))
                taskScheduler_instance.run(worker_nums=4)
    except KeyboardInterrupt:
        logger.info("Shutting down gracefully...")
        taskScheduler_instance.stop()