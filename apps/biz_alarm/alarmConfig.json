{"tasks": [{"task_name": "XA抓取量监控", "monitor_time": 86400, "user": ["王帮旭"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "xa", "time_scope": 43200, "threshold_val": 20000, "cur_val": 0}, "is_active": true}, {"task_name": "bj抓取量监控", "monitor_time": 86400, "user": ["丁良益"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "bj", "time_scope": 43200, "threshold_val": 6000, "cur_val": 0}, "is_active": true}, {"task_name": "fjxm抓取量监控", "monitor_time": 86400, "user": ["丁良益"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "fjxm", "time_scope": 43200, "threshold_val": 600, "cur_val": 0}, "is_active": true}, {"task_name": "gd抓取量监控", "monitor_time": 86400, "user": ["丁良益"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "gd", "time_scope": 43200, "threshold_val": 70000, "cur_val": 0}, "is_active": true}, {"task_name": "gdgz抓取量监控", "monitor_time": 86400, "user": ["丁良益"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "gdgz", "time_scope": 43200, "threshold_val": 30000, "cur_val": 0}, "is_active": false}, {"task_name": "gdsz抓取量监控", "monitor_time": 86400, "user": ["丁良益"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "gdsz", "time_scope": 43200, "threshold_val": 500, "cur_val": 0}, "is_active": false}, {"task_name": "gdzh抓取量监控", "monitor_time": 86400, "user": ["丁良益"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "gdzh", "time_scope": 43200, "threshold_val": 30000, "cur_val": 0}, "is_active": true}, {"task_name": "gz抓取量监控", "monitor_time": 86400, "user": ["马子博"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "gz", "time_scope": 43200, "threshold_val": 7000, "cur_val": 0}, "is_active": true}, {"task_name": "han抓取量监控", "monitor_time": 86400, "user": ["史安琪"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "han", "time_scope": 43200, "threshold_val": 40000, "cur_val": 0}, "is_active": true}, {"task_name": "hlj抓取量监控", "monitor_time": 86400, "user": ["史安琪"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "hlj", "time_scope": 43200, "threshold_val": 2000, "cur_val": 0}, "is_active": true}, {"task_name": "js抓取量监控", "monitor_time": 86400, "user": ["史安琪"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "js", "time_scope": 43200, "threshold_val": 6000, "cur_val": 0}, "is_active": true}, {"task_name": "jx抓取量监控", "monitor_time": 86400, "user": ["丁良益"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "jx", "time_scope": 43200, "threshold_val": 10000, "cur_val": 0}, "is_active": true}, {"task_name": "sccd抓取量监控", "monitor_time": 86400, "user": ["丁良益"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "sccd", "time_scope": 43200, "threshold_val": 2000, "cur_val": 0}, "is_active": true}, {"task_name": "sh抓取量监控", "monitor_time": 86400, "user": ["丁良益"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "sh", "time_scope": 43200, "threshold_val": 7000, "cur_val": 0}, "is_active": true}, {"task_name": "tj抓取量监控", "monitor_time": 86400, "user": ["丁良益"], "task_type": "spider", "monitoring_cycle": 1800, "task_params": {"item_name": "company", "inst_name": "tj", "time_scope": 43200, "threshold_val": 10000, "cur_val": 0}, "is_active": true}, {"task_name": "china_npo抓取量监控", "monitor_time": 86400, "user": ["丁良益"], "task_type": "spider2", "monitoring_cycle": 1800, "task_params": {"item_name": "credit", "inst_name": "china_npo", "time_scope": 43200, "threshold_val": 10000, "cur_val": 0}, "is_active": true}, {"task_name": "hk抓取量监控", "monitor_time": 86400, "user": ["丁良益"], "task_type": "spider2", "monitoring_cycle": 1800, "task_params": {"item_name": "brno", "inst_name": "hk", "time_scope": 86400, "threshold_val": 80000, "cur_val": 0}, "is_active": true}, {"task_name": "cods抓取量监控", "monitor_time": 86400, "user": ["丁良益"], "task_type": "spider2", "monitoring_cycle": 1800, "task_params": {"item_name": "credit", "inst_name": "cods", "time_scope": 86400, "threshold_val": 600, "cur_val": 0}, "is_active": true}, {"task_name": "史安琪的crawler容器数量监控", "monitor_time": 3600, "user": ["史安琪"], "task_type": "pin", "monitoring_cycle": 300, "task_params": {"key_name": "anqi_healthCheck_crawler", "calculation_type": "active", "threshold_val": 10, "cur_val": 0, "message": ""}, "is_active": true}, {"task_name": "史安琪的parser容器数量监控", "monitor_time": 3600, "user": ["史安琪"], "task_type": "pin", "monitoring_cycle": 300, "task_params": {"key_name": "anqi_healthCheck_parser", "calculation_type": "active", "threshold_val": 4, "cur_val": 0, "message": ""}, "is_active": true}, {"task_name": "史安琪的fusion容器数量监控", "monitor_time": 3600, "user": ["史安琪"], "task_type": "pin", "monitoring_cycle": 300, "task_params": {"key_name": "anqi_healthCheck_fusion", "calculation_type": "active", "threshold_val": 4, "cur_val": 0, "message": ""}, "is_active": true}, {"task_name": "史安琪的crawler_kafka数量监控", "monitor_time": 3600, "user": ["史安琪"], "task_type": "pin", "monitoring_cycle": 60, "task_params": {"key_name": "an<PERSON>_<PERSON><PERSON><PERSON>_crawler", "calculation_type": "count", "threshold_val": 10, "cur_val": 0, "message": ""}, "is_active": true}, {"task_name": "史安琪的parser_kafka数量监控", "monitor_time": 3600, "user": ["史安琪"], "task_type": "pin", "monitoring_cycle": 60, "task_params": {"key_name": "an<PERSON>_<PERSON><PERSON>ka_parser", "calculation_type": "count", "threshold_val": 10, "cur_val": 0, "message": ""}, "is_active": true}, {"task_name": "史安琪的fusion_kafka数量监控", "monitor_time": 3600, "user": ["史安琪"], "task_type": "pin", "monitoring_cycle": 60, "task_params": {"key_name": "anqi_Kafka_fusion", "calculation_type": "count", "threshold_val": 10, "cur_val": 0, "message": ""}, "is_active": true}, {"task_name": "史安琪的property2数量监控", "monitor_time": 3600, "user": ["史安琪"], "task_type": "pin", "monitoring_cycle": 60, "task_params": {"key_name": "anqi_property2_monitor", "calculation_type": "count", "threshold_val": 0, "cur_val": 0, "message": ""}, "is_active": true}, {"task_name": "股比计算调度业务进程存活性监控", "monitor_time": 600, "user": ["于志国"], "task_type": "process", "monitoring_cycle": 60, "task_params": {"server_ip": "*************", "process_name": "realtime_process", "threshold_val": 17, "cur_val": 0}, "is_active": false}, {"task_name": "股比计算计算接口存活性监控", "monitor_time": 600, "user": ["于志国"], "task_type": "port", "monitoring_cycle": 60, "task_params": {"url": "http://shareholder-picker-gsdata.jindidata.com/company_handler", "method": "GET", "timeout": 3, "max_retries": 3, "params": {"gid": 4968741295}, "threshold_val": 1, "cur_val": 0}, "is_active": false}, {"task_name": "股比计算 - 非上市入库模块存活性监控", "monitor_time": 600, "user": ["于志国", "马子博"], "task_type": "port", "monitoring_cycle": 60, "task_params": {"url": "http://*************:5011/exec_update_sql", "method": "POST", "timeout": 5, "max_retries": 3, "threshold_val": 1, "cur_val": 0}, "is_active": false}, {"task_name": "股比计算 - 非上市解析模块存活性监控", "monitor_time": 600, "user": ["马子博"], "task_type": "port", "monitoring_cycle": 60, "task_params": {"url": "http://*************:8101/unlisted/get_shareholder_info/3412812936", "method": "GET", "timeout": 3, "max_retries": 3, "threshold_val": 1, "cur_val": 0}, "is_active": false}, {"task_name": "史安琪的parser_pod(kafka-lag监控)", "monitor_time": 1800, "user": ["史安琪"], "task_type": "kafka", "monitoring_cycle": 60, "task_params": {"bootstrap_servers_name": "gs", "topic_name": "gsxt.data_parser", "consumer_group": "gs_data_parser_prod", "threshold_val": 20000, "cur_val": 0}, "is_active": true}, {"task_name": "史安琪的fusion_pod(kafka-lag监控)", "monitor_time": 1800, "user": ["史安琪"], "task_type": "kafka", "monitoring_cycle": 60, "task_params": {"bootstrap_servers_name": "gs", "topic_name": "gsxt.data_fusion", "consumer_group": "gs_data_fusion_prod", "threshold_val": 30000, "cur_val": 0}, "is_active": true}, {"task_name": "王帮旭的eventlog-parser(redis监控)", "monitor_time": 1800, "user": ["王帮旭"], "task_type": "redis", "monitoring_cycle": 60, "task_params": {"redis_server_name": "gs", "redis_db": "3", "queue_name": "eventlog_spider:crawler_output.online", "queue_type": "zset", "threshold_val": 2000, "cur_val": 0}, "is_active": true}, {"task_name": "王帮旭的年报渠道入库(redis监控)", "monitor_time": 3600, "user": ["王帮旭"], "task_type": "redis", "monitoring_cycle": 60, "task_params": {"redis_server_name": "gs", "redis_db": "2", "queue_name": "channel_report:tasks", "queue_type": "zset", "threshold_val": 5000, "cur_val": 0}, "is_active": false}, {"task_name": "史安琪的gs-data-crawler(kafka-lag监控)", "monitor_time": 1800, "user": ["史安琪"], "task_type": "kafka", "monitoring_cycle": 60, "task_params": {"bootstrap_servers_name": "gs", "topic_name": "gsxt.data_crawler", "consumer_group": "gs_data_crawler_prod", "threshold_val": 1000, "cur_val": 0}, "is_active": true}]}