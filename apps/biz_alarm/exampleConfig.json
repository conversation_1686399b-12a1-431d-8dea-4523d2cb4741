{"tasks": [{"task_name": "某接口存活性监控", "user": ["马子博"], "task_type": "port", "monitoring_cycle": 30, "task_params": {"url": "http://msv-gsdata.jindidata.com/read_list_version", "method": "POST", "timeout": 3, "max_retries": 1, "$full_comment1": "以下为选填字段", "headers": {}, "params": {}, "data": {}, "$full_comment2": "threshold为阈值 高于或低于，cur_val不需要改", "threshold_val": 1, "cur_val": 0}, "is_active": false}, {"task_name": "某进程存活性监控", "user": ["马子博"], "task_type": "process", "monitoring_cycle": 60, "task_params": {"server_ip": "*************", "process_name": "from_kafka", "threshold_val": 1, "cur_val": 0}, "is_active": false}, {"task_name": "kafka-lag监控", "user": ["马子博"], "task_type": "kafka", "monitoring_cycle": 30, "task_params": {"bootstrap_servers_name": "gs", "topic_name": "msv_data", "consumer_group": "log_msv", "threshold_val": 100, "cur_val": 0}, "is_active": false}, {"task_name": "redis监控", "user": ["马子博"], "task_type": "redis", "monitoring_cycle": 60, "task_params": {"redis_server_name": "gs", "redis_db": "5", "queue_name": "searchCompany_gzold_queue_updateOffline", "queue_type": "zset", "threshold_val": 1, "cur_val": 0}, "is_active": false}, {"task_name": "某spider抓取量监控", "user": ["马子博"], "task_type": "spider", "monitoring_cycle": 60, "task_params": {"item_name": "company", "inst_name": "gz", "time_scope": 7200, "threshold_val": 400, "cur_val": 0}, "is_active": false}, {"task_name": "PIN检测", "user": ["马子博"], "task_type": "pin", "monitoring_cycle": 60, "task_params": {"key_name": "example_for_heart_list", "calculation_type": "active", "threshold_val": 5, "cur_val": 0}, "is_active": false}]}