from flask import Flask, request, jsonify
import redis
import json
import time
import threading

app = Flask(__name__)
pool = redis.ConnectionPool(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com',
                            port=6379, username='root', password='3lvadmpiSj61ge', db=8, max_connections=10)
redis_client = redis.Redis(connection_pool=pool)



@app.route('/pygs_pin_cal', methods=['POST'])
def pin():
    data = request.json
    key_name = data.get("key_name", "")
    instance_id = data.get("instance_id", "")
    count_num = data.get("count", 1)
    if not instance_id:
        instance_id = request.remote_addr

    calculation_type = ""
    pin_list = read_config()

    key_name_list = []
    key_type_dict_list = []
    for pin_item in pin_list:
        key_name_list.append(pin_item["key_name"])
        key_type_dict_list.append({f'{pin_item["key_name"]}': f'{pin_item["calculation_type"]}'})

    if key_name not in key_name_list:
        return jsonify({"error": "Not Record Key Name"}), 400

    for pin_item in key_type_dict_list:
        for key in pin_item.keys():
            if key == key_name:
                calculation_type = pin_item[key_name]
    if not calculation_type:
        return jsonify({"error": "Not Record Calculation Type"}), 400

    # key_name, type, instance_id
    redis_key = f"{key_name}:{calculation_type}"

    # 判断是否有值
    instance_value = redis_client.hget(redis_key, instance_id)
    if not instance_value:
        redis_client.hset(redis_key, instance_id, json.dumps([(f"{int(time.time()*1000)}", f"{count_num}")]))
    else:
        json_value = json.loads(instance_value)
        json_value.append((f"{int(time.time()*1000)}", f"{count_num}"))
        redis_client.hset(redis_key, instance_id, json.dumps(json_value))

    return jsonify({"status": "success", "message": f"Added container {instance_id} to {key_name}"}), 200


def read_config():
    path = "./alarmConfig.json"
    with open(path, 'r') as f:
        tasks = json.load(f)["tasks"]
    result = find_dict_with_keys(tasks, {"key_name", "calculation_type"})
    return result


def find_dict_with_keys(data, keys):
    result = []
    if isinstance(data, dict):
        if keys.issubset(data.keys()):
            result.append(data)
        for value in data.values():
            result.extend(find_dict_with_keys(value, keys))
    elif isinstance(data, list):
        for item in data:
            result.extend(find_dict_with_keys(item, keys))
    return result


def run():
    app.run(host='0.0.0.0', port=6521)


def threading_flask_run():
    task_executor_thread = threading.Thread(target=run)
    task_executor_thread.start()


if __name__ == "__main__":
    run()
