# -*- coding: UTF-8 -*-
'''
@Project ：pygs-work-parent 
@File    ：pinTestRequests.py
<AUTHOR>
@Date    ：2024/11/21 11:36 
'''
import json

import requests

url = "http://10.99.202.163:6521/pygs_pin_cal"

headers = {
    "Content-Type": "application/json"
}
data = {
    "key_name": "your_key_name",
    "instance_id": "your_instance_id",
}
data = json.dumps(data)
response = requests.post(url, headers=headers, data=data)
print(response.json())