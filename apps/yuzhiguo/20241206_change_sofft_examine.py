from dao.company_change_info import CompanyChangeInfoDao
from my_sever.company_staff_sort_sever import CompanyStaffSortDao
from libs.dt import to_date, to_datetime
import requests, time, csv
from collections import Counter
from dao.company_graph import CompanyGraphDao
from dao.company import CompanyDao

change_info_sever = CompanyChangeInfoDao()
company_staff_sort_sever = CompanyStaffSortDao()
company_graph_sever = CompanyGraphDao()
company_sever = CompanyDao()


change_item_map = [
    "高级管理人员备案（董事、监事、经理等）",
]


def ner_post(content_after, retry_times=3):
    """算法接口"""
    for i in range(retry_times):
        try:
            url = "http://10.99.199.173:6117/shareholder_ner"
            payload = {'text': content_after}
            r = requests.post(url, data=payload, timeout=10)
            if r.status_code == 200:
                return r.json()
            else:
                continue
        except Exception as e:
            print(e)
            continue
    return None


def get_change_sofft_date(cid, approved_time):
    """基于ner获取变更记录最新主要人员的名单"""
    change_data = change_info_sever.get_many('company_id', cid)
    new_change_data = []
    for i in change_data:
        change_time = i.change_time
        if change_time is None or change_time == '':
            continue
        if i.change_item in change_item_map:
            # 优化核准日期不变股东变更的场景
            # if to_date(change_time) >= approved_time:
            new_change_data.append(i)
    # print('有符合大于等于核准日期的主要人员数据', new_change_data)
    # 获取有主要人员变更的变更记录
    if new_change_data:
        # 排序选出最新的主要人员变更记录
        new_change_list = sorted(new_change_data, key=lambda x: x.change_time, reverse=True)
        new_change_list = new_change_list[0]
        # print('最新的主要人员变更数据：', new_change_list)
        # 调用ner
        get_ner_date = ner_post(new_change_list.content_after)
        if get_ner_date:
            shareholder_list = set([i['word'] for i in get_ner_date if i['type'] == 'human'])
            # print(shareholder_list)
            if shareholder_list:
                return shareholder_list
    return None


def get_company_staff_sort_date(gid):
    """获取当前主要人员信息表数据"""
    staff = set()
    date = company_staff_sort_sever.get_many('company_gid', gid)
    for i in date:
        if i.name:
            staff.add(i.name)
    return staff


def main(gid):
    """主要人员不自洽检测主逻辑"""
    try:
        id_data = company_graph_sever.get_by_gid(gid)
        cid = id_data.cid
    except:
        # 无cid 返回
        print(f'{gid}无cid,退出')
        return None
    c_data = company_sever.get(cid)
    # 核准日期
    try:
        approved_time = c_data.approved_date
    except:
        approved_time = ''
    if not approved_time or approved_time == '':
        print(f'{gid},无核准日期，退出')
        return None
    old_date = get_company_staff_sort_date(gid)
    new_date = get_change_sofft_date(cid, approved_time)
    print('主要人员原始表数据：', old_date)
    print('变更记录提取主要人员数据：', new_date)
    change_counter = Counter(new_date)
    raw_counter = Counter(old_date)
    contrast_status = (change_counter != raw_counter)
    if contrast_status:
        diff1 = old_date.difference(new_date)
        diff2 = new_date.difference(old_date)
        print("在 主要人员表 中但不在 最新变更记录主要人员 中的元素:", diff1)
        print("在 最新变更记录主要人员 中但不在 主要人员表 中的元素:", diff2)
        print('不一致')
        with open('20241206_sofft_diff.csv', 'a', newline='', encoding='utf-8') as f:
            witer = csv.writer(f)
            witer.writerow([gid, diff1, diff2])
    else:
        print('一致')


if __name__ == '__main__':
    # gid = 5782710828
    # main(gid)
    with open('20241202_date.csv', 'r', encoding='utf-8') as f:
        date = csv.DictReader(f)
        for i in date:
            gid = dict(i)['gid']
            print(f"当前gid:{gid}")
            # cid = 2622193135
