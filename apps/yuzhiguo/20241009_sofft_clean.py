from clients.redis._redis import Redis
import csv
from dao.company_graph import CompanyGraphDao
import time
from loguru import logger

redis = Redis(**{
        "host": "redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com",
        "port": 6379,
        "password": "3lvadmpiSj61ge",
        "db": 5,
    })
company_graph_sever = CompanyGraphDao()

# with open('20241008_gids.csv', 'r', encoding='utf-8') as f:
#     data = csv.DictReader(f)
#     count = 0
#     count_err = 0
#     for i in data:
#         dict_data = dict(i)
#         gid = dict_data.get('gid', '')
#         cgs_date = company_graph_sever.get_by_gid(gid)
#         if not cgs_date:
#             logger.error(f'未获取到cid:{gid}')
#             count_err += 1
#             continue
#         try:
#             cid = cgs_date.cid
#         except:
#             logger.error(f'未获取到cid:{gid}')
#             count_err += 1
#             continue
#         logger.info(f'cid:{cid}, gid:{gid}')
#         redis.lpush('dealCompanyStaffSync', cid)
#         count += 1
#         logger.info(f'已推送{count}条数据')
#         time.sleep(0.1)


gid = 5100220206
cgs_date = company_graph_sever.get_by_gid(gid)
cid = cgs_date.cid
redis.lpush('dealCompanyStaffSync', cid)
