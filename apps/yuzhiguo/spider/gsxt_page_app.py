import requests
import warnings
import json
import csv

warnings.filterwarnings("ignore")


def send_request_with_retry(code, retry_times=3):
    """搜索重试"""
    for i in range(retry_times):
        try:
            headers = {
                "Host": "app.gsxt.gov.cn",
                "Accept": "application/json",
                "xweb_xhr": "1",
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 MicroMessenger/6.8.0(0x16080000) NetType/WIFI MiniProgramEnv/Mac MacWechat/WMPF XWEB/30817",
                # "Sec-Fetch-Site": "cross-site",
                # "Sec-Fetch-Mode": "cors",
                # "Sec-Fetch-Dest": "empty",
                # "Referer": "https://servicewechat.com/wx5b0ed3b8c0499950/15/page-frame.html",
                "Accept-Language": "zh-CN,zh"
            }
            data = {
                "conditions": "{\"excep_tab\":\"0\",\"ill_tab\":\"0\",\"area\":\"0\",\"cStatus\":\"0\",\"xzxk\":\"0\",\"xzcf\":\"0\",\"dydj\":\"0\"}",
                "searchword": code,
                "sourceType": "W"
            }
            proxies = {
                "http": "http://************:30636",
                "https": "http://************:30636",
            }
            url = "https://app.gsxt.gov.cn/gsxt/corp-query-app-search-1.html"
            r = requests.post(url, headers=headers, data=data, proxies=proxies, verify=False, timeout=10).json()
            pripid = r.get('data', {}).get('result', {}).get('data', [])[0].get('pripid', None)
            nodeNum = r.get('data', {}).get('result', {}).get('data', [])[0].get('nodeNum', None)
            entType = r.get('data', {}).get('result', {}).get('data', [])[0].get('entType', None)
            if not pripid or not nodeNum or not entType:
                continue
            return pripid, nodeNum, entType
        except Exception as e:
            print(e)
            continue
    return None, None, None


def post_request_with_retry(url, headers, data, params, retry_times=3):
    """照面重试"""
    for i in range(retry_times):
        try:
            proxies = {
                "http": "http://************:30636",
                "https": "http://************:30636",
            }
            r = requests.post(url, headers=headers, params=params, data=data, proxies=proxies, verify=False, timeout=10)
            if len(str(r.json())) > 100:
                return r.json()
            else:
                continue
        except Exception as e:
            print(e)
            continue
    return None


def get_request_with_retry(url, headers, params, retry_times=3):
    """详情重试"""
    for i in range(retry_times):
        try:
            proxies = {
                "http": "http://************:30636",
                "https": "http://************:30636",
            }
            response = requests.get(url, headers=headers, params=params, proxies=proxies, verify=False, timeout=10)
            print(len(str(response.json())))
            if len(str(response.json())) > 100:
                return response.json()
            else:
                continue
        except Exception as e:
            print(e)
            continue
    return None


def base_info(pripid, nodeNum, entType):
    """解析照面信息"""
    base_date = {}
    headers = {
        "Host": "app.gsxt.gov.cn",
        "xweb_xhr": "1",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 MicroMessenger/6.8.0(0x16080000) NetType/WIFI MiniProgramEnv/Mac MacWechat/WMPF XWEB/30817",
        "Content-Type": "application/json",
        "Accept": "*/*",
        # "Sec-Fetch-Site": "cross-site",
        # "Sec-Fetch-Mode": "cors",
        # "Sec-Fetch-Dest": "empty",
        # "Referer": "https://servicewechat.com/wx5b0ed3b8c0499950/15/page-frame.html",
        "Accept-Language": "zh-CN,zh"
    }
    url = f"https://app.gsxt.gov.cn/gsxt/corp-query-entprise-info-primaryinfoapp-entbaseInfo-{pripid}.html"
    params = {
        "nodeNum": nodeNum,
        "entType": entType,
        "sourceType": "W"
    }
    data = {}
    data = json.dumps(data, separators=(',', ':'))
    response = post_request_with_retry(url, headers, data, params)
    if not response:
        print("请求失败")
        return None
    result = response.get('result', {})
    if result:
        # 公司名称
        base_date['name'] = result.get('entName', result.get('traName', ''))
        # 注册状态
        base_date['reg_status'] = result.get('regState_CN', '')
        # 经营期限
        base_date['opFrom'] = result.get('opFrom', '')
        base_date['opTo'] = result.get('opTo', '')
        # 统一信用代码
        base_date['property1'] = result.get('uniscId', '')
        # 法人
        base_date['legal_person_name'] = result.get('name', '')
        # 成立日期
        base_date['estiblish_time'] = result.get('estDate', '')
        # 地址
        base_date['reg_location'] = result.get('dom', result.get('opLoc', ''))
        # 企业类型
        base_date['company_org_type'] = result.get('entType_CN', '')
        # 核准日期
        base_date['approved_time'] = result.get('apprDate', '')
        # 登记机关
        base_date['reg_institute'] = result.get('regOrg_CN', '')
        # 注册资本
        base_date['reg_capital'] = result.get('regCap', '')
        # 经营范围
        base_date['business_scope'] = result.get('opScope', '')
    return base_date


def shareholder_info(pripid, nodeNum):
    """股东信息"""
    shareholder_list = []
    params = {
        "nodeNum": nodeNum,
        "entType": "1",
        "sourceType": "W"
    }
    headers = {
        "Host": "app.gsxt.gov.cn",
        "xweb_xhr": "1",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 MicroMessenger/6.8.0(0x16080000) NetType/WIFI MiniProgramEnv/Mac MacWechat/WMPF XWEB/30817",
        "Content-Type": "application/json",
        "Accept": "*/*",
        # "Sec-Fetch-Site": "cross-site",
        # "Sec-Fetch-Mode": "cors",
        # "Sec-Fetch-Dest": "empty",
        # "Referer": "https://servicewechat.com/wx5b0ed3b8c0499950/15/page-frame.html",
        "Accept-Language": "zh-CN,zh"
    }
    url = f"https://app.gsxt.gov.cn/gsxt/corp-query-entprise-info-shareholder-{pripid}.html"
    response = get_request_with_retry(url, headers, params)
    if response:
        recordsTotal = response.get('recordsTotal', '')
        if recordsTotal:
            for i in range(0, recordsTotal, 5):
                new_params = {
                    "nodeNum": nodeNum,
                    "entType": "1",
                    "sourceType": "W",
                    "start": i
                }
                page_response = get_request_with_retry(url, headers, new_params)
                if page_response:
                    records = page_response.get('data', [])
                    for record in records:
                        investor_name = record.get('inv', '')
                        capital = record.get('liSubConAm', '')
                        detail_url = record.get('url', '')
                        detail_url = detail_url.replace('http://***************:8082', 'https://app.gsxt.gov.cn')
                        print(investor_name, capital)
                        detail_response = get_request_with_retry(detail_url, headers, params)
                        detail_result = []
                        if detail_response:
                            detail_data = detail_response.get('data', {})
                            for detail in detail_data:
                                dics = {}
                                try:
                                    if detail[0]:
                                        subConAm = detail[0].get('subConAm', '')
                                        conForm_CN = detail[0].get('conForm_CN', '')
                                        conDate = detail[0].get('conDate', '')
                                        dics['subConAm'] = subConAm
                                        dics['conForm_CN'] = conForm_CN
                                        dics['conDate'] = conDate
                                        detail_result.append(dics)
                                except Exception as e:
                                    print(e)
                                    continue
                        # print(investor_name, capital)
                        dic = {}
                        dic['investor_name'] = investor_name
                        dic['capital'] = capital
                        dic['detail'] = detail_result
                        shareholder_list.append(dic)
            if len(shareholder_list) == recordsTotal:
                return shareholder_list
            else:
                print("数据不匹配,重试中...")
                shareholder_list = shareholder_info(pripid, nodeNum)
                if shareholder_list:
                    return shareholder_list
        else:
            print("没有数据")
            return shareholder_list
    else:
        print("请求失败")
        shareholder_list = shareholder_info(pripid, nodeNum)
        if shareholder_list:
            return shareholder_list


def person_info(pripid, nodeNum):
    """主要人员信息"""
    headers = {
        "Host": "app.gsxt.gov.cn",
        "xweb_xhr": "1",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 MicroMessenger/6.8.0(0x16080000) NetType/WIFI MiniProgramEnv/Mac MacWechat/WMPF XWEB/30817",
        "Content-Type": "application/json",
        "Accept": "*/*",
        # "Sec-Fetch-Site": "cross-site",
        # "Sec-Fetch-Mode": "cors",
        # "Sec-Fetch-Dest": "empty",
        # "Referer": "https://servicewechat.com/wx5b0ed3b8c0499950/15/page-frame.html",
        "Accept-Language": "zh-CN,zh"
    }
    url = f"https://app.gsxt.gov.cn/gsxt/corp-query-entprise-info-KeyPerson-{pripid}.html"
    params = {
        "nodeNum": nodeNum,
        "entType": "1",
        "sourceType": "W"
    }
    response = get_request_with_retry(url, headers, params)
    if not response:
        print("请求失败")
        return person_info(pripid, nodeNum)
    person_data_list = response.get('data', [])
    if not person_data_list:
        print("没有主要人员信息")
        return None
    dic = {}
    for i in person_data_list:
        person_name = i.get('name', '')
        person_position = i.get('position_CN', '')
        print(person_name, person_position)
        # 人名相同的合并职位用逗号分隔职位
        if person_name in dic.keys():
            dic[person_name] = dic[person_name] + ',' + person_position
        else:
            dic[person_name] = person_position
    return dic


def app_info(cid, code):
    """app端主函数"""
    # 搜索接口
    pripid, nodeNum, entType = send_request_with_retry(code)
    if not pripid or not nodeNum or not entType:
        pripid, nodeNum, entType = send_request_with_retry(code)
    # 照面信息
    base = base_info(pripid, nodeNum, entType)
    # 股东信息
    shareholder = shareholder_info(pripid, nodeNum)
    # 主要人员
    person = person_info(pripid, nodeNum)
    print(base)
    print(shareholder)
    print(person)
    with open('20241024_date_out.csv', 'a', encoding='utf-8', newline='') as f:
        witer = csv.writer(f)
        witer.writerow([cid, code, base, shareholder, person])


if __name__ == '__main__':
    # code = "9111010831813798XE"
    # app_info(code)
    with open('20241024_cid.csv', 'r', encoding='utf-8') as f:
        data = csv.DictReader(f)
        count = 0
        for i in data:
            dict_data = dict(i)
            cid = dict_data.get('rankedrecords.id', '')
            code = dict_data.get('rankedrecords.property1', '')
            app_info(cid, code)
            count += 1
            print(f'已抓取{count}数据')
