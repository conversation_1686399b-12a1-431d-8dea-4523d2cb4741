-- 必要信息  cid gid 企业名称 统一信用代码 公司类型 企业状态 法人 成立日期 核准日期 登记机关
-- 注册地址 注册资本 经营范围 股东数量 主要人员数量
-- 获取抽样名单
-- SELECT id from ods.ods_prism1_company_df WHERE pt=20241017 and dw_is_del=0
-- and reg_status not like '%销%' and company_org_type like '%股份有限公司（外资）%'
-- distribute by rand() sort by rand() limit 1;


drop table test.cids_20241018_df;


create table if not exists test.cids_20241018_df(
     cid string
     )
     row format delimited
     FIELDS TERMINATED BY ','
        stored as textfile;



load data inpath 'hdfs:///user/yuzhiguo/my_data/20241018_cid.csv' into table test.cids_20241018_df;


with
a as (SELECT company_id from ods.ods_gsxt_company_supplement_df WHERE pt=20241020 and dw_is_del=0 and ent_type=1),

c as(SELECT id,name,company_org_type,property1,reg_status,legal_person_name,estiblish_time,
approved_time,reg_institute,reg_location,reg_capital,business_scope
from ods.ods_prism1_company_df WHERE pt=20241020 and dw_is_del=0 and reg_status not like '%销%'),

cg as (select company_id, graph_id from ods.ods_prism1_company_graph_df where pt=20241020  and
deleted = 0 and dw_is_del=0),

eq as(select company_graph_id, count(company_graph_id) as cc from ods.ods_prism1_equity_ratio_df where pt=20241020 and deleted=0 and dw_is_del=0
group by company_graph_id),

ps as(SELECT company_gid, count(company_gid) as pc
    FROM ods.ods_prism_company_staff_sort_df
    WHERE pt = 20241020 AND deleted = 0 AND dw_is_del = 0 group by company_gid)

select a.company_id,cg.graph_id,c.name,c.company_org_type,c.property1,c.reg_status,c.legal_person_name,c.estiblish_time,
c.approved_time,c.reg_institute,c.reg_location,c.reg_capital,c.business_scope,eq.cc,ps.pc
from a left join c on a.company_id=c.id left join cg on c.id=cg.company_id
left join eq on cg.graph_id=eq.company_graph_id left join ps on cg.graph_id=ps.company_gid
where c.id is not null and cg.graph_id is not null and eq.company_graph_id is not null and ps.company_gid is not null
distribute by rand() sort by rand() limit 57;

