from datetime import datetime, timedelta, timezone
import json
import re
from typing import Dict, List
import argparse
import csv
import time
from datetime import timedelta
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.log2 import setup_logger
from libs.env import get_stack_info, ConstantProps
from libs.concurrent import BoundedExecutor

from gslib.schedule import immediately_gs

from dao.company import Company
from dao.company import CompanyDao
from dao.company import CompanyGraph
from dao.company import CompanyGraphDao
from dao.reports.annual_report import AnnualReport
from dao.reports.annual_report import AnnualReportDao
from dao.reports.annual_report_brief import AnnualReportBrief
from dao.reports.annual_report_brief import AnnualReportBriefDao
from dao.key_person.history_company_staff import HistoryCompanyStaff
from dao.key_person.history_company_staff import HistoryCompanyStaffDao
from dao.key_person.company_staff import CompanyStaff
from dao.key_person.company_staff import CompanyStaffDao
from dao.company_hk import CompanyHk
from dao.company_hk import CompanyHkDao
from clients.obs_client import OBSClient
from clients.redis.redis_queue import RedisQueue
from gslib.msv import msv_query_base_info, msv_query_list_dim
from libs.dt import to_date, to_datetime
from gslib.id_center import id_center_query, EntityType
from gslib.credit_code import credit_code_valid
import requests
from decimal import Decimal
from apps.yuzhiguo.app_change_demo.versions_model import VersionModel
from apps.yuzhiguo.app_change_demo.person_versions_model import PersonVersionModel

logger = setup_logger()
fs = dict()
process_code_stat = Counter()

redis_queue = RedisQueue(name='normal_schedule_queue', max_length=1000000, **ConstantProps.PROPS_GS_REDIS_ONLINE)
obs_client = OBSClient(bucket_name='jindi-oss-gsxt')
company_dao = CompanyDao(max_write_per_minute=3000)
company_graph_dao = CompanyGraphDao()
history_company_staff_dao = HistoryCompanyStaffDao()
company_staff_dao = CompanyStaffDao()
company_hk_dao = CompanyHkDao()
# 金额提取正则
amount_re = re.compile("^[\-0-9]*(?:\.[0-9]+)?")


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    DUP = auto()  # 已经生效过的
    EXCLUDE = auto()  # 非目标集合
    OK = auto()  # 本次目标集合


@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    context: Dict = field(default_factory=dict)
    ts: float = field(default_factory=time.time)


# 核准日期 > 最大变更记录日期OR NULL
# 属于公司
# 成立日期 < 核准日期 == 有变更
# 核准日期为2022年后 == 有多源多版本数据
# 核准日期 > 最大变更日期

def get_msv_data(table_name, cid: int, ts=None, source=None, human=False):
    body = {
        "table_name": table_name,
        "cid": cid,
    }
    if ts:
        body['ts'] = ts
    if source:
        body['source'] = source
    if human:
        body['human'] = human
    try:
        data_url = "http://msv-gsdata.jindidata.com/read_list_version"
        msv_data = requests.post(data_url, data=json.dumps(body))
        msv_data = json.loads(msv_data.content)
    except:
        msv_data = {}
    return msv_data.get('data', {})


def get_investor_msv_data(cid, ts=None, source='gsxt_page', human=False):
    """获取股东数据"""
    return get_msv_data("company_investor", cid, ts, source, human=human)


def get_person_msv_data(cid, ts=None, source='XA', human=False):
    """获取股东数据"""
    return get_msv_data("company_staff", cid, ts, source, human=human)


def get_base_info_msv_data(cid, ts=None, source='XA'):
    return get_msv_data("company_base_info", cid, ts, source)


def delete_empty_data(origin_data):
    result = {}
    for source, data in origin_data.items():
        new_data = [item for item in data if item]
        result[source] = new_data
    return result


def delete_abnormal_data(origin_data):
    result = {}
    for source, date in origin_data.items():
        # 使用列表推导式来创建新的列表，只包含'investor_id'的字典
        new_list = [[j for j in i if 'investor_id' in j] for i in date]
        # 过滤掉空列表，并将非空列表添加到结果中
        new_list = [inner_list for inner_list in new_list if inner_list]
        if new_list:
            result[source] = new_list
    return result


def get_amount(amomon):
    amount = 0
    if isinstance(amomon, str):
        result = amount_re.findall(amomon)
        if len(result) == 1:
            try:
                amount += float(result[0])
            except Exception:
                pass
    return amount


def get_amount_from_capital(capital):
    """提起金额"""
    if not capital:
        return 0
    amount = 0
    if isinstance(capital, list):
        capital_json = capital
    else:
        try:
            capital_json = json.loads(capital)
        except:
            result = amount_re.findall(str(capital))
            if len(result) == 1:
                try:
                    amount = Decimal(str(result[0]))
                except Exception:
                    pass
            return amount

    if isinstance(capital_json, (int, float)):
        return capital_json

    for cap in capital_json:
        amomon = cap.get("amomon", 0)
        result = amount_re.findall(str(amomon))
        if len(result) == 1:
            try:
                amount += Decimal(str(result[0]))
            except Exception:
                pass
    return float(amount)


def gsxt_data_selection(investor_version, reg_capital):
    """
    确定工商使用列表页还是详情页
    """
    try:
        reg_capital_num = Decimal(str(get_amount_from_capital(reg_capital)))
    except:
        reg_capital_num = 0
    # 详情认缴总和
    capital_sum = 0
    for investor in investor_version:
        capital_json = json.loads(investor['capital'])
        capital_num = get_amount_from_capital(capital_json)
        if capital_num:
            capital_sum += capital_num
    # 列表页认缴总和
    detail_sum = 0
    for investor in investor_version:
        detail_json = json.loads(investor['detail'])
        subscribe_amount = detail_json.get("subscribeAmount", detail_json.get("subscribe_amount", ""))
        detail_num = get_amount(subscribe_amount)
        if detail_num:
            detail_sum += detail_num
    # 判断详情或列表谁与注册资本一致，默认值 为 0 代表默认规则不变
    if capital_sum == reg_capital_num and capital_sum != 0 and reg_capital_num != 0:
        return 'detail_page'
    elif detail_sum == reg_capital_num and detail_sum != 0 and reg_capital_num != 0:
        return 'list_page'
    else:
        return 0


def handle_gsxt_list_capital(investor):
    """
    列表选取认缴规则
    """
    try:
        capital_json = json.loads(investor['capital'])
        detail_json = json.loads(investor['detail'])
        capital_sum = get_amount_from_capital(capital_json)
        subscribe_amount = detail_json.get("subscribeAmount", detail_json.get("subscribe_amount", ""))
        detail_sum = get_amount(subscribe_amount)
        if not detail_sum or capital_sum == detail_sum:
            return
        if len(capital_json) != 1:
            capital_json = [{
                "amomon": subscribe_amount,
                "time": "",
                "paymet": ""
            }]
        else:
            capital_json[0]['amomon'] = subscribe_amount
        investor['capital'] = json.dumps(capital_json, ensure_ascii=False)
    except:
        print("handle_gsxt_capital error")


def handle_gsxt_detail_capital(investor):
    """详情认缴选取规则"""
    try:
        capital_json = json.loads(investor['capital'])
        detail_json = json.loads(investor['detail'])
        capital_sum = get_amount_from_capital(capital_json)
        subscribe_amount = detail_json.get("subscribeAmount", detail_json.get("subscribe_amount", ""))
        detail_sum = get_amount(subscribe_amount)
        if not detail_sum or capital_sum == detail_sum:
            return
        if len(capital_json) != 1:
            capital_json = [{
                "amomon": str(capital_sum),
                "time": "",
                "paymet": ""
            }]
        else:
            capital_json[0]['amomon'] = str(capital_sum)
        investor['capital'] = json.dumps(capital_json, ensure_ascii=False)
    except:
        print("handle_gsxt_capital error")


def judgement_thereinto_lack_one_subscribe(investor_version, investor_version_list_len):
    capital_count = 0
    capital_sum = Decimal('0')
    for investor in investor_version:
        capital_json = json.loads(investor['capital'])
        capital_num = get_amount_from_capital(capital_json)
        if capital_num:
            capital_count += 1
            capital_sum += Decimal(str(capital_num))
    if investor_version_list_len - capital_count == 1:
        return True, capital_sum
    else:
        return False, False


def handle_gsxt_capital_completion(investor_version, reg_capital, capital_sum):
    """
    当其中一个股东无认缴时进行认缴补全
    """
    try:
        reg_capital_num = Decimal(str(get_amount_from_capital(reg_capital)))
    except:
        reg_capital_num = 0
    if reg_capital_num != 0 and reg_capital_num > capital_sum:
        for investor in investor_version:
            capital_json = json.loads(investor['capital'])
            capital_num_ = get_amount_from_capital(capital_json)
            if not capital_num_ and capital_num_ == 0:
                try:
                    capital_json = json.loads(investor['capital'])
                    detail_json = json.loads(investor['detail'])
                    capital_sum_ = get_amount_from_capital(capital_json)
                    subscribe_amount = detail_json.get("subscribeAmount", detail_json.get("subscribe_amount", ""))
                    detail_sum = get_amount(subscribe_amount)
                    if not detail_sum or capital_sum_ == detail_sum:
                        pass
                    if len(capital_json) != 1:
                        capital_json = [{
                            "amomon": str(Decimal(str(reg_capital_num)) - Decimal(str(capital_sum))),
                            "time": "",
                            "paymet": ""
                        }]
                    else:
                        capital_json[0]['amomon'] = str(Decimal(str(reg_capital_num)) - Decimal(str(capital_sum)))
                    investor['capital'] = json.dumps(capital_json, ensure_ascii=False)
                except:
                    print("handle_gsxt_capital error")


def merge_company(XA_company, gsxt_company):
    try:
        if not XA_company:
            return gsxt_company
        if not gsxt_company:
            return XA_company
        XA_company.reverse()
        gsxt_company.reverse()
        xa_len = len(XA_company)
        gsxt_len = len(gsxt_company)
        new_company = []
        xa_i = 0
        gsxt_i = 0

        while True:
            if xa_i == xa_len:
                new_company.extend(gsxt_company[gsxt_i:])
                break
            if gsxt_i == gsxt_len:
                new_company.extend(XA_company[xa_i:])
                break
            if XA_company[xa_i][0]['msv_first'] <= gsxt_company[gsxt_i][0]['msv_first']:
                new_company.append(XA_company[xa_i])
                xa_i += 1
                continue

            if gsxt_company[gsxt_i][0]['msv_first'] < XA_company[xa_i][0]['msv_first']:
                new_company.append(gsxt_company[gsxt_i])
                gsxt_i += 1
                continue
        new_company.reverse()
        return new_company
    except:
        print("gsxt and XA merge 照面信息数据异常")
        return []


def compare_timestamp_with_date_str(timestamp_ms, date_str):
    """时间戳与核准日期比较"""
    # 将日期字符串转换为datetime对象，这里我们假设时间为00:00:00 UTC
    date_obj = datetime.strptime(date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
    # 将datetime对象转换为毫秒级时间戳
    date_timestamp_ms = int(date_obj.timestamp() * 1000)
    # 比较时间戳
    if timestamp_ms > date_timestamp_ms:
        return True
    elif timestamp_ms < date_timestamp_ms:
        return False
    else:
        return True


def get_max_company(msv_company, it):
    """获取it版本最大核准日期的company"""
    new_msv_company_list = []
    for msv_company_data in msv_company:
        approved_time = msv_company_data[0].get('approved_time', '0')
        if not approved_time:
            return msv_company[0][0]
        if compare_timestamp_with_date_str(it, approved_time):
            new_msv_company_list.append(msv_company_data[0])
    if not new_msv_company_list:
        return None
    if new_msv_company_list:
        max_company = sorted(new_msv_company_list, key=lambda x: x['approved_time'], reverse=False)
        return max_company[-1]


def new_get_person_versions(msv_person, msv_company, source):
    """主要人员获取版本"""
    msv_person.reverse()
    msv_company.reverse()
    result = []
    i_len = len(msv_person)
    i_index = 0
    while i_index < i_len:
        persons = msv_person[i_index]
        it = max([person['msv_first'] for person in persons])
        # 取核准日期小于等于msv_first的所有msv_company列表取最大的核准日期的company
        company = get_max_company(msv_company, it)
        if not company:
            i_index += 1
            continue
        result.append(PersonVersionModel(company, persons, source))
        i_index += 1
    return result


def new_get_investor_versions(msv_investor, msv_company, source):
    msv_investor.reverse()
    msv_company.reverse()
    result = []
    i_len = len(msv_investor)
    i_index = 0
    while i_index < i_len:
        investors = msv_investor[i_index]
        it = max([investor['msv_first'] for investor in investors])
        # 取核准日期小于等于msv_first的所有msv_company列表取最大的核准日期的company
        company = get_max_company(msv_company, it)
        if not company:
            i_index += 1
            continue
        result.append(VersionModel(company, investors, source))
        i_index += 1
    return result


def new_handle_investor_versions(investor_versions: list[VersionModel], source: str):
    """相同核准日期的股东版本使用最大核准日期"""
    if len(investor_versions) < 2:
        return investor_versions
    new_investor_versions = []
    # 跟后一个相等，就丢弃掉，否则使用，但是核准日期会使用前一个的
    for i, investor in enumerate(investor_versions):
        if i + 1 < len(investor_versions) and investor.versions == investor_versions[i + 1].versions:
            if "annual_report" not in investor.source_set:
                investor_versions[i].approved_time = investor_versions[i + 1].approved_time
        else:
            new_investor_versions.append(investor)

    return new_investor_versions


def check_gsxt_shake(investor_versions, reg_capital, source):
    """删除抖动版本"""
    v1_subscribe_mark = False
    v2_subscribe_mark = False
    v1 = investor_versions[-1]
    v2 = investor_versions[-2]
    v1_set = set(v1.versions.split("__"))
    v2_set = set(v2.versions.split("__"))
    v1_name = set()
    v1_sum = Decimal('0')
    for investor in v1.investors[source]:
        v1_num = Decimal(str(get_amount_from_capital(investor['capital'])))
        if v1_num == 0:
            v1_subscribe_mark = True
        v1_sum += v1_num
        v1_name.add(investor['investor_name'] + "_" + str(v1_num))

    v2_name = set()
    v2_sum = Decimal('0')
    for investor in v2.investors[source]:
        v2_num = Decimal(str(get_amount_from_capital(investor['capital'])))
        if v2_num == 0:
            v2_subscribe_mark = True
        v2_sum += v2_num
        v2_name.add(investor['investor_name'] + "_" + str(v2_num))
    if v1_subscribe_mark and v2_subscribe_mark:
        return
    if (v1_set.issubset(v2_set) and v1_name.issubset(v2_name)) or (
            v2_set.issubset(v1_set) and v2_name.issubset(v1_name)):  # 包含逻辑，可能会丢弃版本
        company_reg_capital = get_amount_from_capital(reg_capital)
        if v1_sum == company_reg_capital and v2_sum != company_reg_capital:
            pass
        elif v2_sum == company_reg_capital and v1_sum != company_reg_capital:
            print(f'{investor_versions[-1]},被删除')
            del investor_versions[-1]
        else:
            if v1_set.issubset(v2_set) and v1_sum != float(company_reg_capital):
                print(f'{investor_versions[-1]},被删除')
                del investor_versions[-1]
            else:
                pass
    else:  # 非包含逻辑,相信后一个版本
        pass


def _check_shake(cid: int, source: str, investor_versions: list[VersionModel], reg_capital):
    if not investor_versions:
        return False

    if len(investor_versions) >= 4:
        if investor_versions[-1].versions == investor_versions[-3].versions and \
                investor_versions[-2].versions == investor_versions[-4].versions:
            # print(f"股东数据抖动,cid:{cid},source:{source}")
            if source in ["gsxt_page", "bjold", "xm"]:
                check_gsxt_shake(investor_versions, reg_capital, source)
                return True
    if len(investor_versions) >= 2:
        if source in ["gsxt_page", "bjold", "xm"]:
            check_gsxt_shake(investor_versions, reg_capital, source)
            return True
    return True


def source_data(msv_investor_data, msv_company_data, cid, reg_capital, company_type):
    """
    股东版本、注册资本、公司类型
    """
    source_data = {}
    for source, msv_investor in msv_investor_data.items():
        if source != 'gsxt_page' or not msv_investor:
            continue
        # gsxt_page 详情和认缴策略
        if source == "gsxt_page":  # 处理认缴额的问题
            for investor_version in msv_investor:
                # 确定使用详情还是列表 detail_page、 list_page
                rules_mark = gsxt_data_selection(investor_version, reg_capital)
                if rules_mark == 'list_page':
                    # 列表逻辑
                    for investor in investor_version:
                        # print(f'使用国家工商{rules_mark},数据')
                        handle_gsxt_list_capital(investor)
                elif rules_mark == 'detail_page':
                    # 详情逻辑
                    for investor in investor_version:
                        # print(f'使用国家工商{rules_mark},数据')
                        handle_gsxt_detail_capital(investor)
                else:
                    # 默认逻辑
                    for investor in investor_version:
                        # print(f'使用国家工商默认规则,数据')
                        handle_gsxt_list_capital(investor)

        # 认缴补全 非上市和上市公司不使用认缴补全策略
        if source == 'gsxt_page' and company_type != '非上市股份有限公司':
            for investor_version in msv_investor:
                investor_version_list_len = len(investor_version)
                state_data, capital_sum = judgement_thereinto_lack_one_subscribe(investor_version,
                                                                                 investor_version_list_len)
                for investor in investor_version:
                    if state_data and capital_sum:
                        # print('命中认缴补全')
                        handle_gsxt_capital_completion(investor_version, reg_capital, capital_sum)

        if source != "gsxt_page":
            company_source = source
            msv_company = msv_company_data.get(company_source, [])
        else:
            XA_company = msv_company_data.get('XA', [])
            gsxt_company = msv_company_data.get('gsxt', [])
            msv_company = merge_company(XA_company, gsxt_company)
        # 形成股东版本
        investor_versions = new_get_investor_versions(msv_investor, msv_company, source)
        # 对版本进行去重
        investor_versions = new_handle_investor_versions(investor_versions, source)
        # 抖动处理
        check_result = _check_shake(cid, source, investor_versions, reg_capital)
        if check_result:
            source_data[source] = investor_versions
    return source_data


def validate_input(pattern, input_string):
    """
    判断字段是否满足要求
    """
    if re.match(pattern, input_string):
        return False
    else:
        return True


def get_category(value):
    """
    规范出资类型
    """
    # 货币、 实物、知识产权、土地使用权、劳务和信用出资
    if re.findall('[货币|现金|货|资金|人民币|投资金|美元]', value):
        return '货币'
    if re.findall('设备|实物|原料|零部件|货物|建筑物|厂房|电脑|切割机|场地|商品|车|酒楼设施', value):
        return '实物'
    if re.findall('商标|知识|专利|著作|高新技术|科技成果', value):
        return '知识产权'
    if re.findall('债权', value):
        return '债权'
    if re.findall('股权|资本公积转增', value):
        return '股权'
    if re.findall('非专利技术', value):
        return '非专利技术'
    if re.findall('土地', value):
        return '土地使用权'
    if re.findall('劳务|信用', value):
        return '劳务和信用'
    if re.findall('以个人|家庭共有财产', value):
        return '个人出资'
    if re.findall('净资产', value):
        return '净资产'
    if re.findall('其他|其它', value):
        return '其他'
    if re.findall('null|NULL|[0-9]+|nu|同上|[a-z]+|不确定|�|\||选择出资方式|-|/|公司', value):
        return ''
    else:
        return value


def check_capital(capital):
    try:
        new_capital = []
        for cap in capital:
            keys = list(cap.keys())
            for key in keys:
                if key not in ("amomon", "time", "paymet"):
                    del cap[key]
            if not cap:
                continue

            if "amomon" not in cap:
                continue
            if "time" not in cap:
                cap['time'] = ""
            if "paymet" not in cap:
                cap['paymet'] = ""
            cap['amomon'] = str(cap['amomon'])
            cap['time'] = str(cap['time'])
            cap['paymet'] = str(cap['paymet'])
            if cap['amomon'] in ("null", "None"):
                cap['amomon'] = ""
            if cap['time'] in ("null", "None"):
                cap['time'] = ""
            if validate_input(r'^[0-9]{4}-[0-9]{2}-[0-9]{2}$', cap['time']):
                cap['time'] = ""
            if cap['paymet'] in ("null", "None"):
                cap['paymet'] = ""
            else:
                cap['paymet'] = get_category(cap['paymet'])

            new_capital.append(cap)
    except:
        # print("capital 格式错误")
        new_capital = capital
    return new_capital


def _calculation_ratio(result: list, reg_capital):
    """计算比例"""
    if not result:
        return result
    # 数据原本就是处理好的，不需要添加gid等信息
    if "investor_id" not in result[0]:
        return result

    # investor_ids = [str(item['investor_id']) for item in result]
    # if investor_ids:
    #     company_info = self.company_server.get_company_with_gid_by_cids(investor_ids)
    #     human_info = self.human_server.get_human_with_gid_by_ids(investor_ids)

    new_investor = []
    all_amount = 0
    is_full = True
    for investor in result:
        investor_id = investor['investor_id']
        investor_type = investor['investor_type']
        name = investor['investor_name']

        amount = get_amount_from_capital(investor['capital'])
        if amount:
            all_amount += amount
        else:
            is_full = False
        new_investor.append({
            "amount": amount,
            "capital": check_capital(investor.get('capital', [])),
            # "capitalActl": check_capital(investor.get('capital_actl', [])),
            "name": name,
            # "id": id,
            # "type": investor_type,
            "percent": 0,
            # "desc": 0
        })
    company_reg_capital = get_amount_from_capital(reg_capital)
    # 处理因重复多次认缴导致的比例计算错误
    all_amount = 0
    is_full = True
    for investor in new_investor:
        amount = get_amount_from_capital(investor['capital'])
        if amount:
            all_amount += amount
        else:
            is_full = False
    # 只有全部股东有认缴额的时候，才计算股比，否则不计算股比
    if is_full:
        for investor in new_investor:
            investor['percent'] = Decimal(float(investor['amount']) / all_amount).quantize(Decimal("0.000001"),
                                                                                           rounding="ROUND_HALF_UP")
    # elif len(new_investor) == 1:
    #     new_investor[0]['percent'] = 1.0000
    # elif all_amount and all_amount <= company_reg_capital:
    #     for investor in new_investor:
    #         if investor['amount']:
    #             investor['percent'] = Decimal(float(investor['amount']) / float(company_reg_capital)).quantize(
    #                 Decimal("0.000001"), rounding="ROUND_HALF_UP")

    # else:
    #     print(f"没有股比,gid:{self.gid},cid:{self.cid}")
    return new_investor


def process_base_info(tc):
    """照面信息"""
    cid = int(tc.inputs['company_2.cid'])
    credit_code = tc.inputs['company_2.credit_code']
    appr_date = to_date(tc.inputs['company_2.appr_date'])
    max_change_date = to_date(tc.inputs['change_info_2.max_change_date'])

    msv_base_info_list = msv_query_base_info(cid=cid)
    msv_base_info_cur, msv_base_info_lst = None, None
    for msv_base_info in msv_base_info_list:
        msv_appr_date = to_date(msv_base_info['approved_time'])
        if msv_appr_date <= appr_date:
            if msv_base_info_cur is None:
                msv_base_info_cur = msv_base_info
            elif msv_base_info_lst is None and msv_appr_date < to_date(msv_base_info_cur['approved_time']):
                msv_base_info_lst = msv_base_info

    # logger.info(f'{msv_base_info_cur}')
    # logger.info(f'{msv_base_info_lst}')
    if not msv_base_info_cur or not msv_base_info_lst:
        logger.info('need at least 2 versions')
        return
    if to_date(msv_base_info_cur['approved_time']) != appr_date:
        logger.info('cur msv approved_time != appr_date')
        return

    for key in (msv_base_info_cur.keys() | msv_base_info_lst.keys()):
        if key in ['id', 'msv_deleted', 'msv_first', 'source', 'msv_last_time', 'cid', 'msv_last', 'approved_time', 'reg_status',
                   'reg_number','legal_person_id',]:
            continue
        if key == 'reg_capital':
            cur_reg = msv_base_info_cur.get(key, None)
            lst_reg = msv_base_info_lst.get(key, None)
            cur_num = get_amount_from_capital(cur_reg)
            lst_num = get_amount_from_capital(lst_reg)
            if cur_num == lst_num:
                continue
        cur = msv_base_info_cur.get(key, None)
        lst = msv_base_info_lst.get(key, None)
        if cur == lst:
            continue
        logger.info(f'{key} {lst} -> {cur}')
        # with open('base.csv', 'a', encoding='utf-8') as f:
        #     witer = csv.writer(f)
        #     witer.writerow([cid, credit_code, appr_date, key, lst, cur])
    pass


def process_shareholder_info(tc):
    """股东信息变更"""
    cid = int(tc.inputs['company_2.cid'])
    credit_code = tc.inputs['company_2.credit_code']
    appr_date = to_date(tc.inputs['company_2.appr_date'])
    tyc_company_data = company_dao.get(field='id', value=cid)
    reg_capital = tyc_company_data.reg_capital
    company_type = tyc_company_data.company_org_type
    max_change_date = to_date(tc.inputs['change_info_2.max_change_date'])

    # 股东信息
    msv_investor_data = get_investor_msv_data(cid)

    # 过滤股东数据
    msv_investor_data = delete_empty_data(msv_investor_data)
    msv_investor_data = delete_abnormal_data(msv_investor_data)
    # 照面信息
    msv_company_data = get_base_info_msv_data(cid)
    # 过滤照面数据
    msv_company_data = delete_empty_data(msv_company_data)
    # 照面信息合并股东版本
    gsxt_page_data = source_data(msv_investor_data, msv_company_data, cid, reg_capital, company_type)
    # print(gsxt_page_data)
    if not gsxt_page_data:
        return '没有数据'
    if len(gsxt_page_data['gsxt_page']) < 2:
        return '只有一个版本'
    # 计算比例
    current_version = gsxt_page_data['gsxt_page'][-1].investors['gsxt_page']
    current_version_app_time = gsxt_page_data['gsxt_page'][-1].approved_time
    previous_version = gsxt_page_data['gsxt_page'][-2].investors['gsxt_page']
    previous_version_app_time = gsxt_page_data['gsxt_page'][-2].approved_time
    change_time = max(current_version_app_time, previous_version_app_time)
    # print(type(change_time), type(max_change_date))
    if not max_change_date:
        max_change_date = to_date('0001-01-01')
    if to_date(change_time) < max_change_date:
        return '变更时间小于最大变更时间'
    if to_date(change_time) != appr_date:
        return '变更时间不等于核准时间'
    # 计算股东比例
    current_version_investor_ratio = _calculation_ratio(current_version, previous_version)
    previous_version_investor_ratio = _calculation_ratio(previous_version, current_version)
    content_after = ""
    for i in current_version_investor_ratio:
        name = i['name']
        amount = i['amount']
        percent = i['percent'] * 100
        # print(f"股东:{name},认缴额:{amount},比例:{percent}")
        content_after += f"股东:{name},认缴额:{amount},比例:{str(float(percent)) + '%'};\n"
    content_before = ""
    for j in previous_version_investor_ratio:
        name = j['name']
        amount = j['amount']
        percent = j['percent'] * 100
        # print(f"股东:{name},认缴额:{amount},比例:{percent}")
        content_before += f"股东:{name},认缴额:{amount},比例:{str(float(percent)) + '%'};\n"
    print('-------变更时间--------')
    print(change_time)
    print('--------股东信息：变更前---------')
    print(content_before)
    print('--------股东信息：变更后---------')
    print(content_after)
    # with open('shareholder.csv', 'a', encoding='utf-8') as f:
    #     witer = csv.writer(f)
    #     witer.writerow([cid, credit_code, appr_date, change_time, content_before, content_after])

def meger_person_date(msv_person_data, msv_company_data):
    """合并主要人员版本"""
    source_data = {}
    for source, msv_person in msv_person_data.items():
        if source != 'XA' or not msv_person:
            continue
        if source != "XA":
            company_source = source
            msv_company = msv_company_data.get(company_source, [])
        else:
            XA_company = msv_company_data.get('XA', [])
            gsxt_company = msv_company_data.get('gsxt', [])
            msv_company = merge_company(XA_company, gsxt_company)
        # 形成股东版本
        person_versions = new_get_person_versions(msv_person, msv_company, source)
        # 对版本进行去重
        # 抖动处理
        source_data[source] = person_versions
    return source_data



def is_same_year(date1, date2):
    """
    判断两个日期是否为同一年份
    """
    year1 = datetime.strptime(date1, '%Y-%m-%d').year
    year2 = datetime.strptime(date2, '%Y-%m-%d').year
    return year1 == year2

def process_person_info(tc):
    """主要人员"""
    cid = int(tc.inputs['company_2.cid'])
    credit_code = tc.inputs['company_2.credit_code']
    appr_date = to_date(tc.inputs['company_2.appr_date'])
    max_change_date = to_date(tc.inputs['change_info_2.max_change_date'])

    # 获取多源多版本主要人员数据
    msv_person_data = get_person_msv_data(cid)
    # 过滤主要人员数据
    msv_person_data = delete_empty_data(msv_person_data)
    # 照面信息
    msv_company_data = get_base_info_msv_data(cid)
    # 过滤照面数据
    msv_company_data = delete_empty_data(msv_company_data)
    # 过滤照面信息取核准日期做大和第二大的照面信息 核准日期相同使用抓取时间最大的过滤
    # app_list = []
    # for source, msv_company in msv_company_data.items():
    #     if source != 'XA' or not msv_company:
    #         continue
    #     # 获取最大和第二大核准日期照面信息
    #     for i in msv_company:
    #         if i[0]['approved_time']:
    #             app_list.append(i[0]['approved_time'])
    # if not app_list:
    #     return '无数据'
    # app_list = sorted(app_list, reverse=True)
    # max_app = app_list[0]
    # lst_app = app_list[1]
    # max_new_msv_company_data = {}
    # lst_new_msv_company_data = {}
    # for source, msv_company in msv_company_data.items():
    #     if source!= 'XA' or not msv_company:
    #         continue
    #     max_date = []
    #     lst_date = []
    #     for i in msv_company:
    #         if i[0]['approved_time'] == max_app:
    #             max_date.append(i)
    #     for j in msv_company:
    #         if j[0]['approved_time'] == lst_app:
    #             lst_date.append(j)
    #     if not max_date or not lst_date:
    #         return '没有数据'
    #     max_date = sorted(max_date, key=lambda x: (x[0]['msv_first']), reverse=True)
    #     lst_date = sorted(lst_date, key=lambda x: (x[0]['msv_first']), reverse=True)
    #     max_new_msv_company_data[source] = [max_date[0]]
    #     lst_new_msv_company_data[source] = [lst_date[0]]
    # max_msv_company_data = max_new_msv_company_data
    # lst_msv_company_data = lst_new_msv_company_data

    # 照面信息合并主要人员版本
    # max_xa_data = meger_person_date(msv_person_data, max_msv_company_data)
    # lst_xa_date = meger_person_date(msv_person_data, lst_msv_company_data)
    # print(max_xa_data)
    # print(lst_xa_date)

    xa_data = meger_person_date(msv_person_data, msv_company_data)
    if not xa_data:
        return '没有数据'
    # print(xa_data)
    if len(xa_data['XA']) < 2:
        return '只有一个版本'
    current_version = xa_data['XA'][-1].persons['XA']
    current_version_app_time = xa_data['XA'][-1].approved_time
    current_versions = xa_data['XA'][-1].versions
    previous_version = xa_data['XA'][-2].persons['XA']
    previous_version_app_time = xa_data['XA'][-2].approved_time
    previous_versions = xa_data['XA'][-2].versions
    if not max_change_date:
        max_change_date = to_date('0001-01-01')
    if not is_same_year(current_version_app_time, previous_version_app_time):
        return '不是同年份,不符合规则'
    if current_versions == previous_versions:
        return '版本相同'
    change_time = max(current_version_app_time, previous_version_app_time)
    # print(type(change_time), type(max_change_date))
    if to_date(change_time) <= max_change_date:
        return '变更时间小于最大变更时间'
    if to_date(change_time) != appr_date:
        return '变更时间不等于核准时间'

    content_after = ""
    for i in current_version:
        staff_name = i['staff_name']
        staff_position = i['staff_position']
        # print(f"主要人员:{staff_name},职位:{staff_position}")
        content_after += f"主要人员:{staff_name},职位:{staff_position};\n"
    content_before = ""
    for j in previous_version:
        staff_name = j['staff_name']
        staff_position = j['staff_position']
        # print(f"主要人员:{staff_name},职位:{staff_position}")
        content_before += f"主要人员:{staff_name},职位:{staff_position};\n"
    print('-------变更时间--------')
    print(change_time)
    print('--------主要人员：变更前---------')
    print(content_before)
    print('--------主要人员：变更后---------')
    print(content_after)
    # with open('person.csv', 'a', encoding='utf-8') as f:
    #     witer = csv.writer(f)
    #     witer.writerow([cid, credit_code, appr_date, change_time, content_before, content_after])


def process(tc: TaskContext):
    cid = int(tc.inputs['company_2.cid'])
    credit_code = tc.inputs['company_2.credit_code']
    appr_date = to_date(tc.inputs['company_2.appr_date'])
    max_change_date = to_date(tc.inputs['change_info_2.max_change_date'])
    logger.info(f'{credit_code} {cid} {appr_date} {max_change_date}')

    # 通过msv 检查appr_date的核变内容
    # 照面信息
    process_base_info(tc)
    # 股东信息
    process_shareholder_info(tc)
    # 主要人员
    process_person_info(tc)


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code not in [ProcessCode.INITIAL, ProcessCode.EXCLUDE]:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1
    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()


def main(args):
    logger.info(f'args={args}')
    with (
        BoundedExecutor(max_workers=args.worker_num, thread_name_prefix='worker') as worker_pool,
        open(f'{args.input_csv_name}.csv', 'r') as fin,
    ):
        reader = csv.DictReader(fin)
        for did, d in enumerate(reader):
            if '销' in d['company_2.reg_status']:
                continue
            if did < args.input_csv_lid_min:
                continue
            if did >= args.input_csv_lid_max != -1:
                break
            d['did'] = did
            tc = TaskContext(inputs=d)
            future: Future = worker_pool.submit(process, tc)
            fs[future] = tc
            future.add_done_callback(callback_fn)

        while len(fs) > 0:
            time.sleep(1)
        logger.info(f'process_code_stat {process_code_stat}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='通用多线程处理程序-csv')
    ap.add_argument('--input-csv-name', type=str, default='20241021_generated_appr_change_info.01p', help='')
    ap.add_argument('--input-csv-lid-min', type=int, default=-1)
    ap.add_argument('--input-csv-lid-max', type=int, default=1000)
    ap.add_argument('--worker-num', type=int, default=1)

    # main(ap.parse_args())

    # _d = {
    #     'did': 0,
    #     'company_2.cid': '1901834',
    #     'company_2.credit_code': '91110117327306570G',
    #     'company_2.appr_date': '2024-10-10',
    #     'change_info_2.max_change_date': '2017-04-10',
    #
    # }
    # _d = {
    #     'did': 0,
    #     'company_2.cid': '2373997000',
    #     'company_2.credit_code': '91110111MA7EP97K1M',
    #     'company_2.appr_date': '2024-09-18',
    #     'change_info_2.max_change_date': '',
    #
    # }
    _d = {
        'did': 0,
        'company_2.cid': '*********',
        'company_2.credit_code': '91110109MA01PT8H3H',
        'company_2.appr_date': '2024-09-05',
        'change_info_2.max_change_date': '',

    }
    tc = TaskContext(inputs=_d)
    process(tc)
    logger.info(f'tc={tc}')

