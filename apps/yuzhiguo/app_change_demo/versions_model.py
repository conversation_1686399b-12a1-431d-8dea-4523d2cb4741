import json


def get_capital_json(capital):
    try:
        capital_json = json.loads(capital)
    except:
        print("capital or capital_actl解析异常，请查看")
        capital_json = []
    return capital_json

class VersionModel(object):
    def __init__(self, company: dict, investors: list[dict], source) -> None:  # 重新构建model
        approved_time = company.get("approved_time", "0")
        self.approved_time = approved_time if approved_time else "0"
        investor_ids = [str(investor['investor_id']) for investor in investors]
        investor_ids.sort()
        self.versions = "__".join(investor_ids)
        for investor in investors:
            if source != "annual_report":
                investor['capital'] = get_capital_json(investor['capital'])
                investor['capital_actl'] = get_capital_json(investor['capital_actl'])
        self.investors = {
            source: investors
        }
        self.source_set = set([source, ])
        self.source = source
        self.scrape_source_list = ['gsxt_page', 'jsold', 'tjold', 'jxold', 'hanold', 'gzold', 'hljold',
                                   'gdold', 'shold', 'gdsz', 'xm', 'gdzh']
        self.msv_first = max([int(investor.get('msv_last_time', investor.get('msv_first', 0))) if investor.get(
            'source') in self.scrape_source_list else investor.get('msv_first', 0) for investor in investors])
        # self.msv_first = max([investor.get('msv_first', 0) for investor in investors])

    def __str__(self):
        return str({
            "versions": self.versions,
            "msv_first": self.msv_first,
            "approved_time": self.approved_time,
            "source": self.source,
            "source_set": self.source_set
        })

    def _get_capital(self):
        result = []
        for investor in self.investors:
            result.append({
                investor['investor_id']: investor['capital']
            })
        return result






