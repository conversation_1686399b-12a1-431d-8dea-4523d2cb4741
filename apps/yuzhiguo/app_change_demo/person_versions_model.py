import json

class PersonVersionModel(object):
    def __init__(self, company: dict, persons: list[dict], source) -> None:  # 重新构建model
        approved_time = company.get("approved_time", "0")
        self.approved_time = approved_time if approved_time else "0"
        staff_ids = set([str(person['staff_id']) for person in persons])
        list(staff_ids).sort()
        self.versions = "__".join(staff_ids)
        self.persons = {
            source: persons
        }
        self.source_set = set([source, ])
        self.source = source
        self.msv_first = max([int(person.get('msv_last_time', person.get('msv_first', 0))) if person.get(
            'source') in 'XA' else person.get('msv_first', 0) for person in persons])
        # self.msv_first = max([investor.get('msv_first', 0) for investor in investors])

    def __str__(self):
        return str({
            "versions": self.versions,
            "msv_first": self.msv_first,
            "approved_time": self.approved_time,
            "source": self.source,
            "source_set": self.source_set
        })






