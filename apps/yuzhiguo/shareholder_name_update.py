# encoding=utf8
from libs.env import ConstantProps, get_stack_info
from clients.redis._redis import Redis
import time
from dao.company import CompanyDao
from dao.company_graph import CompanyGraphDao
from apps.yuzhiguo.my_sever.equity_ratio import EquityRatioDao
from apps.yuzhiguo.my_sever.equity_ratio_gsxt import EquityRatioGsxtDao
from libs.log2 import setup_logger

logger = setup_logger(debug=False)
company_sever = CompanyDao()
company_graph_sever = CompanyGraphDao()
equity_ratio_sever = EquityRatioDao()
equity_ratio_gsxt_sever = EquityRatioGsxtDao()
redis_client = Redis(**ConstantProps.PROPS_GS_REDIS_ONLINE, db=4)


def shareholder_name_update(cid):
    """股东名称更新逻辑"""
    # 获取公司名称
    company_date = company_sever.get(cid, 'id')
    name = company_date.name
    # 获取对应gid
    try:
        gid_date = company_graph_sever.get_by_cid(cid)
        gid = gid_date.cgid
    except Exception as e:
        logger.warning(f"当前cid无gid：{cid},{e}")
        return None
    # 获取股东表股东GID等于gid且股东name不等于name的数据，获取ID
    equity_ratio_list = equity_ratio_sever.get_by_shareholder_gid(gid)
    # equity_ratio 表更新股东名称
    if equity_ratio_list:
        for item in equity_ratio_list:
            if item.shareholder_name != name:
                equity_ratio_sever.update_shareholder_name(item.id, name)
                logger.info(f"股东信息表更新的记录ID：{item.id},shareholder_name更新为：{name}")
    # equity_ratio_gsxt表更新股东名称
    equity_ratio_gsxt_list = equity_ratio_gsxt_sever.get_by_shareholder_gid(gid)
    if equity_ratio_gsxt_list:
        for item in equity_ratio_gsxt_list:
            if item.shareholder_name != name:
                equity_ratio_gsxt_sever.update_shareholder_name(item.id, name)
                logger.info(f"工商登记表更新的记录ID：{item.id},shareholder_name更新为：{name}")


def fetch_tasks(queue_name):
    task_withscores = redis_client.zrange(queue_name, 0, 20, withscores=True)
    if task_withscores:
        for task in task_withscores:
            redis_client.zrem(queue_name, task[0])
        return task_withscores
    return None


def consume_redis_queue():
    """消费队列"""
    queue_name = "shareholder_name_clean"
    while True:
        if redis_client.zcard(queue_name) > 0:
            tasks = fetch_tasks(queue_name)
            if tasks:
                for item in tasks:
                    cid = int(item[0])
                    shareholder_name_update(cid)
                    logger.info(f"处理数据: {cid}")
        else:
            time.sleep(20)


if __name__ == '__main__':
    consume_redis_queue()