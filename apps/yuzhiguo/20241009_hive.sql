WITH
c as (SELECT id, company_org_type
from ods.ods_prism1_company_df WHERE pt =20241007 and company_org_type like '%有限责任公司%'
and property1 like '91%' and reg_status not like '%销%' and (property2 IS NULL or property2='')),

a AS (
    SELECT company_gid,human_gid
    FROM ods.ods_prism_company_staff_sort_df
    WHERE pt = 20241007 AND deleted = 0 AND dw_is_del = 0
),

b AS (
    SELECT  company_id,staff_id
    FROM ods.ods_prism1_company_staff_df
    WHERE pt = 20241007 AND  dw_is_del = 0
),

hg as (SELECT human_id, graph_id as hgid from ods.ods_prism1_human_graph_df
WHERE pt=20241007 and deleted=0 and dw_is_del=0),

cg as (select company_id, graph_id from ods.ods_prism1_company_graph_df where pt=20241007 and
deleted = 0 and dw_is_del=0),

-- 需要公司gid 和人员cid
t1 as (SELECT company_gid as gid,hg.human_id as hcid FROM a LEFT JOIN hg on a.human_gid=hg.hgid),

t2 as (SELECT cg.graph_id as gid,b.staff_id as hcid FROM b LEFT JOIN cg on b.company_id=cg.company_id),

t3 as(SELECT COALESCE(t1.gid,t2.gid) as gid FROM t1  FULL OUTER JOIN t2 ON t1.gid = t2.gid AND t1.hcid = t2.hcid
WHERE t1.gid IS NULL or t2.gid IS NULL),

t4 as( SELECT DISTINCT gid FROM t3)
-- 统计主要人员两个表不一致涉及企业：86021
SELECT gid FROM c LEFT JOIN cg on c.id=cg.company_id LEFT JOIN t4 on cg.graph_id=t4.gid WHERE gid IS NOT NULL;
