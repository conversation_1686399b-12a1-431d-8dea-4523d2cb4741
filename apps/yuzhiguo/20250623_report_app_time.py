from biz_utils.schedule import gs_dispatch
import csv
import time

with open('20250623_2024_cid.csv', 'r') as f:
    reader = csv.DictReader(f)
    for row in reader:
        if row['sample_year'] == '2024':
            gs_dispatch(reason='app_time_report_2024', cid=row['sample_cid'], dims=['annualreport2', ], immediately=True)
        else:
            gs_dispatch(reason='app_time_report_2023', cid=row['sample_cid'], dims=['annualreport2', ], immediately=True)
        time.sleep(2)