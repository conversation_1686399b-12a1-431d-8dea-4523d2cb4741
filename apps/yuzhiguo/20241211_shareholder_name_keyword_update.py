from clients.redis._redis import Redis
from libs.env import ConstantProps
from libs.log2 import setup_logger
import csv, time


logger = setup_logger(debug=False)

redis_client = Redis(**ConstantProps.PROPS_GS_REDIS_ONLINE, db=4)


count = 0
with open('20241210_cids.csv', 'r', encoding='utf-8') as f:
    date = csv.DictReader(f)
    for i in date:
        cid = dict(i)['c.id']
        redis_client.zadd('shareholder_name_clean', {cid: 1})
        count += 1
        logger.info(f"当前cid:{cid},当前第：{count}个")
        time.sleep(0.1)