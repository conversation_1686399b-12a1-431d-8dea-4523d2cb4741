# encoding=utf8
from my_sever.equity_ratio import EquityRatioDao
from my_sever.bad_history_investor import BadHistoryInvestorDao
from libs.env import ConstantProps
from clients.redis._redis import Redis
from gslib.gs_enum import EntityType
from dao.company_graph import CompanyGraphDao
import time, json, threading, csv
from libs.log2 import setup_logger

logger = setup_logger()
eq_sever = EquityRatioDao()
cg_sever = CompanyGraphDao()
bad_sever = BadHistoryInvestorDao()
# 队列
redis_client = Redis(**ConstantProps.PROPS_GS_REDIS_ONLINE, db=4)


def get_name_type(shareholder_type):
    """股东类型对应关系"""
    if shareholder_type == EntityType.HUMAN:
        return 1
    elif shareholder_type == EntityType.ORG:
        return 2
    elif shareholder_type == EntityType.TYPE3:
        return 3
    elif shareholder_type == EntityType.UNSET:
        return 0


def get_redis_gid_by_sgid(gid):
    """
    gid查询队列中对应的所有s_gid的列表,
    """
    sh_gids = redis_client.smembers('equity_ratio_offline')
    sh_gids_for_this_gid = [s.split(':')[1] for s in sh_gids if s.startswith(gid + ':')]
    if sh_gids_for_this_gid:
        return sh_gids_for_this_gid
    else:
        return []


def judgement_init(gid, sgid, shareholder_type):
    """
    判断gid, sgid, shareholder_type在不在股东表
    """
    try:
        shareholder_data = eq_sever.get_by_gid(gid)
    except Exception as e:
        # 处理可能的异常
        print(f"Error occurred while fetching shareholder data: {e}")
        return False
    make = False
    if shareholder_data:
        for data in shareholder_data:
            new_gid = data.company_graph_id
            new_sgid = data.shareholder_graph_id
            new_shareholder_type = get_name_type(data.shareholder_type)
            if int(gid) == new_gid and int(sgid) == new_sgid and int(shareholder_type) == new_shareholder_type:
                make = True
    if not make:
        return True
    else:
        return False


def whether_at_queue(gid, interval=1):
    """
    检查gid对应的cid是否在14个队列中
    """
    while True:
        # 获取公司ID对应的cid
        cid_data = cg_sever.get_by_gid(gid)
        if not cid_data:
            return False
        cid = cid_data.cid
        # 14个队列key
        key_list = [f"equity_ratio_company_ids_{i}" for i in range(15)]
        # 检查cid是否在任意一个集合中
        found_in_queue = False
        for key in key_list:
            score = redis_client.zscore(key, cid)
            if score is not None:
                logger.info(f"---当前gid:{gid},还在队列中---")
                found_in_queue = True
                break
        if not found_in_queue:
            return True
        # 等待一段时间后再检查
        time.sleep(interval)


def data_to_redis(gid, reason):
    """
    数据集写入队列:生产者
    gid：公司gid
    reason：标记
    """
    eq_data = eq_sever.get_by_gid(gid)
    if eq_data:
        for i in eq_data:
            gid = i.company_graph_id
            sgid = i.shareholder_graph_id
            shareholder_type = get_name_type(i.shareholder_type)
            print(gid, sgid, shareholder_type, reason)
            # 写入队列
            redis_client.sadd('equity_ratio_offline', f'{gid}:{sgid}:{shareholder_type}:{reason}')
            logger.info(f'当前gid:{gid}，插入{sgid}:{shareholder_type}:{reason}成功')


def get_gid_by_redis_data(gid):
    """
    主逻辑：消费者
    """
    # 获取集合中的所有成员
    members = redis_client.smembers('equity_ratio_offline')
    # 遍历成员，找到与 gid 匹配的记录
    for member in members:
        if member.startswith(str(gid) + ':'):
            parts = member.split(':')
            if len(parts) == 4:
                res_gid, res_sgid, res_types, res_reason = parts
                print(res_gid, res_sgid, res_types, res_reason)
                # 检查队列 增加查询Redis队列是否被消费（把控进度）
                queue_checks = whether_at_queue(res_gid)
                if queue_checks:
                    # 判断是否在股东表
                    date_init = judgement_init(res_gid, res_sgid, res_types)
                    if date_init:
                        print(f'当前gid:{gid},已不在队列中，进入下一步')
                        # 插入
                        insert_status = bad_sever.insert_by_gid(res_gid, res_sgid, res_types, res_reason)
                        if insert_status:
                            print("插入成功")
                        else:
                            print("插入失败")
    redis_client.srem('equity_ratio_offline', *[m for m in members if m.startswith(str(gid) + ':')])
    print(f"已删除所有与gid: {gid} 相关的记录")

    # 获取当前集合数量
    count = redis_client.scard('equity_ratio_offline')
    print(f"集合 equity_ratio_offline 中的元素数量是: {count}")


class GetHtml(threading.Thread):

    def __init__(self, gid, sgid, sh_type, reason, semaphore, mode):
        super(GetHtml, self).__init__()
        self.gid = gid
        self.sgid = sgid
        self.sh_type = sh_type
        self.reason = reason
        self.mode = mode
        self.semaphore = semaphore

    def run(self):
        if self.mode:
            # 生产
            data_to_redis(self.gid, self.reason)
        elif not self.mode:
            # 消费
            get_gid_by_redis_data(self.gid)
        self.semaphore.release()


class SendUrl(threading.Thread):

    def __init__(self, semaphore: threading.Semaphore, mode):
        super(SendUrl, self).__init__()
        self.mode = mode
        self.semaphore = semaphore

    def run(self):
        with open('gids_data.csv', 'r', encoding='utf-8') as f:
            gids_data = csv.DictReader(f)
            for i in gids_data:
                dict_data = dict(i)
                gid = dict_data.get('graph_id', '')
                sgid = dict_data.get('sgid', '')
                sh_type = dict_data.get('sh_type', '')
                reason = dict_data.get('测试', '')
                self.semaphore.acquire()
                get_html_thread = GetHtml(gid=gid, sgid=sgid, sh_type=sh_type, reason=reason,
                                          semaphore=self.semaphore, mode=self.mode)
                get_html_thread.start()
                logger.info(f'---当前第--{i}--个---')


if __name__ == '__main__':
    # 低效模式
    gids = [4054956461]
    for gid in gids:
        # 生产
        # data_to_redis(gid, reason='测试')
        # 消费
        get_gid_by_redis_data(gid)

    # 高效模式
    # semaphore = threading.Semaphore(30)
    # # mode True:生产模式 False:消费模式
    # send_url = SendUrl(semaphore, mode=True)
    # send_url.start()


