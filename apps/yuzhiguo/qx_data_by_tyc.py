from my_sever.t_whole_park import QxWholeParkDao
from my_sever.qianzhan_park import Qi<PERSON><PERSON>hanParkDao
from typing import List
from gslib.id_center import id_center_query
from datetime import datetime
import re

ty_sever = QianZhanParkDao()


class QxSeverDao(QxWholeParkDao):
    def __init__(self, **kwargs):
        self.auto_limit = False  # SQL语句是否补全limit
        super().__init__(**kwargs)
        self._auto_limit = self.auto_limit

    def get_all_yid(self):
        sql = 'select yid,name from {} limit 83412 '.format(self.db_tb_name)
        for o in self.mysql_client.select_many(sql):
            yield o

    def get_data_join(self, yid):
        sql = f"select * from {self.db_tb_name} left join db_business_reproduce.t_whole_park_relation on t_whole_park.yid= t_whole_park_relation.yid where t_whole_park.yid = '{yid}' and t_whole_park_relation.company_name is not null"
        items: List = list()
        for o in self.mysql_client.select_many(sql):
            item = self.entity_class.from_dict(o)
            if item is not None:
                items.append(item)
        return items


def get_base(province):
    base_dict = {"sh": "上海", "yn": "云南", "nmg": "内蒙古", "bj": "北京", "jl": "吉林", "sc": "四川",
                 "tj": "天津",
                 "nx": "宁夏", "ah": "安徽",
                 "sd": "山东",
                 "sx": "山西", "gd": "广东", "gx": "广西", "xj": "新疆", "js": "江苏", "jx": "江西", "hb": "河北",
                 "hen": "河南", "zj": "浙江",
                 "hain": "海南",
                 "hub": "湖北", "hun": "湖南", "gs": "甘肃", "fj": "福建", "xz": "西藏", "gz": "贵州", "ln": "辽宁",
                 "cq": "重庆", "snx": "陕西",
                 "qh": "青海",
                 "hlj": "黑龙江"}
    for k, v in base_dict.items():
        if v in province:
            return k


def get_gid(company_name):
    company_gid = id_center_query(name=company_name, use_graph_id=True)[1]
    if company_gid:
        return company_gid
    else:
        print(f'---{company_name} 没有gid---')
        return 0


def get_qx_park_data(data):
    import uuid
    dic = {}
    company_name_list = []
    graph_id_list = []
    uuid_str = str(uuid.uuid4())
    uuid = uuid_str.replace("-", "")  # 随机生成 唯一标志
    create_time = datetime.now()  # 创建时间
    update_time = datetime.now()  # 更新时间
    deleted = 0  # 是否删除
    for i in data:
        park_data = dict(i)
        park_name = park_data.get('name')  # 园区名称
        company_name = park_data.get('company_name')  # 园区公司名称
        print(company_name)
        province = park_data.get('province')  # 省
        base = get_base(province)  # 地域简称
        city = park_data.get('city')  # 市
        district = park_data.get('district')  # 区
        district_code = park_data.get('district_code')  # 区代码
        city_code = district_code[:4] + '00'  # 市代码
        province_code = district_code[:2] + '0000'  # 省代码
        park_area = park_data.get('area')  # 占地面积
        coordinate = ';'.join(re.findall('\[(\d+\.\d+, \d+\.\d+)\]', park_data.get('ploy')))  # 园区坐标
        company_name_list.append(company_name)  # 公司列表
        gid = str(get_gid(company_name))  # gid
        graph_id_list.append(gid)  # gid_list
        province_code_new = "{:08d}".format(int(province_code))  # 新版省代码
        city_code_new = "{:08d}".format(int(city_code))  # 新版市代码
        district_code_new = "{:08d}".format(int(district_code))  # 新版区代码
    company_number = len(company_name_list)
    dic['park_name'] = park_name
    dic['province'] = province
    dic['city'] = f"{city}, {district}"
    dic['base'] = base
    dic['province_code'] = province_code
    dic['city_code'] = city_code
    dic['district_code'] = district_code
    dic['park_area'] = park_area
    dic['company_number'] = company_number
    dic['coordinate'] = coordinate
    dic['create_time'] = create_time
    dic['update_time'] = update_time
    dic['deleted'] = deleted
    dic['company_name_list'] = '	;	'.join(company_name_list)
    dic['company_total'] = company_number
    dic['graph_id_list'] = '	;	'.join(graph_id_list)
    dic['uuid'] = uuid
    dic['province_code_new'] = province_code_new
    dic['city_code_new'] = city_code_new
    dic['district_code_new'] = district_code_new
    return dic


# 优化 将qx yid 和 name 先查询出来 再去tyc_park 查询没有再进行join
Qx_sever = QxSeverDao()
# 83412
yid_all = Qx_sever.get_all_yid()
count = 0
fail_count = 0
for yid in yid_all:
    # print(yid)
    yid_str = yid['yid']
    qx_park_name = yid['name']
    # 查询tyc 园区是否有数据
    tyc_park = ty_sever.get_park_name_by_data(qx_park_name)
    # print(tyc_park)
    # if tyc_park:
    #     qx_date = Qx_sever.get_data_join(yid_str)
    #     if qx_date:
    #         # 解析字段对齐
    #         parse_data = get_qx_park_data(qx_date)
    #         # print(parse_data)
    #         if parse_data:
    #             up_state = ty_sever.update_data(parse_data['park_name'], parse_data['company_number'], parse_data['update_time'],
    #                                  parse_data['company_name_list'], parse_data['company_total'], parse_data['graph_id_list'])
    #             # print(parse_data['park_name'], parse_data['company_number'], parse_data['update_time'],
    #             #                      parse_data['company_name_list'], parse_data['company_total'], parse_data['graph_id_list'])
    #             # print(up_state)
    #             if up_state:
    #                 count += 1
    #                 print(f'---{qx_park_name} 更新成功,当前计数: {count}---')
    #             else:
    #                 fail_count += 1
    #                 print(f'---{qx_park_name} 更新失败,当前失败计数: {fail_count}---')
    # ---插入新数据部分---
    if not tyc_park:
        qx_date = Qx_sever.get_data_join(yid_str)
        # 有qx园区企业的数据才考虑插入
        if qx_date:
            # 解析字段对齐
            parse_data = get_qx_park_data(qx_date)
            print(parse_data)
            if parse_data:
                insert_id = ty_sever.insert_data(
                    parse_data['park_name'],parse_data['province'],parse_data['city'],parse_data['base'],
                    parse_data['province_code'],parse_data['city_code'],parse_data['district_code'],parse_data['park_area'],
                    parse_data['company_number'],parse_data['coordinate'],None,parse_data['create_time'],
                    parse_data['update_time'],parse_data['deleted'],parse_data['company_name_list'],
                    parse_data['company_total'],parse_data['graph_id_list'],parse_data['uuid'],
                    parse_data['province_code_new'],parse_data['city_code_new'],parse_data['district_code_new']
                )
                if insert_id:
                    print(f'---{insert_id}插入成功---')
                # print(parse_data['park_name'], parse_data['province'], parse_data['city'], parse_data['base'],
                #       parse_data['province_code'], parse_data['city_code'], parse_data['district_code'],
                #       parse_data['park_area'],
                #       parse_data['company_number'], parse_data['coordinate'], None, parse_data['create_time'],
                #       parse_data['update_time'], parse_data['deleted'], parse_data['company_name_list'],
                #       parse_data['company_total'], parse_data['graph_id_list'], parse_data['uuid'],
                #       parse_data['province_code_new'], parse_data['city_code_new'], parse_data['district_code_new'])
                    count += 1
                    print(f'---{qx_park_name} 插入成功,当前计数: {count}---')

