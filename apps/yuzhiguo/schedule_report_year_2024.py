import time
from resx.log import setup_logger
from resx.config import CFG_MYSQL_ZX_RDS113
from resx import mysql_dao
from resx.hive_client import HiveClient
from biz_utils.schedule import gs_dispatch
logger = setup_logger()
hive_client = HiveClient()
report_cid_dispatch_log = mysql_dao.MySQLDao(db_tb_name='test.report_cid_dispatch_log',
                                                  entity_class=dict,
                                                  primary_index_fields=(['cid'], []),
                                                  **CFG_MYSQL_ZX_RDS113)



while True:
    items = list(hive_client.run(hql=open('schedule_report_year_2024.sql', 'r').read()))
    logger.info(f'total items={len(items)}')
    for iid, item in enumerate(items):
        cid = item['random_sample.id']
        logger.info(f'cid={cid}, iid={iid}')
        # 查询是否已经调度过
        if report_cid_dispatch_log.get(cid=cid):
            logger.info(f'cid={cid} already dispatched')
            continue
        gs_dispatch(reason='new_report_2024_v1', cid=cid, dims=['annualreport_2024', ], immediately=True)
        # 投词后写入数据库
        report_cid_dispatch_log.save({'cid': cid})
        time.sleep(2)
