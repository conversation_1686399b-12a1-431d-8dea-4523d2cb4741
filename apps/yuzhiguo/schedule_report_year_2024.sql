WITH
c AS (
    SELECT
        id,
        approved_time
    FROM
        ods.ods_prism1_company_df
    WHERE
        pt = ${pt}
        AND dw_is_del = 0
        AND reg_status NOT LIKE '%销%'
        AND company_org_type NOT LIKE '%分%'
        AND property1 LIKE '91%'
),

report AS (
    SELECT
        company_id AS cid
    FROM
        ods.ods_prism1_annual_report_df
    WHERE
        pt = ${pt}
        AND dw_is_del = 0
        AND report_year = 2024
),

yc AS (
    SELECT
        company_id
    FROM
        ods.ods_prism1_company_abnormal_info_df
    WHERE
        pt = ${pt}
        AND deleted = 0
        AND dw_is_del = 0
),

zx AS (
    SELECT
        company_id
    FROM
        ods.ods_prism1_company_brief_cancel_announcement_info_df
    WHERE
        pt = ${pt}
        AND deleted = 0
        AND dw_is_del = 0
),

ba AS (
    SELECT
        company_id
    FROM
        ods.ods_prism1_company_cancel_record_and_announcement_info_df
    WHERE
        pt = ${pt}
        AND deleted = 0
        AND dw_is_del = 0
),

qs AS (
    SELECT
        company_id
    FROM
        ods.ods_prism1_company_liquidating_info_df
    WHERE
        pt = ${pt}
        AND deleted = 0
        AND dw_is_del = 0
),

t1 as(SELECT
    c.id,
    c.approved_time as app_time
FROM
    c
    LEFT JOIN report ON c.id = report.cid
    LEFT JOIN yc ON c.id = yc.company_id
    LEFT JOIN zx ON c.id = zx.company_id
    LEFT JOIN ba ON c.id = ba.company_id
    LEFT JOIN qs ON c.id = qs.company_id
WHERE
    report.cid IS NULL
    AND yc.company_id IS NULL
    AND zx.company_id IS NULL
    AND ba.company_id IS NULL
    AND qs.company_id IS NULL
    AND c.approved_time IS NOT NULL
    AND YEAR(c.approved_time) IN (2023, 2024)
),

-- 生成随机排序字段并过滤数据
random_sample AS (
    SELECT
        t1.id,
        t1.app_time,
        RAND() AS rand_order  -- 生成随机排序字段
    FROM
        t1
)

-- 随机抽取40000条数据
SELECT
    random_sample.id,
    random_sample.app_time,
    random_sample.rand_order
FROM
    random_sample
ORDER BY
    random_sample.rand_order
LIMIT 40000