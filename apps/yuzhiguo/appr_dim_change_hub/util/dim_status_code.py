class DimStatusCode:
    NOT_PROCESS = 0
    SUC = 200
    SUC_WITH_DIFF = 201
    FORCE_UPDATE = 202
    COMPANY_NOT_EXIST_ERR = 300
    MSMV_ERR = 301
    BASE_APP_TIME_ERR = 302
    BASE_CREDIT_CODE_ERR = 303
    BASE_REG_NUM_ERR = 304
    BASE_BASE_FIELD_ERR = 305
    FAIL = 500

    # clean error
    CLEAN_ERROR = 5001
    # fusion error
    FUSION_ERROR = 5002

    IndividualIndustrialCommercialHouseholds = "个体工商户"
    FamilyRun = "家庭经营"
    NokeyPersonPartten = "个体|分|全民|联营|股份制|事业单位|合伙|个人独资|非公司|办事处|集团"

    # 主要人员
    orgTypeCodeList = ["2000", "2100", "2110", "2120", "2121", "2122", "2123",
                       "2130", "2140", "2150", "2151", "2152", "2153", "2190", "2200", "2210", "2211", "2212", "2213",
                       "2219",
                       "2220", "2221", "2222", "2223", "2229", "3000", "3100", "3200", "3300", "3400", "3500", "4000",
                       "4100",
                       "4110", "4120", "4200", "4210", "4220", "4300", "4310", "4320", "4330", "4340", "4341", "4400",
                       "4410",
                       "4420", "4500", "4530", "4531", "4532", "4533", "4540", "4550", "4551", "4552", "4553", "4560",
                       "4600",
                       "4700", "5300", "5310", "5333", "5390", "5400", "5410", "5420", "5430", "5490", "5800", "5810",
                       "5820",
                       "5830", "5840", "5890", "6300", "6310", "6320", "6333", "6390", "6400", "6410", "6420", "6430",
                       "6490",
                       "6800", "6810", "6820", "6830", "6840", "6890", "7000", "7100", "7110", "7120", "7130", "7190",
                       "7200",
                       "7300", "7310", "7390", "8000", "9000", "9200", "9500", "9900", "2124", "2224", "4350", "5320",
                       "4342"]

    ImmunizationInforListIm = []

    VALID = "有效"
    REPEAT = "重复"
    INVALID = "无效"
    WY = "万元"
    WG = "万股"
    W = "万"
    WRMB = "万人民币"
    _101 = 101
    # 标记抓取被去重后的数据。
    _3 = 3
    _0 = 0
    _1 = 1
    DIFF = "diff"
    SAME = "same"
