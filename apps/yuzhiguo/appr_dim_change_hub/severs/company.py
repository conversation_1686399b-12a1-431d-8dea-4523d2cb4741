# encoding=utf8
from dao.company import Company, CompanyDao, CompanyGraph, CompanyGraphDao
from libs.env import ConstantProps


class CompanySever(CompanyDao):
    def get_source_flag(self, name, source_flag):
        """通过公司名称查询标记值"""
        sql = "select source_flag from prism.company where name = %s and source_flag like %s order by length(source_flag) desc, source_flag desc limit 1"
        res = self.mysql_client.select(sql, args=(name, f"{source_flag}%"))
        return res


if __name__ == '__main__':
    sever = CompanySever()
    print(sever.get_source_flag('深圳市盛桥创鑫投资合伙企业', 'http://qyxy.baic.gov.cn/'))
