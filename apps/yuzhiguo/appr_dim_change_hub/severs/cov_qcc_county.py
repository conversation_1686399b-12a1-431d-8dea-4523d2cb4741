from typing import List
from datetime import datetime, date
from pydantic import Field, conint
from typing import Optional
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps, get_props_mysql
from dao.deps.mysql_dao import MySQLDao
from gslib.gs_enum import EntityType


class CovQccCounty(BaseEntity):
    id: int = Field(default=None)
    province_code: str = Field(default=None)
    province_name: str = Field(default=None)
    city_code: str = Field(default=None)
    city_name: str = Field(default=None)
    county_code: str = Field(default=None)
    county_name: str = Field(default=None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

class CovQccCountyDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in get_props_mysql('mysql.tyc.gs.hw_gsxt_inner_rw').items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'internal.cov_qcc_county')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CovQccCounty)
        super().__init__(**kwargs)

    def get_prov(self):
        """获取省份映射数据"""
        new_map = {}
        sql = "select province_code, province_name from internal.cov_qcc_county group by province_name limit 100"
        res = self.mysql_client.select_many(sql,)
        if res:
            for i in res:
                new_map[i['province_name']] = i['province_code']
        return new_map

    def get_city(self):
        """获取城市映射数据"""
        new_map = {}
        sql = "select province_code, city_name from internal.cov_qcc_county group by city_name limit 1000"
        res = self.mysql_client.select_many(sql, )
        if res:
            for i in res:
                new_map[i['city_name']] = i['province_code']
        return new_map

    def get_county(self):
        """获取区映射数据"""
        new_map = {}
        sql = "select province_code, county_name from internal.cov_qcc_county limit 4000"
        res = self.mysql_client.select_many(sql, )
        if res:
            for i in res:
                new_map[i['county_name']] = i['province_code']
        return new_map


if __name__ == '__main__':
    sever = CovQccCountyDao()
    print(sever.get_prov())