import requests
import json

data_url = "http://msv-gsdata.jindidata.com/read_list_version"


def send_request_with_retry(data_url, data, retry_times=3):
    """搜索重试"""
    for i in range(retry_times):
        try:
            r = requests.post(data_url, data=data, timeout=60)
            if r.status_code == 200:
                return r
            else:
                print('多源多版本接口异常:', r)
                continue
        except Exception as e:
            print('多源多版本接口异常:', e)
            continue
    return {}


def get_msv_data(table_name, cid: int, ts=None, source=None, human=False):
    body = {
        "table_name": table_name,
        "cid": cid,
    }
    if ts:
        body['ts'] = ts
    if source:
        body['source'] = source
    if human:
        body['human'] = human
    try:
        # msv_data = requests.post(data_url, data=json.dumps(body))
        msv_data = send_request_with_retry(data_url, json.dumps(body))
        msv_data = json.loads(msv_data.content)
    except:
        msv_data = {}
    return msv_data.get('data', {})
