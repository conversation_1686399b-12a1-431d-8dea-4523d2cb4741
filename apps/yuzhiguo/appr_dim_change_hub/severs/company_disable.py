from typing import List
from datetime import datetime, date
from pydantic import Field, conint
from typing import Optional
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps, get_props_mysql
from dao.deps.mysql_dao import MySQLDao
from gslib.gs_enum import EntityType


class CompanyDisable(BaseEntity):
    id: int = Field(default=None)
    company_id: int = Field(default=None)
    reason: str = Field(default=None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class CompanyDisableDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in get_props_mysql('mysql.tyc.gs.hw_gsxt_inner_rw').items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'dispatch.company_disable')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanyDisable)
        super().__init__(**kwargs)

    def get_cid_by_date(self, cid):
        sql = """select * from dispatch.company_disable  where company_id =%s"""
        res = self.mysql_client.select(sql,cid)
        if res:
            return True
        else:
            return False


if __name__ == '__main__':
    sever = CompanyDisableDao()
    print(sever.get_cid_by_date(*********))