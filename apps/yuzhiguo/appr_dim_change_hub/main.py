# encoding=utf8

import argparse
import time, json
from libs.log2 import setup_logger
from concurrent.futures import ThreadPoolExecutor
from libs.concurrent import BoundedExecutor
from clients.kafka_client import KafkaConsumerClient
from entity.eventlog import Eventlog, SpiderCode
from libs.env import get_stack_info, ConstantProps
from apps.yuzhiguo.appr_dim_change_hub import basic_information,company_staff,shareholder_info

logger = setup_logger(debug=False)


msv_base_date = basic_information.MsvBaseData()

def dim_change_mark(msv_res):
    """判断维度数据是否有变化，有则返回维度名称"""
    changed_dimensions = []
    if not msv_res:
        return changed_dimensions
    for key, dimension in msv_res.items():
        change_sum = dimension['insert_data'] + dimension['delete_data'] + dimension['update_data']
        if change_sum != 0:
            changed_dimensions.append(key)
    return changed_dimensions


def process_func(eventlog: Eventlog):
    if eventlog.spider_code != SpiderCode.SUCCESS:
        return
    # item_name 字段为空返回
    if not eventlog.selector.item_name or eventlog.selector.item_name != "company":
        return
    cid = int(eventlog.selector.word)
    # 通过cid获取company表数据，验证是否符合规则，没有公司数据返回不进行处理，
    # sourceFlag 属性是否为 null，或者它是否不是以 "http://qyxy.baic.gov.cn/" 开头的
    msv_res = eventlog.parser.get('msv_res', {})
    changed_dimensions = dim_change_mark(msv_res)
    if not changed_dimensions:
        return
    for dim in changed_dimensions:
        if dim == "company_base_info":
            dim_state = msv_base_date.base_info(cid)
            # print(dim_state)
        if dim == "company_staff":
            pass
        if dim == "company_investor":
            pass


def dump_func(process_executor: BoundedExecutor):
    # event log consumer
    count = 0
    consumer = KafkaConsumerClient(kafka_topic='gsxt.data_fusion', group_id='appr_dim_change_hub_demo',
                                   **ConstantProps.PROPS_GS_KAFKA_ONLINE)
    for sid, s in enumerate(consumer.read()):
        try:
            if sid % 1000 == 0:
                logger.info('sid=%s', sid)
            if 'octopus' not in s:
                continue
            d = json.loads(s)
            eventlog = Eventlog.from_dict(d)
            if not eventlog:
                logger.warning('error from dict s=%s', s)
                continue
            if not eventlog.event_id.startswith('octopus'):
                continue
            process_executor.submit(process_func, eventlog)
            count += 1
            if count >= 100000:
                logger.info('Reached the maximum number of processed entries. Shutting down...')
                # 停止所有处理线程
                process_executor.shutdown()
                # 退出dump_func函数
                return
        except Exception as e:
            logger.warning('e=%s s=%s trace=%s', e, s, get_stack_info())
            continue
    consumer.close()


def main():
    # logger.info('args=%s', ap_args)
    dump_executor = ThreadPoolExecutor(max_workers=ap_args.dump_worker_num, thread_name_prefix='dump')
    process_executor = BoundedExecutor(max_workers=ap_args.process_worker_num, thread_name_prefix='process')

    for _ in range(ap_args.dump_worker_num):
        dump_executor.submit(dump_func, process_executor)

    while True:
        logger.info('main thread do nothing.')
        time.sleep(5)


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='appr_dim_change_hub.py')
    ap.add_argument('--dump-worker-num', type=int, default=1, choices=[1, 2, 4, 8, 16], help='当前kafka分区是16')
    ap.add_argument('--process-worker-num', type=int, default=1, help='处理线程数 可以设置大些')
    ap_args = ap.parse_args()
    # app_name = 'appr_dim_change_hub'
    # logger = setup_logger(path='./log.solver.yml', app_name=app_name, info_backup_count=24*4)
    main()
