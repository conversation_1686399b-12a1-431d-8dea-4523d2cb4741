# encoding=utf8
# 工商照面信息逻辑
import csv
import re
import time

from libs.log2 import setup_logger
from apps.yuzhiguo.appr_dim_change_hub.severs.company_disable import CompanyDisableDao
from apps.yuzhiguo.appr_dim_change_hub.util.dim_status_code import DimStatusCode
from apps.yuzhiguo.appr_dim_change_hub.severs.msv import get_msv_data
from apps.yuzhiguo.appr_dim_change_hub.severs.company import CompanySever
from libs.dt import to_date, to_datetime
from dao.company import Company, CompanyDao, CompanyGraph, CompanyGraphDao
from apps.yuzhiguo.appr_dim_change_hub.severs.cov_qcc_county import CovQccCountyDao
import datetime
from decimal import Decimal


logger = setup_logger(debug=False)


class MsvBaseData:
    def __init__(self):
        # 照面停止更新表
        self.company_disable_sever = CompanyDisableDao()
        self.company_dao = CompanyDao()
        self.company_sever = CompanySever()
        self.msv_first = None
        self.PROVINCE_MAP = {
            "上海": "sh",
            "云南": "yn",
            "北京": "bj",
            "吉林": "jl",
            "四川": "sc",
            "天津": "tj",
            "宁夏": "nx",
            "安徽": "ah",
            "山东": "sd",
            "山西": "sx",
            "广东": "gd",
            "广西": "gx",
            "新疆": "xj",
            "江苏": "js",
            "江西": "jx",
            "河北": "heb",
            "河南": "hen",
            "浙江": "zj",
            "海南": "han",
            "湖北": "hub",
            "湖南": "hun",
            "甘肃": "gs",
            "福建": "fj",
            "西藏": "xz",
            "贵州": "gz",
            "辽宁": "ln",
            "重庆": "cq",
            "陕西": "snx",
            "青海": "qh",
            "内蒙": "nmg",
            "黑龙": "hlj"
        }
        self.cov_qcc_county_sever = CovQccCountyDao()
        self.freeTradeZone_ = "中国（上海）自由贸易试验区,中国（广东）自由贸易试验区,中国（天津）自由贸易试验区,中国（福建）自由贸易试验区,中国（辽宁）自由贸易试验区,中国（浙江）自由贸易试验区,中国（河南）自由贸易试验区,中国（湖北）自由贸易试验区,中国（重庆）自由贸易试验区,中国（四川）自由贸易试验区,中国（陕西）自由贸易试验区,中国（海南）自由贸易试验区,中国（山东）自由贸易试验区,中国（江苏）自由贸易试验区,中国（广西）自由贸易试验区,中国（河北）自由贸易试验区,中国（云南）自由贸易试验区,中国（黑龙江）自由贸易试验区,中国（北京）自由贸易试验区,中国（湖南）自由贸易试验区,中国（安徽）自由贸易试验区"
        self.key_mapping = {
            'id': 'cid',
            'property1': 'credit_code',
            'approved_time': 'approved_date',
            'legal_person_id': 'legal_id',
            'legal_person_name': 'legal_name',
            'legal_person_type': 'legal_type',      # 统一为int类型
            'company_org_type': 'company_org_type',
            'name': 'name',
            'reg_number': 'reg_number',
            'reg_status': 'reg_status',
            'reg_location': 'reg_location',
            'reg_institute': 'reg_institute',
            'reg_capital': 'reg_capital',
            'business_scope': 'business_scope',
            'estiblish_time': 'establish_date',     # 修改为datetime.date对象
            'from_date': 'from_time',
            'to_time': 'to_date',
            # 'org_number': 'org_number',               # 组织机构代码是从统一信用代码提取的不需要对比
            # 'crawledtime': 'last_crawled_time',      # 解析时间不需要diff
            'base': 'base',     # 解决大小写问题
            'source_flag': 'source_flag'

        }

    def miss_data(self, last_company) -> bool:
        try:
            if not last_company['legal_person_name'] or last_company['legal_person_id'] == 0:
                return True
            return (last_company['business_scope'] is None or last_company['business_scope'].strip() == '') or \
            (last_company['company_org_type'] is None or last_company['company_org_type'].strip() == '') or \
            (last_company['property1'] is None or last_company['property1'].strip() == '') or \
            last_company['estiblish_time'] is None or to_date(last_company['estiblish_time']) > to_date(time.time()) or \
            last_company['from_time'] is None or to_date(last_company['from_time']) > to_date(time.time()) or \
            (last_company['approved_time'] is not None and to_date(last_company['approved_time']) > to_date(time.time())) or \
            (last_company['name'] is None or last_company['name'].strip() == '') or \
            (last_company['reg_institute'] is None or last_company['reg_institute'].strip() == '') or \
            (last_company['reg_location'] is None or last_company['reg_location'].strip() == '') or \
            (last_company['reg_number'] is None or last_company['reg_number'].strip() == '') or \
            (last_company['reg_status'] is None or last_company['reg_status'].strip() == '')
        except:
            return True

    def is_del_disable(self, cid):
        """检测是否在停止更新表"""
        res = self.company_disable_sever.get_cid_by_date(cid)
        if res:
            return True
        else:
            return False



    def is_messy_data(self, name):
        """检查公司名称是否乱码"""
        if name and re.search(r'[^\x00-\x7F]', name):
            return True
        else:
            return False

    def delete_empty_data(self, origin_data):
        """过滤无效的照面信息"""
        result = {}
        for source, data in origin_data.items():
            new_data = [item for item in data if item]
            result[source] = new_data
        return result

    def set_msv_first(self, msv_first):
        """校验msv_first时间"""
        self.msv_first = to_date(msv_first)
        if len(str(msv_first)) < 11 and msv_first < 1686567433:
            self.msv_first = to_date(msv_first * 1000)
        return self.msv_first

    def set_msv_last_time(self, msv_data):
        """遍历数据，使每个数据都有有效的msv_last_time值，没有则用msv_first的值赋值给msv_last_time"""
        new_msv_date = {}
        for source, source_date in msv_data.items():
            # 检验是否有效的msv_last_time
            try:
                new_msv_date[source] = source_date[0][0]
            except Exception as e:
                print(f"{source},无有效值，报错信息:{e}")
                continue
        if not new_msv_date:
            print('无有效照面信息')
            return None
        for source, source_date in new_msv_date.items():
            # 过滤公司名称异常数据
            if not source_date['name'] or not self.is_messy_data(source_date['name']):
                print(f'来源：{source},{source_date["name"]},公司名称异常')
                try:
                    if not source_date.get('msv_last_time', '') or source_date.get('msv_last_time', '') == 0:
                        source_date['msv_last_time'] = self.set_msv_first(source_date['msv_first'])
                except Exception as e:
                    print(f'来源：{source},{source_date}无msv_last_time，使用msv_first更新msv_last_time,报错信息{e}')
                    source_date['msv_last_time'] = self.set_msv_first(source_date['msv_first'])
                continue
            try:
                if not source_date.get('msv_last_time', '') or source_date.get('msv_last_time', '') == 0:
                    source_date['msv_last_time'] = self.set_msv_first(source_date['msv_first'])
            except Exception as e:
                print(f'来源：{source},{source_date}无msv_last_time，使用msv_first更新msv_last_time,报错信息{e}')
                source_date['msv_last_time'] = self.set_msv_first(source_date['msv_first'])
        return new_msv_date

    def get_msv_company_date(self, cid):
        """获取多源多版本照面信息"""
        # 获取所有来源最新的版本数据
        msv_company_data = get_msv_data(table_name='company_base_info', cid=cid, ts=-1)
        if not msv_company_data:
            logger.warning(f"No data in MSMV, company id: {cid}")
            return
        # 过滤无效数据
        msv_company_data = self.delete_empty_data(msv_company_data)
        # 循环遍历是否有msv_last_time值，没有则使用msv_first的值赋值给msv_last_time
        msv_company_data = self.set_msv_last_time(msv_company_data)
        return msv_company_data

    def max_gsxt_page(self, msv_company_data):
        """合并xa和gsxt"""
        XA_company = msv_company_data.get('XA', {})
        gsxt_company = msv_company_data.get('gsxt', {})
        if XA_company and gsxt_company:
            # 以核准日期比较谁大选谁，核准日期相同使用msv_last_time最大的
            if to_date(XA_company['approved_time']) > to_date(gsxt_company['approved_time']):
                return XA_company
            elif to_date(XA_company['approved_time']) < to_date(gsxt_company['approved_time']):
                return gsxt_company
            elif to_date(XA_company['approved_time']) == to_date(gsxt_company['approved_time']):
                if to_date(XA_company['msv_last_time']) > to_date(gsxt_company['msv_last_time']):
                    return XA_company
                elif to_date(XA_company['msv_last_time']) < to_date(gsxt_company['msv_last_time']):
                    return gsxt_company
        if XA_company:
            return XA_company
        if gsxt_company:
            return gsxt_company

    def max_company_date(self, msv_company_data, mode=False):
        """依据核准日期和msv_last_time获取最大数据"""
        max_approved_time = None
        max_item = None
        for item_key, item_value in msv_company_data.items():
            # 根据mode过滤国家来源数据
            if mode:
                if item_key in ['XA', 'gsxt']:
                    continue
            if item_value['approved_time'] is None:
                continue
            approved_time = to_date(item_value['approved_time'])
            msv_last_time = to_date(item_value['msv_last_time'])

            if max_approved_time is None or to_date(approved_time) > to_date(max_approved_time):
                max_approved_time = approved_time
                max_item = item_value
            elif approved_time == max_approved_time:
                if to_date(msv_last_time) > to_date(max_item['msv_last_time']):
                    max_item = item_value
        return max_item

    def get_optimal_company(self, msv_company_data, max_gs_page_company):
        """
        获取最优照面信息
        """
        if not max_gs_page_company:
            # 直接对比其他来源核准日期最大数据，核准日期相同时使用msv_last_time最大日期版本数据
            max_company = self.max_company_date(msv_company_data)
            return max_company
        # 当有国家工商数据时，先获取非国家工商的数据在比较国家工商和其他来源最大核准日期的数据
        non_gj_company = self.max_company_date(msv_company_data, mode=True)
        # 没有国家工商以外的数据源直接使用国家工商最大的数据返回
        if not non_gj_company:
            max_company = self.max_company_date(msv_company_data)
            return max_company
        # 然后依据核准日期和msv_last_time选取最大的来源数据返回
        non_gj_approved_time = to_date(non_gj_company['approved_time'])
        max_gs_approved_time = to_date(max_gs_page_company['approved_time'])
        if not max_gs_approved_time:
            max_gs_approved_time = to_date('0001-01-01')
        if non_gj_approved_time > max_gs_approved_time:
            return non_gj_company
        elif non_gj_approved_time < max_gs_approved_time:
            return max_gs_page_company
        else:
            # non_gj_msv_last_time = to_date(non_gj_company['msv_last_time'])
            # max_gs_msv_last_time = to_date(max_gs_page_company['msv_last_time'])
            # if non_gj_msv_last_time > max_gs_msv_last_time:
            #     return non_gj_company
            # else:
            return max_gs_page_company

    def get_old_company(self, cid):
        """获取原始表中的公司数据"""
        company: Company = self.company_dao.get(cid)
        assert company
        return company

    def remove_nbsp_space(self, value):
        """过滤空格"""
        return re.sub('&nbsp;', '', value)

    def to_upper_case(self, value):
        """转大写"""
        return value.upper()

    def is_normal_reg_number(self, reg_number):
        """检查注册号是否符合规范"""
        if not reg_number or not isinstance(reg_number, str):
            return False
        pattern = re.compile("^[A-Z0-9a-z]{13,15}$")
        return bool(pattern.match(reg_number))

    def check_reg_numbers(self, old_reg_number, last_reg_number):
        """检查注册号是否相等"""
        if last_reg_number and last_reg_number != old_reg_number:
            return True
        else:
            return False

    def is_valid_string(self, s):
        """
        检查字符串是否不为 None 且长度大于 0，并且不包含仅由空格组成的字符串。
        :param s: 要检查的字符串
        :return: 如果字符串有效，返回 True；否则返回 False
        """
        if s is None:
            return False
        if len(s.strip()) == 0:
            return False
        return True

    def remove_nbsp_space(self, value):
        """处理非空字符并转大写"""
        if value is not None and '\u00A0' in value:  # replace &nbsp;
            value = value.replace('\u00A0', '')
        return value.upper()

    def is_normal_credit_code(self, credit_code):
        """检验统一信用代码是否是有效值"""
        if credit_code is None or credit_code.strip() == "":
            return False
        pattern = re.compile(r"^[A-Z0-9a-z]{18}$")
        return bool(pattern.match(credit_code))

    def is_conflict_credit_code(self, credit_code_a, credit_code_b):
        """检查两个统代是否冲突"""
        return (
                self.is_normal_credit_code(credit_code_a)
                and self.is_normal_credit_code(credit_code_b)
                and credit_code_a.lower() != credit_code_b.lower()
                and (
                        (self.is_macredit_code(credit_code_a) and self.is_macredit_code(credit_code_b))
                        or (not self.is_macredit_code(credit_code_a) and not self.is_macredit_code(credit_code_b))
                        or (self.is_macredit_code(credit_code_a) and not self.is_macredit_code(credit_code_b))
                )
        )

    def is_macredit_code(self, credit_code):
        if not self.is_normal_credit_code(credit_code):
            return False
        return credit_code[8:10].lower() == "ma"

    def uniform_name(self, name):
        """公司名称清洗"""
        if name is not None and name != "":
            name = name.strip()
            name = re.sub(r'\*', '', name)
            name = re.sub(r'\(', '（', name)
            name = re.sub(r'\)', '）', name)
            name = re.sub(r'&nbsp;', '', name)
            name = re.sub(r' ', '', name)
            name = re.sub(r'　', '', name)
            name = re.sub(r' \|', '|', name)
            name = self.filter_id_card2(name)
        return name

    def filter_id_card2(self, str):
        """公司名称清洗"""
        if str is None:
            return None
        else:
            pattern = re.compile(
                "([1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx])|([1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2})")
            matcher = pattern.match(str)

            while matcher:
                ori_url = matcher.group(1)
                if ori_url is not None:
                    re_ori_url = "********"
                    if len(ori_url) > 16:
                        re_ori_url = ori_url.replace(ori_url[6:14], "********")

                    str = str.replace(ori_url, re_ori_url)

                matcher = pattern.match(str)

            return str

    def is_error_name(self, name):
        """公司名称检查"""
        if not name:  # 空字符算作错误
            return True
        reg_ex = "^[^a-zA-Z\u4e00-\u9fa5]+$"  # 匹配纯字符和数字
        pattern = re.compile(reg_ex)
        matcher = pattern.match(name)
        return matcher is not None


    def is_normal_credit_code(self, credit_code):
        """检查统一信用代码"""
        if not credit_code:
            return False
        pattern = re.compile("^[A-Z0-9a-z]{18}$")
        matcher = pattern.match(credit_code)
        return matcher is not None

    def set_company_name(self, old_company, optimal_company, new_company):
        """公司名称"""
        c_name = self.uniform_name(optimal_company['name'])
        if c_name and c_name != "无" and "无字号" not in c_name:
            new_company['name'] = c_name
        elif optimal_company['company_org_type'] and "个体|个人" in optimal_company['company_org_type']:
            if not self.is_error_name(old_company['name']) and old_company['name'] != old_company['legal_person_name']:
                new_company['name'] = old_company['name']
            elif not self.is_error_name(optimal_company['legal_person_name']):
                new_company['name'] = optimal_company['legal_person_name']
            elif self.is_normal_credit_code(new_company['property1']):
                new_company['name'] = new_company['property1']
            else:
                new_company['name'] = ""

    def update_reg_status(self, optimal_company, old_company, new_company):
        """注册状态"""
        if optimal_company['reg_status'] and old_company['reg_status']:
            if old_company['reg_status'].startswith(optimal_company['reg_status'].strip()[0]):
                new_company['reg_status'] = optimal_company['reg_status']
            else:
                logger.warning("Unknown reg status: {}, cid: {}".format(optimal_company['reg_status'], new_company['id']))

    def get_normalized_reg_capital_amount(self, original_reg_capital, type):
        """金额规范化，type 1注册资本 2实缴资本"""
        if not original_reg_capital or not isinstance(original_reg_capital, str):
            return None
        # 空格处理、金额占位符处理
        temp = re.sub(r'( |，|,|\t)', '', original_reg_capital)
        if '.' not in temp:
            return None
        # 小数点除零处理
        temp = re.sub(r'0*$', '', temp)
        # 数值
        digital = re.search(r'(\d+)', temp).group(1)
        # 获取数字
        d = Decimal(digital)
        # 单位：没有单位时。转换成万
        unit_matcher = re.search(r'(\w+)', original_reg_capital)
        has_unit = bool(unit_matcher)
        if not has_unit and d >= Decimal("1000000") and type == 1 and '元' not in original_reg_capital:
            d /= Decimal("10000")
        # 处理社会组织的注册资金
        unit_npo_matcher = re.search(r'(\w+)', original_reg_capital)
        has_npo_unit = bool(unit_npo_matcher)
        if has_npo_unit:
            # 位数大于4位情况，除以10000
            if d >= Decimal("10000"):
                d /= Decimal("10000")
        if not has_unit and not has_npo_unit and '元' in original_reg_capital and type == 1:
            d /= Decimal("10000")
        # 扩充100W
        d *= Decimal("1000000")
        amount = int(d)
        return amount

    def extract_chinese_str(self, value):
        """获取中文字符"""
        if not value:
            return ""
        reg = "[\\u4e00-\\u9fa5]"
        matcher = re.compile(reg).findall(value)
        return "".join(matcher)

    def contain_more_chinese_char(self, str_a, str_b):
        """a字符串长度是否大于b字符串长度"""
        return len(self.extract_chinese_str(str_a)) > len(self.extract_chinese_str(str_b))


    def rm_enter(self, str):
        """处理经营范围"""
        if not str or str.isspace():
            return str
        return str.replace('\r', '').replace('\n', '')

    def same_credit_code(self, credit_code_a, credit_code_b):
        """统一信用码统一处理比较"""
        return credit_code_a is not None and credit_code_b is not None and credit_code_a.upper() == credit_code_b.upper()

    def update_establishment_time(self, optimal_company, old_company_date, new_company):
        """成立日期"""
        min_date = to_date(datetime.datetime(1901, 12, 13))
        if optimal_company['estiblish_time'] is not None and to_date(optimal_company['estiblish_time']) < to_date(time.time()) and to_date(optimal_company['estiblish_time']) > min_date:
            if old_company_date['establish_date'] is not None and old_company_date['establish_date'] != optimal_company['estiblish_time'] \
                    and not self.same_credit_code(optimal_company['credit_code'], old_company_date['credit_code']):
                try:
                    raise ValueError("成立时间禁止变更")
                except ValueError as e:
                    logger.warning(e)
            new_company['estiblish_time'] = optimal_company['estiblish_time']
        else:
            new_company['estiblish_time'] = None

    def equals_str_ignore_blank(self, str1, str2):
        """用于检验两个公司名称是否一致"""
        if str1 is None or str2 is None:
            return str1 == str2
        str1 = str1.strip()
        str2 = str2.strip()
        return str1 == str2

    def get_new_source_flag(self, new_cname, source_flag):
        """返回source_flag"""
        company = self.company_sever.get_source_flag(new_cname, source_flag)
        if company is None or not company['source_flag']:
            return source_flag
        str_arr = company['source_flag'].split("_")
        index = 1
        if len(str_arr) == 2:
            index = int(str_arr[1]) + 1
        return source_flag + "_" + str(index)

    def get_base_by_area_code(self, code):
        """枚举base"""
        if not code or (len(code) != 6 and len(code) != 2):
            return None
        if code.startswith("10"):
            return "gj"
        elif code.startswith("11"):
            return "bj"
        elif code.startswith("12"):
            return "tj"
        elif code.startswith("13"):
            return "heb"
        elif code.startswith("14"):
            return "sx"
        elif code.startswith("15"):
            return "nmg"
        elif code.startswith("21"):
            return "ln"
        elif code.startswith("22"):
            return "jl"
        elif code.startswith("23"):
            return "hlj"
        elif code.startswith("31"):
            return "sh"
        elif code.startswith("32"):
            return "js"
        elif code.startswith("33"):
            return "zj"
        elif code.startswith("34"):
            return "ah"
        elif code.startswith("35"):
            return "fj"
        elif code.startswith("36"):
            return "jx"
        elif code.startswith("37"):
            return "sd"
        elif code.startswith("41"):
            return "hen"
        elif code.startswith("42"):
            return "hub"
        elif code.startswith("43"):
            return "hun"
        elif code.startswith("44"):
            return "gd"
        elif code.startswith("45"):
            return "gx"
        elif code.startswith("46"):
            return "han"
        elif code.startswith("50"):
            return "cq"
        elif code.startswith("51"):
            return "sc"
        elif code.startswith("52"):
            return "gz"
        elif code.startswith("53"):
            return "yn"
        elif code.startswith("54"):
            return "xz"
        elif code.startswith("61"):
            return "snx"
        elif code.startswith("62"):
            return "gs"
        elif code.startswith("63"):
            return "qh"
        elif code.startswith("64"):
            return "nx"
        elif code.startswith("65"):
            return "xj"
        elif code.startswith("71"):
            return "tw"
        elif code.startswith("81"):
            return "hk"
        elif code.startswith("82"):
            return "mo"
        else:
            return None

    def get_base_by_credit_code_or_reg_num(self, credit_code, reg_number):
        """根据地区代码获取base"""
        area_code = self.get_area_code(credit_code, reg_number)
        return self.get_base_by_area_code(area_code)

    def get_area_code(self, credit_code, reg_number):
        """检验统一信用代码和注册号，返回地区代码"""
        area_code = ""
        if credit_code and len(credit_code) == 18:
            area_code = credit_code[2:4]
        elif reg_number and self.is_normal_reg_number(reg_number):
            area_code = reg_number[:2]
        return area_code

    def find_index(self, string, s, default_value=-1):
        """处理异常"""
        try:
            index = string.index(s)
        except ValueError:
            index = default_value
        return index

    def get_location(self, string):
        """根据登记机关"""
        if "省" in string or "自治区" in string or "行政区" in string:
            return self.address_resolution_for_pro(string)
        if "市场监督管理局" in string:
            string = string[:string.index("市场监督管理局")]
        if "市场监管局" in string:
            string = string[:string.index("市场监管局")]
        if not string.startswith("市") and ("市" in string or "自治州" in string or "地区" in string or "行政单位" in string or "盟" in string or "市辖区" in string):
            substrings = ["区", "县", "旗", "镇", "岛"]
            conditions = []
            for substr in substrings:
                if substr in string:
                    conditions.append(self.find_index(string, "市") > self.find_index(string, "区"))
                else:
                    conditions.append(False)  # 如果子字符串不存在，则条件为False

            # 添加额外的条件
            conditions.append("市市" in string)

            conditions.append((self.find_index(string, "市") > 0 and "县" not in string))

            if any(conditions):
                return self.address_resolution_for_city(string)
        if "县" in string or "区" in string or "旗" in string or "海域" in string or "岛" in string:
            return self.address_resolution_for_country(string)
        if "区" in string:
            return self.address_resolution_for_town(string)
        return ""

    def address_resolution_for_pro(self, string):
        # 解析省份信息的逻辑
        regex = "(?P<province>[^省]+自治区|.*?省|.*?行政区|.*?市)"
        pattern = re.compile(regex)
        matcher = pattern.search(string)
        if matcher:
            province = matcher.group("province")
            province = province.strip()
            if province:
                return province.replace("省", "").replace("自治区", "").replace("行政区", "")
        return None

    def address_resolution_for_city(self, string):
        # 解析城市信息的逻辑
        regex = "(?P<city>[^市]+自治州|.*?地区|.*?行政单位|.+盟|市辖区|.*?市|.*?县)"
        pattern = re.compile(regex)
        matcher = pattern.search(string)
        if matcher:
            city = matcher.group("city")
            city = city.strip()
            if city:
                return city
        return None

    def address_resolution_for_country(self, string):
        # 解析县或区信息的逻辑
        regex = "(?P<county>[^县]+县|.+区|.+市|.+旗|.+海域|.+岛)"
        pattern = re.compile(regex)
        matcher = pattern.search(string)
        if matcher:
            county = matcher.group("county")
            county = county.strip()
            if county:
                return county
        return None

    def address_resolution_for_town(self, string):
        # 解析镇或区信息的逻辑
        regex = "(?P<town>[^区]+区)"
        pattern = re.compile(regex)
        matcher = pattern.search(string)
        if matcher:
            town = matcher.group("town")
            town = town.strip()
            if town:
                return town.replace("区", "")
        return None

    def get_province_base(self, province_name):
        """根据省份获取base"""
        return self.PROVINCE_MAP.get(province_name)

    def get_base_by_like_right_province_name(self, province_str):
        """获取表中数据，寻找可能的base"""
        prov_map = self.cov_qcc_county_sever.get_prov()
        if not prov_map:
            return ""
        base_set = set()
        for province_name, province_code in prov_map.items():
            if province_name.startswith(province_str):
                base_set.add(province_code)
        if len(base_set) == 1:
            return base_set.pop()
        else:
            return ""

    def get_base_by_city_or_contry(self, location):
        """从表映射的市区获取base的可能"""
        # city
        city_map = self.cov_qcc_county_sever.get_city()
        base_set = set()
        for city_name, city_code in city_map.items():
            if city_name.startswith(location):
                base_set.add(city_code)
        if len(base_set) == 1:
            return base_set.pop()

        # county
        county_map = self.cov_qcc_county_sever.get_county()
        base_set = set()
        for county_name, county_code in county_map.items():
            if county_name.startswith(location):
                base_set.add(county_code)
        if len(base_set) == 1:
            return base_set.pop()

        return ""

    def get_base_by_location(self, location):
        """从表中查询是否有对应base"""
        if not location:
            return None
        # province equity
        base = ""
        if "县" not in location:
            base = self.get_province_base(location[:2] if len(location) >= 2 else location)

        if base:
            return base
        # province likely
        base = self.get_base_by_like_right_province_name(location)

        if base:
            return base
        # county or city likely
        base = self.get_base_by_city_or_contry(location)

        return base

    def extract_base_from_reg_location(self, reg_location):
        """特殊处理"""
        # 高新区处理
        for reg_lo in self.freeTradeZone_.split(","):
            if reg_lo in reg_location:
                return reg_lo[reg_lo.rindex("（") + 1:reg_lo.rindex("）")]
        # 提取地址省市
        return self.get_location(reg_location)

    def get_base_by_reg_location(self, reg_location):
        """注册地址提取base"""
        if not reg_location or reg_location.isspace():
            return reg_location
        return self.extract_base_from_reg_location(reg_location)

    def get_company_base(self, latest_company):
        """获取公司base"""
        base = None
        # 1. get base by reg institute
        if latest_company['reg_institute'] and latest_company['reg_institute'].strip():
            # 如果是国家工商总局 --> gj
            if latest_company['reg_institute'] == "国家工商行政管理总局" or self.get_area_code(latest_company.get('property1', ''),latest_company.get('reg_number', '')) == "10":
                return "gj"
            location = self.get_location(latest_company['reg_institute'])
            if location and location.strip():
                base = self.get_base_by_location(location)
                if base and base.strip():
                    return base
        # 2. get base by reg location
        base_by_reg_location = self.get_base_by_reg_location(latest_company['reg_location'])
        if base_by_reg_location and base_by_reg_location.strip():
            base = self.get_base_by_location(base_by_reg_location)
            if base and base.strip():
                return base
        # 3. get base by credit code and regNumber
        base = self.get_base_by_credit_code_or_reg_num(latest_company['property1'], latest_company.get('reg_number', ''))
        if base and base.strip():
            return base
        if not latest_company['base'] or not latest_company['base'].strip():
            logger.error("Failed to get company base, please check, cid: {}", latest_company['id'])
        return latest_company['base']

    def get_base_by_location_by_institute(self, location):
        """处理base子逻辑"""
        if not location or location.isspace():
            return None
        # province equity
        base = ""
        if "县" not in location:
            base = self.get_province_base(location[:2] if len(location) >= 2 else location)
        if base:
            return base
        # province likely
        base = self.get_base_by_like_right_province_name(location)
        if base:
            return base
        # county or city likely
        base = self.get_base_by_city_or_country_by_institute(location)
        return base

    def get_base_by_city_or_country_by_institute(self, city_or_county_str):
        """根据城市和地区获取base"""
        # city
        city_or_county_str_new = city_or_county_str[:2] if len(
            city_or_county_str) > 2 and "县" not in city_or_county_str and "市" not in city_or_county_str else city_or_county_str
        base_set = set()
        city_map = self.cov_qcc_county_sever.get_city()
        for city_name, city_code in city_map.items():
            if city_name.startswith(city_or_county_str_new):
                base_set.add(city_code)
        if len(base_set) == 1:
            return base_set.pop()

        # county
        base_set = set()
        county_map = self.cov_qcc_county_sever.get_county()
        for county_name, county_code in county_map.items():
            if county_name.startswith(city_or_county_str):
                base_set.add(county_code)
        if len(base_set) == 1:
            return base_set.pop()

        return ""

    def get_company_base_new(self, latest_company):
        """处理base的新逻辑"""
        base = None
        # 1. get base by reg institute
        if latest_company['reg_institute'] and latest_company['reg_institute'].strip():
            # 如果是国家工商总局 --> gj
            if latest_company['reg_institute'] == "国家工商行政管理总局" or self.get_area_code(
                    latest_company.get('property1', ''), latest_company.get('reg_number', '')) == "10":
                return "gj"
            location = self.get_location(latest_company['reg_institute'])
            if location and location.strip():
                base = self.get_base_by_location_by_institute(location)
                if base and base.strip():
                    return base
        # 2. get base by reg location
        base_by_reg_location = self.get_base_by_reg_location(latest_company['reg_location'])
        if base_by_reg_location and base_by_reg_location.strip():
            base = self.get_base_by_location(base_by_reg_location)
            if base and base.strip():
                return base
        # 3. get base by credit code and regNumber
        base = self.get_base_by_credit_code_or_reg_num(latest_company['property1'], latest_company.get('reg_number', ''))
        if base and base.strip():
            return base
        if not latest_company['base'] or not latest_company['base'].strip():
            logger.error("Failed to get company base, please check, cid: {}", latest_company['id'])
        return latest_company['base']


    def field_checks(self, optimal_company, old_company_date):
        """最优数据与原始数据字段检验"""
        old_company_date = dict(old_company_date)
        new_company = {}
        # 公司id
        new_company['id'] = old_company_date['cid']
        try:
            # 注册号
            old_reg_number = old_company_date['reg_number']
            last_reg_number = optimal_company['reg_number']
            if last_reg_number:
                # 去除空格，小写转大写
                last_reg_number = self.to_upper_case(self.remove_nbsp_space(last_reg_number))
                # 检查是否符合注册号规则并且是否等于原始注册号
                if self.is_normal_reg_number(last_reg_number) and self.check_reg_numbers(old_reg_number, last_reg_number):
                    raise ValueError("注册号，禁止更新")
                # 修复缺少注册号问题
                new_company['reg_number'] = last_reg_number
        except Exception as e:
            logger.warning(f'{optimal_company},注册号处理异常,报错信息：{e}')

        try:
            # 统一信用代码
            old_credit_code = old_company_date['credit_code']
            last_credit_code = optimal_company['credit_code']
            if self.is_valid_string(last_credit_code):
                # 如果统一信用代码是有效的并且与原始统代不同并且与原始统代有冲突
                if self.is_normal_credit_code(self.remove_nbsp_space(last_credit_code)) and last_credit_code != old_credit_code and self.is_conflict_credit_code(last_credit_code,old_credit_code):
                    raise ValueError("统一信用代码，禁止更新")
                else:
                    new_company['property1'] = last_credit_code
        except Exception as e:
            logger.warning(f'{optimal_company},统一信用代码处理异常,报错信息：{e}')


        try:
            # 核准日期
            old_approved_time = old_company_date['approved_date']
            last_approved_time = to_date(optimal_company['approved_time'])
            if old_approved_time is not None and old_approved_time > last_approved_time if last_approved_time is not None else datetime.datetime(1970, 1, 1):
                logger.error(f'核准日期回退')
            # 检查最后版本核准日期不为空并且小于当前时间并且大于1901-01-01
            if last_approved_time is not None and last_approved_time < to_date(time.time()) and last_approved_time > to_date(datetime.datetime(1901, 1, 1)):
                new_company['approved_time'] = last_approved_time
            else:
                new_company['approved_time'] = None
        except Exception as e:
            logger.warning(f'{optimal_company},核准日期处理异常,报错信息：{e}')

        # 法人id
        new_company['legal_person_id'] = optimal_company['legal_person_id']
        # 法人
        new_company['legal_person_name'] = optimal_company['legal_person_name'].strip()
        # 法人类型
        new_company['legal_person_type'] = optimal_company['legal_person_type']
        # 公司类型
        new_company['company_org_type'] = optimal_company['company_org_type'].strip()
        # 公司名称
        self.set_company_name(old_company_date, optimal_company, new_company)
        # 注册状态
        self.update_reg_status(optimal_company, old_company_date, new_company)
        # 注册地址
        new_company['reg_location'] = optimal_company['reg_location'].strip()
        # 登记机关
        reg_institute = optimal_company.get('reg_institute', '')
        if reg_institute is None:
            reg_institute = ''
        new_company['reg_institute'] = reg_institute.strip()
        # 注册资本
        original_reg_capital = self.get_normalized_reg_capital_amount(old_company_date['reg_capital'], 1)
        msv_reg_capital = self.get_normalized_reg_capital_amount(optimal_company['reg_capital'], 1)

        if msv_reg_capital is not None and (msv_reg_capital != original_reg_capital or self.contain_more_chinese_char(optimal_company['reg_capital'], old_company_date['reg_capital'])):
            new_company['reg_capital'] = optimal_company['reg_capital']

            if new_company['company_org_type'] and "个体" in new_company['company_org_type'] and msv_reg_capital is not None and (msv_reg_capital / 1000000) > 5000:
                new_company['reg_capital'] = ""
        else:
            new_company['reg_capital'] = old_company_date['reg_capital']

        # 分公司、分支机构等无注册资本
        parent_id_str = str(old_company_date.get('parent_id', ''))
        pattern = re.compile(r'^[0-9\-_.]+$')
        if parent_id_str != '0' and parent_id_str and pattern.match(parent_id_str):
            new_company['reg_capital'] = ""
        # 经营范围
        new_company['business_scope'] = self.rm_enter(optimal_company['business_scope'])
        # 成立日期
        self.update_establishment_time(optimal_company, old_company_date, new_company)
        # 经营期限 开始时间
        if optimal_company['from_time'] is not None and to_date(optimal_company['from_time']) < to_date(time.time()) and to_date(optimal_company['from_time']) > to_date('1901-12-13'):
            new_company['from_time'] = to_date(optimal_company['from_time'])
        elif 'from_time' in new_company and new_company['from_time']:
            new_company['from_time'] = new_company['from_time']
        else:
            new_company['from_time'] = old_company_date['from_date']

        # 经营期限 结束时间
        if optimal_company['to_time'] is not None and to_date(optimal_company['to_time']) > to_date('1901-12-13'):
            if (optimal_company['to_time'] is not None and to_date(optimal_company['to_time']) < to_date(optimal_company['from_time'])) or (to_date(optimal_company['estiblish_time']) is not None and to_date(optimal_company['to_time']) < to_date(optimal_company['estiblish_time'])):
                print("Company base info - invalid to_time value:", optimal_company['to_time'])
            else:
                new_company['to_time'] = to_date(optimal_company['to_time'])
        elif optimal_company['from_time'] is not None:
            # If from time is empty, it should be erroneous data, and at this time, do not update to time as empty
            new_company['to_time'] = None

        # 组织机构代码
        if new_company.get('property1', '') and len(new_company.get('property1', '')) == 18:
            org_number = new_company['property1'].upper()[8:16] + "-" + new_company['property1'].upper()[16:17]
            new_company['org_number'] = org_number

        # 解析时间 通过event_log获取 待实现
        new_company['crawledtime'] = datetime.datetime.now()

        # 公司类型代码
        if old_company_date.get('company_type','') is None:
            new_company['company_type'] = 0

        # source_flag
        if new_company.get('name', None) is not None and not self.equals_str_ignore_blank(old_company_date['name'],optimal_company['name']):
            new_source_flag = self.get_new_source_flag(optimal_company['name'], "http://qyxy.baic.gov.cn/")
            new_company['source_flag'] = new_source_flag
        else:
            new_company['source_flag'] = old_company_date['source_flag']


        # base
        company_base = self.get_company_base(new_company)
        company_base_new = self.get_company_base_new(new_company)
        if company_base != company_base_new:
            logger.info("通过登记机关解析出的结果不一致cid:{},location:{},old:{},new:{}", new_company['id'],new_company['reg_institute'], company_base, company_base_new)
        new_company['base'] = old_company_date['base'].lower()
        return new_company


    def supplement_date(self, last_company, max_gs_page_company):
        """补充字段"""
        if not max_gs_page_company:
            return last_company
        for key in last_company:
            if last_company[key] is None or last_company[key] == '' or last_company[key] == 0:
                if key == 'property1':
                    key = 'credit_code'
                if key == 'id':
                    key = 'cid'
                if key == 'from_date':
                    key = 'from_time'
                last_company[key] = max_gs_page_company.get(key)
                print(f'补充了{key}字段的值{max_gs_page_company.get(key)}')
        return last_company



    def supplemental_data(self, last_company, max_gs_page_company):
        """是否补充字段"""
        if not self.miss_data(last_company):
            return last_company
        logger.info('字段缺失,使用国家工商数据补充')
        # 获取所有国家工商来源数据
        last_company = self.supplement_date(last_company, max_gs_page_company)
        return last_company



    def unify_keys(self, dictionary, mapping):
        """统一键"""
        unified_dict = {}
        for key, value in dictionary.items():
            if key in ["org_number", "crawledtime"]:
                continue
            if key in mapping:
                new_key = mapping[key]
                unified_dict[new_key] = value
            else:
                unified_dict[key] = value
        return unified_dict

    def str_none_unification(self, date_str):
        """用于diff时的空值统一"""
        if date_str is None or date_str == '':
            return ""

    # 使用统一键名称后的字典进行对比
    def compare_dicts(self,cid, dict_a, dict_b):
        """对比"""
        diff_mark = False
        # 对比前处理成立日期修改为date格式
        try:
            estiblish_time = to_date(dict_a['estiblish_time'])
        except:
            estiblish_time = to_date('0000-00-00')
        dict_a['estiblish_time'] = estiblish_time
        unified_dict_a = self.unify_keys(dict_a, self.key_mapping)
        unified_dict_b = self.unify_keys(dict_b, self.key_mapping)

        # 比较两个统一键名称后的字典
        for key in unified_dict_a:
            if self.str_none_unification(unified_dict_a[key]) != self.str_none_unification(unified_dict_b[key]):
                diff_mark = True
                print(f"Key '{key}' has different values: {unified_dict_a[key]} vs {unified_dict_b[key]}")
                with open('20241205_company_diff.csv', 'a', newline='', encoding='utf-8') as f:
                    witer = csv.writer(f)
                    witer.writerow([cid, key, unified_dict_a[key], unified_dict_b[key], dict_a, dict_b])
        if not diff_mark:
            with open('20241205_company_unanimous.csv', 'a', newline='', encoding='utf-8') as f:
                witer = csv.writer(f)
                witer.writerow([cid, dict_a, dict_b])

    def mergeMsvGs(self, msmvSourceAndCompanyMap):
        """合并国家工商数据"""
        msvNew = {}
        try:
            sourceNeedDel = None
            if "gsxt" in msmvSourceAndCompanyMap and "XA" in msmvSourceAndCompanyMap:
                msvGs = msmvSourceAndCompanyMap["gsxt"]
                msvXa = msmvSourceAndCompanyMap["XA"]
                # 都会有 msvLastTime
                sourceNeedDel = "XA" if to_date(msvGs['msv_last']) > to_date(msvXa['msv_last']) else "gsxt"
            for key in msmvSourceAndCompanyMap:
                if key == sourceNeedDel:
                    continue
                msvNew[key] = msmvSourceAndCompanyMap[key]
            return msvNew
        except Exception as e:
            print(f"合并 gs 与 xa 异常: {e}")
            return msmvSourceAndCompanyMap

    def contains_cancel(self, msmvSourceAndCompanyMap):
        """检验企业状态是否包含销"""
        for key, msmvCompany in msmvSourceAndCompanyMap.items():
            if msmvCompany['reg_status'] and "销" in msmvCompany['reg_status']:
                return True
        return False

    def contains_move_out(self, msmvSourceAndCompanyMap):
        """检验企业状态是否包含迁出"""
        for key, msmvCompany in msmvSourceAndCompanyMap.items():
            if msmvCompany['reg_status'] and "迁出" in msmvCompany['reg_status']:
                return True
        return False

    def isSupportedSource(self, source):
        # 在这里实现判断数据源是否受支持的逻辑
        SUPPORT_SOURCE_SET = {
            "gsxt",
            "XA",
            "jsold",
            "gdold",
            "gzold",
            "hanold",
            "hljold",
            "xm",
            "app2",
            "bjold",
            "fjold",
            "jxold",
            "tjold",
            "gdsz",
            "CHANNEL_GS_JS_NEW",
            "CHANNEL_GS_JS_CHANGE",
            "CHANNEL_GS_ZS_NEW",
            "CHANNEL_GS_ZS_CHANGE",
            "CHANNEL_GS_SN_NEW",
            "CHANNEL_GS_SN_CHANGE",
            "CHANNEL_GS_ZS_API",
            "CHANNEL_GS_JX_DATA",
            "CHANNEL_GS_QX_DATA",
            "CHANNEL_GS_JX_CHANGE",
            "CHANNEL_GS_KK_DATA",
            "shold",
            "gdzh",
            "CHANNEL_GS_DF1_DATA",
            "CHANNEL_GS_DF2_DATA",
            "CHANNEL_GS_ZS_DATA",
            "CHANNEL_GS_YH_DATA"
        }
        return source in SUPPORT_SOURCE_SET or "CHANNEL" in source

    def isMessyData(self, name):
        # return "??" in name
        if name and re.search(r'[^\x00-\x7F]', name):
            return False
        else:
            return True



    def str_to_timestamp(self, s):
        try:
            # 尝试将字符串解析为日期格式
            time_struct = time.strptime(s, "%Y-%m-%d")
            timestamp = time.mktime(time_struct)
        except ValueError:
            # 如果解析为日期格式失败，尝试将其解析为时间戳（假设为整数）
            try:
                timestamp = int(s)
            except ValueError:
                # 如果都解析失败，返回 None 表示无法转换
                timestamp = None
        return timestamp

    def compareSource(self, fusedMsmvCompany, newMsv, containsCancel):
        # 按照存在国家工商对比
        if (fusedMsmvCompany['source'] == "XA" or fusedMsmvCompany['source'] == "gsxt"):
            # 地方没有核准日期,直接不使用
            if newMsv.get('approved_time', None) is None:
                return fusedMsmvCompany
            time = self.str_to_timestamp(newMsv['approved_time'])
            timeLong = time + (30 * 24 * 60 * 60 * 1000 if containsCancel else 14 * 24 * 60 * 60 * 1000)
            if fusedMsmvCompany['msv_last'] > timeLong:
                return fusedMsmvCompany
            else:
                # 国家工商没有核准日期，不选用国家工商
                if fusedMsmvCompany.get('approved_time', None) is None:
                    return newMsv
                if self.str_to_timestamp(fusedMsmvCompany['approved_time']) >= self.str_to_timestamp(newMsv['approved_time']):
                    return fusedMsmvCompany
                else:
                    return newMsv
        else:
            # 不是国家工商之间的对比
            if newMsv.get('approved_time', None) is None:
                return fusedMsmvCompany
            if fusedMsmvCompany.get('approved_time', None) is None:
                return newMsv
            if self.str_to_timestamp(fusedMsmvCompany['approved_time']) > self.str_to_timestamp(newMsv['approved_time']):
                return fusedMsmvCompany
            elif self.str_to_timestamp(fusedMsmvCompany['approved_time']) == self.str_to_timestamp(newMsv['approved_time']):
                # 比较优先级
                # 这里需要根据实际的 sourcePriorityProp 来实现比较逻辑
                priority = 0  # 示例，实际根据具体情况修改
                if priority >= 0:
                    return fusedMsmvCompany
                else:
                    return newMsv
            else:
                return newMsv

    def chooseBestOneNewNew(self, msmvSourceAndCompanyMap, containsCancel=False):
        # 判断是否全部核准日期为 null
        isAllApprovedTimeNull = True
        maxApp = None
        for key, msmv in msmvSourceAndCompanyMap.items():
            if not self.isSupportedSource(key) or self.isMessyData(msmv['name']):
                continue
            if msmv['approved_time'] is not None:
                isAllApprovedTimeNull = False
                break
            if maxApp is None or to_date(msmv['msv_first']) > to_date(maxApp['msv_first']):
                maxApp = msmv

        if isAllApprovedTimeNull:
            return maxApp

        gsxtMsv = None
        if "XA" in msmvSourceAndCompanyMap and not self.isMessyData(msmvSourceAndCompanyMap["XA"]['name']):
            gsxtMsv = msmvSourceAndCompanyMap["XA"]

        if "gsxt" in msmvSourceAndCompanyMap and not self.isMessyData(msmvSourceAndCompanyMap["gsxt"]['name']):
            msmvCompany = msmvSourceAndCompanyMap["gsxt"]
            if gsxtMsv is None:
                gsxtMsv = msmvCompany
            else:
                approvedTime = msmvCompany['approved_time']
                if approvedTime and (
                        gsxtMsv['approved_time'] is None or to_date(approvedTime) > to_date(gsxtMsv['approved_time'])):
                    gsxtMsv = msmvCompany
                elif approvedTime and gsxtMsv['approved_time'] and to_date(approvedTime) == to_date(
                        gsxtMsv['approved_time']) and to_date(msmvCompany['msv_last']) > to_date(gsxtMsv['msv_last']):
                    gsxtMsv = msmvCompany

        fusedMsmvCompany = gsxtMsv if gsxtMsv else None
        for key, msmv in msmvSourceAndCompanyMap.items():
            if key not in ["gsxt", "XA"] and self.isSupportedSource(key) and not self.isMessyData(msmv['name']):
                if fusedMsmvCompany is None:
                    fusedMsmvCompany = msmv
                else:
                    fusedMsmvCompany = self.compareSource(fusedMsmvCompany, msmv, containsCancel)
        return fusedMsmvCompany

    def getGjMsvCompany(self, msmvSourceAndCompanyMap):
        gsxtMsv = None
        if "gs" in msmvSourceAndCompanyMap and not self.isMessyData(msmvSourceAndCompanyMap["gs"]['name']):
            gsxtMsv = msmvSourceAndCompanyMap["gsxt"]
        if "XA" in msmvSourceAndCompanyMap and not self.isMessyData(msmvSourceAndCompanyMap["XA"]['name']):
            msmvCompany = msmvSourceAndCompanyMap["XA"]
            if gsxtMsv is None:
                gsxtMsv = msmvCompany
            else:
                approvedTime = msmvCompany['approved_time']
                if approvedTime and (
                        gsxtMsv['approved_time'] is None or to_date(approvedTime) > to_date(gsxtMsv['approved_time'])):
                    gsxtMsv = msmvCompany
        return gsxtMsv

    def mergeMsvGs(self, msmvSourceAndCompanyMap):
        msvNew = {}
        try:
            sourceNeedDel = None
            if "gsxt" in msmvSourceAndCompanyMap and "XA" in msmvSourceAndCompanyMap:
                msvGs = msmvSourceAndCompanyMap["gsxt"]
                msvXa = msmvSourceAndCompanyMap["XA"]
                # 都会有 msvLastTime
                sourceNeedDel = "XA" if to_date(msvGs['msv_last']) > to_date(msvXa['msv_last']) else "gsxt"
            for key in msmvSourceAndCompanyMap:
                if key == sourceNeedDel:
                    continue
                msvNew[key] = msmvSourceAndCompanyMap[key]
            return msvNew
        except Exception as e:
            print(f"合并 gs 与 xa 异常: {e}")
            return msmvSourceAndCompanyMap

    def setEventLogFusionSource(self, source):
        # 在这里实现设置事件日志融合源的逻辑
        print(f"设置事件日志融合源为: {source}")

    def fuse_msv_data(self, msv_data, cid):
        """最优版本选取逻辑"""
        fusedMsmvCompany = None
        # 设置 msv_last_time 的默认值为 msv_first
        for msv in msv_data:
            if msv.get('msv_last_time', None) is None or msv.get('msv_last_time', None) == 0:
                msv['msv_last_time'] = msv['msv_first']

        # 按 msv_first 降序排序
        msmvSourceAndCompanyMap = {msv['source']: msv for msv in sorted(msv_data, key=lambda x: x['msv_first'], reverse=True)}

        # 合并 gs 与 xa 数据源的公司数据
        msmvSourceAndCompanyMap = self.mergeMsvGs(msmvSourceAndCompanyMap)

        containsCancel = self.contains_cancel(msmvSourceAndCompanyMap)
        containsMoveOut = self.contains_move_out(msmvSourceAndCompanyMap)

        if containsMoveOut:
            try:
                print(f"包含迁出 cid:{cid}")
                msmvCompany = self.chooseBestOneNewNew(msmvSourceAndCompanyMap)
                try:
                    if "迁出" in msmvCompany.reg_status:
                        gjMsvCompany = self.getGjMsvCompany(msmvSourceAndCompanyMap)
                        msmvCompany['reg_status'] = gjMsvCompany['reg_status']
                except Exception as e:
                    print(f"迁入迁出企业处理异常: {e}")
                source = msmvCompany['source']
                sourceNew = msmvCompany['source']
                if source == sourceNew:
                    print(f"预处理迁出选出数据源一致 cid:{cid}")
                else:
                    print(f"预处理迁出选出数据不一致 cid:{cid}, oldName:{msmvCompany['name']}, newName:{msmvCompany['name']}, oldSource:{source}, newSource:{sourceNew}, oldState:{msmvCompany['reg_status']}, newState:{msmvCompany['reg_status']}, oldAppTime:{msmvCompany['approved_time']}, newAppTime:{msmvCompany['approved_time']}")
                fusedMsmvCompany = msmvCompany
                print(f"预迁出: {fusedMsmvCompany}")
            except Exception as e:
                print(f"预处理迁出异常, cid:{cid}, {e}")
        elif not containsCancel:
            try:
                msmvSourceAndCompanyMap = self.mergeMsvGs(msmvSourceAndCompanyMap)
                fusedMsmvCompany = self.chooseBestOneNewNew(msmvSourceAndCompanyMap)
                print(
                    f"正常选取多源多版本: {cid}, source:{fusedMsmvCompany['source']}, app:{fusedMsmvCompany['approved_time']}")
            except Exception as e:
                print(f"扩大时效性选取多源多版本, {e}")
        else:
            try:
                fusedMsmvCompany = self.chooseBestOneNewNew(msmvSourceAndCompanyMap, containsCancel)
            except Exception as e:
                print(f"注吊销打点异常, {e}")

        if fusedMsmvCompany is None:
            try:
                raise Exception(f"多源多版本中无符合条件数据，company id: {cid}")
            except Exception as e:
                print(e)
        if self.isMessyData(fusedMsmvCompany['name']):
            try:
                raise Exception("Msmv messy code")
            except Exception as e:
                print(e)

        self.setEventLogFusionSource(fusedMsmvCompany['source'])
        return fusedMsmvCompany

    def base_info(self, cid):
        """照面信息主要逻辑"""
        try:
            # 首先检查是否停止更新
            if self.is_del_disable(cid):
                logger.info(f"Company is in company_disable, will not process base info, cId: {cid}")
                return DimStatusCode.NOT_PROCESS
            # 获取多源多版本数据
            msv_company_data = self.get_msv_company_date(cid)
            if not msv_company_data:
                return DimStatusCode.FAIL
            new_msv_company_data = list(msv_company_data.values())
            # 选取最优版本
            optimal_company = self.fuse_msv_data(new_msv_company_data, cid)
            # max_gs_page_company = self.max_gsxt_page(msv_company_data)
            # optimal_company = self.get_optimal_company(msv_company_data, max_gs_page_company)
            # 通过cid获取原始公司数据
            old_company_date = self.get_old_company(cid)
            # 检查原始数据与最终版数据的具体字段
            last_company = self.field_checks(optimal_company, old_company_date)
            # 缺失数据使用国家工商来源补充
            # last_company = self.supplemental_data(last_company, old_company_date)
            # 对比公司数据，原始表数据与产出数据对比
            self.compare_dicts(cid, last_company, dict(old_company_date))

            # 更新公司数据
            print(last_company)
            # 处理下游事件和数据
            logger.info(f"Complete processing dim - base info, cId: {cid}")
            return DimStatusCode.SUC
        except Exception as e:
            logger.error(f"Failed to process base info: {e}")
            logger.warning(f"Failed to process base info, company: {cid}, error message: {e}")
            with open('20241205_company_err.csv', 'a', newline='', encoding='utf-8') as f:
                witer = csv.writer(f)
                witer.writerow([cid, e])


if __name__ == '__main__':
    msv_base_date = MsvBaseData()
    # with open('20241202_company_diff.csv','r',encoding='utf-8') as f:
    #     date = csv.DictReader(f)
    #     for i in date:
    #         cid = dict(i)['cid']
    #         print(f"当前cid:{cid}")
    #         # cid = 2622193135
    #         msv_base_date.base_info(int(cid))

    cid = 2335021036
    msv_base_date.base_info(cid)
