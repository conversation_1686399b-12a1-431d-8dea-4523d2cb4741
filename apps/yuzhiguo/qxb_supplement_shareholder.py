# encoding=utf8

import argparse
import re
import requests
from libs.log2 import setup_logger
from typing import List
from datetime import datetime, timedelta
from libs.env import ConstantProps
from clients.redis.redis_hash import RedisHash
from apps.yuzhiguo.my_sever.t_partners import PartnersDao, Partners
from dao.qxb.enterprise import EnterpriseDao, Enterprise
from gslib.id_center import id_center_query, EntityType
from dao.investors.equity_ratio import EquityRatioDao
from dao.company import CompanyDao, CompanyGraphDao
from dao.human import HumanGraphDao
import json, csv, time
from decimal import Decimal
import random

from apps.octopus.utils.conf_manager import OctopusConfManager
from apps.octopus.utils.entry_manager import EntryManager

logger = setup_logger()

partners_dao = PartnersDao()
qx_company_dao = EnterpriseDao()
tyc_sharding_dao = EquityRatioDao()
company_dao = CompanyDao()
company_graph_dao = CompanyGraphDao()
human_graph_dao = HumanGraphDao()

amount_re = re.compile("^[\-0-9]*(?:\.[0-9]+)?")


def get_amount_from_capital(capital):
    if not capital:
        return 0
    amount = 0
    if isinstance(capital, list):
        capital_json = capital
    else:
        try:
            capital_json = json.loads(capital)
        except:
            result = amount_re.findall(str(capital))
            if len(result) == 1:
                try:
                    amount = Decimal(str(result[0]))
                except Exception:
                    pass
            return amount

    if isinstance(capital_json, (int, float)):
        return capital_json

    for cap in capital_json:
        amomon = cap.get("amomon", 0)
        result = amount_re.findall(str(amomon))
        if len(result) == 1:
            try:
                amount += Decimal(str(result[0]))
            except Exception:
                pass
    return float(amount)


def is_dictionary_complete(d, required_keys):
    """字典键检查"""
    return all(key in d for key in required_keys)


def process_one(e: Partners):
    """格式化数据结构"""
    # required_keys = ['shareholder_graph_id', 'shareholder_name', 'shareholder_type', 'amount', 'capital', 'capitalActl', 'percent', 'source']
    dic = {}
    # print(e.stock_name, e.stock_type, e.stock_percent, e.should_capi_items, e.real_capi_items)
    shareholder_name = e.stock_name
    shareholder_eid = e.stock_id
    if shareholder_eid:
        qx_shareholder_company_data = qx_company_dao.get(shareholder_eid)
        credit_code = qx_shareholder_company_data.credit_code
        if credit_code and len(credit_code) == 18:
            interval = random.uniform(0.1, 0.2)
            entity_type, entity_id = id_center_query(name=shareholder_name, credit_code=credit_code)
            time.sleep(interval)
        else:
            interval = random.uniform(0.1, 0.2)
            entity_type, entity_id = id_center_query(name=shareholder_name)
            time.sleep(interval)
    else:
        interval = random.uniform(0.1, 0.2)
        entity_type, entity_id = id_center_query(name=shareholder_name)
        time.sleep(interval)
    if entity_type == EntityType.HUMAN:
        investor_hg = human_graph_dao.get_by_hid(entity_id)
        if not investor_hg:
            return {}
        investor_type, investor_gid = 1, investor_hg.hgid
    elif entity_type == EntityType.ORG:
        investor_cg = company_graph_dao.get_by_cid(entity_id)
        if not investor_cg:
            return {}
        investor_type, investor_gid = 2, investor_cg.cgid
    elif entity_type == EntityType.TYPE3:
        investor_type, investor_gid = 3, 0
    else:
        investor_type, investor_gid = 3, 0
    try:
        capital_str = e.should_capi_items  # 替换字段名称
        capital_str_rep = capital_str.replace('shoud_capi', 'amomon').replace('invest_type', 'paymet').replace(
            'should_capi_date', 'time')
        amount = get_amount_from_capital(capital_str_rep)
        capital = json.loads(capital_str_rep)
    except:
        amount = 0
        capital = []

    try:
        capitalActl_str = e.real_capi_items  # 替换字段名称
        capitalActl_str_rep = capitalActl_str.replace('shoud_capi', 'amomon').replace('invest_type', 'paymet').replace(
            'should_capi_date', 'time')
        capitalActl = json.loads(capitalActl_str_rep)
    except:
        capitalActl = []

    dic['shareholder_graph_id'] = investor_gid
    dic['shareholder_name'] = shareholder_name
    dic['shareholder_type'] = investor_type
    dic['amount'] = amount
    dic['capital'] = capital
    dic['capitalActl'] = capitalActl
    if not e.stock_percent:
        dic['percent'] = '0'
    else:
        dic['percent'] = e.stock_percent
    dic['source'] = 114  # CHANNEL_GS_QX_DATA
    return dic


def process_list(items: List[Partners]):
    """返回结构化后的股东列表"""
    shareholder_list = []
    for item in items:
        if item.is_history != 0:
            continue
        shareholder = process_one(item)
        if not shareholder:
            return []
        shareholder_list.append(shareholder)
    if not shareholder_list:
        return []
    return shareholder_list


def process(data):
    if data:
        for qx_data in data:
            qx_data: Enterprise
            if not qx_data.credit_code:  # 没有统一信用代码数据排除
                continue
            if not qx_data.credit_code.startswith('91'):  # 排除统一信用代码非91开头数据
                continue
            if '个体' in qx_data.org_type:  # 排除个体户
                continue
            if not qx_data.reg_number or len(qx_data.reg_number) != 15:
                continue
            eid = qx_data.eid
            reg_number = qx_data.reg_number
            qx_company_data = qx_company_dao.get(eid)
            credit_code = qx_company_data.credit_code
            interval = random.uniform(0.1, 0.2)
            entity_type, cid = id_center_query(credit_no=credit_code)
            time.sleep(interval)
            if entity_type != EntityType.ORG:
                logger.warning(f'get no cid credit_no={credit_code}')
                interval = random.uniform(0.1, 0.2)
                entity_type, cid = id_center_query(reg_number=reg_number)
                time.sleep(interval)
                if entity_type != EntityType.ORG:
                    logger.warning(f'get no cid reg_number={reg_number}')
                    continue
            company_graph_item = company_graph_dao.get_by_cid(cid)
            company_graph_id = company_graph_item.cgid
            # company_graph_id 获取 公司名称
            tyc_company_data = company_dao.get(field='id', value=cid)
            tyc_company_name = tyc_company_data.name
            # 判断tyc股东信息是否存在，存在则不补充数据
            tyc_share_ratio_data = tyc_sharding_dao.get_many(field='company_graph_id', value=company_graph_id)
            if not list(tyc_share_ratio_data):
                # 解析qx数据
                e = partners_dao.get_many(value=eid)
                qx_shareholder_information = process_list(list(e))
                if not qx_shareholder_information:
                    continue
                logger.info('tyc没有股东信息，需要从qx补充')
                logger.info(qx_shareholder_information)
                result_list_ = ['_'.join(
                    [i.get('shareholder_name'), str(i.get('shareholder_type')), str(i.get('shareholder_graph_id')),
                     str(i.get('amount'))]) for i in qx_shareholder_information]
                result_sum = sum([i.get('amount') for i in qx_shareholder_information])
                logger.info(f'股东名称列表：{result_list_}')
                logger.info(f'认缴和：{result_sum}')
                # 插入数据到tyc股东信息表
                with open('20240820_qx_tyc股东.csv', 'a', encoding='utf-8', newline='') as f:
                    witer = csv.writer(f)
                    witer.writerow([company_graph_id, tyc_company_name, result_list_, result_sum])
            else:
                continue


def main(args):
    logger.info(f'{args}')
    redis_offset = RedisHash(db=2, name='qxb_supplement_shareholder_yzg', **ConstantProps.PROPS_GS_REDIS_ONLINE)
    row_update_time_default = datetime.now() - timedelta(days=args.start_days)

    qx_company_dao.sharding_scan(
        process_fn=process,
        process_workers=args.process_num,
        scan_workers=args.dump_num,
        part_num=args.part_num,
        redis_offset=redis_offset,
        init_offset=row_update_time_default,
        infinite_wait_secs=30,
        scan_key='row_update_time'
    )

    # eid = '0016d650-d702-443f-a4a7-8dd9c1a8b910'
    # # 查询公司主体名称
    #
    # e = partners_dao.get_many(value=eid)
    # process(list(e))


if __name__ == '__main__':
    from libs.log2 import setup_logger

    ap = argparse.ArgumentParser(description='qxb_supplement_shareholder.py')
    ap.add_argument('--dump-num', type=int, default=4, help='dump线程数')
    ap.add_argument('--process-num', type=int, default=8, help='处理线程数')
    ap.add_argument('--part-num', type=int, default=256, help='part num')
    ap.add_argument('--start-days', type=int, default=500, help='首次运行 row-update-time开始时间')
    logger = setup_logger()
    main(ap.parse_args())

    # 枚举出人员还是企业，是否港澳台
    # 根据人员或企业配置不同参数
    # 人员配置名称+人员类型 type = EntityType.HUMAN
    # 企业配置名称+统一信用代码&注册号 type=EntityType.ORG credit_no='code'  reg_number='number'

    # eid = '67444ff5-a9cd-41fa-b47d-444a3753c02e'
    # credit_code = '91320583MAC63RXA6B'
    # reg_number = '320583400075897'
    # entity_type, cid = id_center_query(credit_no=credit_code)
    # if entity_type != EntityType.ORG:
    #     logger.warning(f'get no cid credit_no={credit_code}')
    #     entity_type, cid = id_center_query(reg_number=reg_number)
    #     if entity_type != EntityType.ORG:
    #         logger.warning(f'get no cid reg_number={reg_number}')
    #         pass
    # company_graph_item = company_graph_dao.get_by_cid(cid)
    # company_graph_id = company_graph_item.cgid
    # # company_graph_id 获取 公司名称
    # tyc_company_data = company_dao.get(field='id', value=cid)
    # tyc_company_name = tyc_company_data.name
    # e = partners_dao.get_many(value=eid)
    # qx_shareholder_information = process_list(list(e))
    # print(tyc_company_name, company_graph_id)
    # print(qx_shareholder_information)

    # shareholder_name = '贺野系统工程有限公司'
    # entity_type, entity_id = id_center_query(name=shareholder_name)
    # print(entity_type,entity_id)

    # reg_number = '***************'
    # entity_type, cid = id_center_query(reg_number=reg_number)
    # print(entity_type, cid)
    # company_graph_id = 2517967295
    # tyc_share_ratio_data = tyc_sharding_dao.get_many(field='company_graph_id', value=company_graph_id)
    # if list(tyc_share_ratio_data):
    #     print('有')
    # else:
    #     print('无')
    # def get_id(name: str = None,
    #            credit_no: str = None,
    #            reg_number: str = None,
    #            type_: int = 0,
    #            region: str = None,
    #            **kwargs):
    #     params = {
    #         'name': name if isinstance(name, str) else None,
    #         'unique_code': credit_no if isinstance(credit_no, str) and len(credit_no) == 18 else None,
    #         'reg_num': reg_number if isinstance(reg_number, str) and len(reg_number) == 15 else None,
    #         'type': type_ if isinstance(type_, int) else None,
    #         'region': region if isinstance(region, str) else None,
    #     }
    #     url = "http://idcenter-gsdata.jindidata.com/api/id-center/entity/v1"  # 确保URL是正确的
    #     headers = {
    #         'Authentication': '8ONunBC5pE6UpRSKBaMaUDCJOtP9VHLb',
    #         'Content-Type': 'application/json',
    #     }
    #
    #     try:
    #         response = requests.post(url, headers=headers, json=params, timeout=3)
    #     except (requests.exceptions.RequestException, requests.exceptions.Timeout) as e:
    #         logger.warning(f'get_id request failed: {e}')
    #         return None, 0
    #
    #     if response.status_code != 200:
    #         logger.warning(f'Bad response: {response.status_code} {response.text}')
    #         return None, 0
    #
    #     response_data = response.json()
    #     if response_data.get('code', None) == 200:
    #         entity_type = response_data.get('data', {}).get('best_id_type', 0)
    #         cid = response_data.get('data', {}).get('best_id', 0)
    #         return entity_type, cid
    #     else:
    #         return 3, 0
    #
    # print(get_id(name='贺野系统工程有限公司'))
