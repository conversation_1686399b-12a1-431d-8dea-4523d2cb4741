# encoding=utf8

import argparse
from datetime import datetime, timedelta
from libs.env import ConstantProps
from libs.log import setup_logger
from libs.concurrent import BoundedExecutor
from libs.dt import to_datetime
from gslib.credit_code import credit_code_valid
from gslib.id_center import id_center_query
from gslib.gs_enum import EntityType
from clients.redis.redis_hash import RedisHash
from dao.qxb.enterprise import EnterpriseDao, Enterprise
from t_stockholder_percentage_new import StockholderPercentageDao
from dao.investors.equity_ratio import EquityRatioDao
import json, re, csv

logger = setup_logger()

sharding_dao = StockholderPercentageDao()
qx_company_dao = EnterpriseDao()
tyc_sharding_dao = EquityRatioDao()

amount_re = re.compile("^[\-0-9]*(?:\.[0-9]+)?")


def get_amount_from_capital(capital):
    if not capital:
        return 0
    amount = 0
    if isinstance(capital, list):
        capital_json = capital
    else:
        try:
            capital_json = json.loads(capital)
        except:
            result = amount_re.findall(str(capital))
            if len(result) == 1:
                try:
                    amount = float(result[0])
                except Exception:
                    pass
            return amount

    if isinstance(capital_json, (int, float)):
        return capital_json

    for cap in capital_json:
        amomon = cap.get("amomon", 0)
        result = amount_re.findall(str(amomon))
        if len(result) == 1:
            try:
                amount += float(result[0])
            except Exception:
                pass
    return amount


def process(eid: str):
    qx_company_data = qx_company_dao.get(eid)
    # qx 股东数据
    qx_share_ratio_data = sharding_dao.get(eid)
    qx_share_ratio_json_data = json.loads(qx_share_ratio_data.percentage)
    qx_share_ratio_list = []
    for qx_share_ratio in qx_share_ratio_json_data:
        qx_dic = dict()
        qx_dic['shareholder_name'] = qx_share_ratio['stock_name']
        qx_dic['shareholder_type'] = qx_share_ratio['stock_type']
        qx_dic['percent'] = qx_share_ratio['stock_percent']
        qx_dic['capital'] = qx_share_ratio['should_capi']
        qx_share_ratio_list.append(qx_dic)
    # print('qx公司信息:', qx_company_data)
    print('qx股东列表:', qx_share_ratio_list)
    # 获取统一信用码
    credit_code = qx_company_data.credit_code
    # 通过统一信用码调用id中心 获取gid
    entity_id = id_center_query(credit_no=credit_code, use_graph_id=True)[1]
    # print('gid:', entity_id)
    # 通过 gid 获取 tyc股东列表
    tyc_share_ratio_data = tyc_sharding_dao.get_many(field='company_graph_id', value=entity_id)
    tyc_share_ratio_list = []
    for tyc_share_ratio in tyc_share_ratio_data:
        # print(tyc_share_ratio)
        tyc_dic = dict()
        tyc_dic['shareholder_name'] = tyc_share_ratio.shareholder_name
        tyc_dic['shareholder_type'] = tyc_share_ratio.shareholder_type
        tyc_dic['percent'] = tyc_share_ratio.percent
        tyc_dic['capital'] = get_amount_from_capital(json.loads(tyc_share_ratio.capital))
        tyc_share_ratio_list.append(tyc_dic)
    print('tyc 股东列表：', tyc_share_ratio_list)
    if len(qx_share_ratio_list) != len(tyc_share_ratio_list):
        print('---股东列表不一致---')
        diff = 1
    else:
        print('---股东列表一致---')
        diff = 0
    with open('20240320_qx_tyc股东diff.csv', 'a', encoding='utf-8', newline='') as f:
        witer = csv.writer(f)
        witer.writerow([entity_id, str(qx_share_ratio_list), str(tyc_share_ratio_list), diff])


def main():
    logger.info('args=%s', ap_args.__dict__)
    enterprise_dao = EnterpriseDao(batch_size=ap_args.row_limit)
    enterprise_offsets = RedisHash(
        db=2,
        name='access_qx_share_ratio_offset_yzg',
        **ConstantProps.PROPS_GS_REDIS_ONLINE,
    )
    process_executor = BoundedExecutor(max_workers=ap_args.process_num, thread_name_prefix='process')

    for items in enterprise_dao.sharding_scan(
            worker_num=ap_args.dump_num,
            part_num=ap_args.idx_num,
            redis_offset=enterprise_offsets,
            init_offset=datetime.now() - timedelta(days=ap_args.start_days),
            infinite_wait_secs=60,
            max_offset=to_datetime('2123-01-01'),
            scan_key='row_update_time',
    ):
        for item in items:
            item: Enterprise = item
            try:
                process_executor.submit(
                    process,
                    eid=item.eid
                )
            except Exception as e:
                logger.exception(f'fail process {item} e={e}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser(description='access_qx_share_ratio.py')
    ap.add_argument('--dump-num', type=int, default=1, help='线程数')
    ap.add_argument('--row-limit', type=int, default=100, help='一次sql行数')
    ap.add_argument('--delay-hour', type=int, default=1, help='只计算scan-key在delay-hour前的')
    ap.add_argument('--start-days', type=int, default=100, help='row-update-time开始时间')
    ap.add_argument('--idx-num', type=int, default=4, help='只计算0-N的分片')
    ap.add_argument('--process-num', type=int, default=1, help='线程数')
    ap_args = ap.parse_args()

    main()
