# encoding=utf8

import re
import json
from datetime import date, datetime
from typing import List, Dict, Type, Optional
from pydantic import Field
from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao, EntityType
from dao.deps.mysql_sharding_dao import MySQLShardingDao
from entity.deps.entity import BaseEntity
from dao.qxb.enterprise import EnterpriseDao, Enterprise
from gslib.id_center import id_center_query
from dao.investors.equity_ratio import EquityRatioDao
from gslib.gs_enum import EntityType

amount_re = re.compile("^[\-0-9]*(?:\.[0-9]+)?")


def get_amount_from_capital(capital):
    if not capital:
        return 0
    amount = 0
    if isinstance(capital, list):
        capital_json = capital
    else:
        try:
            capital_json = json.loads(capital)
        except:
            result = amount_re.findall(str(capital))
            if len(result) == 1:
                try:
                    amount = float(result[0])
                except Exception:
                    pass
            return amount

    if isinstance(capital_json, (int, float)):
        return capital_json

    for cap in capital_json:
        amomon = cap.get("amomon", 0)
        result = amount_re.findall(str(amomon))
        if len(result) == 1:
            try:
                amount += float(result[0])
            except Exception:
                pass
    return amount


class CompareHandle:
    def __init__(self):
        pass


class StockholderPercentageNew(BaseEntity):
    # 企业eid，T_ENTERPRISE外键
    eid: str  # 企业eid
    stock_name: str     # 股东名称
    stock_type: str     # 股东类型
    stock_percent: str  # 出资比例
    total_real_capi: str    # 实缴
    total_should_capi: str  # 认缴
    should_capi_items: str  # 认缴明细
    real_capi_items: str    # 实缴明细
    row_update_time: datetime = Field(alias='row_update_time')  # 记录变更时间

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


# 分库分表的 MySQLDao
class StockholderPercentageDao(MySQLShardingDao):
    def __init__(self, **kwargs):
        super().__init__(sharding_key='eid', pk_name='eid', entity_class=StockholderPercentageNew, **kwargs)

    # 给定v 给出 分库分表位置
    @classmethod
    def do_sharding(cls, v: str) -> int:
        if not re.fullmatch('[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', v):
            raise ValueError('bad format eid={}'.format(v))
        db_id = int('0x' + v[-1], 16)
        tb_id = int('0x' + v[-2], 16)
        return db_id * 16 + tb_id

    @classmethod
    def gen_dao_list(cls, **kwargs) -> List[MySQLDao]:
        return cls.gen_dao_list_qxb(
            db_prefix='db_enterprise',
            tb_prefix='t_partners',
            **kwargs,
        )

    @classmethod
    def gen_dao_list_qxb(
            cls,
            db_prefix: str,
            tb_prefix: str,
            **kwargs,
    ) -> List[MySQLDao]:
        dao_list = list()
        for db_id in range(16):
            for tb_id in range(16):
                dao_args_copy = dict(**kwargs)
                dao_list.append(
                    MySQLDao(
                        **ConstantProps.PROPS_QX_LIST[db_id],
                        db_tb_name='{}_{}.{}_{}'.format(db_prefix, db_id, tb_prefix, tb_id),
                        **dao_args_copy,
                    )
                )
        return dao_list


def diff_data(eid):
    # eid = '0016d650-d702-443f-a4a7-8dd9c1a8b910'
    # qx 公司数据
    qx_company_data = qx_company_dao.get(eid)
    # qx 股东数据
    qx_share_ratio_data = sharding_dao.get(eid)
    qx_share_ratio_json_data = json.loads(qx_share_ratio_data.percentage)
    qx_share_ratio_list = []
    for qx_share_ratio in qx_share_ratio_json_data:
        qx_dic = dict()
        qx_dic['shareholder_name'] = qx_share_ratio['stock_name']
        qx_dic['shareholder_type'] = qx_share_ratio['stock_type']
        qx_dic['percent'] = qx_share_ratio['stock_percent']
        qx_dic['capital'] = qx_share_ratio['should_capi']
        qx_share_ratio_list.append(qx_dic)
    # print('qx公司信息:', qx_company_data)
    print('qx股东列表:', qx_share_ratio_list)
    # 获取统一信用码
    credit_code = qx_company_data.credit_code
    # 通过统一信用码调用id中心 获取gid
    entity_id = id_center_query(credit_no=credit_code, use_graph_id=True)[1]
    # print('gid:', entity_id)
    # 通过 gid 获取 tyc股东列表
    tyc_share_ratio_data = tyc_sharding_dao.get_many(field='company_graph_id', value=entity_id)
    tyc_share_ratio_list = []
    for tyc_share_ratio in tyc_share_ratio_data:
        # print(tyc_share_ratio)
        tyc_dic = dict()
        tyc_dic['shareholder_name'] = tyc_share_ratio.shareholder_name
        tyc_dic['shareholder_type'] = tyc_share_ratio.shareholder_type
        tyc_dic['percent'] = tyc_share_ratio.percent
        tyc_dic['capital'] = get_amount_from_capital(json.loads(tyc_share_ratio.capital))
        tyc_share_ratio_list.append(tyc_dic)
    print('tyc 股东列表：', tyc_share_ratio_list)
    if len(qx_share_ratio_list) != len(tyc_share_ratio_list):
        print('---股东列表不一致---')
    else:
        print('---股东列表一致---')


if __name__ == '__main__':
    from libs.log import setup_logger

    logger = setup_logger()
    sharding_dao = StockholderPercentageDao()
    qx_company_dao = EnterpriseDao()
    tyc_sharding_dao = EquityRatioDao()
    eid = '0016d650-d702-443f-a4a7-8dd9c1a8b910'
    diff_data(eid)
