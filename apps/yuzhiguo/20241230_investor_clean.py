# encoding=utf8
import csv
import json
import time
from dao.company import CompanyDao
from dao.company_graph import CompanyGraphDao
from apps.yuzhiguo.my_sever.equity_ratio import EquityRatioDao
from libs.log2 import setup_logger
import re
from decimal import Decimal
import copy

logger = setup_logger(debug=False)
company_sever = CompanyDao()
company_graph_sever = CompanyGraphDao()
equity_ratio_sever = EquityRatioDao()




def amounts(reg_capital):
    """
    提取并返回注册资本中的金额部分
    Args:
        reg_capital: 注册资本字符串
    Returns:
        str: 提取的金额字符串
    """
    try:
        if not reg_capital:
            return ''
        # 使用正则表达式提取所有数字和小数点
        amounts = re.findall(r'[-0-9.]+', reg_capital)

        for amount_value in amounts:
            # 处理小数点前无0问题
            if amount_value.startswith('.'):
                amount_value = '0' + amount_value

            # 检验金额是否有效
            if re.match(r'^-?\d*\.?\d*$', amount_value):
                return amount_value  # 返回第一个有效的金额

        return ''  # 如果没有有效金额，返回空字符串
    except Exception as e:
        logger.error(f"提取金额失败: {e}")
        return ''

def units(reg_capital):
    """
    提取并返回注册资本中的单位部分
    Args:
        reg_capital: 注册资本字符串
    Returns:
        str: 提取的单位字符串
    """
    try:
        # 使用正则表达式提取单位
        units = re.findall(r'([元百千万亿])', reg_capital)

        if units:
            # 优先返回'万'，否则返回第一个单位
            return '万' if '万' in units else units[0]

        return ''  # 如果没有单位，返回空字符串
    except Exception as e:
        logger.error(f"提取单位失败: {e}")
        return ''

def currencys(reg_capital):
    """
    提取并返回注册资本中的货币单位
    Args:
        reg_capital: 注册资本字符串
    Returns:
        str: 提取的货币单位
    """
    try:
        # 使用正则表达式提取货币
        currencies = re.findall(
            r'(人民币|美元?|港币|港元?|韩元?|缅元?|香港元?|比索|泰铢|日元?|英镑|澳元?|澳大利亚|欧元?|法郎|加元|加拿大元|新西兰|阿富汗尼|新台币|新加坡|科摩罗法郎|马克|韩国|智利比索|丹麦克朗|克朗|挪威克朗|哥伦比亚|股|卢布|塔拉|泰国|奥地利|马来西亚|菲律宾|德拉克马|阿塞拜疆马纳特)',
            reg_capital
        )

        if not currencies:
            return ''  # 如果没有找到货币，返回空字符串

        currency = currencies[0]  # 获取第一个匹配的货币

        # 处理特定货币的标准化
        currency_mapping = {
            '人民币': '元人民币',
            '港': '港元',
            '欧': '欧元',
            '澳': '澳元',
            '新加坡': '新加坡元',
            '加': '加拿大元',
            '马克': '马克',
            '韩': '韩元',
            '新西兰': '新西兰元',
            '哥伦比亚': '哥伦比亚比索',
            '股': '股',
            '泰国': '泰铢',
            '马来西亚': '马来西亚币',
            '菲律宾': '菲律宾比索',
            '奥地利': '奥地利先令'
        }

        # 处理特殊情况
        if '澳' in currency and '门' in currency:
            currency = '澳门元'

        return currency_mapping.get(currency, currency)  # 返回标准化后的货币

    except Exception as e:
        logger.error(f"提取货币失败: {e}")
        return ''



def _get_registration_info(company):
    """获取注册资本相关信息"""
    reg_capital = company.reg_capital
    return {
        'capital': reg_capital,
        'unit': units(reg_capital),
        'currency': currencys(reg_capital),
        'amount': Decimal(str(amounts(reg_capital))) if amounts(reg_capital) else Decimal('0')
    }


def _process_normal_case(investors, reg_info):
    """处理普通情况下的资本金额规范化"""
    for investor in investors:
        investor.capital = _process_capital_field(investor.capital, reg_info)
        investor.capitalActl = _process_capital_field(investor.capitalActl, reg_info)
    return investors



def _process_capital_field(capital, reg_info):
    """处理资本字段"""
    capital = json.loads(capital)
    for amom in capital:
        amom['amomon'] = normalize_single_amount(amom['amomon'], reg_info)
    capital = json.dumps(capital, ensure_ascii=False)
    return capital


def _format_amount(amount, unit, currency):
    """格式化金额"""
    return f"{amount}{unit}{currency}" if amount else ""


def normalize_single_amount(amount_str, reg_info):
    """规范化单个金额字符串"""
    if not amounts(amount_str):
        return ""

    amount = amounts(amount_str)
    unit = units(amount_str)
    currency = currencys(amount_str)

    # 亿转万
    if '亿' in unit:
        amount = Decimal(str(amount)) * 10000
        return _format_amount(amount, reg_info['unit'], reg_info['currency'])

    # 使用注册资本单位和货币
    if reg_info['unit'] and '元' not in reg_info['unit'] and reg_info['currency']:
        return f"{amount}{reg_info['unit']}{reg_info['currency']}"

    # 使用万作为默认单位
    if reg_info['unit'] == '万' and not reg_info['currency']:
        return f"{amount}万元人民币"

    # 处理无单位无货币情况
    if not reg_info['unit'] and not reg_info['currency']:
        if unit and '元' not in unit:
            return f"{amount}{unit}元人民币"
        return f"{amount}万元人民币"

    return amount_str



def run(gid):
    try:
        gid_date = company_graph_sever.get_by_gid(gid)
        cid = gid_date.cid
    except Exception as e:
        cid = 0
        logger.warning(f"当前gid无cid:{gid},{e}")

    company_date = company_sever.get(cid, 'id')
    reg_info = _get_registration_info(company_date)
    equity_ratio_list = equity_ratio_sever.get_by_gid(gid)
    old_equity_ratio_list = copy.deepcopy(equity_ratio_list)
    # print(equity_ratio_list)
    investors = _process_normal_case(equity_ratio_list, reg_info)
    # print(investors)

    for investor in investors:
        for equity_ratio in old_equity_ratio_list:
            if investor.id == equity_ratio.id:
                print(investor.id, equity_ratio.capitalActl, investor.capitalActl)
                with open('20241230_investor_clean_out.csv', 'a', newline='', encoding='utf-8') as f:
                    witer = csv.writer(f)
                    witer.writerow([investor.id, equity_ratio.capitalActl, investor.capitalActl])


with open('20241230_investor_clean_out.csv', 'r', encoding='utf-8') as f:
    data = csv.DictReader(f)
    count = 0
    for i in data:
        dict_data = dict(i)
        id_ = dict_data['id']
        old = dict_data['old']
        new = dict_data['new']
        print(id_, old, new)
        if len(new) > 2:
            equity_ratio_sever.update_capitalActl(id_, new)
        time.sleep(0.1)
        # run(gid)