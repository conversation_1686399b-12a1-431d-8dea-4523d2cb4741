# encoding=utf8

import csv
from my_sever.company_staff_sever import CompanyStaffSeverDao
from my_sever.company_staff_sort_sever import CompanyStaffSortSeverDao
from dao.company_graph import CompanyGraphDao
from dao.human import HumanDao, HumanGraphDao, HumanGraph
from typing import Optional
from loguru import logger



class HumanSever(HumanGraphDao):
    def get_by_hgid(self, hgid, ) -> Optional[HumanGraph]:
        sql = f'select * from {self.db_tb_name} where graph_id=%s and deleted=0'
        for d in self.mysql_client.select_many(sql, args=(hgid,)):
            item = self._to_entity(d)
            if item is not None:
                return item
        return None


# ------流程开始------
# 首先获取要清洗的职位表id
# select id,company_gid,name,position,human_gid from company_staff_sort where id=*********;
# 通过company_gid获取company_cid 通过human_gid获取human_cid 通过position获取hcid和hgid,
# select company_id from company_graph where graph_id=3189845147 and deleted=0; # cid *********
# select human_id from human_graph where graph_id=3029114193 and deleted=0; # hcid 23579048
# select id from human where name='王玉苓' and sourceflag='http'; # new_hcid 1540199
# select graph_id from human_graph where human_id=1540199 and deleted=0; # new_hgid 2074608959
# 通过company_cid和human_cid 获取company_staff表的记录
# select id,company_id,staff_id,staff_type,staff_type_name from company_staff where company_id=********* and staff_id=23579048;
# 更新 id=2807848719 company_staff  的staff_id=new_hcid和staff_type_name=未知
# update company_staff set staff_id=new_hcid,staff_type_name='未知' where id=2807848719;
# 更新 id=********* company_staff_sort 的name=position，position=未知，human_gid=new_hgid
# update company_staff_sort set name=position,position='未知',human_gid=new_hgid where id=*********;
# ------流程结束------
company_staff_sort_sever = CompanyStaffSortSeverDao()
company_staff_sever = CompanyStaffSeverDao()
company_graph_sever = CompanyGraphDao()
human_graph_sever = HumanSever()
human_sever = HumanDao()

logger.add('20240920_staff_clean.log', encoding='utf-8')


with open('20240919_要清洗的数据名单.csv', 'r', encoding='utf-8') as f:
    data = csv.DictReader(f)
    count = 0
    company_staff_count = 0
    company_staff_err = 0
    company_staff_sort_count = 0
    company_staff_sort_err = 0
    for i in data:
        dict_data = dict(i)
        id_ = dict_data.get('b.id')
        logger.info(f"原始company_staff_sort表id: {id_}")
        # 通过id获取company_staff_sort表数据
        # id_ = *********
        old_staff_sort_data = company_staff_sort_sever.get_id_by_date(id_)
        logger.info(f'company_staff_sort表数据： {old_staff_sort_data}')
        company_gid = old_staff_sort_data.company_gid
        name = old_staff_sort_data.name
        position = old_staff_sort_data.position
        human_gid = old_staff_sort_data.human_gid
        logger.info(f'company_staff_sort表数据：{ company_gid}, {name}, {position}, {human_gid}')
        # 通过company_gid获取company_cid 通过human_gid获取human_cid 通过position获取hcid和hgid
        company_graph_date = company_graph_sever.get_by_gid(company_gid)
        logger.info(f"company_graph表数据： {company_graph_date}")
        company_cid = company_graph_date.cid
        logger.info(f"公司cid: {company_cid}")
        human_graph_date = human_graph_sever.get_by_hgid(human_gid)
        logger.info(f"通过原始的hgid获取的human_graph表数据： {human_graph_date}")
        human_cid = human_graph_date.hid
        logger.info(f"原始hgid对应的人员hcid: {human_cid}")
        # 通过异常职位名称获取人员hcid和hgid
        human_date = human_sever.get_by_name_source(position, 'http://qyxy.baic.gov.cn/')
        logger.info(f"通过人名获取的human_graph表数据: {human_date}")
        new_hcid = human_date.id
        new_hgid = human_graph_sever.get_by_hid(new_hcid).hgid
        logger.info(f"通过人名获取的human_graph表的新hgid:{new_hgid}")
        # company_staff 表操作 通过company_cid 和 human_cid 获取数据
        try:
            old_company_staff_date = company_staff_sever.get_cid_hcid_by_date(company_cid, human_cid)
            logger.info(f"原始company_staff表数据：{old_company_staff_date}")
            old_company_staff_id = old_company_staff_date.id
        except:
            logger.error(f'未获取到原始company_staff表数据:{company_gid, company_cid, human_cid}')
            count += 1
            continue
        logger.info(f"获取的原始company_staff表的id：{old_company_staff_id}")
        logger.info(f"update company_staff set staff_id={new_hcid}, staff_type_name='未知' where id = {old_company_staff_id}")
        # 更新 company_staff 表
        if company_staff_sever.id_update(old_company_staff_id, new_hcid, '未知'):
            logger.info('更新 company_staff 表成功')
            company_staff_count += 1
        else:
            logger.error('更新 company_staff 表失败')
            company_staff_err += 1
        logger.info(f'update company_staff_sort set name={position}, position="未知", human_gid={new_hgid} where id = {id_}')
        # 更新 company_staff_sort 表
        if company_staff_sort_sever.id_update(id_, position, '未知', new_hgid):
            logger.info('更新 company_staff_sort 表成功')
            company_staff_sort_count += 1
        else:
            logger.error('更新 company_staff_sort 表失败')
            company_staff_sort_err += 1
        logger.info(f"###########################---{count}---##############################")
        logger.info(f"company_staff表成功更新{company_staff_count}条数据")
        logger.info(f"company_staff表更新失败{company_staff_err}条数据")
        logger.info(f"company_staff_sort表成功更新{company_staff_sort_count}条数据")
        logger.info(f"company_staff_sort表更新失败{company_staff_sort_err}条数据")
        count += 1



