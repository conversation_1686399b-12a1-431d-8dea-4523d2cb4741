# encoding=utf8
from clients.kafka_client import KafkaConsumerClient
from libs.env import ConstantProps, get_stack_info
import json, time
from libs.log2 import setup_logger
from entity.entity_binlog import BinlogEntity
from clients.redis._redis import Redis

logger = setup_logger(debug=False)

redis_client = Redis(**ConstantProps.PROPS_GS_REDIS_ONLINE, db=4)


def main():
    kafka_consumer = KafkaConsumerClient(kafka_topic='prism.company',
                                         group_id='shareholder_name_clean', **ConstantProps.PROPS_KAFKA_CANAL_PROD)

    for sid, s in enumerate(kafka_consumer.read()):
        try:
            d = json.loads(s)
            binlog: BinlogEntity = BinlogEntity.from_dict(d)
            if not binlog:
                continue
            for data_, changes in zip(binlog.data, binlog.old or []):
                if all(x in ['updatetime', 'crawledtime'] for x in changes):
                    continue
                if 'name' in changes and data_['property1'].startswith('91'):
                    cid = data_['id']
                    redis_client.zadd('shareholder_name_clean', {cid: 1})
                    logger.info(f'cid:{cid},name:{data_["name"]}')
        except Exception as e:
            logger.warning(f'e={e} s={s} trace={get_stack_info()}')
            continue

    kafka_consumer.close()


if __name__ == '__main__':
    main()
