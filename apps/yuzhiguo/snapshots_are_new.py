# encoding=utf8

from my_sever.company_snapshot_info import CompanySnapshotInfoDao
import requests, json, time
from gslib.id_center import id_center_query
from libs.log2 import setup_logger
from datetime import datetime, timedelta
logger = setup_logger()


company_snapshot_sever = CompanySnapshotInfoDao()


def send_request_with_retry(url, retry_times=3):
    for i in range(retry_times):
        try:
            r = requests.get(url, timeout=10)
            return r
        except Exception as e:
            print(e)
            time.sleep(5)
            continue
    return None


def get_snapshot(gid):
    """
    :param gid: gid
    :return: 更新快照 返回 1 成功 0 失败
    """
    try:
        url = "http://10.99.203.222:16666/company/snapshot/generate_realtime?gid={}".format(gid)
        response = send_request_with_retry(url)
        if response.status_code == 200:
            return 1
        else:
            return 0
    except:
        return 0


def run():
    while True:
        snap_data = company_snapshot_sever.query1()
        if snap_data:
            companyname = snap_data.get('companyname', '')
            companyname_id = snap_data.get('id', '')
            try:
                c_gid = id_center_query(name=companyname, use_graph_id=True)[1]
            except:
                # 无id 返回
                c_gid = ''
                logger.warning(f'{companyname}无id,退出')
                update_snap_time = company_snapshot_sever.update_time(companyname_id, datetime.now())
                if update_snap_time:
                    logger.info(f"---无id,未重新生成快照，更新时间成功: name: {companyname}---")
                continue
            if c_gid:
                # print(c_gid)
                # 调用接口更新快照
                star_data = get_snapshot(c_gid)
                if star_data == 1:
                    # 更新update时间
                    if companyname:
                        # 先查后更新
                        snapshot_date = company_snapshot_sever.get_data(companyname)
                        if snapshot_date:
                            snapshot_id = snapshot_date.get('id', '')
                            if snapshot_id:
                                update_snap_time = company_snapshot_sever.update_time(snapshot_id, datetime.now())
                                if update_snap_time:
                                    logger.info(f"---更新时间成功: name: {companyname}---")
                    logger.info(f'---照面更新成功: gid:{c_gid}--name:{companyname}---')
                else:
                    if companyname:
                        # 先查后更新
                        snapshot_date = company_snapshot_sever.get_data(companyname)
                        if snapshot_date:
                            snapshot_id = snapshot_date.get('id', '')
                            if snapshot_id:
                                # 获取当前时间
                                now = datetime.now()
                                # 计算25天之前的时间
                                twenty_five_days_ago = now - timedelta(days=25)
                                update_snap_time = company_snapshot_sever.update_time(snapshot_id, twenty_five_days_ago)
                                if update_snap_time:
                                    logger.info(f"---更新时间成功: name: {companyname}---")
                    logger.warning(f'---照面更新失败：gid:{c_gid}--name:{companyname}---')
                    continue
            else:
                logger.warning(f"---当前name:{companyname}没有对应gid")
                update_snap_time = company_snapshot_sever.update_time(companyname_id, datetime.now())
                if update_snap_time:
                    logger.info(f"---无id,未重新生成快照，更新时间成功: name: {companyname}---")
                continue


if __name__ == '__main__':
    run()