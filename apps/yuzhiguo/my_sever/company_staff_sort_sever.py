# encoding=utf8

from dao.company_staff_sort import CompanyStaffSortDao
from dao.company_staff_sort import CompanyStaffSort
from libs.env import ConstantProps


class CompanyStaffSort2(CompanyStaffSort):
    id: int
    company_gid: int
    name: str
    position: str
    human_gid: int
    deleted: int


class CompanyStaffSortSeverDao(CompanyStaffSortDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_staff_sort')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanyStaffSort2)
        super().__init__(**kwargs)

    def get_id_by_date(self, id_):
        """通过主键ID获取数据记录"""
        sql = f'select * from {self.db_tb_name} where id = %s'
        ret = self.mysql_client.select(sql, args=(id_,))
        return self.entity_class.from_dict(ret)

    def id_update(self, id_, old_position, position_name, new_hgid):
        """根据id更新数据记录"""
        sql = f'update {self.db_tb_name} set name=%s,position=%s,human_gid=%s where id = %s'
        ret = self.mysql_client.execute(sql, args=(old_position, position_name, new_hgid, id_))
        return ret


if __name__ == '__main__':
    dao = CompanyStaffSortSeverDao()
    # 查询
    # print(dao.get_id_by_date(*********))
    # 更新
    print(dao.id_update(*********, '小亮4', '未知', 5094907113))
