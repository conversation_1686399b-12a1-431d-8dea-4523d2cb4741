from typing import List
from datetime import datetime, date
from pydantic import Field, conint
from typing import Optional
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps, get_props_mysql
from dao.deps.mysql_dao import MySQLDao
from gslib.gs_enum import EntityType


class EquityRatio(BaseEntity):
    id: int = Field(default=None)
    company_graph_id: int = Field(default=None)
    shareholder_type: EntityType = Field(default=None)
    shareholder_graph_id: conint(strict=True, ge=0) = Field(default=None)
    shareholder_name: str = Field(default=None)
    amount: Optional[float] = Field(default=None)
    capital: str = Field(default=None)
    capitalActl: str = Field(default=None)
    percent: Optional[float] = Field(default=None)
    source: Optional[int] = Field(default=None)
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)
    deleted: int = Field(default=0)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class EquityRatioDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in get_props_mysql('mysql.tyc.gs.hw_gsxt_outer_rw').items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.equity_ratio')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', EquityRatio)
        super().__init__(**kwargs)

    def get_by_gid(self, gid) -> List[EquityRatio]:
        sql = """select * from prism.equity_ratio  where company_graph_id =%s"""
        items: List = list()
        for o in self.mysql_client.select_many(sql, args=(gid,)):
            item = self.entity_class().from_dict(o)
            if item is not None:
                items.append(item)
        return items

    def get_by_shareholder_gid(self, gid):
        """通过股东GID获取数据"""
        sql = """select * from prism.equity_ratio  where  shareholder_graph_id=%s and deleted = 0 and shareholder_type = 2"""
        items: List = list()
        for o in self.mysql_client.select_many(sql, args=(gid,)):
            item = self.entity_class().from_dict(o)
            if item is not None:
                items.append(item)
        return items

    def update_shareholder_name(self, id_, name):
        sql = """update prism.equity_ratio set shareholder_name=%s where id=%s"""
        ret = self.mysql_client.execute(sql, args=(name, id_))
        return ret

    def update_capitalActl(self, id_, capitalActl):
        sql = """update prism.equity_ratio set capitalActl=%s where id=%s"""
        ret = self.mysql_client.execute(sql, args=(capitalActl, id_))
        return ret

    def update_capital(self, id_, capital):
        sql = """update prism.equity_ratio set capital=%s where id=%s"""
        ret = self.mysql_client.execute(sql, args=(capital, id_))
        return ret

    def to_mysql(self, company_graph_id, company_name, shareholder_graph_id, shareholder_name, shareholder_type, amount,
                 capital, capitalActl, percent, source):
        sql = "INSERT INTO prism.equity_ratio (company_graph_id, company_name, shareholder_graph_id, shareholder_name, shareholder_type, amount, capital, capitalActl, percent, source) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"
        insert_id = self.mysql_client.insert(sql, args=(
        company_graph_id, company_name, shareholder_graph_id, shareholder_name, shareholder_type, amount, capital,
        capitalActl, percent, source))
        return insert_id


if __name__ == '__main__':
    sever = EquityRatioDao()
    # data = sever.get_by_gid('3361910266')
    # print(data)
    # 认缴和实缴需要做字符串写入
    insert_id = sever.to_mysql(6864520322,'全维度测试saq（个体工商户）',
                               1985474938,'李明亮',1,
                               0.0,'[]','[]','0',114)
    print(insert_id)
