from typing import List
from gslib.id_center import id_center_query
from datetime import datetime, date
from pydantic import Field
from typing import Optional
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps, get_props_mysql
from dao.deps.mysql_dao import MySQLDao



class QxWholeParkRelationInfo(BaseEntity):
    id: int
    name: str
    yid: str
    self_id: str
    md5: str
    eid: str
    company_name: str
    score: int = Field(default=None)
    last_updated_time: int
    created_time: int
    row_update_time: Optional[datetime] = Field(default=None)
    local_row_update_time: Optional[datetime] = Field(default=None)

    id_: int = Field(alias='id')
    name_: str = Field(alias='name')
    yid_: str = Field(alias='yid')
    self_id_: str = Field(alias='self_id')
    province_: str = Field(alias='province')
    city_: str = Field(alias='city')
    district_: str = Field(alias='district')
    district_code_: str = Field(alias='district_code')
    area_: int = Field(alias='area')
    description_: str = Field(alias='description')
    ploy_: str = Field(alias='ploy')
    last_updated_time_: int = Field(alias='last_updated_time')
    created_time_: int = Field(alias='created_time')
    row_update_time_: Optional[datetime] = Field(default=None, alias='row_update_time')
    local_row_update_time_: Optional[datetime] = Field(default=None, alias='local_row_update_time')

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class QxWholeParkRelationDao(MySQLDao):
    def __init__(self, **kwargs):

        for k, v in get_props_mysql('mysql.tyc.qxb.b').items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'db_business_reproduce.t_whole_park_relation')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', QxWholeParkRelationInfo)
        super().__init__(**kwargs)

    def get_data(self, yid):
        sql = 'select * from {} where yid =%s '.format(self.db_tb_name)
        items: List = list()
        for o in self.mysql_client.select_many(sql, args=(yid,)):
            item = self.entity_class.from_dict(o)
            if item is not None:
                items.append(item)
        return items


    def get_data_join(self, yid):
        sql = f"select * from {self.db_tb_name} left join db_business_reproduce.t_whole_park on t_whole_park.yid= t_whole_park_relation.yid where t_whole_park.yid = %s"
        items: List = list()
        for o in self.mysql_client.select_many(sql, args=(yid,)):
            item = self.entity_class.from_dict(o)
            if item is not None:
                items.append(item)
        return items


def get_base(province):
    base_dict = {"sh": "上海", "yn": "云南", "nmg": "内蒙古", "bj": "北京", "jl": "吉林", "sc": "四川", "tj": "天津",
                 "nx": "宁夏", "ah": "安徽",
                 "sd": "山东",
                 "sx": "山西", "gd": "广东", "gx": "广西", "xj": "新疆", "js": "江苏", "jx": "江西", "hb": "河北",
                 "hen": "河南", "zj": "浙江",
                 "hain": "海南",
                 "hub": "湖北", "hun": "湖南", "gs": "甘肃", "fj": "福建", "xz": "西藏", "gz": "贵州", "ln": "辽宁",
                 "cq": "重庆", "snx": "陕西",
                 "qh": "青海",
                 "hlj": "黑龙江"}
    for k, v in base_dict.items():
        if v in province:
            return k


def get_gid(company_name):
    company_gid = id_center_query(name=company_name, use_graph_id=True)[1]
    if company_gid:
        return company_gid
    else:
        return 0


def get_qx_park_data(data):
    import uuid
    dic = {}
    company_name_list = []
    graph_id_list = []
    uuid_str = str(uuid.uuid4())
    uuid = uuid_str.replace("-", "")  # 随机生成 唯一标志
    create_time = datetime.now()  # 创建时间
    update_time = datetime.now()  # 更新时间
    deleted = 0  # 是否删除
    for i in data:
        park_data = dict(i)
        park_name = park_data.get('name')  # 园区名称
        company_name = park_data.get('company_name')  # 园区公司名称
        province = park_data.get('province_')  # 省
        base = get_base(province)  # 地域简称
        city = park_data.get('city_')  # 市
        district = park_data.get('district_')  # 区
        district_code = park_data.get('district_code_')  # 区代码
        city_code = district_code[:4] + '00'  # 市代码
        province_code = district_code[:2] + '0000'  # 省代码
        park_area = park_data.get('area_')  # 占地面积
        coordinate = park_data.get('ploy_').replace('[', '').replace(']', '')  # 园区坐标
        company_name_list.append(company_name)  # 公司列表
        gid = str(get_gid(company_name))  # gid
        graph_id_list.append(gid)  # gid_list
        province_code_new = "{:08d}".format(int(province_code))  # 新版省代码
        city_code_new = "{:08d}".format(int(city_code))  # 新版市代码
        district_code_new = "{:08d}".format(int(district_code))  # 新版区代码
        # park_name ,province ,city ,base, province_code, city_code, district_code
        # park_area, company_number,coordinate(园区坐标),center_coordinate(中心坐标)
        # create_time，update_time，deleted ，company_name_list（公司名称列表）
        # company_total（公司名称计数）graph_id_list（gid列表），uuid
        # province_code_new, city_code_new, district_code_new
        # print(park_name, province, city, base, province_code, city_code, district_code, park_area,
        #       coordinate,
        #       center_coordinate, create_time, update_time, deleted, company_name_list, graph_id_list,
        #       province_code_new, city_code_new, district_code_new)
    company_number = len(company_name_list)
    dic['park_name'] = park_name
    dic['province'] = province
    dic['city'] = f"{city}, {district}"
    dic['base'] = base
    dic['province_code'] = province_code
    dic['city_code'] = city_code
    dic['district_code'] = district_code
    dic['park_area'] = park_area
    dic['company_number'] = company_number
    dic['coordinate'] = coordinate
    dic['create_time'] = create_time
    dic['update_time'] = update_time
    dic['deleted'] = deleted
    dic['company_name_list'] = '	;	'.join(company_name_list)
    dic['company_total'] = company_number
    dic['graph_id_list'] = '	;	'.join(graph_id_list)
    dic['uuid'] = uuid
    dic['province_code_new'] = province_code_new
    dic['city_code_new'] = city_code_new
    dic['district_code_new'] = district_code_new
    return dic


if __name__ == '__main__':
    qx_park_relation_sever = QxWholeParkRelationDao()
    data = qx_park_relation_sever.get_data('7701ee47f5369f6d')
    # data = qx_park_relation_sever.get_data_join('1bcaabe0e12d0862')
    print(data)
    # qx_park_data = get_qx_park_data(data)
    # print(qx_park_data)
