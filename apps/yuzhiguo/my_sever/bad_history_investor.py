from typing import List
from datetime import datetime, date
from pydantic import Field, conint
from typing import Optional
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps, get_props_mysql
from dao.deps.mysql_dao import MySQLDao
from gslib.gs_enum import EntityType


class BadHistoryInvestor(BaseEntity):
    id: int = Field(default=None)
    company_graph_id: int = Field(default=None)
    shareholder_graph_id: conint(strict=True, ge=0) = Field(default=None)
    shareholder_type: EntityType = Field(default=None)
    reason: str = Field(default=None)
    deleted: int = Field(default=None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class BadHistoryInvestorDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in get_props_mysql('mysql.tyc.gs.hw_gsxt_outer_rw').items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.bad_history_investor')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', BadHistoryInvestor)
        super().__init__(**kwargs)

    def get_by_gid(self, gid) -> List[BadHistoryInvestor]:
        sql = """select * from prism.bad_history_investor  where company_graph_id =%s"""
        items: List = list()
        for o in self.mysql_client.select_many(sql, args=(gid,)):
            item = self.entity_class().from_dict(o)
            if item is not None:
                items.append(item)
        return items

    def del_by_gid(self, gid):
        sql = "DELETE FROM prism.bad_history_investor WHERE company_graph_id = %s"
        res = self.mysql_client.execute(sql, args=(gid,))
        return res

    def insert_by_gid(self, company_graph_id, bad_shareholder_graph_id, shareholder_type, reason):
        sql = ('INSERT INTO prism.bad_history_investor'
               ' (company_graph_id,bad_shareholder_graph_id,shareholder_type,reason) VALUES (%s, %s, %s, %s)')
        ret = self.mysql_client.insert(sql, args=(company_graph_id, bad_shareholder_graph_id, shareholder_type, reason))
        return ret


if __name__ == '__main__':
    sever = BadHistoryInvestorDao()
    # 查询
    # data = sever.get_by_gid('4054956461')
    # print(data)
    # 插入
    # ins_data = sever.insert_by_gid(3049276175,2334435818,2,'分公司')
    # print(ins_data)
    # 删除
    del_data = sever.del_by_gid(3049276175)
    print(del_data)
