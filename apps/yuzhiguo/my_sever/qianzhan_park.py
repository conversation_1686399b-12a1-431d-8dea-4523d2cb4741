import re
from datetime import datetime, date
from pydantic import Field
from typing import Optional
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao
from dao.company_graph import CompanyGraphDao
from dao.company import CompanyDao



class QianzhanParkInfo(BaseEntity):
    id: int
    park_name: Optional[str] = Field(default=None)
    province: Optional[str] = Field(default=None)
    city: Optional[str] = Field(default=None)
    base: Optional[str] = Field(default=None)
    province_code: Optional[str] = Field(default=None)
    city_code: Optional[str] = Field(default=None)
    district_code: Optional[str] = Field(default=None)
    park_area: Optional[str] = Field(default=None)
    company_number: Optional[str] = Field(default=None)
    coordinate: Optional[str] = Field(default=None)
    center_coordinate: Optional[str] = Field(default=None)
    create_time: Optional[datetime] = Field(default=None)
    update_time: Optional[datetime] = Field(default=None)
    deleted: int
    company_name_list: Optional[str] = Field(default=None)
    company_total: int
    graph_id_list: Optional[str] = Field(default=None)
    uuid: Optional[str] = Field(default=None)
    province_code_new: Optional[str] = Field(default=None)
    city_code_new: Optional[str] = Field(default=None)
    district_code_new: Optional[str] = Field(default=None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class QianZhanParkDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.qianzhan_park')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', QianzhanParkInfo)
        super().__init__(**kwargs)

    def get_park_name_by_data(self, park_name):
        sql = 'select * from {} where park_name =%s '.format(self.db_tb_name)
        ret = self.mysql_client.select(sql, args=(park_name,))
        return self.entity_class.from_dict(ret)

    def insert_data(self, park_name, province, city, base, province_code, city_code, district_code, park_area,
                    company_number, coordinate, center_coordinate, create_time, update_time, deleted, company_name_list,
                    company_total, graph_id_list, uuid, province_code_new, city_code_new, district_code_new):
        sql = ('INSERT INTO {}'
               '(park_name, province, city, base, province_code, city_code, district_code, park_area, '
               'company_number, coordinate, center_coordinate, create_time, update_time, deleted, company_name_list, '
               'company_total, graph_id_list, uuid, province_code_new, city_code_new, district_code_new) VALUES '
               '(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)').format(
            self.db_tb_name)
        ret = self.mysql_client.insert(sql, args=(
            park_name, province, city, base, province_code, city_code, district_code, park_area,
            company_number, coordinate, center_coordinate, create_time, update_time, deleted, company_name_list,
            company_total, graph_id_list, uuid, province_code_new, city_code_new, district_code_new))
        return ret

    def update_data(self, park_name, company_number, update_time, company_name_list, company_total, graph_id_list):
        sql = "update ignore {} set company_number=%s, update_time=%s, company_name_list=%s,company_total=%s,graph_id_list=%s where park_name=%s".format(
            self.db_tb_name)
        ret = self.mysql_client.execute(sql, args=(
            company_number, update_time, company_name_list,
            company_total, graph_id_list, park_name))
        return ret > 0

    def get_many_data(self, id_):
        sql = 'select * from {} where id =%s'.format(self.db_tb_name)
        ret = self.mysql_client.select(sql, args=(id_,))
        return self.entity_class.from_dict(ret)

    def update_data_graph_id_list(self, id_, new_graph_id_list_str, new_company_name_list_str, new_company_total):
        sql = "update ignore {} set graph_id_list=%s, company_name_list=%s,company_total=%s,company_number=%s where id=%s".format(
            self.db_tb_name)
        ret = self.mysql_client.execute(sql, args=(
            new_graph_id_list_str, new_company_name_list_str,
            new_company_total, new_company_total, id_))
        return ret > 0

    def get_many_id(self):
        sql = 'select id from {} order by id DESC limit 49190'.format(self.db_tb_name)
        ret = self.mysql_client.select_many(sql,)
        return ret



if __name__ == '__main__':
    # logger.add('20240308_update_qianzhan_park.log')
    qianzhan_sever = QianZhanParkDao()
    cg_sever = CompanyGraphDao()
    c_sever = CompanyDao()

    # print(qianzhan_sever.get_park_name_by_data('佛山创意产业园'))
    # park_name = '飞天谷·跨境电商产业园'
    # province = '江西省'
    # city = '赣州市, 章贡区'
    # base = 'jx'
    # province_code = '360000'
    # city_code = '360700'
    # district_code = '360702'
    # park_area = '111'
    # company_number = '0'
    # coordinate = '120.471973,36.145629;120.475374,36.144182;120.474516,36.143247;120.472027,36.144347;120.471394,36.145118'
    # center_coordinate = None
    # create_time = datetime.now()
    # update_time = datetime.now()
    # deleted = 0
    # company_name_list = None
    # company_total = 0
    # graph_id_list = None
    # uuid = None
    # province_code_new = '00360000'
    # city_code_new = '00360700'
    # district_code_new = '00360702'
    # res = qianzhan_sever.insert_data(park_name, province, city, base, province_code, city_code, district_code,
    #                                  park_area,
    #                                  company_number, coordinate, center_coordinate, create_time, update_time, deleted,
    #                                  company_name_list,
    #                                  company_total, graph_id_list, uuid, province_code_new, city_code_new,
    #                                  district_code_new)
    # print(res)
    # 获取所有园区id
    id_data = qianzhan_sever.get_many_id()
    count = 0
    update_count = 0
    fail_count = 0
    if id_data:
        for i in id_data:
            count += 1
            id_ = i.get('id')
            # logger.info(id_)
            if id_ < 41159:
                print(f'当前id{id_},第{count}个')
                # id_ = 2
                # 清洗逻辑
                data = qianzhan_sever.get_many_data(int(id_))
                company_total = data.company_total
                company_name_list = data.company_name_list
                graph_id_list = data.graph_id_list
                print(f'原始公司列表字段值：{company_name_list}')
                print(f'原始gid列表字段值：{graph_id_list}')
                print(f'原始公司计数：{company_total}')
                old_company_name_list = re.split('[;]', company_name_list)
                old_graph_id_list = re.split('[;]', graph_id_list)
                print(f'原始gid列表数：{len(old_graph_id_list)}')
                print(f'原始name列表数：{len(old_company_name_list)}')
                new_graph_id_list = []
                new_company_name_list = []
                for company_name in old_company_name_list:
                    # 去除空字符串
                    if company_name and len(company_name.strip()) > 3:
                        new_company_name_list.append(company_name.strip())
                for graph_id in old_graph_id_list:
                    gid_log = cg_sever.get_by_gid(graph_id)
                    if gid_log:
                        new_graph_id_list.append(graph_id)
                print(f'新gid列表数：{len(new_graph_id_list)}')
                print(f'新name列表数：{len(new_company_name_list)}')
                if not new_graph_id_list and new_company_name_list:
                    for name in new_company_name_list:
                        company_data = c_sever.get(field='name', value=name)
                        if company_data:
                            cid = company_data.cid
                            gid_data = cg_sever.get_by_cid(cid)
                            if gid_data:
                                gid = gid_data.cgid
                                new_graph_id_list.append(str(gid))
                new_company_total = len(new_graph_id_list)
                print(f'更新的计数字段数：{new_company_total}')
                new_graph_id_list_str = '	;	'.join(new_graph_id_list)
                new_company_name_list_str = '	;	'.join(new_company_name_list)
                print(f'更新的gid列表字段：{new_graph_id_list_str}')
                print(f'更新的company列表字段：{new_company_name_list_str}')
                # 更新
                update_log = qianzhan_sever.update_data_graph_id_list(id_, new_graph_id_list_str, new_company_name_list_str, new_company_total)
                if update_log:
                    update_count += 1
                    print(f'{id_}，更新成功')
                else:
                    fail_count += 1
                    print(f'{id_}, 更新失败')
    print(f'共计：{count}记录,更新成功{update_count}条记录，未更新{fail_count}条记录')