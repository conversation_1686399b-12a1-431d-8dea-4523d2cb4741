# encoding=utf8

from dao.company_staff import CompanyStaffDao


class CompanyStaffSeverDao(CompanyStaffDao):
    def get_id_by_date(self, id_):
        """通过主键ID获取数据记录"""
        sql = f'select * from {self.db_tb_name} where id = %s'
        ret = self.mysql_client.select(sql, args=(id_,))
        return self.entity_class.from_dict(ret)

    def get_cid_hcid_by_date(self, cid, hcid):
        """通过cid+hcid获取数据记录"""
        sql = f'select * from {self.db_tb_name} where company_id = %s and staff_id = %s'
        ret = self.mysql_client.select(sql, args=(cid, hcid))
        return self.entity_class.from_dict(ret)

    def id_update(self, id_, new_hcid, staff_type_name):
        """根据ID更新数据记录"""
        sql = f'update {self.db_tb_name} set staff_id=%s,staff_type_name=%s where id = %s'
        ret = self.mysql_client.execute(sql, args=(new_hcid, staff_type_name, id_))
        return ret


if __name__ == '__main__':
    dao = CompanyStaffSeverDao()
    # 查询
    # print(dao.get_id_by_date(2807848719))
    # 更新
    print(dao.id_update(2781119118, 1351213, '未知'))