# encoding=utf8

from datetime import datetime
from pydantic import Field
from libs.log2 import setup_logger
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao

logger = setup_logger()


class CompanySnapshotInfo(BaseEntity):
    id: int
    companyname: str = Field(default=None)
    source: str = Field(default=None)
    imgurl: str = Field(default=None)
    deleted: int = Field(default=None)
    createtime: datetime = Field(default=None)  # 变更日期
    updatetime: datetime  # 创建时间

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class CompanySnapshotInfoDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_snapshot')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanySnapshotInfo)
        super().__init__(**kwargs)

    def get_data(self, companyname):
        sql = f'select * from {self.db_tb_name} where companyname =%s'
        ret = self.mysql_client.select(sql, args=(companyname,))
        return ret

    def update_time(self, _id, updatetime):
        sql = 'update {} set updatetime=%s  where id=%s limit 1'.format(
            self.db_tb_name)
        ret = self.mysql_client.execute(sql, args=(updatetime, _id))
        return ret == 1

    def query1(self):
        query1 = """SELECT * FROM prism.company_snapshot 
        WHERE (updatetime IS NOT NULL AND updatetime < NOW() - INTERVAL 1 MONTH)  
        OR   
        (updatetime IS NULL AND createtime < NOW() - INTERVAL 1 MONTH)"""
        res = self.mysql_client.select(query1)
        return res


if __name__ == '__main__':
    company_snapshot_sever = CompanySnapshotInfoDao()
    data = company_snapshot_sever.get_data("云南旭浩源机械设备有限公司")
    print(data)
    print(datetime.now())
    # 更新
    new_data = company_snapshot_sever.update_time(36002195, datetime.now())
    print(new_data)
