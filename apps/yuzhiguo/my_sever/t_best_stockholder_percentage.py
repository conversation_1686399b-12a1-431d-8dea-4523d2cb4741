# encoding=utf8

import re
from copy import copy
from datetime import date, datetime
from typing import List, Type, Optional, Any
from pydantic import Field
from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao
from dao.deps.mysql_sharding_dao import MySQL<PERSON>hardingDao
from entity.deps.entity import BaseEntity
from decimal import Decimal


class BestStockholderPercentage(BaseEntity):
    eid: str = Field(default='')  # 企业eid
    percentage: str = Field(default='')  # 股东数据

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


# 分库分表的 MySQLDao
class BestStockholderPercentageDao(MySQLShardingDao):
    def __init__(self, **kwargs):
        super().__init__(sharding_key='eid', pk_name='eid', entity_class=BestStockholderPercentage, **kwargs)

    # 给定v 给出 分库分表位置
    @classmethod
    def do_sharding(cls, v: str) -> int:
        if not re.fullmatch('[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', v):
            raise ValueError('bad format eid={}'.format(v))
        db_id = int('0x' + v[-1], 16)
        tb_id = int('0x' + v[-2], 16)
        return db_id * 16 + tb_id

    @classmethod
    def gen_dao_list(cls, **kwargs) -> List[MySQLDao]:
        return cls.gen_dao_list_qxb(
            db_prefix='db_sub_enterprises',
            tb_prefix='t_best_stockholder_percentage',
            **kwargs,
        )

    @classmethod
    def gen_dao_list_qxb(cls, db_prefix: str, tb_prefix: str, **kwargs) -> List[MySQLDao]:
        dao_list = list()
        for db_id in range(16):
            for tb_id in range(16):
                dao_args_copy = copy(kwargs)
                dao_list.append(
                    MySQLDao(
                        **ConstantProps.PROPS_QX_LIST[db_id],
                        db_tb_name='{}_{}.{}_{}'.format(db_prefix, db_id, tb_prefix, tb_id),
                        **dao_args_copy,
                    )
                )
        return dao_list


if __name__ == '__main__':
    from libs.log import setup_logger

    logger = setup_logger()
    sharding_dao = BestStockholderPercentageDao()
    for i in sharding_dao.get_many(value='b0c6b6af-69f0-4675-9931-ebe5d34605bc', field='eid'):
        print(i)