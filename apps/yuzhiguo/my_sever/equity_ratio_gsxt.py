from typing import List
from datetime import datetime, date
from pydantic import Field, conint
from typing import Optional
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps, get_props_mysql
from dao.deps.mysql_dao import MySQLDao
from gslib.gs_enum import EntityType


class EquityRatioGsxt(BaseEntity):
    id: int = Field(default=None)
    company_graph_id: int = Field(default=None)
    shareholder_type: EntityType = Field(default=None)
    shareholder_graph_id: conint(strict=True, ge=0) = Field(default=None)
    shareholder_name: str = Field(default=None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class EquityRatioGsxtDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in get_props_mysql('mysql.tyc.gs.hw_gsxt_outer_rw').items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.equity_ratio_gsxt')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', EquityRatioGsxt)
        super().__init__(**kwargs)

    def get_by_gid(self, gid) -> List[EquityRatioGsxt]:
        sql = """select * from prism.equity_ratio_gsxt  where company_graph_id =%s"""
        items: List = list()
        for o in self.mysql_client.select_many(sql, args=(gid,)):
            item = self.entity_class().from_dict(o)
            if item is not None:
                items.append(item)
        return items

    def get_by_shareholder_gid(self, gid):
        """通过股东GID获取数据"""
        sql = """select * from prism.equity_ratio_gsxt  where  shareholder_graph_id=%s and deleted = 0 and shareholder_type = 2"""
        items: List = list()
        for o in self.mysql_client.select_many(sql, args=(gid,)):
            item = self.entity_class().from_dict(o)
            if item is not None:
                items.append(item)
        return items

    def update_shareholder_name(self, id_, name):
        sql = """update prism.equity_ratio_gsxt set shareholder_name=%s where id=%s"""
        ret = self.mysql_client.execute(sql, args=(name, id_))
        return ret

    # def to_mysql(self, company_graph_id, company_name, shareholder_graph_id, shareholder_name, shareholder_type, amount,
    #              capital, capitalActl, percent, source):
    #     sql = "INSERT INTO prism.equity_ratio (company_graph_id, company_name, shareholder_graph_id, shareholder_name, shareholder_type, amount, capital, capitalActl, percent, source) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"
    #     insert_id = self.mysql_client.insert(sql, args=(
    #     company_graph_id, company_name, shareholder_graph_id, shareholder_name, shareholder_type, amount, capital,
    #     capitalActl, percent, source))
    #     return insert_id


if __name__ == '__main__':
    sever = EquityRatioGsxtDao()
    # data = sever.get_by_gid('3361910266')
    # print(data)
    # 认缴和实缴需要做字符串写入
    print(sever.get_by_shareholder_gid(*********))
