from typing import List
from gslib.id_center import id_center_query
from datetime import datetime, date
from pydantic import Field
from typing import Optional
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps, get_props_mysql
from dao.deps.mysql_dao import MySQLDao
import re


class QxWholeParkInfo(BaseEntity):
    id: int
    name: str
    yid: str
    self_id: str
    province: str
    city: str
    district: str
    district_code: str
    area: int
    description: str
    ploy: str
    url: str
    last_updated_time: int
    created_time: int
    row_update_time: Optional[datetime] = Field(default=None)
    local_row_update_time: Optional[datetime] = Field(default=None)

    id_: int = Field(alias='id')
    name_: str = Field(alias='name')
    yid_: str = Field(alias='yid')
    company_name: str = Field(alias='company_name')

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class QxWholeParkDao(MySQLDao):
    def __init__(self, **kwargs):

        for k, v in get_props_mysql('mysql.tyc.qxb.b').items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'db_business_reproduce.t_whole_park')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', QxWholeParkInfo)
        super().__init__(**kwargs)

    def get_data(self, yid):
        sql = 'select * from {} where yid =%s '.format(self.db_tb_name)
        ret = self.mysql_client.select(sql, args=(yid,))
        return self.entity_class.from_dict(ret)

    def get_data_join(self, yid):
        sql = ("select * from {} left join db_business_reproduce.t_whole_park_relation on"
               " t_whole_park.yid= t_whole_park_relation.yid where t_whole_park.yid = %s").format(
            self.db_tb_name)
        items: List = list()
        for o in self.mysql_client.select_many(sql, args=(yid,)):
            item = self.entity_class.from_dict(o)
            if item is not None:
                items.append(item)
        return items


if __name__ == '__main__':
    qx_park_sever = QxWholeParkDao()
    # print(qx_park_sever.get_data('4be48974486498a2'))
    data = qx_park_sever.get_data_join('4be48974486498a2')
    print(data)
