from apps.yuzhiguo.my_sever.t_partners import PartnersDao, Partners
from apps.yuzhiguo.my_sever.t_best_stock_info import BestStockInfoDao
from dao.qxb.enterprise import EnterpriseDao, Enterprise
from dao.company import CompanyDao, CompanyGraphDao
from apps.yuzhiguo.my_sever.equity_ratio import EquityRatioDao
from typing import List
from dao.human import HumanGraphDao
import json, time, re
from datetime import datetime
from loguru import logger

partners_dao = PartnersDao()
base_stock_info = BestStockInfoDao()
qx_company_dao = EnterpriseDao()
tyc_sharding_dao = EquityRatioDao()
company_dao = CompanyDao()
company_graph_dao = CompanyGraphDao()
human_graph_dao = HumanGraphDao()


logger.add('20250113_sub_time_supplement.log', encoding='utf-8')


def get_tyc_company(company_graph_id):
    # 获取tyc公司数据
    company_graph_item = company_graph_dao.get(field='graph_id', value=company_graph_id)
    company_cid = company_graph_item.cid
    # company_graph_id 获取 公司名称
    tyc_company_data = company_dao.get(field='id', value=company_cid)
    return tyc_company_data


def get_qx_stock_shareholder_list(eid):
    """获取最佳qx股东数据，都有认缴日期才返回数据"""
    shareholder_list = []
    data = base_stock_info.get_many(value=eid)
    for item in data:
        if item.is_history != 0:
            continue
        if item.con_date is None or item.con_date == '':
            return []
        shareholder_list.append(item)
    return shareholder_list


def get_tyc_shareholder_list(gid):
    """获取tyc股东列表数据"""
    # 获取tyc股东列表数据
    tyc_share_ratio_data = tyc_sharding_dao.get_many(field='company_graph_id', value=gid)
    new_tyc_share_ratio_list = []
    for share_ratio in tyc_share_ratio_data:
        # 无认缴的排除
        if not share_ratio.amount:
            return []
        # 原始股东表有认缴日期的排除
        capital = json.loads(share_ratio.capital)
        try:
            is_time = capital[0]['time']
        except:
            is_time = ''
        if is_time:
            return []
        # 过滤上市和非上市股份公司股东
        if share_ratio.source != 100:
            new_tyc_share_ratio_list.append(share_ratio)
    return new_tyc_share_ratio_list


def get_is_shareholder_name_subscription_same(qx_shareholder_list, tyc_shareholder_list):
    """判断股东名称和认缴是否一致"""
    if not tyc_shareholder_list or not qx_shareholder_list:
        return False
    qx_shareholder = []
    tyc_shareholder = []
    for shareholder in qx_shareholder_list:
        shareholder_name = shareholder.stock_name
        sub_num = float(shareholder.should_capi)
        qx_version = '_'.join([shareholder_name, str(sub_num)])
        qx_shareholder.append(qx_version)
    for shareholder in tyc_shareholder_list:
        shareholder_name = shareholder.shareholder_name
        sub_num = shareholder.amount
        tyc_version = '_'.join([shareholder_name, str(sub_num)])
        tyc_shareholder.append(tyc_version)
    # 判断两个列表是否一致
    return set(qx_shareholder) == set(tyc_shareholder)


def check_sub_time(sub_time, company):
    """检验认缴日期是否符合规范，并且大于等于成日日期"""
    # 检查日期格式
    if not re.match(r'^\d{4}-\d{2}-\d{2}$', sub_time):
        return False

    # 解析日期
    parsed_sub_time = datetime.strptime(sub_time, "%Y-%m-%d").date()

    # 检查日期是否大于等于成立日期
    return parsed_sub_time >= company.establish_date


def run(company_graph_id, eid):
    """通过qx补充认缴日期"""
    company_data = get_tyc_company(company_graph_id)
    qx_shareholder_list = get_qx_stock_shareholder_list(eid)
    # qx股东列表都有认缴日期的情况下才有数据
    if qx_shareholder_list:
        tyc_shareholder_list = get_tyc_shareholder_list(company_graph_id)
        print(qx_shareholder_list)
        print(tyc_shareholder_list)
        # 判断股东列表是否相同认缴是否相同 都相同的情况下补充认缴
        is_shareholder_name_subscription_same = get_is_shareholder_name_subscription_same(qx_shareholder_list,
                                                                                          tyc_shareholder_list)
        if is_shareholder_name_subscription_same:
            tyc_shareholder_dict = {tyc_shareholder.shareholder_name: tyc_shareholder for tyc_shareholder in
                                    tyc_shareholder_list}
            for qx_shareholder in qx_shareholder_list:
                # 使用字典查找，避免嵌套循环
                tyc_shareholder = tyc_shareholder_dict.get(qx_shareholder.stock_name)
                if tyc_shareholder:
                    tyc_id = tyc_shareholder.id
                    name = tyc_shareholder.shareholder_name
                    sub_time = qx_shareholder.con_date
                    # 认缴日期检验 大于等于成立日期
                    if not check_sub_time(sub_time, company_data):
                        continue
                    capital_json = json.loads(tyc_shareholder.capital)
                    for capital in capital_json:
                        if not capital.get('time'):
                            capital['time'] = sub_time
                    capital_str = json.dumps(capital_json, ensure_ascii=False)
                    # tyc_sharding_dao.update_capital(tyc_id, capital_str)
                    logger.info(f"company_graph_id:{company_graph_id}, tyc_id:{tyc_id}, name:{name}, capital_str:{capital_str}")
                    with open('20250113_sub_time_out.csv', 'a', encoding='utf-8', newline='') as f:
                        witer = csv.writer(f)
                        witer.writerow([company_graph_id, tyc_id, name, capital_str])


if __name__ == '__main__':
    import csv

    with open('20250106_gid_eid.csv', 'r', encoding='utf-8') as f:
        data = csv.DictReader(f)
        count = 0
        for i in data:
            if count > 1000:
                break
            dict_data = dict(i)
            company_graph_id = dict_data['t2.company_graph_id']
            eid = dict_data['qx.eid']
            logger.info(f"company_graph_id:{company_graph_id}, eid:{eid}, count:{count}")
            run(company_graph_id, eid)
            count += 1

    # eid = 'e778007c-5543-4533-915d-77cf4ba5f8f4'
    # company_graph_id = 6970675434
    # run(company_graph_id, eid)

