from my_sever.equity_ratio import EquityRatioDao
from my_sever.equity_ratio_test import EquityRatioTestDao
from typing import List, Dict, Any
from my_sever.equity_ratio import EquityRatio
from my_sever.equity_ratio_test import EquityRatioTest


def compare_equity_ratios(equity_ratio_list: List[EquityRatio], equity_ratio_test_list: List[EquityRatioTest]) -> List[Dict[str, Any]]:
    # 定义要对比的字段
    fields_to_compare = [
        "company_graph_id",
        "shareholder_type",
        "shareholder_graph_id",
        "shareholder_name",
        "amount",
        "capital",
        "capitalActl",
        "percent",
        "source"
    ]

    # 将 equity_ratio_list 和 equity_ratio_test_list 转换为字典，方便对比
    equity_ratio_dict = {item.shareholder_graph_id: item for item in equity_ratio_list}
    equity_ratio_test_dict = {item.shareholder_graph_id: item for item in equity_ratio_test_list}

    # 对比结果
    discrepancies = []

    # 检查 equity_ratio_list 中的项
    for gid, equity_ratio in equity_ratio_dict.items():
        if gid in equity_ratio_test_dict:
            # 对比字段值
            for field in fields_to_compare:
                value_in_equity_ratio = getattr(equity_ratio, field)
                value_in_equity_ratio_test = getattr(equity_ratio_test_dict[gid], field)

                if value_in_equity_ratio != value_in_equity_ratio_test:
                    discrepancies.append({
                        "shareholder_graph_id": gid,
                        "field": field,
                        "value_in_equity_ratio": value_in_equity_ratio,
                        "value_in_equity_ratio_test": value_in_equity_ratio_test
                    })
        else:
            discrepancies.append({
                "shareholder_graph_id": gid,
                "message": "Not found in equity_ratio_test_list"
            })

    # 检查 equity_ratio_test_list 中的项
    for gid in equity_ratio_test_dict.keys():
        if gid not in equity_ratio_dict:
            discrepancies.append({
                "shareholder_graph_id": gid,
                "message": "Not found in equity_ratio_list"
            })

    return discrepancies

gid = 3089854392

equity_ratio_sever = EquityRatioDao()
equity_ratio_test_sever = EquityRatioTestDao()



gids = [
5092384851,
5157362255,
5781337839,
6603347731,
7330676584,
6731665697,
6644019470,
6404421713,
3145619079,
3392895807,
5656650817,
6474526299,
6232285118,
6507805781,
3090368810,
4352081314,
6208076721,
6430879826,
5808455014,
6961914123,
7270054170,
3331583038,
40248878,
6534737938,
5679516122,
1043713809,
3352506314,
7372110620,
2330245687,
3205200775,
6689206629,
5076010951,
7244956280,
2453420220,
5241987153,
4400911856,
3029818850,
2358549102,
5947490002,
5348467850,
4628081772,
3279756144,
5819373096,
3198657074,
7373361748,
6606279053,
7263507203,
5363496917,
3069810974,
3467798349,
7374103843,
4090044295,
4007992521,
2367799565,
5558035823,
4182203576,
5261048615,
6392119159,
3299451895,
7330632400,
6523410206,
7377322887,
7122144925,
3173094922
]
for gid in gids:
    equity_ratio_list = equity_ratio_sever.get_by_gid(gid)
    equity_ratio_test_list = equity_ratio_test_sever.get_by_gid(gid)
    diff = compare_equity_ratios(equity_ratio_list, equity_ratio_test_list)
    if diff:
        log = '不一致'
    else:
        log = '一致'
    print(gid, log)