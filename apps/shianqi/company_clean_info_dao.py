# encoding=utf8

import logging
from typing import Optional
from datetime import datetime, date, timedelta
from pydantic import Field, conint
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from libs.collection import sub_if
from gslib.reg_number import reg_number_valid
from gslib.credit_code import credit_code_valid
from gslib.gs_enum import EntityType

logger = logging.getLogger(__name__)


class CompanyCleanInfo(BaseEntity):
    cid: int = Field(alias='id')
    reg_capital_amount: Optional[int] = Field(default=None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def logic_validate(self) -> bool:
        if not credit_code_valid(self.credit_code):
            return False
        if not reg_number_valid(self.reg_number):
            return False
        if self.establish_date:
            if self.establish_date < date(year=1970, month=1, day=1):
                return False
            if self.establish_date > (datetime.now() + timedelta(days=30)).date():
                return False
            if self.approved_date and self.approved_date < self.establish_date:
                return False
        return True

    def __hash__(self):
        return self.cid


class CompanyCleanInfoDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.company_clean_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', CompanyCleanInfo)

        super().__init__(**kwargs)

    def i_u_by_any(self, *args, **kwargs):
        item = CompanyCleanInfo.from_dict(args[0])
        self.save_by_pk(item)


if __name__ == '__main__':
    from libs.log import setup_logger
    logger = setup_logger()
    dao = CompanyCleanInfoDao(batch_size=1000, entity_class=None)
    for o in dao.scan(total=200, start=0):
        logger.info('%s', o)
