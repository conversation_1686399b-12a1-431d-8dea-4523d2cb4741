import datetime
import json

from apps.shianqi.equity_ratio import EquityRatio, EquityRatioDao
from compare import CompareService
from dao.company import Company, CompanyDao
from dao.company_graph import CompanyGraphDao, CompanyGraph
from dao.company_hk import CompanyHkDao, CompanyHk
from dao.id_center import CreditCodeIndexDao
from dao.qcc.general_crawl_task_input import GeneralCrawlTaskInput, GeneralCrawlTaskInputDao
from dao.qcc.general_crawl_task_output import GeneralCrawlTaskOutput, GeneralCrawlTaskOutputDao

if __name__ == '__main__':
    con = {
        'credit_code': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False
        },
        'holder_type': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'amount': {
            'type': 'str',
            'need_split': False,
            'is_json': False,
            # 'is_array': True,
        },
        'share_percent': {
            'type': 'str',
            'need_split': False,
            'is_json': False,
            # 'is_array': True,
        },
    }
    compare_service = CompareService()
    general_input_dao = GeneralCrawlTaskInputDao()
    general_output_dao = GeneralCrawlTaskOutputDao()
    credit_dao = CreditCodeIndexDao()
    company_graph_dao = CompanyGraphDao()
    equity_dao = EquityRatioDao()
    company_dao = CompanyDao()
    company_hk_dao = CompanyHkDao()
    task_name = 'pBetter_firm_page_normal'
    task_name_holder = 'pBetter_holder_normal'
    today = datetime.date.today()
    yes = today - datetime.timedelta(days=2)
    create_time = str(yes) + ' 00:00:00'
    yes_yes_yes = today - datetime.timedelta(days=4)
    create_time_yes_yes = str(yes_yes_yes) + ' 00:00:00'
    all_equal = 0
    count = 0
    max_id = 1
    tyc_has = 0
    qcc_has = 0
    equal_not_equal_res = dict()
    all_has_but_position_not_equal = 0
    size_not_equal = 0
    size_not_equal_array = []
    for i in range(1000):
        items = general_input_dao.get_many_by_task_name_and_id(task_name, create_time, max_id)
        if items:
            for item in items:
                try:
                    item: GeneralCrawlTaskInput
                    if max_id < item.id:
                        max_id = item.id
                    if item.status != 1:
                        # 任务不是完成状态
                        continue
                    print('获取到任务' + str(item.id))
                    out_item = general_output_dao.get_by_input_id('input_id', item.id)
                    if not out_item:
                        continue
                    out_item: GeneralCrawlTaskOutput
                    out_res = out_item.output

                    out_json = json.loads(out_res)
                    credit_code = out_json['credit_code']
                    if not credit_code:
                        continue
                    firm_id = out_json['long_firm_id']
                    item_staff = general_input_dao.get_one_by_task_name_and_firm_id(task_name_holder, create_time_yes_yes,
                                                                                    firm_id)
                    item_staff: GeneralCrawlTaskInput
                    if not item_staff:
                        continue
                    staff_input_id = item_staff.id
                    # 查找qcc抓取主要人员的结果
                    staff_out_item = general_output_dao.get_by_input_id('input_id', staff_input_id)
                    staff_out_item: GeneralCrawlTaskOutput
                    if not staff_out_item:
                        continue
                    credit_res = credit_dao.get(credit_code)
                    if not credit_res:
                        continue
                    cid = credit_res['company_id']
                    company_graph_item = company_graph_dao.get_by_cid(cid)
                    company_graph_item: CompanyGraph
                    if not company_graph_item:
                        continue
                    # 判断公司股份有限公司
                    company_res = company_dao.get(cid)
                    if not company_res:
                        continue
                    company_res: Company

                    # 目前为止，可以判断股东
                    count = count + 1

                    gid = company_graph_item.cgid
                    companyStaffList = equity_dao.get_many('company_graph_id', gid)
                    compare_tyc = []
                    compare_qcc = []
                    staff_output = staff_out_item.output
                    staff_out_json = json.loads(staff_output)
                    if 'holder' in staff_out_json:
                        staff_out_json_1 = staff_out_json['holder']
                        if 'holder' in staff_out_json_1:
                            qcc_employees = staff_out_json_1['holder']
                        else:
                            qcc_employees = staff_out_json_1
                        company_type = company_res.company_org_type
                        if company_type and '股份' in company_type and '非上市' in company_type:
                            qcc_employees = []
                            if 'ipo_holder' in staff_out_json:
                                qcc_employees = staff_out_json['ipo_holder']
                            else:
                                staff_out_json_2 = staff_out_json['holder']
                                if 'ipo_holder' in staff_out_json_2:
                                    qcc_employees = staff_out_json_2['ipo_holder']
                        if qcc_employees:
                            len_qcc = len(qcc_employees)
                            if len_qcc > 10:
                                all_equal = all_equal + 1
                                continue
                            for qcc_it in qcc_employees:
                                share_percent = qcc_it['share_percent']
                                percent_amount = '%.2f' % 0.0
                                if share_percent:
                                    share_percent: str
                                    share_percent = share_percent.replace('%', '')
                                    percent_amount = '%.2f' % float(share_percent)
                                amount_detail = qcc_it['subscribed_capital_detail']
                                amount_flo = ''
                                # amount_flo = '%.2f' % 0.0
                                # if amount_detail:
                                #     qcc_detail_fol = 0
                                #     for it_amount_qcc_detail in amount_detail:
                                #         should_capi = it_amount_qcc_detail['ShouldCapi']
                                #         should_capi:str(should_capi)
                                #         should_capi = should_capi.replace(',', '')
                                #         if should_capi:
                                #             qcc_detail_fol = qcc_detail_fol + float(should_capi)
                                #     amount_flo = '%.2f' % qcc_detail_fol

                                # amount = qcc_it['subscribed_capital']
                                # amount: str(amount)
                                # amount = amount.replace(',', '')
                                # if amount:
                                #     amount_flo = '%.2f' % float(amount)
                                holder_type = qcc_it['holder_type']
                                inv_firm_id = qcc_it['long_firm_id']
                                if holder_type == -1:
                                    type = 2
                                elif holder_type == 0:
                                    type = 2
                                elif holder_type == 3:
                                    type = 2
                                elif holder_type == 2:
                                    type = 1
                                else:
                                    type = 2
                                credit_code = ''
                                if inv_firm_id and type == 2:
                                    inv_inpt_it = general_input_dao.get_one_by_task_name_and_firm_id(task_name,
                                                                                                     create_time,
                                                                                                     inv_firm_id)
                                    inv_inpt_it: GeneralCrawlTaskInput
                                    if inv_inpt_it:
                                        inv_input_id = inv_inpt_it.id
                                        # 查找qcc抓取主要人员的结果
                                        inv_out_item = general_output_dao.get_by_input_id('input_id', inv_input_id)
                                        inv_out_item: GeneralCrawlTaskOutput
                                        if inv_out_item:
                                            inv_out_json = json.loads(inv_out_item.output)
                                            credit = inv_out_json['credit_code']
                                            if credit:
                                                credit_code = credit
                                elif type == 1:
                                    credit_code = qcc_it['holder_name']
                                compare_qcc.append({
                                    'holder_name': qcc_it['holder_name'],
                                    'credit_code': credit_code,
                                    'holder_type': type,
                                    'amount': amount_flo,
                                    'share_percent': percent_amount,
                                    'am_us': float(percent_amount)
                                })
                            # 通过资比例
                            compare_qcc = sorted(compare_qcc, key=lambda x: (x['am_us'], x['holder_name']))
                    if companyStaffList:
                        for tyc_staff in companyStaffList:
                            tyc_staff: EquityRatio
                            amount = tyc_staff.amount
                            amount_flo = '%.2f' % 0.0
                            if amount:
                                amount_flo = '%.2f' % amount
                            percent = tyc_staff.percent
                            percent_amount = '%.2f' % 0.0
                            if percent:
                                percent_las = percent * 100
                                percent_amount = '%.2f' % percent_las
                            graph_id = tyc_staff.shareholder_graph_id
                            credit_code = ''
                            if tyc_staff.shareholder_type.value == 2:
                                company_graph_item = company_graph_dao.get_by_gid(graph_id)
                                company_graph_item: CompanyGraph
                                if company_graph_item:
                                    inv_company_res = company_dao.get(company_graph_item.cid)
                                    inv_company_res: Company
                                    if inv_company_res:
                                        base = inv_company_res.base
                                        if base and base == 'hk':
                                            company_res_hk = company_hk_dao.get_by_cid(inv_company_res.cid)
                                            if company_res_hk:
                                                company_res_hk: CompanyHk
                                                credit_code = company_res_hk.company_num
                                            print('===hk' + str(cid) + ',firm_id' + firm_id)
                                        else:
                                            credit_code = inv_company_res.credit_code
                            else:
                                credit_code = tyc_staff.shareholder_name
                            compare_tyc.append({
                                'holder_name': tyc_staff.shareholder_name,
                                'credit_code': credit_code,
                                'holder_type': tyc_staff.shareholder_type.value,
                                # 'amount': amount_flo,
                                'amount': '',
                                'share_percent': percent_amount,
                                'am_us': float(percent_amount)
                            })
                        compare_tyc = sorted(compare_tyc, key=lambda x: (x['am_us'], x['holder_name']))
                    # 数量不一致
                    if len(compare_tyc) > 10:
                        all_equal = all_equal + 1
                        print('holder----all--equal')
                        continue
                    if len(compare_tyc) != len(compare_qcc):
                        size_not_equal = size_not_equal + 1
                        arr_tyc = []
                        arr_qcc = []
                        for it_tyc in compare_tyc:
                            arr_tyc.append(it_tyc['holder_name'])
                        for it_qcc in compare_qcc:
                            arr_qcc.append(it_qcc['holder_name'])
                        size_not_equal_array.append({
                            'cid': cid,
                            'firm_id': firm_id,
                            'diff': ','.join(arr_tyc) + '_' + ','.join(arr_qcc)
                        })
                        continue
                    # 暂时只比较数量
                    all_equal = all_equal + 1
                    print('holder----all--equal')
                    continue
                    # res = compare_service.compare_holder(compare_tyc, compare_qcc, con)
                    # if not res['a_has_res'] and not res['b_has_res'] and not res['equal_not_equal']:
                    #     all_equal = all_equal + 1
                    #     print('holder----all--equal')
                    #     continue
                    # if res['equal_not_equal']:
                    #     all_has_but_position_not_equal = all_has_but_position_not_equal + 1
                    # equal_not_equal_res[str(cid) + '_' + firm_id] = {
                    #     'equal_not_equal':res['equal_not_equal'],
                    #     'detail_tyc': compare_tyc,
                    #     'detail_qcc': compare_qcc,
                    # }
                    # print('=====')
                    # print('====cid:' + str(cid) + ',firm_id:' + firm_id + ',' + str(res))
                except Exception as e:
                    print('执行异常，继续下一个任务')
                    print(e)
    print('最终结果====' + str(equal_not_equal_res))
    print('最终结果股东数量不一致====' + str(size_not_equal_array))
    print(
        '最终结果count:' + str(count) + ',股东数量不一致:' + str(size_not_equal) + ',tyc_has:' + str(
            tyc_has) + ',qcc_has:' + str(
            qcc_has) + ',all_has_but_position_not_equal:' + str(
            all_has_but_position_not_equal))
    print('all_equal:' + str(all_equal))

    # compare_a = []
    # compare_b = []
    # compare_a.append({
    #     'staff_name': '张三',
    #     'sta_ff_type_name': '董事长'
    # })
    # compare_a.append({
    #     'staff_name': '李四',
    #     'sta_ff_type_name': '监事'
    # })
    # compare_b.append({
    #     'staff_name': '张三',
    #     'sta_ff_type_name': '董事长,总经理'
    # })
    # con = {
    #     'staff_name': {
    #         'type': 'str',  # 'double','int'
    #         'need_split': False,
    #         'is_json': False,
    #         # 'is_array': False
    #     },
    #     'sta_ff_type_name': {
    #         'type': 'str',
    #         'need_split': True,
    #         'is_json': False,
    #         # 'is_array': True,
    #     },
    # }
    # res = compare_service.compare_array(compare_b, compare_a, con)
    # print('=====')
    # print(res)
