import json
import logging
import time
from datetime import datetime
from typing import Optional

import ddddocr
from pydantic import Field, conint

# from PIL import Image
# from io import BytesIO
# from requests import Session, Response
from clients.redis._redis import Redis
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from libs.req import SessionRequestManager
from libs.concurrent import BoundedExecutor
from dao.company_reg_capital import CompanyRegCapital,CompanyRegCapitalDao
from dao.company import CompanyDao,Company

redis_client = Redis(host='redis-bc8ade66-d5d2-4653-9aaf-6401f43b3479.cn-north-4.dcs.myhuaweicloud.com', port=6379,
                     password='s3zSjZaxduont9qg0pcv', db=5)
# redis_client = Redis(host='redis-bc8ade66-d5d2-4653-9aaf-6401f43b3479.cn-north-4.dcs.myhuaweicloud.com', port=6379,
#                      password='s3zSjZaxduont9qg0pcv', db=5)
redis_key_list = 'company_double_check_to_redis_saq'

redis_client_double_check = Redis(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com', port=6379,
                     password='3lvadmpiSj61ge', db=0)

if __name__ == '__main__':
    count = 0
    for i in range(3330000):
        eid = redis_client.rpop(redis_key_list)
        if eid:
            print('获取到的任务:' + eid)
            count = count + 1
            redis_client_double_check.sadd('qxb_gs_other_dimension_eid_set', eid)
            time.sleep(0.1)
        else:
            break