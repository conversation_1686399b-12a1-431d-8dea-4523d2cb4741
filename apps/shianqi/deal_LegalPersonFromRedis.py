import json
import logging
import requests
import time
from datetime import datetime
from typing import Optional

import ddddocr
from pydantic import Field, conint

# from PIL import Image
# from io import BytesIO
# from requests import Session, Response
from clients.redis._redis import Redis
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from libs.req import SessionRequestManager
from libs.concurrent import BoundedExecutor
from dao.company_reg_capital import CompanyRegCapital,CompanyRegCapitalDao
from dao.company import CompanyDao,Company

redis_client = Redis(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com', port=6379,
                     password='3lvadmpiSj61ge', db=5)
# redis_client = Redis(host='redis-bc8ade66-d5d2-4653-9aaf-6401f43b3479.cn-north-4.dcs.myhuaweicloud.com', port=6379,
#                      password='s3zSjZaxduont9qg0pcv', db=5)
redis_key_list = 'dealCompanyLegalPersonNameToRedis'

def pitch_your_words(code):
    gsxt_touch_change = 'http://*************/gsxt/v2/update/merge'
    params = {"companySign": code, "record": "company_base_info", "channelType": 2,
              "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
                          "change_dims": ["company_base_info"]}}
    res = requests.post(gsxt_touch_change, json=params)
    date = res.json()
    print(date)

if __name__ == '__main__':
    company_dao = CompanyDao()
    for i in range(30000):
        cid_str = redis_client.rpop(redis_key_list)
        if cid_str:
            print('获取到的任务:' + cid_str)
            company_res = company_dao.get(int(cid_str))
            company_res: Company
            credit_code = company_res.credit_code
            if credit_code:
                pitch_your_words(credit_code)
            else:
                print('不存在统一信用代码' + cid_str)
        else:
            break

