import datetime
import requests
from typing import Dict, Optional, List
from apps.equity_ratio_ng.msv import MSVBaseInfoVersion, MSVCompanyBase
from apps.shianqi.equity_ratio import EquityRatioDao
from dao.company import Company, CompanyDao, CompanyPercentageDao
from dao.company_graph import CompanyGraphDao
from dao.company_hk import CompanyHkDao
from dao.id_center import CreditCodeIndexDao
import re
from decimal import Decimal


def msv_get_all(cid: int, table):
    msv_params = {"table_name": table, "cid": cid}
    try:
        response = requests.post(
            url='http://msv-gsdata.jindidata.com/read_list_version',
            headers={
                'Content-Type': 'application/json',
            },
            json=msv_params,
            timeout=5,
        )
    except requests.exceptions.ReadTimeout:
        return None
    if response.status_code != 200:
        return None
    js = response.json()['data']
    return js

def compare_base_info(compare_a: dict, compare_b: dict, config: dict):
    all_equal = True
    a_has_res = []
    b_has_res = []
    equal_not_equal = []
    keys = list(config.keys())
    for key_name in keys:
        if key_name == 'reg_status':
            value_a = compare_a[key_name]
            value_b = compare_b[key_name]
            # if value_a and('正常' in value_a or '存续' in value_a or '在营' in value_a or '开业' in value_a or '在册' in value_a or '在业' in value_a):
            #     value_a = '正常'
            # if value_b and ('正常' in value_b or '存续' in value_b or '在营' in value_b or '开业' in value_b or '在册' in value_b or '在业' in value_b):
            #     value_b = '正常'
            if value_a and not value_b:
                all_equal = False
                a_has_res.append(key_name + ':tyc_' + value_a + ',qcc_null')
            elif not value_a and value_b:
                all_equal = False
                b_has_res.append(key_name + ':tyc_null' + ',qcc_' + value_b)
            elif not value_a and not value_b:
                continue
            elif value_a != value_b:
                all_equal = False
                equal_not_equal.append(key_name + ':tyc_' + value_a + ',qcc_' + value_b)
        else:
            if compare_a[key_name] and not compare_b[key_name]:
                all_equal = False
                a_has_res.append(key_name + ':tyc_' + compare_a[key_name] + ',qcc_null')
            elif not compare_a[key_name] and compare_b[key_name]:
                all_equal = False
                b_has_res.append(key_name + ':tyc_null' + ',qcc_' + compare_b[key_name])
            elif not compare_a[key_name] and not compare_b[key_name]:
                continue
            else:
                val_a = str(compare_a[key_name]).replace('（', "(").replace('）', ')')
                val_b = str(compare_b[key_name]).replace('（', "(").replace('）', ')')
                if val_a != val_b:
                    all_equal = False
                    equal_not_equal.append(key_name + ':tyc_' + compare_a[key_name] + ',qcc_' + compare_b[key_name])
    result = {
        'all_equal':all_equal,
        'a_has_res':a_has_res,
        'b_has_res':b_has_res,
        'equal_not_equal':equal_not_equal
    }
    return result
def deal_reg_cap(str_a):
    if not str_a or str_a == '万':
        reg_qcc = '0'
    else:
        if '.' in str_a:
            numbers = re.findall(r'\d+\.\d+', str_a)
        else:
            numbers = re.findall(r'\d+', str_a)
        if not numbers or len(numbers) == 0:
            return '0'
        num = Decimal(numbers[0])
        num_s = Decimal('1000000')
        int_res = num * num_s
        reg_qcc = str(int_res.to_integral() // 100)
    return reg_qcc
def build_compare_res(msv_item):
    s = {
        'reg_status':msv_item['reg_status'],
        'estiblish_time': msv_item['estiblish_time'],
        'name': msv_item['name'],
        'reg_capital': msv_item['reg_capital'],
        'company_org_type': msv_item['company_org_type'],
        'from_time': msv_item['from_time'],
        'to_time': msv_item['to_time'],
        'approved_time': msv_item['approved_time'],
        'reg_institute':msv_item['reg_institute'],
        'reg_location': msv_item['reg_location']
    }
    return s

if __name__ == '__main__':

    con = {
        'reg_status': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'estiblish_time': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'name': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'reg_capital': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'company_org_type': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'from_time': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'to_time': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'approved_time': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'reg_institute': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'reg_location': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
    }

    credit_dao = CreditCodeIndexDao()
    company_graph_dao = CompanyGraphDao()
    equity_dao = EquityRatioDao()
    company_dao = CompanyDao()
    company_percentage_dao = CompanyPercentageDao()
    company_hk_dao = CompanyHkDao()
    task_name = 'pBetter_firm_page_normal'
    task_name_holder = 'pBetter_holder_normal'
    today = datetime.date.today()
    yes = today - datetime.timedelta(days=1)
    create_time = str(yes) + ' 00:00:00'
    all_equal = 0
    count = 0
    all_company = 0
    all_count = 0
    has_need_del_company = 0
    has_need_del_count = 0

    equal_not_equal_res = dict()
    all_has_but_position_not_equal = 0
    size_not_equal = 0
    size_not_equal_array = []
    min_id = 1
    cal = 0
    for i in range(100000):
        if (i % 1000) == 0:
            print('统计结果总量:' + str(all_count) + ',可以删除:' + str(has_need_del_count))
        res = company_percentage_dao.get_many_by_min_id(min_id)
        if res:
            for item in res:
                # try:
                    item: Company
                    cid = item.cid
                    if cid > min_id:
                        min_id = cid
                    cal = cal + 1
                    if cal > 3:
                        cal = 0
                    if cal > 1:
                        continue
                    print('cid:' + str(cid))
                    if cid == 500:
                        print('==')
                    msv_res = msv_get_all(cid, 'company_base_info')
                    if not msv_res:
                        continue
                    msv_keys = msv_res.keys()
                    if msv_keys:
                        # xa
                        for key in msv_keys:
                            items_msv = msv_res[key]
                            source_set = set()
                            if not items_msv:
                                continue
                            all_count = all_count + 1
                            if len(items_msv) == 1:
                                continue
                            len_arr = len(items_msv)
                            for i in range(len_arr - 1):
                                all_count = all_count + 1
                                if not items_msv[i] or not items_msv[i + 1]:
                                    print('存在为空数组的情况cid:' + str(cid) + ',source:' + key + ',i:' + str(i))
                                    continue
                                a_len_arr = items_msv[i][0]
                                b_len_arr = items_msv[i + 1][0]
                                if not a_len_arr or not b_len_arr:
                                    print('存在为空数组的情况cid:' + str(cid) + ',source:' + key + ',i:' + str(i))
                                    continue
                                a_compare_val = build_compare_res(a_len_arr)
                                b_compare_val = build_compare_res(b_len_arr)
                                a_compare_val['reg_capital'] = deal_reg_cap(a_compare_val['reg_capital'])
                                b_compare_val['reg_capital'] = deal_reg_cap(b_compare_val['reg_capital'])
                                res = compare_base_info(a_compare_val, b_compare_val, con)
                                if not res['a_has_res'] and not res['b_has_res'] and not res['equal_not_equal']:
                                    print('数据相同需要删除cid:'+str(cid)+',source:' + key + ',i:' + str(i))
                                    has_need_del_count = has_need_del_count + 1
                                    continue
                                else:
                                    if res['a_has_res']:
                                        print('数据不一致cid:' + str(cid) + ',source:' + key +',' + str(i) + ',a_has_res:' + str(res['a_has_res']))
                                    if res['b_has_res']:
                                        print('数据不一致cid:' + str(cid) + ',source:' + key +',' + str(i)+ ',b_has_res:' + str(res['b_has_res']))
                                    if res['equal_not_equal']:
                                        print('数据不一致cid:' + str(cid) + ',source:' + key +',' + str(i)+ ',equal_not_equal:' + str(
                                            res['equal_not_equal']))
                            # xa下面的list
                            # base_arr = []
                            # for it in items_msv:
                            #     if not it:
                            #         continue
                            #     msv_data = it.data
                            #     if msv_data:
                            #         for company_base in msv_data:
                                        # approve = company_base.approved_date
                                        # all_count = all_count + 1
                                        # if approve:
                                        #     time_str = approve.strftime('%Y-%m-%d %H:%M:%S')
                                        #     if time_str in source_set:
                                        #         has_need_del_count = has_need_del_count + 1
                                        #         print('need cid:' + str(cid) + ',source:' + key)
                                        #     else:
                                        #         source_set.add(time_str)
                                        # else:
                                        #     time_str = 'null'
                                        #     if time_str in source_set:
                                        #         has_need_del_count = has_need_del_count + 1
                                        #         print('need cid:' + str(cid) + ',source:' + key)
                                        #     else:
                                        #         source_set.add(time_str)
                # except:
                #     print('执行失败')
    print('统计结果总量:' + str(all_count) + ',可以删除:' + str(has_need_del_count))
