import requests
from compare import CompareService
from dao.qcc.general_crawl_task_input import GeneralCrawlTaskInput, GeneralCrawlTaskInputDao
from dao.qcc.general_crawl_task_output import GeneralCrawlTaskOutput, GeneralCrawlTaskOutputDao
import json
from dao.id_center import CreditCodeIndexDao
from dao.company_graph import CompanyGraphDao, CompanyGraph
from dao.company_staff_sort import CompanyStaffSortDao, CompanyStaffSort
import datetime
from dao.company import CompanyDao, Company
import time
def pitch_your_words(code):
    gsxt_touch_change = 'http://172.24.114.24/gsxt/v2/update/merge'
    # params = {"companySign": code, "record": "investor_repair", "channelType": 2,
    #           "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
    #                       "change_dims": "company_base_info"}}
    params = {"companySign": code, "record": "base_info_repair", "channelType": 2,
              "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
                          "change_dims": ["company_base_info"]}}
    # params = {"companySign": code, "record": "status_repair", "channelType": 2,
    #           "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
    #                       "change_dims": "company_base_info"}}
    res = requests.post(gsxt_touch_change, json=params)
    date = res.json()
    print('code:' + code)
    print(date)
if __name__ == '__main__':
    company_graph_dao = CompanyGraphDao()
    company_dao = CompanyDao()
    for name in ['343038']:
        # input_path = f'/Users/<USER>/Desktop/chuming.log'
        input_path = f'/Users/<USER>/Downloads/query-hive-415403.csv'
        # input_path = f'/home/<USER>/pygs-work-parent/apps/shianqi/chuming.log'
        # input_path = f'/home/<USER>/wangbangxu/pygs/temp/shianqi/无人员的公司数据.csv'
        with open(input_path, 'r') as f:
            send_data = []
            for lid, line in enumerate(f):
                credit_code, *_ = line.strip().split(',')
                if lid == 0:
                    continue
                if lid % 100 == 0:
                    print('休眠20s')
                    time.sleep(10)
                pitch_your_words(credit_code)
