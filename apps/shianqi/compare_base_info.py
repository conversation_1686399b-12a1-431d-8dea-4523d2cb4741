from compare import CompareService
from dao.qcc.general_crawl_task_input import GeneralCrawlTaskInput, GeneralCrawlTaskInputDao
from dao.qcc.general_crawl_task_output import GeneralCrawlTaskOutput, GeneralCrawlTaskOutputDao
import json
from dao.id_center import CreditCodeIndexDao
from dao.company_graph import CompanyGraphDao, CompanyGraph
from dao.company_staff_sort import CompanyStaffSortDao, CompanyStaffSort
from dao.company import CompanyDao, Company
import datetime
from company_clean_info_dao import CompanyCleanInfoDao, CompanyCleanInfo
import re
import unicodedata
from decimal import Decimal

def match_replace(pattern_s, body:str):
    if not body:
        return body
    match = re.search(pattern_s, body)
    if match:
        ma = match.group()
        return body.replace(ma, '')
    return body

def convert_fullwidth_to_halfwidth(text):
    if not text:
        return text
    return ''.join([unicodedata.normalize('NFKC', char) for char in text]).replace(' ','')


if __name__ == '__main__':
    con = {
        'reg_status': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'establish_date': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'reg_capital': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'company_org_type': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'from_date': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'to_date': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'reg_institute': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'reg_location': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
    }
    compare_service = CompareService()
    company_dao = CompanyDao()
    general_input_dao = GeneralCrawlTaskInputDao()
    general_output_dao = GeneralCrawlTaskOutputDao()
    credit_dao = CreditCodeIndexDao()
    company_graph_dao = CompanyGraphDao()
    company_staff_dao = CompanyStaffSortDao()
    company_clean_dao = CompanyCleanInfoDao()

    task_name = 'pBetter_firm_page_normal'
    # task_name_staff = 'pBetter_employees_normal'
    today = datetime.date.today()
    yes = today - datetime.timedelta(days=2)
    create_time = str(yes) + ' 00:00:00'
    count = 0
    max_id = 1
    all_equal = 0
    tyc_has = 0
    qcc_has = 0
    all_has_but_position_not_equal = 0
    equal_not_equal_res = dict()
    tyc_has_array_dic = dict()
    qcc_has_array_dic = dict()
    for i in range(100):
        items = general_input_dao.get_many_by_task_name_and_id(task_name, create_time, max_id)
        if items:
            for item in items:
                item: GeneralCrawlTaskInput
                if max_id < item.id:
                    max_id = item.id
                if item.status != 1:
                    # 任务不是完成状态
                    continue
                print('获取到任务' + str(item.id))
                if item.id == *********:
                    print('======')
                out_item = general_output_dao.get_by_input_id('input_id', item.id)
                # out_item = general_output_dao.get_by_input_id('input_id', 565970251)
                if not out_item:
                    continue
                out_item: GeneralCrawlTaskOutput
                out_res = out_item.output
                out_json = json.loads(out_res)
                credit_code = out_json['credit_code']
                if not credit_code:
                    continue
                firm_id = out_json['long_firm_id']
                # item_staff = general_input_dao.get_one_by_task_name_and_firm_id(task_name_staff, create_time, firm_id)
                # item_staff: GeneralCrawlTaskInput
                # if not item_staff:
                #     continue
                # staff_input_id = item_staff.id
                # # 查找qcc抓取主要人员的结果
                # staff_out_item = general_output_dao.get_by_input_id('input_id', staff_input_id)
                # staff_out_item: GeneralCrawlTaskOutput
                # if not staff_out_item:
                #     continue
                credit_res = credit_dao.get(credit_code)
                if not credit_res:
                    continue
                cid = credit_res['company_id']
                # company_graph_item = company_graph_dao.get_by_cid(cid)
                # company_graph_item: CompanyGraph
                # if not company_graph_item:
                #     continue
                # 目前为止，可以判断主要人员
                count = count + 1
                company_res = company_dao.get(cid)
                company_res: Company
                property2 = company_res.prop2
                if property2:
                    continue
                company_clean_res = company_clean_dao.get(cid)
                company_clean_res: CompanyCleanInfo
                reg_amount = company_clean_res.reg_capital_amount
                if not reg_amount:
                    reg_amount = 0
                compare_tyc = dict()
                compare_qcc = dict()
                compare_tyc['reg_status'] = company_res.reg_status
                establish_date = company_res.establish_date
                if establish_date:
                    compare_tyc['establish_date'] = str(establish_date)
                # compare_tyc['reg_capital'] = company_res.reg_capital
                compare_tyc['reg_capital'] = str(reg_amount // 100)
                compare_tyc['company_org_type'] = match_replace('\(\d+?\)', company_res.company_org_type.replace('（', '(').replace('）', ')')).strip().replace('台港澳','港澳台')

                # compare_tyc['approved_date'] = company_res.approved_date
                compare_tyc['from_date'] = str(company_res.from_date)
                tyc_to_data = company_res.to_date
                if not tyc_to_data:
                    compare_tyc['to_date'] = '无固定期限'
                else:
                    year = company_res.to_date.year
                    if year > 2100:
                        compare_tyc['to_date'] = '无固定期限'
                    else:
                        compare_tyc['to_date'] = str(company_res.to_date)
                compare_tyc['reg_institute'] = company_res.reg_institute
                compare_tyc['reg_location'] = convert_fullwidth_to_halfwidth(company_res.reg_location.replace('（', '(').replace('）', ')').replace('，', ',')).replace('号*','号').strip()

                compare_qcc['reg_status'] = out_json['reg_status']
                compare_qcc['establish_date'] = out_json['establish_date']
                reg_qcc = out_json['reg_capital']
                if not reg_qcc:
                    reg_qcc = '0'
                else:
                    if '.' in reg_qcc:
                        numbers = re.findall(r'\d+\.\d+', reg_qcc)
                    else:
                        numbers = re.findall(r'\d+', reg_qcc)
                    num = Decimal(numbers[0])
                    num_s = Decimal('1000000')
                    int_res = num * num_s
                    reg_qcc = str(int_res.to_integral() // 100)
                    # reg_qcc = str(int(float(numbers[0]) * 1000000) // 100)
                # compare_qcc['reg_capital'] = out_json['reg_capital']
                compare_qcc['reg_capital'] = reg_qcc
                compare_qcc['company_org_type'] = match_replace('\(\d+?\)', str(out_json['org_type']).replace('（', '(').replace('）', ')')).strip().replace('台港澳','港澳台')
                # compare_qcc['approved_date'] = out_json['']
                business_term = out_json['business_term']
                if business_term:
                    business_term: str
                    split_res = business_term.split("至")
                    compare_qcc['from_date'] = split_res[0].strip()
                    to_time_qcc = split_res[1].strip()
                    if to_time_qcc != '无固定期限':
                        date_to_qc = datetime.datetime.strptime(to_time_qcc, '%Y-%m-%d').date()
                        if date_to_qc.year > 2100:
                            compare_qcc['to_date'] = '无固定期限'
                        else:
                            compare_qcc['to_date'] = to_time_qcc
                    else:
                        compare_qcc['to_date'] = to_time_qcc
                compare_qcc['reg_institute'] = out_json['reg_institute']
                compare_qcc['reg_location'] = convert_fullwidth_to_halfwidth(str(out_json['reg_location']).replace('（', '(').replace('）', ')').replace('，', ',')).replace('号*','号').strip()
                res = compare_service.compare_base_info(compare_tyc, compare_qcc, con)
                if not res['a_has_res'] and not res['b_has_res'] and not res['equal_not_equal']:
                    all_equal = all_equal + 1
                    print('all——equal')
                    continue
                if res['a_has_res']:
                    tyc_has = tyc_has + 1
                    tyc_has_array_dic[str(cid) + '_' + firm_id + '_' + credit_code] = res['a_has_res']
                if res['b_has_res']:
                    qcc_has = qcc_has + 1
                    qcc_has_array_dic[str(cid) + '_' + firm_id+ '_' + credit_code] = res['b_has_res']
                if res['equal_not_equal']:
                    all_has_but_position_not_equal = all_has_but_position_not_equal + 1
                    equal_not_equal_res[str(cid) + '_' + firm_id+ '_' + credit_code] = res['equal_not_equal']
                print('=====')
                print('====cid:' + str(cid) + ',firm_id:' + firm_id + ',' + str(res))
    print('最终结果====' + str(equal_not_equal_res))
    print('最终结果====tyc_has:' + str(tyc_has_array_dic) + ',qcc_has:' + str(qcc_has_array_dic))
    print('最终结果all' + str(count) + ',all_equal:' + str(all_equal) + ',tyc_has:' + str(tyc_has) + ',qcc_has:' + str(
        qcc_has) + ',all_has_but_position_not_equal:' + str(
        all_has_but_position_not_equal))
    # compare_a = []
    # compare_b = []
    # compare_a.append({
    #     'staff_name': '张三',
    #     'sta_ff_type_name': '董事长'
    # })
    # compare_a.append({
    #     'staff_name': '李四',
    #     'sta_ff_type_name': '监事'
    # })
    # compare_b.append({
    #     'staff_name': '张三',
    #     'sta_ff_type_name': '董事长,总经理'
    # })
    # con = {
    #     'staff_name': {
    #         'type': 'str',  # 'double','int'
    #         'need_split': False,
    #         'is_json': False,
    #         # 'is_array': False
    #     },
    #     'sta_ff_type_name': {
    #         'type': 'str',
    #         'need_split': True,
    #         'is_json': False,
    #         # 'is_array': True,
    #     },
    # }
    # res = compare_service.compare_array(compare_b, compare_a, con)
    # print('=====')
    # print(res)
