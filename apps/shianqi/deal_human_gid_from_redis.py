import json
import logging
import time
from datetime import datetime
from typing import Optional

import ddddocr
import requests
from pydantic import Field, conint

# from PIL import Image
# from io import BytesIO
# from requests import Session, Response
from clients.redis._redis import Redis
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from libs.req import SessionRequestManager
from libs.concurrent import BoundedExecutor
from dao.company_reg_capital import CompanyRegCapital,CompanyRegCapitalDao
from dao.company import CompanyDao,Company

redis_client = Redis(host='redis-bc8ade66-d5d2-4653-9aaf-6401f43b3479.cn-north-4.dcs.myhuaweicloud.com', port=6379,
                     password='s3zSjZaxduont9qg0pcv', db=5)
# redis_client = Redis(host='redis-bc8ade66-d5d2-4653-9aaf-6401f43b3479.cn-north-4.dcs.myhuaweicloud.com', port=6379,
#                      password='s3zSjZaxduont9qg0pcv', db=5)
redis_key_list = 'company_human_gid_to_redis_saq'


if __name__ == '__main__':
    count = 0
    for i in range(160000):
        hid = redis_client.rpop(redis_key_list)
        if hid:
            print('获取到的任务:' + hid)
            url = 'http://idcenter-gsdata.jindidata.com/gsxt/idCenter/genGid?human=' + hid
            res = requests.get(url = url)
            print(res.json())
            time.sleep(0.1)
        else:
            break