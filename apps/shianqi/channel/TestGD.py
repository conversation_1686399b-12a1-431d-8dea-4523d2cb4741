# -*- coding: utf-8 -*-

import requests

session = requests.session()

headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Content-Type": "application/json",
    "Origin": "https://gsxt.amr.gd.gov.cn",
    "Referer": "https://gsxt.amr.gd.gov.cn/",
    # "Content-Type": "application/json",
    "Pragma": "no-cache",

    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "sec-ch-ua": '"Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
    "sec-ch-ua-mobile": '?0',
    "sec-ch-ua-platform": '"macOS"',

    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36",
    "Cookie": "wzws_sessionid=gjc2Y2E5N6Bm+QhZgTA5OTkzYYAxMTQuMjQ3LjI1LjE4Mg==; mozi-assist={%22show%22:false%2C%22audio%22:false%2C%22speed%22:%22middle%22%2C%22zomm%22:1%2C%22cursor%22:false%2C%22pointer%22:false%2C%22bigtext%22:false%2C%22overead%22:false}; wzws_cid=ea272d99b4053735bd9a9d4c57decd87c5008696d494e316a3d70cab4b8cfe34aef12ab410d513712b1deedabee7bb3f6654a08cc87d18cba4be06253f51361e2fa5f56b7b17575af1be480a8269717f"
}
url = "https://gsxt.amr.gd.gov.cn/gsxt_gd/ent/base/anomaly/page"
data = {"current":1,"size":10,"entity":{"entNo":"885196ff-014d-1000-e000-43870a0c0115","entType":"6180"}}
proxies = {'http': 'http://127.0.0.1:8888', 'https': 'http://127.0.0.1:8888'}
response = session.post(url, headers=headers, data= data, timeout=60, proxies= proxies)
print(response.text)
