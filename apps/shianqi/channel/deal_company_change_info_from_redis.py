import json
import logging
import time
from datetime import datetime
from typing import Optional

import ddddocr
from pydantic import Field, conint

# from PIL import Image
# from io import BytesIO
# from requests import Session, Response
from clients.redis._redis import Redis
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from libs.req import SessionRequestManager
from libs.concurrent import BoundedExecutor
from dao.company_reg_capital import CompanyRegCapital,CompanyRegCapitalDao
from dao.company import CompanyDao,Company
from gslib.msv import msv_query_list_dim_human


# redis_client = Redis(host='redis-bc8ade66-d5d2-4653-9aaf-6401f43b3479.cn-north-4.dcs.myhuaweicloud.com', port=6379,
#                      password='s3zSjZaxduont9qg0pcv', db=5)
redis_client = Redis(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com', port=6379,
                     password='3lvadmpiSj61ge', db=5)
redis_key_list = 'dealCompanyChangeInfoReSync_saq'
redis_key_list_task = 'dealCompanyChangeInfoReSync'


if __name__ == '__main__':
    company_dao = CompanyDao()
    for i in range(8000000):
        eid = redis_client.rpop(redis_key_list)
        if eid:
            try:
                print('获取重算变更记录任务:' + eid)
                cid = int(eid)
                res_list = msv_query_list_dim_human(cid, 'company_change_info', 'XA')
                if res_list is None or len(res_list) <= 0:
                    print('任务获取到的多源多版本数据为空:' + eid)
                    continue
                else:
                    print('放入任务:' + eid)
                    redis_client.lpush(redis_key_list_task, eid)
                    time.sleep(0.2)
                # time.sleep(0.05)
            except:
                print('执行异常:' + eid)
        else:
            break
