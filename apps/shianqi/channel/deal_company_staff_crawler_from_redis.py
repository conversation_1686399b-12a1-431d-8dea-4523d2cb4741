import json
import logging
import time
from datetime import datetime
from typing import Optional

import ddddocr
from pydantic import Field, conint
import requests

# from PIL import Image
# from io import BytesIO
# from requests import Session, Response
from clients.redis._redis import Redis
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from libs.req import SessionRequestManager
from libs.concurrent import BoundedExecutor
from dao.company_reg_capital import CompanyRegCapital,CompanyRegCapitalDao
from dao.company import CompanyDao,Company
from biz_utils.credit_utils import credit_code_valid

redis_client = Redis(host='redis-bc8ade66-d5d2-4653-9aaf-6401f43b3479.cn-north-4.dcs.myhuaweicloud.com', port=6379,
                     password='s3zSjZaxduont9qg0pcv', db=5)
# redis_client = Redis(host='redis-bc8ade66-d5d2-4653-9aaf-6401f43b3479.cn-north-4.dcs.myhuaweicloud.com', port=6379,
#                      password='s3zSjZaxduont9qg0pcv', db=5)
redis_key_list = 'deal_company_staff_crawler_to_redis_saq'


def pitch_your_words(code):
    gsxt_touch_change = 'http://*************/gsxt/v2/update/merge'
    # params = {"companySign": code, "record": "company_ill_saq", "channelType": 2,
    #           "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
    #                       "change_dims": ["ill"]}}
    params = {"companySign": code, "record": "company_staff_saq", "channelType": 2,
              "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "company_name": code,
                          "change_dims": ["keyperson"]}}
    res = requests.post(gsxt_touch_change, json=params)
    date = res.json()
    print(date)

def pitch_your_words_by_credit_code(code):
    gsxt_touch_change = 'http://*************/gsxt/v2/update/merge'
    params = {"companySign": code, "record": "company_staff_saq", "channelType": 2,
              "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
                          "change_dims": ["keyperson"]}}
    # params = {"companySign": code, "record": "company_ill_saq", "channelType": 2,
    #           "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "company_name": code,
    #                       "change_dims": ["ill"]}}
    res = requests.post(gsxt_touch_change, json=params)
    date = res.json()
    print(date)

if __name__ == '__main__':
    company_dao = CompanyDao()
    for i in range(5000000):
        eid = redis_client.rpop(redis_key_list)
        if eid:
            company = company_dao.get(eid, "id")
            company: Company
            credit_code = company.credit_code
            if credit_code and credit_code is not None and credit_code != '':
                if not credit_code_valid(credit_code):
                    continue
                pitch_your_words_by_credit_code(credit_code)
            else:
                pitch_your_words(company.name)
            time.sleep(0.1)
        else:
            break