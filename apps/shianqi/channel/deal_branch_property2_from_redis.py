import json
import logging
import time
from datetime import datetime
from typing import Optional

import ddddocr
from pydantic import Field, conint

# from PIL import Image
# from io import BytesIO
# from requests import Session, Response
from clients.redis._redis import Redis
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from libs.req import SessionRequestManager
from libs.concurrent import BoundedExecutor
from dao.company_reg_capital import CompanyRegCapital,CompanyRegCapitalDao
from dao.company import CompanyDao,Company

redis_client = Redis(host='redis-bc8ade66-d5d2-4653-9aaf-6401f43b3479.cn-north-4.dcs.myhuaweicloud.com', port=6379,
                     password='s3zSjZaxduont9qg0pcv', db=5)
# redis_client = Redis(host='redis-bc8ade66-d5d2-4653-9aaf-6401f43b3479.cn-north-4.dcs.myhuaweicloud.com', port=6379,
#                      password='s3zSjZaxduont9qg0pcv', db=5)
redis_key_list = 'dealBranchFromRedisKey_saq'
redis_key_list_task = 'dealBranchFromRedisKey'


if __name__ == '__main__':
    company_dao = CompanyDao()
    for i in range(5000000):
        eid = redis_client.rpop(redis_key_list)
        if eid:
            redis_client.lpush(redis_key_list_task, eid)
            # time.sleep(0.05)
        else:
            break