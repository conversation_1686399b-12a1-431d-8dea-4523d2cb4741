from typing import List
import re
ss = {
    'investor_name': {
        'type': 'str',  # 'double','int'
        'need_split': False,
        'is_json': False,
        # 'is_array': False
    },
    'capital': {
        'type': '',
        'need_split': False,
        'is_json': False,
        # 'is_array': True,
    },
}


# 默认按照第一个key作为主key判重
# 目前只处理strl类型对比
class CompareService(object):
    def compare_array(self, compare_a: List, compare_b: List, config: dict):
        result = dict()
        if not compare_a and not compare_b:
            return {'all_equal': True, 'a_has_res': [], 'b_has_res': [], 'equal_not_equal': []}
        if not compare_a and compare_b:
            return {'all_equal': False, 'a_has_res': [], 'b_has_res': compare_b, 'equal_not_equal': []}
        if compare_a and not compare_b:
            return {'all_equal': False, 'a_has_res': compare_a, 'b_has_res': [], 'equal_not_equal': []}
        keys = list(config.keys())
        item_a_dict_array = dict()
        item_b_dict_array = dict()
        set_a = set()
        set_b = set()
        for item_a in compare_a:
            first_key = keys[0]
            first_key_value = item_a[first_key]
            item_a_dict_array[first_key_value] = item_a
            set_a.add(first_key_value)
            # item_a_dict_array[]
            # it_array = []
            # for key in keys:
            #     if key == first_key:
            #         continue
            #
            #     config_value = config[key]
            #     key_type = config_value['type']
            #     need_split = config_value['need_split']
        for item_b in compare_b:
            first_key = keys[0]
            first_key_value = item_b[first_key]
            item_b_dict_array[first_key_value] = item_b
            set_b.add(first_key_value)
        a_has_flag = []
        b_has_flag = []
        all_has = []
        for it in set_a:
            if it not in set_b:
                a_has_flag.append(it)
            else:
                all_has.append(it)
        for it in set_b:
            if it not in set_a:
                b_has_flag.append(it)
        result['a_has_res'] = a_has_flag
        result['b_has_res'] = b_has_flag
        # result['a_b_equal'] = []
        equal_not_equal = []
        # investor_name,或者staff_name
        for name in all_has:
            it_a = item_a_dict_array[name]
            it_b = item_b_dict_array[name]
            first_key = keys[0]
            # 处理配置文件的每一个任务
            for key_name in keys:
                if key_name == first_key:
                    continue
                key_name_config = config[key_name]
                if key_name_config['type'] == 'str':
                    if not key_name_config['need_split']:
                        if it_a[key_name] == it_b[key_name]:
                            print('')
                        else:
                            # equal_not_equal.append(f'item中{name}的{key_name}不一致')
                            va_a_v = it_a[key_name]
                            if va_a_v is None:
                                va_a_v = 'null'
                            va_b_v = it_b[key_name]
                            if va_b_v is None:
                                va_b_v = 'null'
                            equal_not_equal.append(f'{name}_{va_a_v}_{va_b_v}')
                    else:
                        # 处理字符串分割的情况
                        if (it_a[key_name] is None and it_b[key_name] is not None) or (
                                it_a[key_name] is not None and it_b[key_name] is None):
                            # equal_not_equal.append(f'item中{name}的{key_name}不一致')
                            va_a_va = it_a[key_name]
                            if va_a_va is None:
                                va_a_va = 'null'
                            va_b_va = it_b[key_name]
                            if va_b_va is None:
                                va_b_va = 'null'
                            equal_not_equal.append(f'{name}_{va_a_va}_{va_b_va}')
                        elif it_a[key_name] is None and it_b[key_name] is None:
                            print('')
                        else:
                            val_a = str(it_a[key_name])
                            if val_a == '未知':
                                val_a = ''
                            val_b = str(it_b[key_name])
                            if val_b == '未知':
                                val_b = ''
                            array_a = re.split(r',|兼任|兼|;', val_a)
                            array_b = re.split(r',|兼任|兼|;', val_b)
                            set_a = set()
                            set_b = set()
                            for a_se in array_a:
                                if a_se == '经理' or a_se == '总经理':
                                    set_a.add('经理')
                                elif a_se == '执行董事' or a_se == '执行公司事务的董事':
                                    set_a.add('执行董事')
                                else:
                                    set_a.add(a_se)
                            for b_se in array_b:
                                if b_se == '经理' or b_se == '总经理':
                                    set_b.add('经理')
                                elif b_se == '执行董事' or b_se == '执行公司事务的董事':
                                    set_b.add('执行董事')
                                else:
                                    set_b.add(b_se)
                            # set_a = set(array_a)
                            # set_b = set(array_b)
                            list_a = list(set_a)
                            list_a.sort()
                            list_b = list(set_b)
                            list_b.sort()
                            str_a_val = ','.join(list_a)
                            str_b_val = ','.join(list_b)
                            if str_a_val != str_b_val:
                                # equal_not_equal.append(f'item_array中{name}的{key_name}不一致')
                                equal_not_equal.append(f'{name}_{str_a_val}_{str_b_val}')
        result['equal_not_equal'] = equal_not_equal
        return result

    def compare_holder(self, compare_a: List, compare_b: List, config: dict):
        result = dict()
        if not compare_a and not compare_b:
            return {'all_equal': True, 'a_has_res': [], 'b_has_res': [], 'equal_not_equal': []}
        if not compare_a and compare_b:
            return {'all_equal': False, 'a_has_res': [], 'b_has_res': compare_b, 'equal_not_equal': []}
        if compare_a and not compare_b:
            return {'all_equal': False, 'a_has_res': compare_a, 'b_has_res': [], 'equal_not_equal': []}
        result['all_equal'] = False
        result['a_has_res'] = []
        result['b_has_res'] = []
        keys = list(config.keys())
        equal_not_equal = []
        cur = 0
        for item_a in compare_a:
            item_b = compare_b[cur]
            cur = cur + 1
            for key_name in keys:
                if key_name == 'credit_code' and (not item_a[key_name] or not item_b[key_name]):
                    continue
                key_name_config = config[key_name]
                if key_name_config['type'] == 'str':
                    if item_a[key_name] == item_b[key_name]:
                        print('')
                    else:
                        holder_name = item_a['holder_name']
                        equal_not_equal.append(f'{holder_name}:{key_name}:{item_a[key_name]}:{item_b[key_name]}')
        result['equal_not_equal'] = equal_not_equal
        if not equal_not_equal:
            result['all_equal'] = True
        return result

    def compare_change_info(self, compare_a: List, compare_b: List, config: dict):
        result = dict()
        if not compare_a and not compare_b:
            return {'all_equal': True, 'a_has_res': [], 'b_has_res': [], 'equal_not_equal': []}
        if not compare_a and compare_b:
            return {'all_equal': False, 'a_has_res': [], 'b_has_res': compare_b, 'equal_not_equal': []}
        if compare_a and not compare_b:
            return {'all_equal': False, 'a_has_res': compare_a, 'b_has_res': [], 'equal_not_equal': []}
        result['all_equal'] = False
        result['a_has_res'] = []
        result['b_has_res'] = []
        keys = list(config.keys())
        equal_not_equal = []
        cur = 0
        for item_a in compare_a:
            item_b = compare_b[cur]
            cur = cur + 1
            for key_name in keys:
                if key_name == 'credit_code' and (not item_a[key_name] or not item_b[key_name]):
                    continue
                key_name_config = config[key_name]
                if key_name_config['type'] == 'str':
                    if item_a[key_name] == item_b[key_name]:
                        print('')
                    else:
                        holder_name = item_a['holder_name']
                        equal_not_equal.append(f'{holder_name}:{key_name}:{item_a[key_name]}:{item_b[key_name]}')
        result['equal_not_equal'] = equal_not_equal
        if not equal_not_equal:
            result['all_equal'] = True
        return result

    def compare_base_info(self, compare_a: dict, compare_b: dict, config: dict):
        all_equal = True
        a_has_res = []
        b_has_res = []
        equal_not_equal = []
        keys = list(config.keys())
        for key_name in keys:
            if key_name == 'reg_status':
                value_a = compare_a[key_name]
                value_b = compare_b[key_name]
                if value_a and('正常' in value_a or '存续' in value_a or '在营' in value_a or '开业' in value_a or '在册' in value_a or '在业' in value_a):
                    value_a = '正常'
                if value_b and ('正常' in value_b or '存续' in value_b or '在营' in value_b or '开业' in value_b or '在册' in value_b or '在业' in value_b):
                    value_b = '正常'
                if value_a and not value_b:
                    all_equal = False
                    a_has_res.append(key_name + ':tyc_' + value_a + ',qcc_' + value_b)
                elif not value_a and value_b:
                    all_equal = False
                    b_has_res.append(key_name + ':tyc_' + value_a + ',qcc_' + value_b)
                elif value_a != value_b:
                    all_equal = False
                    equal_not_equal.append(key_name + ':tyc_' + value_a + ',qcc_' + value_b)
            else:
                if compare_a[key_name] and not compare_b[key_name]:
                    all_equal = False
                    # a_has_res.append(key_name + ':tyc_' + compare_a[key_name] + ',qcc_' + compare_b[key_name])
                    a_has_res.append(key_name + ':tyc_' + compare_a[key_name] + ',qcc_null')
                elif not compare_a[key_name] and compare_b[key_name]:
                    all_equal = False
                    b_has_res.append(key_name + ':tyc_null' + ',qcc_' + compare_b[key_name])
                else:
                    val_a = str(compare_a[key_name]).replace('（', "(").replace('）', ')')
                    val_b = str(compare_b[key_name]).replace('（', "(").replace('）', ')')
                    if val_a != val_b:
                        all_equal = False
                        equal_not_equal.append(key_name + ':tyc_' + compare_a[key_name] + ',qcc_' + compare_b[key_name])
        result = {
            'all_equal':all_equal,
            'a_has_res':a_has_res,
            'b_has_res':b_has_res,
            'equal_not_equal':equal_not_equal
        }
        return result




if __name__ == '__main__':
    compare_a = []
    compare_b = []
    compare_a.append({
        'staff_name': '张三',
        'sta_ff_type_name': '董事长'
    })
    compare_a.append({
        'staff_name': '李四',
        'sta_ff_type_name': '监事'
    })
    compare_b.append({
        'staff_name': '张三',
        'sta_ff_type_name': '董事长,总经理'
    })
    con = {
        'staff_name': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'sta_ff_type_name': {
            'type': 'str',
            'need_split': True,
            'is_json': False,
            # 'is_array': True,
        },
    }
    compare_service = CompareService()
    res = compare_service.compare_array(compare_b, compare_a, con)
    print('=====')
    print(res)
    # a_has = res['a_has_res']
    # b_has =

