import requests
from compare import CompareService
from dao.qcc.general_crawl_task_input import GeneralCrawlTaskInput, GeneralCrawlTaskInputDao
from dao.qcc.general_crawl_task_output import GeneralCrawlTaskOutput, GeneralCrawlTaskOutputDao
import json
from dao.id_center import CreditCodeIndexDao
from dao.company_graph import CompanyGraphDao, CompanyGraph
from dao.company_staff_sort import CompanyStaffSortDao, CompanyStaffSort
import datetime
from dao.company import CompanyDao, Company
import time
def pitch_your_words(code):
    gsxt_touch_change = 'http://schedule-gsdata.jindidata.com/gsxt/v2/update/merge'
    # params = {"companySign": code, "record": "investor_repair", "channelType": 2,
    #           "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
    #                       "change_dims": "company_base_info"}}
    # params = {"companySign": code, "record": "saq_dfs", "channelType": 2,
    #           "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
    #                       "change_dims": ["base",'duration','shareholder','keyperson','alter'
    #                                       ,'nlic','punish','drranins','check','abnormal','ill','clear','simplecancer',
    #                                       'eNliq','assist','stock','copyright','instant','insnlic','insAltStock',
    #                                       'insnlic','copyright','annualreport2','insPunish','redregcapInfo']}}
    params = {"companySign": code, "record": "saq_nlic", "channelType": 2,
              "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
                          "change_dims": ["abnormal"]}}

    res = requests.post(gsxt_touch_change, json=params)
    date = res.json()
    print(date)

def pitch_your_words(code):
    gsxt_touch_change = 'http://schedule-gsdata.jindidata.com/gsxt/v2/update/merge'
    # params = {"companySign": code, "record": "company_ill_saq", "channelType": 2,
    #           "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
    #                       "change_dims": ["ill"]}}
    params = {"companySign": code, "record": "abnormal_saq", "channelType": 2,
              "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "company_name": code,
                          "change_dims": ["abnormal"]}}
    res = requests.post(gsxt_touch_change, json=params)
    date = res.json()
    print(date)

def pitch_your_words_by_credit_code(code):
    gsxt_touch_change = 'http://schedule-gsdata.jindidata.com/gsxt/v2/update/merge'
    params = {"companySign": code, "record": "abnormal_saq", "channelType": 2,
              "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
                          "change_dims": ["abnormal"]}}
    # params = {"companySign": code, "record": "company_ill_saq", "channelType": 2,
    #           "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "company_name": code,
    #                       "change_dims": ["ill"]}}
    res = requests.post(gsxt_touch_change, json=params)
    date = res.json()
    print(date)

# if __name__ == '__main__':
#     task_str = '92141182MA0L4NQT33'
#     # task_str = '9151010006432782XN'
#     arr = task_str.split(',')
#     set_arr = []
#     for item in arr:
#         if item.startswith('XN'):
#             print('不再进行处理:' + item)
#         elif not item.startswith('9'):
#             set_arr.append(item)
#         else:
#             pitch_your_words(item)

# if __name__ == '__main__':
#     company_graph_dao = CompanyGraphDao()
#     company_dao = CompanyDao()
#     for name in ['343038']:
#         #实时股权冻结与历史股权冻结重复
#         # input_path = f'/Users/<USER>/Desktop/untitled1.html'
#         # input_path = f'/Users/<USER>/Downloads/query-hive-542823.csv'
#         input_path = f'/home/<USER>/pygs-work-parent/apps/shianqi/query-hive-542823.csv'
#         # input_path = f'/home/<USER>/wangbangxu/pygs/temp/shianqi/无人员的公司数据.csv'
#         with open(input_path, 'r') as f:
#             send_data = []
#             for lid, line, in enumerate(f):
#                 # cid,type,source,credit_code, *_ = line.strip().split(',')
#                 try:
#                     cid, *_ = line.strip().split(',')
#                     if lid == 0:
#                         continue
#                     company_res = company_dao.get(cid)
#                     company_res: Company
#                     if company_res.credit_code:
#                         pitch_your_words(company_res.credit_code)
#                     # time.sleep(1)
#                 except Exception as e:
#                     print('===')



if __name__ == '__main__':
    company_dao = CompanyDao()
    for name in ['343038']:
        # 实时股权冻结与历史股权冻结重复
        # input_path = f'/Users/<USER>/Desktop/untitled.html'
        input_path = f'/home/<USER>/pygs-work-parent/apps/shianqi/query-hive-549804.csv'
        with open(input_path, 'r') as f:
            send_data = []
            for lid, line, in enumerate(f):
                try:
                    credit_code, *_ = line.strip().split(',')
                    pitch_your_words_by_credit_code(credit_code)
                    time.sleep(4)
                except Exception as e:
                    print(e)