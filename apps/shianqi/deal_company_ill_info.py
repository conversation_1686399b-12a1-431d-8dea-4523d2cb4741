import json

from clients.kafka_client import KafkaConsumerClient
# from PIL import Image
# from io import BytesIO
# from requests import Session, Response
from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao
from dao.organization_info import OrganizationInfo, OrganizationInfoDao
from datetime import datetime, date, timedelta
from gslib.id_center import id_center_query

organization_info_dao = OrganizationInfoDao()


def dump_func():
    topic_name = 'qx_monitor_v1'
    consumer = KafkaConsumerClient(kafka_topic=topic_name, group_id='tyc_saq_deal_ill_info',
                                   **ConstantProps.PROPS_GS_KAFKA_ONLINE)
    for sid, s in enumerate(consumer.read()):
        try:
            data = json.loads(s)
            database = data['database']
            database: str
            table = data['table']
            table: str
            type = data['type']
            # print(data['daxta'][0])
            if database.startswith('db_enterprise') and table.startswith('t_serious_illegal') and (
                    type == 'UPDATE' or type == 'INSERT' or type == 'DELETE'):
                data_detail = data['data'][0]
                print('启信宝:' + str(data_detail))
                print(data['daxta'][0])

        except Exception as e:
            print('执行异常:' + s)
            print(e)
            continue
    consumer.close()


if __name__ == '__main__':
    dump_func()
