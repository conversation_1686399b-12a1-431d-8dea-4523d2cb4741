from clients.kafka_client import KafkaConsumerClient, KafkaProducerClient
from libs.env import ConstantProps
import json
import redis
import re
from dao.company import CompanyDao, Company

hz_compare = re.compile("[\u4e00-\u9fa5\\s\\(\\)\\（\\）]+")

redis_con = redis.Redis(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com', port=6379, password='3lvadmpiSj61ge',
                        db=20,
                        decode_responses=True)
company_dao = CompanyDao()

out_topic = 'gsxt.company_event_out'
enterprise_topic = 'gsxt.enterprise_event_data'


def zadd(value, queue_name, priority=0.9):
    print('写入情报动态' + str(value))
    if isinstance(value, dict):
        value = json.dumps(value, ensure_ascii=False)
    redis_con.zadd(queue_name, {value: priority})


def format_name(name):
    if name and len(name) > 1:
        name = name.strip()
        hz = hz_compare.match(name)
        if hz:
            name = re.sub("\\s*", "", name)
        name = re.sub("\\(", "（", name)
        name = re.sub("\\)", "）", name)
        name = name.replace("&nbsp;", "")
        name = name.replace(" ", "")
        name = name.replace(" ", "")
        name = name.replace("[", "（")
        name = name.replace("]", "）")
        name = name.replace("\\", "/")
        return name
    return name


def history_names(searchResult):
    for task in searchResult:
        name = task.get('name')
        name = format_name(name)
        histroyNames = task.get('histroyNames')
        if name and histroyNames:
            info = {
                "name": name,
                "parseType": 0,
                "source": 2,
                "companyType": 0,
                "historyName": histroyNames,
                "priority": 0.9
            }
            zadd(info, 'parseCompany_gj_queue_new_history_names')


def crawler_process(event):
    crawler = event.get('crawler')
    # print('crawler===' * 10)
    # pprint(crawler)
    if crawler:
        base = crawler.get('base')
        companyName = crawler.get('companyName')
        taskName = crawler.get('taskName')
        crawlerInfo = crawler.get('crawlerInfo')
        keyWord = crawler.get('keyWord')
        regNum = crawler.get('regNum')
        searchResult = crawler.get('searchResult')

        if searchResult:
            # 处理历史名称
            history_names(searchResult)
            # 处理基本信息
            # base_info(searchResult)


def judge_geti(cid):
    if not cid:
        return False
    res = company_dao.get(cid, 'id')
    res: Company
    if res and "company_org_type" in res and res['res'] and '个体' in res['company_org_type']:
        return True
    return False


def parser_send(parser, cid):
    parserSendInfo = parser.get('parserSendInfo')
    punishmentPdf = parserSendInfo.get('punishmentPdf')
    # 行政处罚决定书
    if punishmentPdf:
        zadd(punishmentPdf, 'downloadCompany_punishment_pdf')
    investorCommitmentImg = parserSendInfo.get('investorCommitmentImg')
    # 简易注销投资人承诺书
    if investorCommitmentImg:
        zadd(investorCommitmentImg, 'downloadCompany_investorCommitment_jpg_new')
    #        updateCompanyApproveEvent = parserSendInfo.get('updateCompanyApproveEvent')
    # 基本信息变更
    #        if updateCompanyApproveEvent:
    #            self.zadd(updateCompanyApproveEvent, config.update_approvedtime_queue)
    updateCompanyReportApproveEvent = parserSendInfo.get('updateCompanyReportApproveEvent')
    # 年报变更
    if updateCompanyReportApproveEvent:
        zadd(updateCompanyReportApproveEvent, 'parseCompany_companyreport_queue')
    changeIds = parserSendInfo.get('changeIds')
    # 变更记录id
    if changeIds:
        for cid in changeIds:
            zadd({'id': cid}, 'searchCompany_ChangeInfoPre_queue')
    mcLegal = parserSendInfo.get('mcLegal')
    if mcLegal:
        if str(mcLegal).endswith(',error'):
            zadd(mcLegal, 'monitor_legal_change_error')
        else:
            geti_flag = judge_geti(cid)
            # 个体工商户法人变更不发送情报动态
            if not geti_flag:
                zadd(mcLegal, 'monitor_legal_change')
    # 主要人员变更
    mcStaff = parserSendInfo.get('mcStaff')
    if mcStaff:
        for item in mcStaff:
            if str(item).endswith(',error'):
                zadd(item, 'monitor_staff_change_error')
            else:
                geti_flag = judge_geti(cid)
                # 个体工商户主要人员不发送情报动态
                if not geti_flag:
                    zadd(item, 'monitor_staff_change')
    # 股东变更
    mcInvestor = parserSendInfo.get('mcInvestor')
    if mcInvestor:
        for item in mcInvestor:
            if str(item).endswith(',error'):
                zadd(item, 'monitor_investor_change_error')
            else:
                zadd(item, 'monitor_investor_change')
    # 经营范围变更
    mcBusinessScope = parserSendInfo.get('mcBusinessScope')
    if mcBusinessScope:
        if str(mcBusinessScope).endswith(',error'):
            zadd(mcBusinessScope, 'monitor_company_business_scope_change_info_error')
        else:
            zadd(mcBusinessScope, 'monitor_company_business_scope_change_info')
    # 状态变更
    mcState = parserSendInfo.get('mcState')
    if mcState:
        if str(mcState).endswith(',error'):
            zadd(mcState, 'monitor_company_reg_status_change_info_error')
        else:
            zadd(mcState, 'monitor_company_reg_status_change_info')
    # 注册地址变更
    mcLocation = parserSendInfo.get('mcLocation')
    if mcLocation:
        if str(mcLocation).endswith(',error'):
            zadd(mcLocation, 'monitor_company_reg_location_change_info_error')
        else:
            zadd(mcLocation, 'monitor_company_reg_location_change_info')
    # 登记机关变更
    mcInstitute = parserSendInfo.get('mcInstitute')
    if mcInstitute:
        if str(mcInstitute).endswith(',error'):
            zadd(mcInstitute, 'monitor_company_reg_institute_change_info_error')
        else:
            zadd(mcInstitute, 'monitor_company_reg_institute_change_info')
    # 注册资本变更
    mcCapital = parserSendInfo.get('mcCapital')
    if mcCapital:
        if str(mcCapital).endswith(',error'):
            zadd(mcCapital, 'monitor_company_reg_capital_change_info_error')
        else:
            zadd(mcCapital, 'monitor_company_reg_capital_change_info')
    # 组织机构变更
    mcOrgType = parserSendInfo.get('mcOrgType')
    if mcOrgType:
        if str(mcOrgType).endswith(',error'):
            zadd(mcOrgType, 'monitor_company_org_type_change_info_error')
        else:
            zadd(mcOrgType, 'monitor_company_org_type_change_info')


def parser_process(event):
    crawler = event.get('crawler')
    if crawler:
        base = crawler.get('base')
        companyName = crawler.get('companyName')
        taskName = crawler.get('taskName')
        crawlerInfo = crawler.get('crawlerInfo')
        keyWord = crawler.get('keyWord')
        regNum = crawler.get('regNum')
        cid = crawler.get('cId')
        parser = event.get('parser')
        if parser:
            companyId = parser.get('companyId')
            parserSendInfo = parser.get('parserSendInfo')
            if parser.get('parserSendInfo'):
                parser_send(parser, cid)


def get_out_event(event):
    crawler = event.get('crawler')
    parser = event.get('parser')
    if crawler and parser:
        out_event = {'company_name': crawler.get('taskName'),
                     'send_time': parser.get('parserSendTs'),
                     'base': crawler.get('base'),
                     'instance': event.get('instance'),
                     'company_id': parser.get('companyId'),
                     'abInfo': parser.get('abInfo'),
                     'new_company': not crawler.get('dbExist'),
                     'search_result': crawler.get('searchResult'),
                     }
        if parser.get('parserSendInfo'):
            news = {k: v for k, v in parser.get('parserSendInfo').items() if v}
            if news:
                out_event['news'] = news
        return out_event


def get_gsxt_enterprise_event(event, data_event):
    if data_event and event.get('parser'):
        data_event['abInfo'] = event.get('parser').get('abInfo')
        # 计算mask的值
        '''
        第1位：是否是新增公司
        第2位：是否发生变更
        '''
        mask = '1' if data_event.get('new_company') else '0'
        mask = '{}{}'.format(mask, '1' if data_event.get('abInfo') else '0')
        out_event = {
            'ts': data_event.get('send_time'),
            'gid': '',
            'from': data_event.get('instance'),
            'type': 'gs_crawl',
            'mask': mask,
            'data': data_event}
        return out_event


def dump_func():
    topic_name = 'gsxt.company_parser'
    consumer = KafkaConsumerClient(kafka_topic=topic_name, group_id='gsxt_event_consumer',
                                   **ConstantProps.PROPS_GS_KAFKA_ONLINE)
    fusion_producer_client = KafkaProducerClient(**ConstantProps.PROPS_GS_KAFKA_ONLINE, kafka_topic=enterprise_topic)
    for sid, s in enumerate(consumer.read()):
        try:
            event = json.loads(s)
            if event.get('crawlerCode') == 0:
                # 处理抓取信息
                crawler_process(event)
                if event.get('parserCode') == 0:
                    # 处理解析信息
                    parser_process(event)
                    # 输出信息
                    out_event = get_out_event(event)
                    # gsxt_enterprise_event 输出信息
                    gsxt_enterprise_event = get_gsxt_enterprise_event(event, out_event)
                    if out_event:
                        # producer.send(out_topic, value=out_event)
                        send_str = json.dumps(gsxt_enterprise_event)
                        fusion_producer_client.write(send_str)
        except Exception as e:
            print('执行异常:' + s)
            print(e)
            continue
    consumer.close()


if __name__ == '__main__':
    dump_func()
