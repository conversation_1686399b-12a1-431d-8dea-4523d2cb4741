from compare import CompareService
from dao.qcc.general_crawl_task_input import GeneralCrawlTaskInput, GeneralCrawlTaskInputDao
from dao.qcc.general_crawl_task_output import GeneralCrawlTaskOutput, GeneralCrawlTaskOutputDao
import json
from dao.id_center import CreditCodeIndexDao
from dao.company_graph import CompanyGraphDao, CompanyGraph
from dao.company_staff_sort import CompanyStaffSortDao
import datetime
from dao.company_history_name import CompanyHistoryNameDao

if __name__ == '__main__':
    con = {
        'history_name': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        }
    }
    compare_service = CompareService()
    general_input_dao = GeneralCrawlTaskInputDao()
    general_output_dao = GeneralCrawlTaskOutputDao()
    credit_dao = CreditCodeIndexDao()
    company_graph_dao = CompanyGraphDao()
    company_staff_dao = CompanyStaffSortDao()
    company_history_dao = CompanyHistoryNameDao()
    task_name = 'pBetter_firm_page_normal'
    today = datetime.date.today()
    yes = today - datetime.timedelta(days=1)
    create_time = str(yes) + ' 00:00:00'
    count = 0
    max_id = 1
    all_equal = 0
    tyc_has = 0
    qcc_has = 0
    all_has_but_position_not_equal = 0
    equal_not_equal_res = dict()
    one_has_array_res = dict()
    for i in range(60):
        items = general_input_dao.get_many_by_task_name_and_id(task_name, create_time, max_id)
        if items:
            for item in items:
                item: GeneralCrawlTaskInput
                if max_id < item.id:
                    max_id = item.id
                if item.status != 1:
                    # 任务不是完成状态
                    continue
                out_item = general_output_dao.get_by_input_id('input_id', item.id)
                if not out_item:
                    continue
                out_item: GeneralCrawlTaskOutput
                out_res = out_item.output
                out_json = json.loads(out_res)
                credit_code = out_json['credit_code']
                firm_id = out_json['long_firm_id']
                if not credit_code:
                    continue
                previous_name = out_json['previous_name']
                if not previous_name:
                    continue
                print('获取到任务' + str(item.id))
                compare_qcc = []
                for name_item in previous_name:
                    compare_qcc.append({
                        'history_name': str(name_item)
                    })
                credit_res = credit_dao.get_by_pk(credit_code)
                if not credit_res:
                    continue
                cid = credit_res['company_id']
                company_graph_item = company_graph_dao.get_by_cid(cid)
                company_graph_item: CompanyGraph
                if not company_graph_item:
                    continue
                # 目前为止，可以判断曾用名
                count = count + 1
                gid = company_graph_item.cgid
                compare_tyc = []
                tyc_history = company_history_dao.get_many('company_gid', gid)
                if tyc_history:
                    for tyc_item_his in tyc_history:
                        compare_tyc.append({
                            'history_name': str(tyc_item_his.company_name)
                        })
                res = compare_service.compare_array(compare_tyc, compare_qcc, con)
                if not res['a_has_res'] and not res['b_has_res'] and not res['equal_not_equal']:
                    all_equal = all_equal + 1
                    print('all——equal')
                    continue
                if res['a_has_res']:
                    tyc_has = tyc_has + 1
                if res['b_has_res']:
                    qcc_has = qcc_has + 1
                if res['a_has_res'] or res['b_has_res']:
                    one_has_array_res[str(cid) + '_' + str(gid) + '_' + firm_id] = res
                if res['equal_not_equal']:
                    all_has_but_position_not_equal = all_has_but_position_not_equal + 1
                    equal_not_equal_res[str(cid) + '_' + str(gid) + '_' + firm_id] = res['equal_not_equal']
                print('=====')
                print('====cid:' + str(cid) + ',firm_id:' + firm_id + ',' + str(res))
    print('最终结果====' + str(one_has_array_res))
    print('最终结果all' + str(count) + ',all_equal:' + str(all_equal) + ',tyc_has:' + str(tyc_has) + ',qcc_has:' + str(
        qcc_has) + ',all_has_but_position_not_equal:' + str(
        all_has_but_position_not_equal))
