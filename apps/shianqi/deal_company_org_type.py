import requests
from compare import CompareService
from dao.qcc.general_crawl_task_input import GeneralCrawlTaskInput, GeneralCrawlTaskInputDao
from dao.qcc.general_crawl_task_output import GeneralCrawlTaskOutput, GeneralCrawlTaskOutputDao
import json
from dao.id_center import CreditCodeIndexDao
from dao.company_graph import CompanyGraphDao, CompanyGraph
from dao.company_staff_sort import CompanyStaffSortDao, CompanyStaffSort
import datetime
from dao.company import CompanyDao, Company
from clients.redis._redis import Redis

redis_client = Redis(host='redis-bc8ade66-d5d2-4653-9aaf-6401f43b3479.cn-north-4.dcs.myhuaweicloud.com', port=6379,
                     password='s3zSjZaxduont9qg0pcv', db=5)
redis_key_list = 'companyReportInvestorIdPro'
# def pitch_your_words(code):
#     gsxt_touch_change = 'http://*************/gsxt/v2/update/merge'
#     # params = {"companySign": code, "record": "investor_repair", "channelType": 2,
#     #           "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
#     #                       "change_dims": "company_base_info"}}
#     params = {"companySign": code, "record": "company_base_info", "channelType": 2,
#               "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
#                           "change_dims": ["simplecancer"]}}
#     # params = {"companySign": code, "record": "status_repair", "channelType": 2,
#     #           "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
#     #                       "change_dims": "company_base_info"}}
#     res = requests.post(gsxt_touch_change, json=params)
#     date = res.json()
#     print(date)

if __name__ == '__main__':
    company_dao = CompanyDao()
    for name in ['343038']:
        #实时股权冻结与历史股权冻结重复
        input_path = f'/Users/<USER>/Downloads/query-hive-445506.csv'
        with open(input_path, 'r') as f:
            send_data = []
            send_set = set()
            for lid, line in enumerate(f):
                cid_str, *_ = line.strip().split(',')
                if lid == 0:
                    continue
                cid = int(cid_str)
                company_res = company_dao.get(cid)
                company_res: Company
                company_org_type = company_res.company_org_type
                if not company_org_type:
                    continue
                company_org_type = company_org_type.strip()
                company_dao.update_company_org_type(cid, company_org_type)

