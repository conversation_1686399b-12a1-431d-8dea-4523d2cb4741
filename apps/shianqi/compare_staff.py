from compare import CompareService
from dao.qcc.general_crawl_task_input import GeneralCrawlTaskInput, GeneralCrawlTaskInputDao
from dao.qcc.general_crawl_task_output import GeneralCrawlTaskOutput, GeneralCrawlTaskOutputDao
import json
from dao.id_center import CreditCodeIndexDao
from dao.company_graph import CompanyGraphDao, CompanyGraph
from dao.company_staff_sort import CompanyStaffSortDao, CompanyStaffSort
import datetime

if __name__ == '__main__':
    con = {
        'staff_name': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'sta_ff_type_name': {
            'type': 'str',
            'need_split': True,
            'is_json': False,
            # 'is_array': True,
        },
    }
    compare_service = CompareService()
    general_input_dao = GeneralCrawlTaskInputDao()
    general_output_dao = GeneralCrawlTaskOutputDao()
    credit_dao = CreditCodeIndexDao()
    company_graph_dao = CompanyGraphDao()
    company_staff_dao = CompanyStaffSortDao()
    task_name = 'pBetter_firm_page_normal'
    task_name_staff = 'pBetter_employees_normal'
    today = datetime.date.today()
    yes = today - datetime.timedelta(days=1)
    yes_yes_yes = today - datetime.timedelta(days=3)
    create_time = str(yes) + ' 00:00:00'
    create_time_yes_yes = str(yes_yes_yes) + ' 00:00:00'
    count = 0
    max_id = 1
    all_equal = 0
    tyc_has = 0
    qcc_has = 0
    all_has_but_position_not_equal = 0
    equal_not_equal_res = dict()
    tyc_has_array_dic = dict()
    qcc_has_array_dic = dict()
    for i in range(300):
    # for i in range(200):
        items = general_input_dao.get_many_by_task_name_and_id(task_name, create_time, max_id)
        if items:
            for item in items:
                item: GeneralCrawlTaskInput
                if max_id < item.id:
                    max_id = item.id
                if item.status != 1:
                    # 任务不是完成状态
                    continue
                print('获取到任务' + str(item.id))
                if item.id == 507709176:
                    print('======')
                out_item = general_output_dao.get_by_input_id('input_id', item.id)
                if not out_item:
                    continue
                out_item: GeneralCrawlTaskOutput
                out_res = out_item.output
                out_json = json.loads(out_res)
                credit_code = out_json['credit_code']
                if not credit_code:
                    continue
                firm_id = out_json['long_firm_id']
                item_staff = general_input_dao.get_one_by_task_name_and_firm_id(task_name_staff, create_time_yes_yes, firm_id)
                item_staff: GeneralCrawlTaskInput
                if not item_staff:
                    continue
                staff_input_id = item_staff.id
                # 查找qcc抓取主要人员的结果
                staff_out_item = general_output_dao.get_by_input_id('input_id', staff_input_id)
                staff_out_item: GeneralCrawlTaskOutput
                if not staff_out_item:
                    continue
                credit_res = credit_dao.get(credit_code)
                if not credit_res:
                    continue
                cid = credit_res['company_id']
                company_graph_item = company_graph_dao.get_by_cid(cid)
                company_graph_item: CompanyGraph
                if not company_graph_item:
                    continue
                # 目前为止，可以判断主要人员
                count = count + 1

                gid = company_graph_item.cgid
                companyStaffList = company_staff_dao.get_many('company_gid', gid)
                compare_tyc = []
                compare_qcc = []
                staff_output = staff_out_item.output
                staff_out_json = json.loads(staff_output)
                if 'employees' in staff_out_json:
                    qcc_employees = staff_out_json['employees']
                    if qcc_employees:
                        if len(qcc_employees) > 10:
                            all_equal = all_equal + 1
                            continue
                        for qcc_it in qcc_employees:
                            compare_qcc.append({
                                'staff_name': qcc_it['employee_name'],
                                'sta_ff_type_name': qcc_it['employee_title']
                            })
                if companyStaffList:
                    for tyc_staff in companyStaffList:
                        tyc_staff: CompanyStaffSort
                        compare_tyc.append({
                            'staff_name': tyc_staff.name,
                            'sta_ff_type_name': tyc_staff.position
                        })
                if len(compare_tyc) > 10 and len(compare_qcc) == 10:
                    all_equal = all_equal + 1
                    print('all——equal')
                    continue
                res = compare_service.compare_array(compare_tyc, compare_qcc, con)
                if not res['a_has_res'] and not res['b_has_res'] and not res['equal_not_equal']:
                    all_equal = all_equal + 1
                    print('all——equal')
                    continue
                if res['a_has_res']:
                    tyc_has = tyc_has + 1
                    tyc_has_array_dic[str(cid) + '_' + str(gid) + '_' + firm_id] = res['a_has_res']
                if res['b_has_res']:
                    qcc_has = qcc_has + 1
                    qcc_has_array_dic[str(cid) + '_' + str(gid) + '_' + firm_id] = res['b_has_res']
                if res['equal_not_equal']:
                    all_has_but_position_not_equal = all_has_but_position_not_equal + 1
                    equal_not_equal_res[str(cid) + '_' + str(gid) + '_' + firm_id] = res['equal_not_equal']
                print('=====')
                print('====cid:' + str(cid) + ',firm_id:' + firm_id + ',' + str(res))
    print('最终结果====' + str(equal_not_equal_res))
    print('最终结果====tyc_has:' + str(tyc_has_array_dic) + ',qcc_has:' + str(qcc_has_array_dic))
    print('最终结果all' + str(count) + ',all_equal:' + str(all_equal) + ',tyc_has:' + str(tyc_has) + ',qcc_has:' + str(
        qcc_has) + ',all_has_but_position_not_equal:' + str(
        all_has_but_position_not_equal))
    # compare_a = []
    # compare_b = []
    # compare_a.append({
    #     'staff_name': '张三',
    #     'sta_ff_type_name': '董事长'
    # })
    # compare_a.append({
    #     'staff_name': '李四',
    #     'sta_ff_type_name': '监事'
    # })
    # compare_b.append({
    #     'staff_name': '张三',
    #     'sta_ff_type_name': '董事长,总经理'
    # })
    # con = {
    #     'staff_name': {
    #         'type': 'str',  # 'double','int'
    #         'need_split': False,
    #         'is_json': False,
    #         # 'is_array': False
    #     },
    #     'sta_ff_type_name': {
    #         'type': 'str',
    #         'need_split': True,
    #         'is_json': False,
    #         # 'is_array': True,
    #     },
    # }
    # res = compare_service.compare_array(compare_b, compare_a, con)
    # print('=====')
    # print(res)
