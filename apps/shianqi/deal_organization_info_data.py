import json

from clients.kafka_client import KafkaConsumerClient
from dao.organization_info import OrganizationInfo, OrganizationInfoDao
from gslib.id_center import id_center_query
import re
from copy import copy
from datetime import date, datetime
from typing import List, Type, Optional

from libs.env import ConstantProps
from dao.deps.mysql_dao import MySQLDao
from dao.deps.mysql_sharding_dao import MySQLShardingDao

organization_info_dao = OrganizationInfoDao()


class EnterpriseDao(MySQLShardingDao):
    def __init__(self, **kwargs):
        super().__init__(sharding_key='eid', pk_name='eid', entity_class=None, **kwargs)

    # 给定v 给出 分库分表位置
    @classmethod
    def do_sharding(cls, v: str) -> int:
        if not re.fullmatch('[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', v):
            raise ValueError('bad format eid={}'.format(v))
        db_id = int('0x' + v[-1], 16)
        tb_id = int('0x' + v[-2], 16)
        return db_id * 16 + tb_id

    @classmethod
    def gen_dao_list(cls, **kwargs) -> List[MySQLDao]:
        return cls.gen_dao_list_qxb(
            db_prefix='db_enterprise',
            tb_prefix='t_enterprise',
            **kwargs,
        )

    @classmethod
    def gen_dao_list_qxb(cls, db_prefix: str, tb_prefix: str, **kwargs) -> List[MySQLDao]:
        dao_list = list()
        for db_id in range(16):
            for tb_id in range(16):
                dao_args_copy = copy(kwargs)
                dao_list.append(
                    MySQLDao(
                        **ConstantProps.PROPS_QX_LIST[db_id],
                        db_tb_name='{}_{}.{}_{}'.format(db_prefix, db_id, tb_prefix, tb_id),
                        **dao_args_copy,
                    )
                )
        return dao_list

enterprise_dao = EnterpriseDao()

def dump_func():
    # with open('/Users/<USER>/Downloads/query-hive-442304.csv', 'r') as f:
    with open('/home/<USER>/pygs-work-parent/apps/shianqi/query-hive-442304.csv', 'r') as f:
        for lid, line, in enumerate(f):
            eid, *_ = line.strip().split(',')
            if lid == 0:
                continue
            try:
                data_detail = enterprise_dao.get(eid)
                if data_detail and 'credit_no' in data_detail and data_detail['credit_no'] and data_detail[
                    'credit_no'] != '':
                    credit_no = data_detail['credit_no']
                    if credit_no.startswith('11') and len(credit_no) == 18:
                        print('获取到的数据:' + str(data_detail))
                        before_data = organization_info_dao.get_one_by_credit_code(credit_no)
                        # before_data:OrganizationInfo
                        # 更新
                        if before_data and before_data['id']:
                            row_update_time = data_detail['row_update_time']
                            row_update_time: datetime
                            timestamp = row_update_time.timestamp()
                            # timestamp = datetime.strptime(row_update_time, '%Y-%m-%d %H:%M:%S').timestamp()
                            us_time = datetime.strptime(str(before_data['update_time']),
                                                        '%Y-%m-%d %H:%M:%S').timestamp()
                            if timestamp > us_time:
                                org_name = before_data['org_name']
                                if data_detail['name'] and data_detail['name'] != '':
                                    org_name = data_detail['name']
                                registration_authority = before_data['registration_authority']
                                if data_detail['belong_org'] and data_detail['belong_org'] != '':
                                    registration_authority = data_detail['belong_org']
                                legal_person = before_data['legal_person']
                                legal_person_id = before_data['legal_person_id']
                                if data_detail['oper_name'] and data_detail['oper_name'] != '':
                                    oper_name = data_detail['oper_name']
                                    if not legal_person or legal_person != oper_name:
                                        id_center_result = id_center_query(oper_name)
                                        legal_person = oper_name
                                        entity_type, legal_person_id = id_center_result
                                registration_date = before_data['registration_date']
                                if data_detail['start_date'] and data_detail['start_date'] != '' and data_detail[
                                    'start_date'] != '0000-00-00':
                                    registration_date = data_detail['start_date']
                                if not registration_date:
                                    registration_date = '0000-00-00'
                                expiry_date = before_data['expiry_date']
                                if (data_detail['term_start'] and data_detail['term_start'] != '') or (
                                        data_detail['term_end'] and data_detail['term_end'] != ''):
                                    expiry_date = data_detail['term_start'] + '至' + data_detail['term_end']
                                registered_capital = before_data['registered_capital']
                                if data_detail['regist_capi'] and data_detail['regist_capi'] != '':
                                    registered_capital = data_detail['regist_capi']
                                reg_status = before_data['reg_status']
                                if data_detail['status'] and data_detail['status'] != '':
                                    reg_status = data_detail['status']
                                # address = before_data.address
                                # if data_detail['']
                                business_scope = before_data['business_scope']
                                if data_detail['scope'] and data_detail['scope'] != '':
                                    business_scope = data_detail['scope']
                                org_source = 'qx_monitor_v1'
                                organization_info_dao.update_by_id(before_data['id'], org_name, registration_authority,
                                                                   legal_person, legal_person_id, registration_date,
                                                                   expiry_date, registered_capital, reg_status,
                                                                   business_scope, org_source)
                        else:
                            org_name = ''
                            if data_detail['name'] and data_detail['name'] != '':
                                org_name = data_detail['name']
                            registration_authority = ''
                            if data_detail['belong_org'] and data_detail['belong_org'] != '':
                                registration_authority = data_detail['belong_org']
                            legal_person = ''
                            legal_person_id = 0
                            if data_detail['oper_name'] and data_detail['oper_name'] != '':
                                oper_name = data_detail['oper_name']
                                id_center_result = id_center_query(oper_name)
                                entity_type, legal_person_id = id_center_result
                            registration_date = '0000-00-00'
                            if data_detail['start_date'] and data_detail['start_date'] != '':
                                registration_date = data_detail['start_date']
                            expiry_date = ''
                            if (data_detail['term_start'] and data_detail['term_start'] != '') or (
                                    data_detail['term_end'] and data_detail['term_end'] != ''):
                                expiry_date = data_detail['term_start'] + '至' + data_detail['term_end']
                            registered_capital = ''
                            if data_detail['regist_capi'] and data_detail['regist_capi'] != '':
                                registered_capital = data_detail['regist_capi']
                            reg_status = ''
                            if data_detail['status'] and data_detail['status'] != '':
                                reg_status = data_detail['status']
                            # address = before_data.address
                            # if data_detail['']
                            business_scope = ''
                            if data_detail['scope'] and data_detail['scope'] != '':
                                business_scope = data_detail['scope']
                            org_source = 'qx_monitor_v1'
                            # 新增
                            organization_info_dao.insert_by(credit_no, org_name, registration_authority,
                                                            legal_person, legal_person_id, registration_date,
                                                            expiry_date, registered_capital, reg_status,
                                                            business_scope, org_source)
            except Exception as e:
                print('执行异常:' + eid)
                print(e)
                continue


if __name__ == '__main__':
    dump_func()
    # enterprise = EnterpriseDao()
    # res = enterprise.get("2b2e9a7c-1f24-11eb-8b26-00163e0ca5c5")
    # print(res)
