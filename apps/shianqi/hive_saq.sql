SELECT ods_prism_gsxt_staff_change_df.gid from ods_prism_gsxt_staff_change_df where ods_prism_gsxt_staff_change_df.pt = '20230903' and length(ods_prism_gsxt_staff_change_df.staff_name) <=1 GROUP BY ods_prism_gsxt_staff_change_df.gid;

SELECT count(t1.a1) from (SELECT concat_ws(',', collect_list(ods_internal_branch_gs_df.cid)) as a1,ods_internal_branch_gs_df.branch_cid from ods_internal_branch_gs_df WHERE ods_internal_branch_gs_df.pt = '20230905' GROUP BY ods_internal_branch_gs_df.branch_cid having count(ods_internal_branch_gs_df.id) >1) t1;

SELECT concat_ws(',', collect_list(ods_internal_branch_gs_df.cid)) as a1,ods_internal_branch_gs_df.branch_cid from ods_internal_branch_gs_df WHERE ods_internal_branch_gs_df.pt = '20230905' GROUP BY ods_internal_branch_gs_df.branch_cid having count(ods_internal_branch_gs_df.id) >1;


SELECT concat_ws(',', collect_list(ods_internal_branch_dig_df.cid)) as a1,ods_internal_branch_dig_df.branch_cid from ods_internal_branch_dig_df WHERE ods_internal_branch_dig_df.pt = '20230905' GROUP BY ods_internal_branch_dig_df.branch_cid having count(ods_internal_branch_dig_df.id) >1;

SELECT * from ods_prism1_company_df as t1 where t1.pt = '20231019' and t1.id = 508043;
SELECT t1.id from ods_prism1_company_df as t1 INNER JOIN (SELECT CAST(ods_prism1_company_df.property2 as BIGINT) as a1 from ods_prism1_company_df where ods_prism1_company_df.pt = '20230913' and length(ods_prism1_company_df.property2) > 0) as t2 on t1.id = t2.a1 where t1.pt = '20230913' and t1.property2 = "" and t1.source_flag like 'http://qyxy.baic.gov.cn%' and length(t1.reg_number) = 15;

SELECT t1.id from ods_prism1_company_df as t1 where t1.pt = '20230913' and t1.property2 = "" and t1.source_flag like 'http://qyxy.baic.gov.cn%' and length(t1.reg_number) = 15 and t1.id = 8931669;

SELECT CAST(ods_prism1_company_df.property2 as BIGINT) as a1 from ods_prism1_company_df where ods_prism1_company_df.pt = '20230913' and length(ods_prism1_company_df.property2) > 0 and ods_prism1_company_df.property2 = '8931669';

SELECT * from ods_prism1_company_change_info_df as t1 where t1.pt = '20230918' and t1.change_time > t1.createTime ORDER BY t1.createTime desc;

SELECT count(*) from ods_prism1_company_df where ods_prism1_company_df.pt = '20230914' and ods_prism1_company_df.id >86277739;

SELECT * from ods_prism1_company_df where ods_prism1_company_df.pt = '20230911' and ods_prism1_company_df.reg_status = '注销' and ods_prism1_company_df.updatetime > '2023-09-01 00:00:00';


SELECT * from ods_prism1_company_df where ods_prism1_company_df.pt = '20230910'  and ods_prism1_company_df.reg_status = '注销' and ods_prism1_company_df.updatetime > '2023-09-01 00:00:00';

SELECT ods_prism1_company_df.id from ods_prism1_company_df where ods_prism1_company_df.pt = '20230910'  and ods_prism1_company_df.reg_status = '注销' and ods_prism1_company_df.updatetime > '2023-09-01 00:00:00';


SELECT ods_prism1_equity_ratio_df.company_graph_id from ods_prism1_equity_ratio_df where ods_prism1_equity_ratio_df.pt = '20230918' GROUP BY ods_prism1_equity_ratio_df.company_graph_id

select * from (SELECT ods_prism1_equity_ratio_df.company_graph_id from ods_prism1_equity_ratio_df where ods_prism1_equity_ratio_df.pt = '20230918' GROUP BY ods_prism1_equity_ratio_df.company_graph_id) as t1 left join
ods_prism_gsxt_investor_snap_df as t2 on t1.company_graph_id = t2.gid where t2.pt = '20230918' and t2.gid is null;

SELECT * from ods_prism_gsxt_investor_snap_df as t1 where t1.pt = '20230921' and t1.snap_date = '1900-01-01';

SELECT * from ods_prism1_company_df as t1 where t1.pt = '20230921' and t1.legal_person_name != ''and t1.legal_person_name is NOT NULL and t1.estiblish_time IS NOT NULL
and t1.crawledtime is NOT NULL and t1.source_flag like 'http://qyxy.baic.gov.cn%' and length(t1.property1) != 18 and length(t1.reg_number) != 15 and id = 84884862;



SELECT * from ods_prism1_company_df as t1 where t1.pt = '20230921' and t1.legal_person_name != ''and t1.legal_person_name is NOT NULL and t1.estiblish_time IS NOT NULL
and t1.crawledtime is NOT NULL and t1.source_flag like 'http://qyxy.baic.gov.cn%' and length(t1.property1) != 18 and length(t1.reg_number) != 15 and id = 84884862;

SELECT * from ods_prism1_company_df as t1 where t1.pt = '20231220' and t1.source_flag not like 'http://qyxy.baic.gov.cn%' and t1.property2 != '' and t1.dw_is_del = 0 limit 100;
SELECT count(*) from ods_prism1_company_df where pt = '20231220' and source_flag like 'http://qyxy.baic.gov.cn%' and dw_is_del = 0 and property2 != '';

SELECT t3.id from (SELECT t1.property2 p2 from ods_prism1_company_df as t1 where t1.pt = '20230926' and t1.source_flag not like 'http://qyxy.baic.gov.cn%' and t1.property2 != '') as t2
INNER JOIN (SELECT * from ods_prism1_company_df as t1 where t1.pt = '20230926' and t1.source_flag like 'http://qyxy.baic.gov.cn%') as t3 on t2.p2 = t3.id;


SELECT * from ods_prism1_company_graph_df as t1 where t1.pt = '20230925' limit 100;

SELECT * from ods.ods_prism_gsxt_investor_snap_df as t1  where t1.pt = '20231007' and t1.snap_date = '0000-00-00' and t1.dw_opt_type != 'DELETE';
SELECT t1.gid from ods.ods_prism_gsxt_investor_snap_df as t1  where t1.pt = '20230923' and t1.snap_date = '0000-00-00' GROUP BY t1.gid;

SELECT t1.gid from ods.ods_prism_gsxt_investor_snap_df as t1  where t1.pt = '20230923' and t1.snap_date = '0000-00-00' GROUP BY t1.gid;
select * from ods.ods_prism_gsxt_investor_snap_df as t1  where t1.id = *********;

select * from
(select distinct company_graph_id, company_name from ods_prism1_equity_ratio_df where pt='20230929')t1
left join (select distinct graph_id from ods_prism_enterprise_df where pt='20230929')t2 on t1.company_graph_id=t2.graph_id
left join (select distinct gid from ods_prism_gsxt_investor_snap_df where pt='20230929')t3 on company_graph_id=gid
where  t2.graph_id is not null and t3.gid is null;

select * from ods.ods_prism_gsxt_investor_snap_df where pt="20231007"
and length(investor_name)>0 and length(investor_name)<2

select ods_prism_gsxt_investor_snap_df.gid from ods.ods_prism_gsxt_investor_snap_df where pt="20231008" and dw_is_del=0
and investor_name like "%\%%"
select ods_prism_gsxt_investor_snap_df.gid from ods.ods_prism_gsxt_investor_snap_df where pt="20231008"
and length(investor_name)>0 and length(investor_name)<2 group by ods_prism_gsxt_investor_snap_df.gid

select ods_prism_gsxt_investor_snap_df.gid from ods.ods_prism_gsxt_investor_snap_df where pt="20231008"
and ods_prism_gsxt_investor_snap_df.investor_gid = 0 group by ods_prism_gsxt_investor_snap_df.gid;


select gid from ods.ods_prism_gsxt_investor_snap_df where pt="20231010"
and length(investor_name)>=0 and length(investor_name)<2 and ods_prism_gsxt_investor_snap_df.dw_is_del=0 group by ods_prism_gsxt_investor_snap_df.gid

select gid from ods.ods_prism_gsxt_investor_snap_df where pt="20231010" and dw_is_del=0
and ods_prism_gsxt_investor_snap_df.investor_type = 1 and ods_prism_gsxt_investor_snap_df.investor_gid = 0

select gid from ods.ods_prism_gsxt_investor_snap_df where pt="20231010" and dw_is_del=0
and ods_prism_gsxt_investor_snap_df.snap_date = '1900-01-01'
group by ods_prism_gsxt_investor_snap_df.gid

select gid from ods.ods_prism_gsxt_investor_snap_df where pt="${}" and dw_is_del=0
and (investor_name like "%实缴%" or
investor_name like "%认缴%"
or investor_name like "%货币%"
or investor_name like "%董事%"
or investor_name like "%监事%"
or investor_name like "%出资%"
or investor_name like "%投资%"
or investor_name like "%实物%"
or investor_name like "%;"
or investor_name like "%；")
group by gid,investor_name

select gid from ods.ods_prism_gsxt_investor_snap_df where pt="${}" and dw_is_del=0
and (investor_name like "%主办单位%" or
investor_name like "%理发服务%"
or investor_name like "%汽车配件%"
or investor_name like "%广告制作%"
or investor_name like "%日用百货%"
or investor_name like "%日用百杂%"
or investor_name like "%文化用品%"
or investor_name like "%日杂百货%"
or investor_name like "%服装零售%"
or investor_name like "%农民成员%"
or investor_name like "%企业积累%"
or investor_name like "%股东变更%"
or investor_name like "%婚纱摄影%"
or investor_name like "%单位投资%")
group by gid

select gid,snap_date from ods.ods_prism_gsxt_investor_snap_df where pt="${}" and  dw_is_del=0
and snap_date="0000-00-00"
group by gid,snap_date

select gid,snap_date from ods.ods_prism_gsxt_investor_snap_df where pt="${}" and  dw_is_del=0
and snap_date>"2023-10-11"
group by gid,snap_date

select gid from ods.ods_prism_gsxt_investor_snap_df where pt="20231010" and dw_is_del=0
and length(investor_name)>0 and length(investor_name)<2 group by gid

select gid,investor_name from ods.ods_prism_gsxt_investor_snap_df where pt="${pt}" and dw_is_del=0
and length(investor_name)>0 and length(investor_name)<2
group by gid,investor_name


select gid,investor_name from ods.ods_prism_gsxt_investor_snap_df where pt="20231010" and dw_is_del=0
and investor_type=2 and length(investor_name)<=4 group by gid,investor_name

select ods_prism_gsxt_investor_snap_df.gid from ods.ods_prism_gsxt_investor_snap_df where pt="20231010" and dw_is_del=0
and investor_name like "%\%%" group by ods_prism_gsxt_investor_snap_df.gid


with a as(
SELECT gid,
investor_gid,
investor_name,
investor_type
FROM ods.ods_prism_gsxt_investor_snap_df
            WHERE pt='${pt}'
              AND dw_is_del=0
group by gid,
investor_gid,
investor_name,
investor_type
)
select gid from a
group by gid,investor_name
having cnt>1

select * from ods_prism1_equity_ratio_df where pt='20231011' limit 500;

 select * from ods_prism_enterprise_df where pt='20231011' and dw_is_del = 0 limit 100;

select t1.company_graph_id from
(select distinct company_graph_id, company_name from ods_prism1_equity_ratio_df where pt='20231011' and dw_is_del = 0)t1
left join (select distinct graph_id from ods_prism_enterprise_df where pt='20231011' and dw_is_del = 0)t2 on t1.company_graph_id=t2.graph_id
left join (select distinct company_gid from ods_prism_company_history_sholder_mirror_df where pt='20231011' and dw_is_del = 0)t3 on t1.company_graph_id=t3.company_gid
where  t2.graph_id is not null and t3.company_gid is null and (t1.company_graph_id %10) = 9;


select t1.gid from
(select gid, min(snap_date) as min_snap_date from test.test_prism_gsxt_investor_snap_copy_df where pt=${pt} and gid % 10 = 9 group by gid)t1 inner join
(select graph_id, establish_date from ods_prism_enterprise_df where pt=${pt})t2 on t1.gid=t2.graph_id
where min_snap_date > establish_date;


WITH s1 as (
SELECT t1.id,t1.gid,t1.staff_gid,t1.position_clean from ods_prism_gsxt_staff_change_df t1 where t1.pt = '20231101' and t1.entry_date is null and t1.dw_is_del = 0)
, s2 as (
SELECT t1.id,t1.gid,t1.staff_gid,t1.position_clean from ods_prism_gsxt_staff_change_df t1 where t1.pt = '20231101' and t1.entry_date is not null and t1.leave_date is null and t1.dw_is_del = 0)
select * from s1 inner join s2 on s1.gid = s2.gid and s1.staff_gid = s2.staff_gid
;

SELECT t1.id,t1.gid,t1.staff_gid,t1.position_clean from ods_prism_gsxt_staff_change_df t1 where t1.pt = '20231105' and t1.entry_date is null and t1.depart = 1 and t1.dw_is_del = 0

-- 刷主要人员历史数据
WITH s1 as (
SELECT t1.id,t1.gid,t1.staff_gid,t1.position_clean from ods_prism_gsxt_staff_change_df t1 where t1.pt = '20231105' and t1.entry_date is null and t1.depart = 0 and t1.dw_is_del = 0)
, s2 as (
SELECT t1.id,t1.gid,t1.staff_gid,t1.position_clean from ods_prism_gsxt_staff_change_df t1 where t1.pt = '20231105' and t1.entry_date is not null and t1.leave_date is null and t1.depart = 0 and t1.dw_is_del = 0)
select s1.gid,s1.staff_gid from s1 LEFT join s2 on s1.gid = s2.gid and s1.staff_gid = s2.staff_gid
-- select count(*) from s1 LEFT join s2 on s1.gid = s2.gid and s1.staff_gid = s2.staff_gid
-- where s2.id is not NULL and s1.gid%2 = 0
where s2.id is not NULL
;


-- 刷主要人员数据
with change_s1 as (
select s1.gid from (
SELECT gid,source from ods_prism_gsxt_staff_change_df t1 where t1.pt = '20231101' and t1.dw_is_del = 0 group by t1.gid,t1.source) s1
group by s1.gid having count(s1.gid) = 1),
change_s2 as (SELECT * from ods_prism_gsxt_staff_change_df t2 where t2.pt = '20231101' and t2.dw_is_del = 0)
select change_s1.gid from change_s1 left join change_s2 on change_s1.gid = change_s2.gid
-- select * from change_s1 left join change_s2 on change_s1.gid = change_s2.gid
where change_s2.gid is not null and change_s2.source = 'CUR';


select count(*) from (
select change_s1.gid from (select s1.gid from (
SELECT gid,source from ods_prism_gsxt_staff_change_df t1 where t1.pt = '20231101' and t1.dw_is_del = 0 group by t1.gid,t1.source) s1
group by s1.gid having count(s1.gid) = 1) change_s1
left join (SELECT * from ods_prism_gsxt_staff_change_df t2 where t2.pt = '20231101' and t2.dw_is_del = 0) change_s2
on change_s1.gid = change_s2.gid
-- select * from change_s1 left join change_s2 on change_s1.gid = change_s2.gid
where change_s2.gid is not null and change_s2.source = 'CUR' group by change_s1.gid) s5;

