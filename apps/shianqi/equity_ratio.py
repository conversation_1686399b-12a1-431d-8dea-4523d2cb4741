# encoding=utf8

import logging
from typing import Optional
from datetime import datetime, date, timedelta
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps
from libs.collection import sub_if
from gslib.reg_number import reg_number_valid
from gslib.credit_code import credit_code_valid
from gslib.gs_enum import EntityType
from typing import Type, Dict, TypeVar, Optional, Generator, List, Tuple

logger = logging.getLogger(__name__)


class EquityRatio(BaseEntity):
    id: int
    company_graph_id: int
    shareholder_type: EntityType
    shareholder_graph_id: int
    shareholder_name: str = Field(default=None)
    percent: float = Field(default=None)
    amount: float = Field(default=None)
    capital: str
    capitalActl:str
    deleted:int

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class EquityRatioDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.equity_ratio')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', EquityRatio)
        super().__init__(**kwargs)

    def update_shareholder_id(self, gid_old: int, gid_new) -> int:
        sql = 'update ignore {} set shareholder_graph_id=%s where shareholder_graph_id=%s limit 128'.format(self.db_tb_name)
        ret = self.mysql_client.execute(sql, args=(gid_new, gid_old))
        return ret

    def get_many(self, field, value, raw=False) -> Generator[EquityRatio, None, None]:
        sql = f'select * from {self.db_tb_name} where {field} = %s'
        for d in self.mysql_client.select_many(sql, args=(value,)):
            item = self._to_entity(d, raw=raw)
            if item is not None:
                yield item
