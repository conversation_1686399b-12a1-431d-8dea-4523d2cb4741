import requests
from compare import CompareService
from dao.qcc.general_crawl_task_input import GeneralCrawlTaskInput, GeneralCrawlTaskInputDao
from dao.qcc.general_crawl_task_output import GeneralCrawlTaskOutput, GeneralCrawlTaskOutputDao
import json
from dao.id_center import CreditCodeIndexDao
from dao.company_graph import CompanyGraphDao, CompanyGraph
from dao.company_staff_sort import CompanyStaffSortDao, CompanyStaffSort
import datetime
from dao.company import CompanyDao, Company
import time
from biz_utils.credit_code import credit_code_valid
def pitch_your_words(code):
    gsxt_touch_change = 'http://172.24.114.24/gsxt/v2/update/merge'
    params = {"companySign": code, "record": "redregcapInfo_saq", "channelType": 2,
              "channel": {"originalTime": "2024-02-01", "channelGroup": "user_search", "credit_code": code,
                          "change_dims": ["redregcapInfo"]}}
    res = requests.post(gsxt_touch_change, json=params)
    date = res.json()
    print(date)
if __name__ == '__main__':
    task_str = '91350212705432612T'
    arr = task_str.split(',')
    for item in arr:
        if not credit_code_valid(item):
            continue
        # print(credit_code_valid(task_str))
        pitch_your_words(item)
        # time.sleep(1)

