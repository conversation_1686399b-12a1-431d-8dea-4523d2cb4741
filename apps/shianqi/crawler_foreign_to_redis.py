import json
import logging
import time
from datetime import datetime
from typing import Optional

import ddddocr
from pydantic import Field, conint

# from PIL import Image
# from io import BytesIO
# from requests import Session, Response
from clients.redis._redis import Redis
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from libs.req import SessionRequestManager
from dao.company import CompanyDao, Company

redis_client = Redis(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com', port=6379,
                     password='3lvadmpiSj61ge', db=5)
redis_key_list = 'crawler_foreign_task_key'

if __name__ == '__main__':
    dao = CompanyDao(batch_size=1000, entity_class=Company)
    send_data = []
    for o in dao.scan(total=1000000, start=0):
        # o: Company
        company_type = o.company_org_type
        cid = o.cid
        if company_type and '有限责任公司' in company_type and ('港' in company_type or '澳' in company_type or '台' in company_type):
            send_data.append(json.dumps({"cid": cid, "name": o.name}))
            if len(send_data) > 128:
                ret = redis_client.rpush(redis_key_list, *send_data)
                send_data = []
    if len(send_data) > 0:
        ret = redis_client.rpush(redis_key_list, *send_data)
        send_data = []