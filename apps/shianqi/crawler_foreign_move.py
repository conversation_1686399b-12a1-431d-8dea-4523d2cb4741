import json
import logging
import time
from datetime import datetime
from typing import Optional

import ddddocr
from pydantic import Field, conint

# from PIL import Image
# from io import BytesIO
# from requests import Session, Response
from clients.redis._redis import Redis
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from libs.req import SessionRequestManager
from libs.concurrent import BoundedExecutor


class WzInvestorInfo(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=None)
    company_id: Optional[int] = Field(default=0)
    area_name: Optional[str] = Field(default='')
    amount: Optional[str] = Field(default='')
    investor_name: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default=None)
    update_time: Optional[datetime] = Field(default=None)
    deleted: Optional[int] = Field(default=0)
    def logic_validate(self) -> bool:
        return True


class CompanyWZDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_TEST.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.wz_investor_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', WzInvestorInfo)
        super().__init__(**kwargs)

class CompanyWZProDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.wz_investor_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', WzInvestorInfo)
        super().__init__(**kwargs)


company_wz_pro_dao = CompanyWZProDao()

if __name__ == '__main__':
    from libs.log import setup_logger
    logger = setup_logger()
    dao = CompanyWZDao(batch_size=1000, entity_class=None)
    for o in dao.scan(total=800000, start=246026):
        logger.info('%s', o)
        o: WzInvestorInfo
        sql = "INSERT INTO prism.wz_investor_info (company_id,area_name,amount,investor_name) VALUES (%s, %s, %s, %s)"
        data = (o['company_id'], o['area_name'], o['amount'], o['investor_name'])
        company_wz_pro_dao.mysql_client.insert(sql, data)
