from compare import CompareService
from dao.qcc.general_crawl_task_input import GeneralCrawlTaskInput, GeneralCrawlTaskInputDao
from dao.qcc.general_crawl_task_output import GeneralCrawlTaskOutput, GeneralCrawlTaskOutputDao
import json
from dao.id_center import CreditCodeIndexDao
from dao.company_graph import CompanyGraphDao, CompanyGraph
from dao.company_change_info import CompanyChangeInfo,CompanyChangeInfoDao
import datetime

if __name__ == '__main__':
    con = {
        'staff_name': {
            'type': 'str',  # 'double','int'
            'need_split': False,
            'is_json': False,
            # 'is_array': False
        },
        'sta_ff_type_name': {
            'type': 'str',
            'need_split': True,
            'is_json': False,
            # 'is_array': True,
        },
    }
    compare_service = CompareService()
    general_input_dao = GeneralCrawlTaskInputDao()
    general_output_dao = GeneralCrawlTaskOutputDao()
    credit_dao = CreditCodeIndexDao()
    company_graph_dao = CompanyGraphDao()
    company_change_info_dao = CompanyChangeInfoDao()
    task_name = 'pBetter_firm_page_normal'
    task_name_staff = 'pBetter_change_normal'
    today = datetime.date.today()
    yes = today - datetime.timedelta(days=3)
    create_time = str(yes) + ' 00:00:00'
    count = 0
    max_id = 1
    all_equal = 0
    tyc_has = 0
    qcc_has = 0
    all_has_but_position_not_equal = 0
    length_not_equal = dict()
    tyc_has_array_dic = dict()
    qcc_has_array_dic = dict()
    for i in range(100):
        items = general_input_dao.get_many_by_task_name_and_id(task_name_staff, create_time, max_id)
        if items:
            for item in items:
                item: GeneralCrawlTaskInput
                if max_id < item.id:
                    max_id = item.id
                if item.status != 1:
                    # 任务不是完成状态
                    continue
                print('获取到任务' + str(item.id))
                out_item = general_output_dao.get_by_input_id('input_id', item.id)
                if not out_item:
                    continue
                out_item: GeneralCrawlTaskOutput
                staff_output = out_item.output
                staff_out_json = json.loads(staff_output)
                # out_res = out_item.output
                # out_json = json.loads(out_res)
                # credit_code = out_json['credit_code']
                # if not credit_code:
                #     continue
                # firm_id = out_json['long_firm_id']
                company_name = staff_out_json['name']
                # item_staff = general_input_dao.get_one_by_task_name_and_firm_id(task_name_staff, create_time, firm_id)
                item_staff = general_input_dao.get_one_by_task_name_and_firm_id(task_name, create_time, company_name)
                item_staff: GeneralCrawlTaskInput
                if not item_staff:
                    continue
                staff_input_id = item_staff.id
                staff_out_item = general_output_dao.get_by_input_id('input_id', staff_input_id)
                staff_out_item: GeneralCrawlTaskOutput
                if not staff_out_item:
                    continue
                out_json = json.loads(staff_out_item.output)
                credit_code = out_json['credit_code']
                firm_id = out_json['long_firm_id']
                if not credit_code:
                    continue
                credit_res = credit_dao.get(credit_code)
                if not credit_res:
                    continue
                cid = credit_res['company_id']
                # company_graph_item = company_graph_dao.get_by_cid(cid)
                # company_graph_item: CompanyGraph
                # if not company_graph_item:
                #     continue
                # 目前为止，可以判断变更记录
                count = count + 1

                # gid = company_graph_item.cgid
                company_change_info_list = company_change_info_dao.get_many('company_id', cid)
                # compare_tyc = []
                # compare_qcc = []
                compare_tyc = set()
                compare_qcc = set()
                # staff_output = staff_out_item.output
                # staff_out_json = json.loads(staff_output)
                if 'change' in staff_out_json:
                    qcc_change = staff_out_json['change']
                    if qcc_change:
                        for qcc_it in qcc_change:
                            change_type = qcc_it['change_type']
                            change_type:str
                            change_type = change_type.replace('标记的为法定代表人', '')
                            if '住所变更' in change_type or '地址变更' in change_type:
                                change_type = '地址变更'
                            if '名称变更' in change_type:
                                change_type = '名称变更'
                            if '注册资本' in change_type or '注册资金' in change_type or '资金数额' in change_type:
                                change_type = '注册资本变更'
                            if '经营范围' in change_type:
                                change_type = '经营范围变更'
                            if '章程备案' in change_type:
                                change_type = '章程备案'
                            if '投资人变更' in change_type or '投资人（股权）变更' in change_type or ('投资人' in change_type and '变更' in change_type):
                                change_type = '投资人变更'
                            if '法定代表人' in change_type:
                                change_type = '法定代表人变更'
                            if '期限变更' in change_type or '经营期限' in change_type or '营业期限' in change_type:
                                change_type = '期限变更'
                            if '名称变更' in change_type:
                                change_type = '名称变更'
                            if '出资方式变更' in change_type:
                                change_type = '出资方式变更'
                            if '董事备案' in change_type:
                                change_type = '董事备案'
                            if '出资比例变更' in change_type:
                                change_type = '出资比例变更'
                            if '其他变更' == change_type or '其他变更;带有*' == change_type:
                                change_type = '其他变更'
                            key = qcc_it['change_date'] + change_type
                            compare_qcc.add(key)
                if company_change_info_list:
                    for tyc_change in company_change_info_list:
                        tyc_change: CompanyChangeInfo
                        change_type = tyc_change.change_item
                        change_type = change_type.replace('标记的为法定代表人', '')
                        if '住所变更' in change_type or '地址变更' in change_type:
                            change_type = '地址变更'
                        if '名称变更' in change_type:
                            change_type = '名称变更'
                        if '注册资本' in change_type or '注册资金' in change_type or '资金数额' in change_type:
                            change_type = '注册资本变更'
                        if '经营范围' in change_type:
                            change_type = '经营范围变更'
                        if '章程备案' in change_type:
                            change_type = '章程备案'
                        if '投资人变更' in change_type or '投资人（股权）变更' in change_type or (
                                '投资人' in change_type and '变更' in change_type):
                            change_type = '投资人变更'
                        if '法定代表人' in change_type:
                            change_type = '法定代表人变更'
                        if '期限变更' in change_type or '经营期限' in change_type or '营业期限' in change_type:
                            change_type = '期限变更'
                        if '名称变更' in change_type:
                            change_type = '名称变更'
                        if '出资方式变更' in change_type:
                            change_type = '出资方式变更'
                        if '董事备案' in change_type:
                            change_type = '董事备案'
                        if '出资比例变更' in change_type:
                            change_type  ='出资比例变更'
                        if '其他变更' == change_type or '其他变更;带有*' == change_type:
                            change_type = '其他变更'
                        key = tyc_change.change_time.strftime('%Y-%m-%d') + change_type
                        compare_tyc.add(key)
                if len(compare_tyc) != len(compare_qcc):
                    length_not_equal[str(cid) + '_' + firm_id] = ',saq,'.join(compare_tyc) + ';saq;' + ',saq,'.join(compare_qcc)
                    continue
                tyc_has_res = set()
                qcc_has_res = set()
                for it in compare_tyc:
                    if it not in compare_qcc:
                        tyc_has_res.add(it)
                for it in compare_qcc:
                    if it not in compare_tyc:
                        qcc_has_res.add(it)
                if tyc_has_res:
                    tyc_has = tyc_has + 1
                    tyc_has_array_dic[str(cid) + '_' + firm_id] = ',saq,'.join(tyc_has_res)
                if qcc_has_res:
                    qcc_has = qcc_has + 1
                    qcc_has_array_dic[str(cid) + '_' + firm_id] = ',saq,'.join(qcc_has_res)
                if not tyc_has_res and not qcc_has_res:
                    all_equal = all_equal + 1
                # res = compare_service.compare_array(compare_tyc, compare_qcc, con)
                # if not res['a_has_res'] and not res['b_has_res'] and not res['equal_not_equal']:
                #     all_equal = all_equal + 1
                #     print('all——equal')
                #     continue
                # if res['a_has_res']:
                #     tyc_has = tyc_has + 1
                #     tyc_has_array_dic[str(cid) + '_' + str(gid) + '_' + firm_id] = res['a_has_res']
                # if res['b_has_res']:
                #     qcc_has = qcc_has + 1
                #     qcc_has_array_dic[str(cid) + '_' + str(gid) + '_' + firm_id] = res['b_has_res']
                # if res['equal_not_equal']:
                #     all_has_but_position_not_equal = all_has_but_position_not_equal + 1
                #     equal_not_equal_res[str(cid) + '_' + str(gid) + '_' + firm_id] = res['equal_not_equal']
                # print('=====')
                print('====cid:' + str(cid) + ',firm_id:' + firm_id + ',' + str(tyc_has_res) + ',' + str(qcc_has_res))
    print('最终结果====tyc_has:' + str(tyc_has_array_dic))
    print('最终结果====qcc_has:' + str(qcc_has_array_dic))
    print('最终结果====length_not_equal:' + str(length_not_equal))
    print('最终结果all' + str(count) + ',all_equal:' + str(all_equal) + ',tyc_has:' + str(tyc_has) + ',qcc_has:' + str(
        qcc_has) + ',length_not_equal:' + str(len(length_not_equal)))
