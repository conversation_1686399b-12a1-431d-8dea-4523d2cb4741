import json
import logging
import time
from datetime import datetime
from typing import Optional

import ddddocr
from pydantic import Field, conint

# from PIL import Image
# from io import BytesIO
# from requests import Session, Response
from clients.redis._redis import Redis
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from libs.req import SessionRequestManager
from libs.concurrent import BoundedExecutor


class WzInvestorInfo(BaseEntity):
    id: conint(strict=True, ge=0) = Field(default=None)
    company_id: Optional[int] = Field(default=0)
    area_name: Optional[str] = Field(default='')
    amount: Optional[str] = Field(default='')
    investor_name: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default=None)
    update_time: Optional[datetime] = Field(default=None)
    deleted: Optional[int] = Field(default=0)
    def logic_validate(self) -> bool:
        return True


class CompanyWZDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_TEST.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.wz_investor_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', WzInvestorInfo)
        super().__init__(**kwargs)


redis_client = Redis(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com', port=6379,
                     password='3lvadmpiSj61ge', db=5)
redis_key_list = 'crawler_foreign_task_key'
# logger = logging.getLogger(__name__)
orc = ddddocr.DdddOcr(show_ad=False)
wz_dao = CompanyWZDao()

PROCESS_WORKER_COUNT = 8

executor_pool = BoundedExecutor(max_workers=PROCESS_WORKER_COUNT)

def process(id_json):
    try:
        cid = id_json['cid']
        name = id_json['name']
        request_manager = SessionRequestManager()
        # session = requests.session()
        search_res = get_search_data(request_manager, name)
        if not search_res or search_res == '':
            # logger.info(f'搜索失败id:{cid},keyword:{name}')
            print('搜索失败id:' + str(cid) + ',keyword:' + name)
            return
        search_result = search_res['data']['wzResult']['result'][0]
        detail_data = get_detail_data(request_manager, search_result)
        amount_unit = detail_data['data']['wzResult']['UNITNAME']
        investor_result_data = detail_data['data']['investorResult']
        if len(investor_result_data) == 0 or investor_result_data is None:
            # logger.info(f"no investor id:{cid},keyword:{name}")
            print('no investor id:' + str(cid) + ',keyword:' + name)
            return
        # company_name = detail_data['data']['wzResult']['ENTP_NAME']
        save_investor(investor_result_data, cid, amount_unit)
        print('处理完成cid:' + str(cid) + ',keyword:' + name)
    except:
        print('执行任务出现异常' + str(id_json))


def save_investor(data, company_id, amount_unit):
    for row in data:
        investor_name = row['INVESTOR_NAME']
        amount = str(row['CAPITAL_AMOUNT'] + amount_unit)
        area_name = row['COUNTRYNAME']
        # 插入数据之前先判断 是否存在
        wz_obj = query_is_exist(investor_name, company_id)
        if wz_obj:
            continue
        else:
            sql = "INSERT INTO prism.wz_investor_info (company_id,area_name,amount,investor_name) VALUES (%s, %s, %s, %s)"
            data = (company_id, area_name, amount, investor_name)
            wz_dao.mysql_client.insert(sql, data)
            # logger.info("insert one data ok")


def query_is_exist(investor_name, company_id):
    fides = ['investor_name', 'company_id']
    values = [investor_name, company_id]
    wz_obj = wz_dao.get_ex(values, fides)
    return wz_obj


def get_detail_data(request_manager, search_data):
    detail_url = "https://wzzxbs.mofcom.gov.cn/gspt/infoPub/entp/search/wzEntpDetail"
    data = {
        'entpId': search_data['ENTP_MAIN_ID'],
        'token': search_data['TOKEN']
    }
    heads = {
        'Referer': "https://wzzxbs.mofcom.gov.cn/gspt/",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"
    }
    detail_resp = request_manager.request(
        url=detail_url,
        post_data=data,
        headers=heads,
        timeout=30
    )
    return detail_resp.json()


def get_search_data(session, keyword):
    for i in range(3):
        # session = requests.session()
        url = f"https://wzzxbs.mofcom.gov.cn/gspt/infoPub/entp/search/vCode?r={str(int(time.time() * 1000))}"
        resp = session.request(
            url=url,
            timeout=60
        )
        content = resp.content
        # img = Image.open(BytesIO(content))
        # img.save('image.jpg')
        # 获取图片的验证码
        result = orc.classification(content).lower()
        if len(result) != 4:
            continue
        # 用验证码过验证
        check_code_data = {
            'searchWzCode': result
        }
        check_code_url = "https://wzzxbs.mofcom.gov.cn/gspt/infoPub/entp/search/checkVCode"
        check_code_resp = session.request(
            url=check_code_url,
            post_data=check_code_data,
            timeout=60
        )
        verify_code = check_code_resp.json()
        if verify_code['message'] != "验证码正确":
            continue
        # 过搜索
        time.sleep(4)
        search_url = "https://wzzxbs.mofcom.gov.cn/gspt/infoPub/entp/search/searchEntpList"
        data = {
            'keyWord': keyword,
            'searchWzCode': result
        }
        heads = {
            'Referer': "https://wzzxbs.mofcom.gov.cn/gspt/",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"
        }
        search_resp = session.request(
            url=search_url,
            post_data=data,
            headers=heads,
            timeout=60
        )
        search_data = search_resp.json()
        if search_data['message'] == '':
            return search_resp.json()
    return None


if __name__ == '__main__':
    while True:
        try:
            id_str = redis_client.lpop(redis_key_list)
            # id_str = '{"cid": 184109642,"name":"广东红苹果科技有限公司"}'
            if id_str:
                id_json = json.loads(id_str)
                # process(id_json)
                executor_pool.submit(process, id_json)
            else:
                # logging.info('未取到任务，休眠五秒钟')
                print('未取到任务，休眠五秒钟')
                time.sleep(5)
        except:
            # logging.info('执行异常')
            print('执行异常')
