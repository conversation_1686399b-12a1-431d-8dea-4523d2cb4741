import json
import logging
import time
from datetime import datetime
from typing import Optional

import ddddocr
from pydantic import Field, conint

# from PIL import Image
# from io import BytesIO
# from requests import Session, Response
from clients.redis._redis import Redis
from dao.deps.mysql_dao import MySQLDao
from entity.deps.entity import BaseEntity
from libs.env import ConstantProps
from libs.req import SessionRequestManager
from libs.concurrent import BoundedExecutor
from dao.company_reg_capital import CompanyRegCapital,CompanyRegCapitalDao
from dao.company import CompanyDao,Company

redis_client = Redis(host='redis-b7bd5364-0555-48ca-87c8-4bf2290622df.cn-north-4.dcs.myhuaweicloud.com', port=6379,
                     password='3lvadmpiSj61ge', db=5)
# redis_client = Redis(host='redis-bc8ade66-d5d2-4653-9aaf-6401f43b3479.cn-north-4.dcs.myhuaweicloud.com', port=6379,
#                      password='s3zSjZaxduont9qg0pcv', db=5)
redis_key_list = 'dealCompanyLegalPersonNameToRedis'

if __name__ == '__main__':
    company_reg_cap_dao = CompanyRegCapitalDao()
    company_dao = CompanyDao()
    for name in ['343038']:
        # 实时股权冻结与历史股权冻结重复
        input_path = f'/Users/<USER>/Downloads/status_ok_hehuo(1).csv'
        with open(input_path, 'r') as f:
            send_data = []
            for lid, line, in enumerate(f):
                cid, *_ = line.strip().split(',')
                # if lid < 1:
                #     continue
                send_data.append(cid)
                if len(send_data) > 256:
                    redis_client.lpush(redis_key_list, *send_data)
                    send_data = []
            if len(send_data) > 0:
                redis_client.lpush(redis_key_list, *send_data)