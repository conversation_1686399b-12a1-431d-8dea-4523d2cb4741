import os
import random
import re
from collections import defaultdict
import openpyxl
import jieba

from dao.company import CompanyDao, CompanyPercentageDao
from dao.internal.branch_dig import BranchDigDao
from dao.internal.branch_gs import BranchGsDao
from dao.gsxt_history_name import GSXTHistoryNameDao
from libs.env import ConstantProps
from dao.company_graph import CompanyGraphDao


class branch:

    def __init__(self):
        self.CompanyDao = CompanyDao()
        self.BranchDigDao = BranchDigDao(**ConstantProps.PROPS_GS_INNER_RW)
        self.BranchGsDao = BranchGsDao(**ConstantProps.PROPS_GS_INNER_RW)
        self.GSXTHistoryNameDao = GSXTHistoryNameDao()
        self.CompanyPercentageDao = CompanyPercentageDao(**ConstantProps.PROPS_GS_INNER_RW)
        self.CompanyGraphDao = CompanyGraphDao()
        self.marks = ('社', '厂', '公司', '农场', '局', '院', '所', '中心', '馆', '站', '药房')
        self.branch_keyword = ('分公司', '办事处', '分支')
        self.filtration_word = ('工会', '招待', '委员会')


    def compare(self, same_name, infos):
        for name in same_name:
            result = None
            num = float('inf')
            for info in infos:
                if info['name'] == name:
                    temp = list(info.values()).count(None)
                    if temp < num:
                        num = temp
                        result = info
            yield result

    def base_input(self, input):
        word_list = jieba.lcut(input)
        pattern = '|'.join([re.escape(mark) + r'\Z' for mark in self.marks])
        res = list()
        for item, word in enumerate(word_list):
            matches = re.search(pattern, word)
            if matches:
                res.append(''.join(word_list[:item + 1]))
        return res

    def filtration(self, s):
        word_list = jieba.lcut(s)
        pattern = '|'.join([re.escape(mark) + r'\Z' for mark in self.filtration_word])
        for item, word in enumerate(word_list):
            matches = re.search(pattern, word)
            if matches:
                return True
        return None

    def select_many_company(self, name):
        sql = "select * from {} where name = %s and (length(property1) = 18 or length(reg_number) = 15 or length(reg_number) = 13)".format(
            self.CompanyDao.db_tb_name)
        company = [i for i in self.CompanyDao.mysql_client.select_many(sql, args=(name,))]
        if company == [] or len(company) > 1:
            return None, None
        res = company[0]
        name_list = [name]

        if company[0]['property2'] and company[0]['property2'] != '':
            sql = "select * from {} where id = %s and (length(property1) = 18 or length(reg_number) = 15 or length(reg_number) = 13)".format(
                self.CompanyDao.db_tb_name)
            name_ = self.CompanyDao.mysql_client.select(sql, args=(company[0]['property2'],))
            name_list.append(name_['name'])
            res = name_

        sql = "select * from {} where cid = %s".format(self.GSXTHistoryNameDao.db_tb_name)
        history = [i['history_name'] for i in
                   self.GSXTHistoryNameDao.mysql_client.select_many(sql, args=(company[0]['id'],))]
        name_list.extend(history)

        return res, name_list



    def select_many_company_fuzzy(self, name):
        sql = "select * from {} where name like %s".format(
            self.CompanyDao.db_tb_name)
        infos = list(self.CompanyDao.mysql_client.select_many(sql, args=(name,)))
        names = [info['name'] for info in infos]
        same_names = [item for item in set(names) if names.count(item) > 1]

        companys = list()
        companys_same = list()
        # sql = "select * from {} where name = %s".format(
        #     self.CompanyDao.db_tb_name)
        # infos = [dict(self.CompanyDao.mysql_client.select(sql, ('北京市熊猫烟花有限公司第二十九零售点', )))]
        for company in infos:
            if company['reg_number'] is None and company['property1'] is None:
                continue

            if self.filtration(company['name']):
                continue

            while company and company['property2'] and company['property2'] != '':
                sql = "select * from {} where id = %s".format(self.CompanyDao.db_tb_name)
                company = self.CompanyDao.mysql_client.select(sql, args=(company['property2'],))
                # print(company)
            if company['name'] not in same_names:
                companys.append(company)
            else:
                companys_same.append(company)
        if companys_same:
            for info in self.compare(same_names, companys_same):
                companys.append(info)

        return companys

    def select_company_name(self, cid):
        sql = "select * from {} where id = %s and property2 = '' and length(name) > 5 and (property1 is not NULL or reg_number is not NULL)".format(
            self.CompanyDao.db_tb_name)
        company = self.CompanyDao.mysql_client.select(sql, args=(cid,))
        return company

    def select_many_branch(self, cid):
        sql = "select * from {} where cid = %s".format(self.BranchGsDao.db_tb_name)
        value = (str(cid),)
        branch_gs = [i for i in self.BranchGsDao.mysql_client.select_many(sql, args=value)]
        return branch_gs

    def update_company(self, cid, ID):
        sql = "UPDATE {} SET parent_id = %s WHERE id = %s;".format(self.CompanyDao.db_tb_name)
        value = (cid, ID)
        self.CompanyDao.mysql_client.execute(sql, args=value)

    def check(self, cid):
        name = self.select_company_name(cid)
        if not name:
            return None, None, None, None
        name = name['name']
        input = self.base_input(name)
        print(input)
        branch = list()
        id_list = list()
        name_list = []
        for p in input:
            com, name_list = self.select_many_company(p)
            if not com:
                continue
            name = com['name']

            cid = com['id']
            if com['company_org_type'] and '分' not in com['company_org_type']:
                for name_ in name_list:
                    com_branch = self.select_many_company_fuzzy(name_ + '%')
                    for bran in com_branch:
                        if bran['id'] != cid:
                            branch.append(
                                {'cid': str(cid), 'branch_name': bran['name'], 'branch_cid': str(bran['id']),
                                 'source': 0,
                                 'is_delete': 0, 'other_info': None})
                            id_list.append(str(bran['id']))

                break

        if branch:
            for bra in self.select_many_branch(cid):
                if bra['branch_cid'] not in id_list:
                    branch.append(
                        {'cid': str(cid), 'branch_name': bra['branch_name'], 'branch_cid': bra['branch_cid'],
                         'source': 0,
                         'is_delete': 0, 'other_info': None})
            for bra in branch:
                self.update_company(cid, bra['branch_cid'])
            self.BranchDigDao.i_u_by_any(*branch)

        return branch, str(cid), str(dict(self.CompanyGraphDao.get(cid, 'company_id'))['cgid']), name, name_list

    def Validation(self, cid, branch, com_list):
        bra = list()
        for com in com_list:
            obj = self.CompanyDao.get(com, 'name')
            if not obj:
                continue
            id_ = dict(obj)['cid']
            bra_com = [i for i in self.CompanyDao.get_many(id_, 'parent_id')]
            for com in bra_com:
                bra.append(dict(com))
        #
        # company = dict(self.CompanyDao.get(cid, 'id'))
        # if company['parent_id'] and company['parent_id'] != 0:
        #     bra = [dict(i) for i in self.CompanyDao.get_many(company['parent_id'], 'parent_id')]
        # elif company['parent_id'] is None or company['parent_id'] == 0:
        #     sql = "select * from {} where parent_id = %s".format(self.CompanyDao.db_tb_name)
        #     bra = [dict(i)['id'] for i in self.CompanyDao.mysql_client.select_many(sql, args=(cid,))]
        # else:
        #     bra = []

        if len(bra) != len(branch):
            bra_id = list()
            branch_id = list()
            for i in bra:
                bra_id.append(str(i['cid']))
            for i in branch:
                branch_id.append(str(i['branch_cid']))

            inc_cid = list()
            dec_cid = list()
            for bra in bra_id:
                if bra not in branch_id:
                    dec_cid.append(bra)
            for branch in branch_id:
                if branch not in bra_id:
                    inc_cid.append(branch)

            # inc_ = inc_[:3]
            # dec_ = dec_[:3]

            inc_gid = list()
            inc_name = list()
            for inc in inc_cid:
                inc_gid.append(dict(self.CompanyGraphDao.get(inc, 'company_id'))['cgid'])
                inc_name.append(dict(self.CompanyDao.get(inc, 'id'))['name'])
            dec_gid = list()
            dec_name = list()
            for dec in dec_cid:
                dec_gid.append(dict(self.CompanyGraphDao.get(dec, 'company_id'))['cgid'])
                dec_name.append(dict(self.CompanyDao.get(dec, 'id'))['name'])
            return inc_name, inc_gid, inc_cid, dec_name, dec_gid, dec_cid
        else:
            return None, None, None, None, None, None


    def run(self):
        # input = '南宁天恒电影有限责任公司'
        # b, cid, gid, name = branch.check(280964)
        # print(b, cid, gid, name)

        A = [{('宁波市鄞州家家乐药房邬隘分店', 73334734): (12, 9)}]
        b, cid, gid, name, com_list = self.check(list(A[0].keys())[0][1])
        inc_name, inc_gid, inc_cid, dec_name, dec_gid, dec_cid = self.Validation(cid, b, com_list)
        print(b)
        print(inc_name)
        print(inc_gid)
        print(inc_cid)
        print(dec_name)
        print(dec_gid)
        print(dec_cid)

        wb = openpyxl.load_workbook('./output.xlsx')

        # for a in A:
        #     b, cid, gid, name, com_list = self.check(list(a.keys())[0][1])
        #     if not b:
        #         continue
        #     if cid:
        #         inc_name, inc_gid, inc_cid, dec_name, dec_gid, dec_cid = self.Validation(cid, b, com_list, sign=True)
        #         if not inc_name:
        #             continue
        #         ws = wb['差异样本']
        #         wa = wb['所有样本']
        #         max_row_num = ws.max_row
        #
        #         for item, _ in enumerate(inc_name):
        #             new_row_data = [name, int(gid), int(cid), inc_name[item], int(inc_gid[item]), int(inc_cid[item]),
        #                             '增加']
        #
        #             # 将新数据添加到最大行数的下一行
        #             for i in range(1, len(new_row_data) + 1):
        #                 ws.cell(max_row_num + 1, i).value = new_row_data[i - 1]
        #             max_row_num += 1
        #         for item, _ in enumerate(dec_name):
        #             new_row_data = [name, int(gid), int(cid), dec_name[item], int(dec_gid[item]), int(dec_cid[item]),
        #                             '减少']
        #
        #             # 将新数据添加到最大行数的下一行
        #             for i in range(1, len(new_row_data) + 1):
        #                 ws.cell(max_row_num + 1, i).value = new_row_data[i - 1]
        #             max_row_num += 1
        #
        #         max_row_num = wa.max_row
        #
        #         n = 0
        #         for com in com_list:
        #             obj = self.CompanyDao.get(com, 'name')
        #             if not obj:
        #                 continue
        #             id_ = dict(obj)['cid']
        #             n += len([i for i in self.CompanyDao.get_many(id_, 'parent_id')])
        #
        #         new_row_data = [name, len(b), n]
        #         for i in range(1, len(new_row_data) + 1):
        #             wa.cell(max_row_num + 1, i).value = new_row_data[i - 1]
        #             # 保存文件
        #         wb.save('output.xlsx')

A = [
    # {('北京市海淀区鹍鹏农工商公司', 21642): (0, 1)},
    # {('北京国经太和塑料有限公司', 33888): (0, 1)},
    {('北京开关厂', 36110): (14, 38)},
    # {('北京凝华科技有限公司', 45552): (0, 1)},
    # {('北京雄霸康宇科技发展有限公司', 46107): (0, 1)},
    # {('北京我爱我家房地产经纪有限公司昌平第十三分公司', 48882): (1252, 1270)},
    # {('北京首邮报刊亭有限责任公司海淀第九十六报刊亭', 51657): (1038, 1035)},
    # {('北京首邮报刊亭有限责任公司朝阳第十八报刊亭', 52212): (1038, 1035)},
    {('保乐力加（中国）贸易有限公司北京分公司', 59429): (25, 27)},
    # {('北京嘉利橡塑制品厂', 61650): (0, 1)},
    {('北京市首冶晶华电子仪器厂', 69429): (0, 4)},
    # {('北京沃尔德防灾绿化技术有限公司', 77201): (0, 1)},
    # {('北京肯德基有限公司甘家口餐厅', 81644): (664, 668)},
    # {('北京朗漫书刊发行连锁有限责任公司第三十六分公司', 82199): (619, 620)},
    # {('北京京成隆达停车管理有限公司金属制品厂', 82754): (1, 2)},
    {('北京纪凯知识产权代理有限公司西城分部', 88860): (3, 4)},
    {('北京气象领略咨询有限责任公司', 91636): (3, 2)},
    # {('北京吉隆利达出租汽车有限责任公司', 98298): (0, 1)},
    {('北京永旺物业管理有限公司', 99963): (0, 1)},
    # {('北京创辉源商贸有限公司', 105516): (1, 2)},
    # {('北京市大中家用电器连锁销售有限公司海淀第四分公司', 107182): (95, 93)},
    # {('兴业银行股份有限公司北京金源支行', 121064): (2679, 2835)},
    # {('华润超级市场有限公司北京鸭子桥华润万家便利超市', 136612): (137, 139)},
    # {('北京巴拉巴拉服饰有限公司西单第一分店', 138833): (102, 100)},
    {('北京檀州烟花鞭炮有限公司密云县第一零售点', 143832): (16, 17)},
    {('北京新网数码信息技术有限公司', 147718): (38, 40)},
    {('北京郊区电信实业有限公司通讯器材分公司', 149383): (19, 20)},
    # {('北京中机伟林矿山机械有限公司', 161042): (2, 3)},
    {('北京鼓风机厂', 162152): (3, 17)},
    # {('北京航石科技公司', 166595): (2, 1)},
    # {('北京唯致动力网络信息科技有限公司', 167705): (0, 1)},
    # {('海洋国际旅行社有限责任公司北京望京南门市部', 169370): (639, 634)},
    {('北京石林土石方工程有限公司', 178253): (0, 1)},
    {('北京市大兴县南郊磁性材料厂', 178808): (0, 1)},
    # {('北京安信瑞德房地产经纪有限公司管庄店', 183248): (402, 404)},
    {('北京捷普创达科技有限公司蒋台分公司', 189908): (2, 3)},
    # {('北京中原房地产经纪有限公司第二百三十四分公司', 202123): (400, 409)},
    # {('北京顺驰不动产网络有限公司蒲黄榆店', 214336): (173, 192)},
    # {('柒一拾壹（北京）有限公司南竹杆胡同店', 214891): (459, 461)},
    # {('嘉事堂药业股份有限公司北京永安里药店', 215446): (150, 154)},
    # {('北京良工投资有限责任公司', 224887): (0, 1)},
    # {('北京毅都永誉农产品有限公司', 227662): (0, 1)},
    # {('北京德尔福技术开发有限公司', 231547): (0, 1)},
    {('北京市通州区丽新照相馆有限责任公司', 235990): (4, 5)},
    {('北京索德电气工业有限公司', 240431): (3, 4)},
    # {('北京青年旅行社股份有限公司东外大街营业部', 242096): (380, 376)},
    # {('北京同聚晟永兴餐饮有限公司分公司', 248202): (1, 2)},
    # {('广亚铝业有限公司', 250423): (1, 3)},
    # {('北京天蜂奇科技开发有限公司', 253754): (2, 3)},
    # {('北京辉宏卡普隆科贸有限公司朝阳分公司', 267083): (3, 4)},
    # {('北京万生堂商贸中心第二分店', 267640): (4, 3)},
    {('中国海洋工程公司北京经营部', 273190): (15, 18)},
    # {('北京北大维信生物科技有限公司北京办事处', 276523): (31, 32)},
    # {('北京链家房地产经纪有限公司广渠门外分店', 277633): (2600, 2602)},
    # {('北京链家房地产经纪有限公司海淀西三旗建材城西路店', 278188): (2600, 2602)},
    {('北京华江文化发展有限公司第一分公司', 280964): (4, 5)},
    {('北京市康达汽车装修厂装饰部', 281519): (0, 6)},
    # {('北京神舟国际旅行社集团有限公司和平西街门市部', 282630): (346, 335)},
    # {('中国饲料集团公司', 283185): (0, 1)},
    # {('北京市通铭环保设备厂技术开发部', 284853): (0, 1)},
    # {('北京东方康弘体育发展有限公司朝阳体育健身分公司', 285408): (7, 8)},
    # {('天茂实业集团股份有限公司', 288738): (1, 4)},
    # {('北京市双兴出租汽车有限责任公司', 293734): (0, 1)},
    # {('北京京客隆商业集团股份有限公司六十八便利店', 298732): (168, 173)},
    # {('北京亚豪房地产经纪有限公司', 299287): (14, 15)},
    # {('北京港佳好邻居连锁便利店有限责任公司苏州街三分店', 300954): (395, 391)},
    # {('北京京创华龙贸易有限公司', 302064): (0, 1)},
    {('北京巴比龙时装有限公司时装店', 302619): (0, 9)},
    # {('德安华（北京）商业风险管理咨询有限公司', 304840): (3, 4)},
    # {('北京市烟花鞭炮有限公司海淀区第五十六零售点', 306505): (290, 284)},
    # {('北京金麦食品有限公司欧格风西饼店', 307617): (1, 2)},
    # {('北京京佑商贸有限公司', 310395): (0, 1)},
    {('北京朝园物业管理中心', 313170): (0, 3)},
    # {('北京港佳好邻居连锁便利店有限责任公司地安门西大街二分店', 315391): (395, 391)},
    # {('广州甘峰听力设备有限公司北京咨询分公司', 316501): (3, 2)},
    # {('北京天科投资咨询有限公司', 318721): (0, 1)},
    # {('北京丫髻山投资管理有限公司', 323718): (0, 1)},
    # {('北京蓝色假日国际旅行社有限公司永定路门市部', 324273): (117, 118)},
    {('中国电影出版社印刷厂北京昌平分厂', 327603): (1, 13)},
    # {('北京中恒驿站数码信息技术有限公司第四营业部', 330378): (60, 58)},
    # {('北京凯撒国际旅行社有限责任公司宣武门外大街门市部', 334264): (205, 209)},
    # {('北京泰源春工贸有限公司餐厅', 335376): (3, 4)},
    # {('北京市朝阳三建第六建筑工程公司第六工程队', 337596): (6, 8)},
    {('中国四达国际经济技术合作公司贸易部', 338151): (12, 9)},
    # {('北京华都佳诚劳动事务咨询服务有限责任公司', 338707): (0, 1)},
    # {('北京市金星卓宏幕墙工程有限公司装饰分公司', 342595): (50, 51)},
    {('北京顺鑫牵手有限责任公司生物食品分公司', 343150): (3, 4)},
    # {('北京雪花电器集团公司新技术开发公司', 343705): (24, 26)},
    {('赛特国际旅行社北京美惠门市部', 344260): (16, 23)},
    # {('北京喜运商贸中心经营部', 348148): (0, 1)},
    # {('北京大兴水泵厂水泵加工服务站', 348703): (0, 4)},
    # {('北京市华光实业公司餐厅', 351479): (1, 2)},
    # {('北京翌鑫盛世物业管理有限责任公司文化发展分公司', 353146): (2, 3)},
    {('廊坊发展股份有限公司', 355366): (0, 4)},
    {('北京市第二肉类联合加工厂熟肉门市部', 355921): (0, 10)},
    # {('北京新速度商贸有限公司第三分公司', 359809): (7, 4)},
    # {('中国铁道旅行社北京鼓楼南大街营业部', 364250): (214, 346)},
    {('北京市隆华商贸公司朝阳汽车配件分公司', 367026): (0, 5)},
    # {('北京市昌平市政经济发展服务中心林营停车场', 368693): (0, 4)},
    # {('北京天本科贸有限责任公司', 372579): (6, 7)},
    # {('北京鼎新联兴投资发展中心（有限合伙）', 378131): (0, 2)},
    # {('北京大京港文化企业服务中心慧发达电器商场', 386461): (0, 1)},
    # {('人民电器集团有限公司北京特利来销售分公司', 391460): (532, 506)},
    # {('密云县檀州宾馆福利木器厂饭店', 396459): (0, 4)},
    # {('北京德邦货运代理有限公司丰台卢沟桥南里分公司', 401457): (341, 342)},
    # {('北京广建宾馆建康台球厅', 402012): (0, 3)},
    # {('丝芙兰（北京）化妆品销售有限公司中关村南大街分公司', 403123): (107, 106)},
    # {('北京双竞科技有限公司', 431990): (0, 1)},
    {('河南向东机械厂', 433655): (2, 14)},
    # {('华普信息技术有限公司北京第一营业部', 436988): (82, 81)},
    {('北京瑞驰国际旅行社有限责任公司德胜门门市部', 441993): (3, 9)},
    # {('北京伟业我爱我家地产投资顾问有限公司朝阳分公司', 443659): (3, 2)},
    # {('北京奥索克体育用品有限公司双井第一分公司', 444771): (7, 8)},
    {('北京京都薇薇国际美容美体连锁有限公司第八分公司', 445326): (10, 12)},
    # {('北京智美力合广告有限公司分公司', 453651): (1, 2)},
    # {('国盛证券有限责任公司北京分公司', 459201): (409, 412)},
    # {('北京市农工商开发贸易公司劳动服务公司供销经理部', 461979): (0, 2)},
    # {('北京市日用工业品批发市场鞋帽服装采购供应站皮鞋经营部', 463090): (0, 2)},
    # {('北京丽华靓泽制衣厂', 465869): (0, 1)},
    # {('北京风光岁月网络技术服务中心', 466424): (0, 1)},
    {('汉普管理咨询（中国）有限公司北京分公司', 475861): (0, 6)},
    # {('北京鑫尊房地产经纪有限责任公司第二百五十九分公司', 479748): (286, 303)},
    # {('北京日东升投资有限责任公司', 480303): (0, 1)},
    # {('威高集团有限公司', 483633): (2, 5)},
    # {('北京创和愿望树餐饮管理有限公司海淀分公司', 491961): (1, 2)},
    # {('北京国润景园投资中心（有限合伙）', 495291): (0, 2)},
    # {('北京国瑞物业服务有限公司国瑞城物业服务中心', 499179): (32, 33)},
    # {('北京中金稳健创业投资中心（有限合伙）', 499734): (0, 2)},
    # {('北京诚信四海市政建设发展有限公司昌平分公司', 500844): (14, 15)},
    {('北京市熊猫烟花有限公司海淀区第十七零售点', 504731): (844, 809)},
    # {('北京坤元隆丰投资管理中心（有限合伙）', 508064): (0, 2)},
    # {('北京华兴银汇资本运营中心（有限合伙）', 509177): (0, 2)},
    # {('北京国大药房连锁有限公司营慧寺连锁店', 511956): (350, 367)},
    # {('北京银河华盛资产管理中心（有限合伙）', 512511): (0, 2)},
    # {('巨龙在线（北京）科技发展有限公司', 513066): (7, 14)},
    # {('北京佳明佳阳光餐饮有限公司第二小关早餐厅', 515845): (88, 92)},
    # {('北京广盟电气有限公司', 518621): (2, 3)},
    # {('北京乐工场资产管理有限公司第一分公司', 519733): (2, 3)},
    # {('北京耐思沃德教育科技有限公司', 521399): (2, 3)},
    # {('北京华钜津桥联合商务咨询有限公司', 528620): (108, 109)},
    {('广州欢网科技有限责任公司北京分公司', 529730): (4, 5)},
    # {('安邦财产保险股份有限公司北京市昌平支公司', 535840): (1171, 1766)},
    # {('北京北建陆港国际物流有限公司', 538615): (0, 1)},
    # {('北京易车互动广告有限公司', 539170): (28, 29)},
    {('北京苏宁电器有限公司通州店', 545283): (33, 34)},
    # {('北京泛地金泰投资有限公司', 546955): (0, 1)},
    # {('内蒙古恒信实业集团有限责任公司', 548620): (1, 2)},
    # {('北京国发投咨询服务中心（有限合伙）', 554727): (0, 2)},
    {('北京高鑫投资管理公司', 562502): (0, 4)},
    # {('北京市凯伯特建设工程股份有限公司', 564170): (0, 2)},
    {('北京华润名汇服饰有限公司北京第三分公司', 574163): (4, 5)}]



if __name__ == '__main__':
    branch = branch()
    branch.run()





        # break

    # branch = branch()
    # diff = list()
    # num = 0
    # for i in range(1000):
    #     sql = 'SELECT * FROM {} limit {}, 1'.format(branch.CompanyDao.db_tb_name, str((i + 1) * 555))
    #     Company = branch.CompanyDao.mysql_client.select(sql)
    #     print(f'cid: {str(Company["id"])}')
    #     info, cid = branch.check(Company['id'])
    #     if not info or info == []:
    #         continue
    #     num += 1
    #     if p := branch.Validation(Company["id"], info, False):
    #         diff.append(p)
    # print(f'一共检查了{num}家存在分支机构的公司')
    # print(diff)
