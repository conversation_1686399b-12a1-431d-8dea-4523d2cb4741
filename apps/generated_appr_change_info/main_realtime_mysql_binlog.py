# encoding=utf8
import json
import argparse
import time
from datetime import timedelta, datetime
from concurrent.futures import ThreadPoolExecutor
from threading import Lock

from libs.log2 import setup_logger
from libs.env import ConstantProps, get_stack_info
from libs.dt import to_datetime
from clients.kafka_client import KafkaConsumerClient
from entity.entity_binlog import BinlogEntity
from dao.company import CompanyGraphDao, CompanyGraph
from apps.generated_appr_change_info.main_process import redis_queue

logger = setup_logger(debug=False)
company_graph_dao = CompanyGraphDao()


cache: dict[int, datetime] = {}  # cache for the last update of id
cache_lock: Lock = Lock()


def topic_executor_fn(topic: str, id_field: str):
    kafka_consumer = KafkaConsumerClient(
        kafka_topic=topic,
        group_id='generated_appr_change_info',
        **ConstantProps.PROPS_KAFKA_CANAL_PROD,
    )
    for sid, s in enumerate(kafka_consumer.read()):
        try:
            # if sid > 100:
            #     logger.info('test first 100 data.')
            #     break
            d = json.loads(s)
            binlog: BinlogEntity = BinlogEntity.from_dict(d)
            if not binlog:
                continue
            # if to_datetime(binlog.ts) + timedelta(seconds=30) > datetime.now():
            #     logger.info('wait 10 secs for binlog delay.')
            #     time.sleep(10)
            # for data_, changes in zip(binlog.data, binlog.old or []):
            for data_idx, data_ in enumerate(binlog.data):
                changes = binlog.old[data_idx] if binlog.old else []
                if len(changes) > 0 and all(x in ['updatetime', 'crawledtime', 'create_time', 'update_time'] for x in changes):
                    continue
                id_value = data_[id_field]
                if 'graph' in id_field:
                    o: CompanyGraph = company_graph_dao.get(value=id_value, field='graph_id')
                    if not o:
                        logger.warning(f'not found company_graph_dao {id_value}')
                    id_value = o.cid
                with cache_lock:
                    cache[id_value] = to_datetime(binlog.ts)
        except Exception as e:
            logger.warning(f'e={e} s={s} trace={get_stack_info()}')
            continue
    kafka_consumer.close()


def main():
    ap = argparse.ArgumentParser(description='main_realtime_mysql_binlog.py')
    ap.add_argument('--topic-cfgs', nargs='+', default=[
        'prism.company|id',
        'prism1.company_change_info|company_id',
        'prism.equity_ratio|company_graph_id',
        'prism1.company_staff|company_id'
    ])
    ap_args = ap.parse_args()
    logger.info(f'ap_args={ap_args}')

    with ThreadPoolExecutor(max_workers=len(ap_args.topic_cfgs), thread_name_prefix='topic_executor') as topic_executor:
        for topic_cfg in ap_args.topic_cfgs:
            topic, id_field = topic_cfg.split('|')
            topic_executor.submit(topic_executor_fn, topic, id_field)

        while True:
            time.sleep(5.0)
            with cache_lock:
                for id_value, ts in list(cache.items()):
                    if ts + timedelta(seconds=30) < datetime.now():
                        redis_queue.push(id_value, realtime=True)
                        logger.info(f'send task {id_value}')
                        del cache[id_value]
                logger.info(f'current cache size {len(cache)}')


if __name__ == '__main__':
    main()
