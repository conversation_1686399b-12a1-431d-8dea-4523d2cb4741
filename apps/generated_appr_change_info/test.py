# encoding=utf8
import json
import pprint
import unittest
from libs.log2 import setup_logger
from apps.generated_appr_change_info.process import *

logger = setup_logger(debug=True)

# 中科鑫睿（北京）技术有限公司 224917000
# 上海常思丝实业有限公司 2440129161 补充缺失部分变更项的情形，主要是上海


class TestMain(unittest.TestCase):
    def setUp(self):
        pass

    def new_output(self, tc):
        new_output = []
        if tc.output:
            for i in tc.output:
                if i.change_item == '投资人变更（包括出资额、出资方式、出资日期、投资人名称等）':
                    change_before = json.loads(i.change_before)
                    change_after = json.loads(i.change_after)
                    for j in change_before:
                        if 'investor_id' in j:
                            del j['investor_id']
                        if 'investor_type' in j:
                            del j['investor_type']
                    for j in change_after:
                        if 'investor_id' in j:
                            del j['investor_id']
                        if 'investor_type' in j:
                            del j['investor_type']
                    change_before = json.dumps(change_before, ensure_ascii=False)
                    change_after = json.dumps(change_after, ensure_ascii=False)
                    i.change_before = change_before
                    i.change_after = change_after
                if i.change_item == '负责人变更（法定代表人、负责人、首席代表、合伙事务执行人等变更）':
                    change_before = json.loads(i.change_before)
                    change_after = json.loads(i.change_after)

                    if'legal_person_id' in change_before:
                        del change_before['legal_person_id']
                    if 'legal_person_type' in change_before:
                        del change_before['legal_person_type']

                    if 'legal_person_id' in change_after:
                        del change_after['legal_person_id']
                    if 'legal_person_type' in change_after:
                        del change_after['legal_person_type']
                    change_before = json.dumps(change_before, ensure_ascii=False)
                    change_after = json.dumps(change_after, ensure_ascii=False)
                    i.change_before = change_before
                    i.change_after = change_after
                if i.change_item == '高级管理人员备案（董事、监事、经理等）':
                    change_before = json.loads(i.change_before)
                    change_after = json.loads(i.change_after)
                    for j in change_before:
                        if'staff_id' in j:
                            del j['staff_id']
                        if 'positions' in j:
                            j['positions'] = sorted(j['positions'])
                    for j in change_after:
                        if'staff_id' in j:
                            del j['staff_id']
                        if 'positions' in j:
                            j['positions'] = sorted(j['positions'])
                    change_before = json.dumps(sorted(change_before, key=lambda x:x['staff_name']), ensure_ascii=False)
                    change_after = json.dumps(sorted(change_after, key=lambda x:x['staff_name']), ensure_ascii=False)
                    i.change_before = change_before
                    i.change_after = change_after
                new_output.append(i)
        return new_output

    def test_北京狂野之马信息技术有限公司(self):
        tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 1901834,}))
        output = self.new_output(tc)
        gac_item = GeneratedApprChangeInfo(
                company_id=1901834,
                change_date=to_date('2024-10-10'),
                change_item='投资人变更（包括出资额、出资方式、出资日期、投资人名称等）',
                change_before=json.dumps([{"investor_name":"宁波明侨投资管理有限公司","subscribe_str":"5万元","subscribe_percent_str":"5%"},
                              {"investor_name":"汪峰","subscribe_str":"8.5万元","subscribe_percent_str":"8.5%"},
                              {"investor_name":"李宏杰","subscribe_str":"51万元","subscribe_percent_str":"51%"},
                              {"investor_name": "天津金米投资合伙企业（有限合伙）", "subscribe_str": "10万元", "subscribe_percent_str": "10%"},
                              {"investor_name": "姚吕", "subscribe_str": "8.5万元", "subscribe_percent_str": "8.5%"},
                              {"investor_name": "王啸", "subscribe_str": "17万元", "subscribe_percent_str": "17%"}],ensure_ascii=False),

                change_after=json.dumps([{"investor_name":"宁波明侨投资管理有限公司","subscribe_str":"5万元","subscribe_percent_str":"7.7519%"},
                              {"investor_name":"李宏杰","subscribe_str":"51万元","subscribe_percent_str":"79.0698%"},
                              {"investor_name": "姚吕", "subscribe_str": "8.5万元", "subscribe_percent_str": "13.1783%"}],ensure_ascii=False)
            )
        self.assertIn(gac_item, output)

    def test_北京宫艺文化有限公司(self):
        tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 2373997000, }))
        output = self.new_output(tc)
        gac_item = GeneratedApprChangeInfo(
            company_id=2373997000,
            change_date=to_date('2024-09-18'),
            change_item='高级管理人员备案（董事、监事、经理等）',

            change_before=json.dumps(
                sorted([
                    {"positions": sorted(["经理","执行董事"]), "staff_name": "朱斌"},
                    {"positions": sorted(["监事"]), "staff_name": "张磊"}
                ], key=lambda x: x['staff_name']),
                ensure_ascii=False),

            change_after=json.dumps(
                sorted([
                {"positions": sorted(["董事", "财务负责人", "经理"]), "staff_name": "左义婷"},
                {"positions": sorted(["监事"]), "staff_name": "邱德龙"}
                ], key=lambda x: x['staff_name']),
                ensure_ascii=False)
        )
        self.assertIn(gac_item, output)
        # self.assertTrue(['2024-09-18', '负责人变更（法定代表人、负责人、首席代表、合伙事务执行人等变更）', '朱斌', '左义婷'] in tc.output)
        # self.assertTrue(['2024-09-18', '高级管理人员备案（董事、监事、经理等）',
        #                  {'朱斌': {'经理', '执行董事'}, '张磊': {'监事'}},
        #                  {'左义婷': {'经理', '董事', '财务负责人'}, '邱德龙': {'监事'}}] in tc.output)
        # self.assertTrue(['2024-09-18', '投资人变更（包括出资额、出资方式、出资日期、投资人名称等）',
        #                  {'许恒微': '出资额:350万元;出资比例:35%', '朱斌': '出资额:650万元;出资比例:65%'},
        #                  {'左义婷': '出资额:1000万元;出资比例:100%'}] in tc.output)
        # self.assertTrue(['2024-06-25', '地址变更（住所地址、经营场所、驻在地址等变更）', '北京市房山区书院南街10号院1号楼9层913', '北京市通州区永乐店镇企业服务中心1号-6476号'] in tc.output)
        # self.assertTrue(['2024-06-25', '登记机关变更', '北京市房山区市场监督管理局', '北京市通州区市场监督管理局'] in tc.output)

    # def test_北京德纳兰景观设计有限公司(self):
    #     # qcc将多次认缴展示为【出资额:118,24万元;出资比例:71%】认为qcc展示不合理
    #     tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 685209, }))
    #     self.assertTrue(['2024-05-13', '投资人变更（包括出资额、出资方式、出资日期、投资人名称等）',
    #                      {'陈合金': '出资额:610万元;出资比例:61%', '孟月玲': '出资额:240万元;出资比例:24%', '北京德纳兰建筑景观设计顾问有限公司': '出资额:150万元;出资比例:15%'},
    #                      {'陈合金': '出资额:118,24万元;出资比例:71%', '孟月玲': '出资额:58万元;出资比例:29%'}] in tc.output)

    def test_北京扬铭盛景商贸有限公司(self):
        # 与友商对比主要人员职位不相同， tyc比qcc多一项【市场主体类型变更】b:'有限责任公司(自然人投资或控股)', a:'有限责任公司(自然人独资)'
        tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 2434094215, }))
        output = self.new_output(tc)
        gac_item = GeneratedApprChangeInfo(
            company_id=2434094215,
            change_date=to_date('2024-09-12'),
            change_item='高级管理人员备案（董事、监事、经理等）',

            change_before=json.dumps(
                sorted([
                    {"positions": sorted(["监事"]), "staff_name": "彭更旺"},
                    {"positions": sorted(["执行董事","经理"]), "staff_name": "王娇娇"}
                ], key=lambda x: x['staff_name']),
                ensure_ascii=False),

            change_after=json.dumps(
                sorted([
                    {"positions": sorted(["董事", "财务负责人", "经理"]), "staff_name": "魏媛媛"},
                ], key=lambda x: x['staff_name']),
                ensure_ascii=False)
        )
        self.assertIn(gac_item, output)


    # def test_六盘水盛恩商贸有限公司(self):
    #     # tyc少主要人员变更，2022年前的数据不能生产出来
    #     tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': *********, }))
    #     self.assertTrue(['2016-12-13', '高级管理人员备案（董事、监事、经理等）',
    #                      {'何吓俤':{'监事',}},
    #                      {'杨英':{'执行董事兼总经理',}}] in tc.output)

    def test_内蒙古汾州酒业有限公司(self):
        # 【市场主体类型变更】需要过滤 b:'有限责任公司(自然人投资或控股)(1130)' a:'有限责任公司(自然人投资或控股)'
        tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': *********, }))
        output = self.new_output(tc)
        gac_item = GeneratedApprChangeInfo(
            company_id=*********,
            change_date=to_date('2023-10-19'),
            change_item='登记机关变更',

            change_before='呼和浩特市赛罕区市场监督管理局',

            change_after='呼和浩特市赛罕区行政审批和政务服务局'
        )
        self.assertIn(gac_item, output)

    # def test_信阳益农文化传媒有限公司(self):
    #     # 我们少一次抓取导致数据错
    #     tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 2408328839, }))
    #     self.assertTrue(['2022-04-07', '高级管理人员备案（董事、监事、经理等）',
    #                      {'周波':{'执行董事兼总经理'},'周玉梅':{'监事'}},
    #                      {'周波':{'执行董事兼总经理'},'周玉梅':{'监事'},'赵承文':{'财务负责人'}}] in tc.output)

    def test_北京久润行商贸有限公司(self):
        tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 610091, }))
        output = self.new_output(tc)
        gac_item = GeneratedApprChangeInfo(
            company_id=610091,
            change_date=to_date('2024-09-06'),
            change_item='地址变更（住所地址、经营场所、驻在地址等变更）',

            change_before='北京市丰台区造甲街110号28幢A1-1553',

            change_after='北京市丰台区青塔大成里芳园4号楼3层302号'
        )
        self.assertIn(gac_item, output)

        # self.assertTrue(['2024-09-06', '地址变更（住所地址、经营场所、驻在地址等变更）',
        #                  '北京市丰台区造甲街110号28幢A1-1553',
        #                  '北京市丰台区青塔大成里芳园4号楼3层302号'] in tc.output)

    # def test_北京润东海泰文化旅游服务有限公司(self):
    #     # 股东信息少抓取一次，导致股东信息错
    #     tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 2470848000, }))
    #     self.assertTrue(['2024-05-27', '投资人变更（包括出资额、出资方式、出资日期、投资人名称等）',
    #                      {'北京海泰百骑庄园红酒贸易有限公司': '出资额:600万元;出资比例:66.6667%',
    #                       '韦金龙':'出资额:150万元;出资比例:16.6667%',
    #                       '北京汇川源翼科技发展有限公司':'出资额:150万元;出资比例:16.6667%'},
    #                      {'北京海泰百骑庄园红酒贸易有限公司': '出资额:600万元;出资比例:40%',
    #                       '韦金龙':'出资额:150万元;出资比例:10%',
    #                       '北京汇川源翼科技发展有限公司':'出资额:150万元;出资比例:10%',
    #                       '北京章丘海泰饭店管理有限公司':'出资额:600万元;出资比例:40%'}] in tc.output)

    # def test_北京优胜众联管理有限公司(self):
    #     # 主要人员bug 【孟如生】职位多了【财务负责人】
    #     tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 2453643047, }))
    #     output = self.new_output(tc)
    #     # gac_item = GeneratedApprChangeInfo(
    #     #     company_id=2453643047,
    #     #     change_date=to_date('2024-01-02'),
    #     #     change_item='地址变更（住所地址、经营场所、驻在地址等变更）',
    #     #     change_before='北京市昌平区安居路7号院6号楼7层722',
    #     #     change_after='北京市昌平区昌平镇西关三角地综合业务楼3单元1-3-3',
    #     # )
    #     # self.assertIn(gac_item, tc.output)
    #     gac_item2 = GeneratedApprChangeInfo(
    #         company_id=2453643047,
    #         change_date=to_date('2024-01-02'),
    #         change_item='高级管理人员备案（董事、监事、经理等）',
    #
    #         change_before=json.dumps(
    #             sorted([
    #                 {"positions": sorted(["财务负责人","执行董事","经理"]), "staff_name": "孟如意"},
    #                 {"positions": sorted(["监事"]), "staff_name": "刘增如"}
    #             ], key=lambda x: x['staff_name']),
    #             ensure_ascii=False),
    #
    #         change_after=json.dumps(
    #             sorted([
    #                 {"positions": sorted(["监事"]), "staff_name": "孙向国"},
    #                 {"positions": sorted(["执行董事","经理"]), "staff_name": "孟如生"}
    #             ], key=lambda x: x['staff_name']),
    #             ensure_ascii=False)
    #     )
    #     self.assertIn(gac_item2, output)

    # def test_东海县华电福新新能源有限公司(self):
    #     # 主要人员【刘冰】职位【总经理】与qcc【刘冰】职位【总经理，执行公司事务的董事，国家工商也无该职位信息
    #     tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 2386466260, }))
    #     output = self.new_output(tc)
    #     gac_item2 = GeneratedApprChangeInfo(
    #         company_id=2386466260,
    #         change_date=to_date('2024-10-23'),
    #         change_item='高级管理人员备案（董事、监事、经理等）',
    #
    #         change_before=json.dumps(
    #             sorted([
    #                 {"positions": sorted(["执行董事", "总经理"]), "staff_name": "张新山"},
    #                 {"positions": sorted(["监事"]), "staff_name": "李勇"}
    #             ], key=lambda x: x['staff_name']),
    #             ensure_ascii=False),
    #
    #         change_after=json.dumps(
    #             sorted([
    #                 {"positions": sorted(["监事"]), "staff_name": "李勇"},
    #                 {"positions": sorted(["执行公司事务的董事", "总经理"]), "staff_name": "刘冰"}
    #             ], key=lambda x: x['staff_name']),
    #             ensure_ascii=False)
    #     )
    #     self.assertIn(gac_item2, output)


    def test_上海古野机械设备有限公司(self):
        tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 58133338, }))
        output = self.new_output(tc)
        gac_item = GeneratedApprChangeInfo(
            company_id=58133338,
            change_date=to_date('2024-06-06'),
            change_item='登记机关变更',

            change_before='黄浦区市场监督管理局',

            change_after='宝山区市场监督管理局'
        )
        self.assertIn(gac_item, output)

    def test_上海市浦东新区书院粮食管理有限公司(self):
        tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 30236207, }))
        output = self.new_output(tc)
        gac_item2 = GeneratedApprChangeInfo(
            company_id=30236207,
            change_date=to_date('2023-08-25'),
            change_item='高级管理人员备案（董事、监事、经理等）',

            change_before=json.dumps(
                sorted([
                    {"positions": sorted(["监事"]), "staff_name": "胡伟"},
                    {"positions": sorted(["监事"]), "staff_name": "姜珊"}
                ], key=lambda x: x['staff_name']),
                ensure_ascii=False),

            change_after=json.dumps(
                sorted([
                    {"positions": sorted(["监事"]), "staff_name": "姜珊"},
                    {"positions": sorted(["监事"]), "staff_name": "徐文付"}
                ], key=lambda x: x['staff_name']),
                ensure_ascii=False)
        )
        self.assertIn(gac_item2, output)

    # def test_上海志斯贸易有限公司(self):
    #     # 主要人员职位 tyc【'董事长', '总经理'】 qcc 【董事长兼总经理】
    #     tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': *********, }))
    #     output = self.new_output(tc)
    #     gac_item2 = GeneratedApprChangeInfo(
    #         company_id=*********,
    #         change_date=to_date('2024-06-19'),
    #         change_item='高级管理人员备案（董事、监事、经理等）',
    #
    #         change_before=json.dumps(
    #             sorted([
    #                 {"positions": sorted(["董事"]), "staff_name": "刘巍"},
    #                 {"positions": sorted(["董事"]), "staff_name": "ZOU YU"},
    #                 {"positions": sorted(["监事"]), "staff_name": "黄硕宙"},
    #                 {"positions": sorted(["财务负责人"]), "staff_name": "章燕"},
    #                 {"positions": sorted(["董事长兼总经理"]), "staff_name": "林潮东"}
    #             ], key=lambda x: x['staff_name']),
    #             ensure_ascii=False),
    #
    #         change_after=json.dumps(
    #             sorted([
    #                 {"positions": sorted(["董事"]), "staff_name": "刘巍"},
    #                 {"positions": sorted(["董事"]), "staff_name": "ZOU YU"},
    #                 {"positions": sorted(["监事"]), "staff_name": "黄硕宙"},
    #                 {"positions": sorted(["财务负责人"]), "staff_name": "章燕"},
    #                 {"positions": sorted(["董事长兼总经理"]), "staff_name": "李伟文"}
    #             ], key=lambda x: x['staff_name']),
    #             ensure_ascii=False)
    #     )
    #     self.assertIn(gac_item2, output)

    def test_上海榕光投资管理有限公司(self):
        tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 69870067, }))
        output = self.new_output(tc)
        gac_item = GeneratedApprChangeInfo(
            company_id=69870067,
            change_date=to_date('2024-02-02'),
            change_item='登记机关变更',

            change_before='嘉定区市场监督管理局',

            change_after='徐汇区市场监督管理局'
        )
        self.assertIn(gac_item, output)

    # def test_上海云律金猫科技有限公司(self):
    #     # 该公司主要人员只有一个版本，所以没有变更信息
    #     tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': *********, }))
    #     output = self.new_output(tc)
    #     gac_item2 = GeneratedApprChangeInfo(
    #         company_id=*********,
    #         change_date=to_date('2023-05-16'),
    #         change_item='高级管理人员备案（董事、监事、经理等）',
    #
    #         change_before=json.dumps(
    #             sorted([
    #                 {"positions": sorted(["执行董事"]), "staff_name": "朱国云"},
    #                 {"positions": sorted(["监事"]), "staff_name": "叶丹"}
    #             ], key=lambda x: x['staff_name']),
    #             ensure_ascii=False),
    #
    #         change_after=json.dumps(
    #             sorted([
    #                 {"positions": sorted(["执行董事"]), "staff_name": "朱国云"},
    #                 {"positions": sorted(["监事"]), "staff_name": "叶丹"},
    #                 {"positions": sorted([""]), "staff_name": "王丹"}
    #             ], key=lambda x: x['staff_name']),
    #             ensure_ascii=False)
    #     )
    #     self.assertIn(gac_item2, output)

    def test_上海永禾益贸易有限公司(self):
        tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': *********, }))
        output = self.new_output(tc)
        gac_item = GeneratedApprChangeInfo(
            company_id=*********,
            change_date=to_date('2024-03-07'),
            change_item='登记机关变更',

            change_before='松江区市场监督管理局',

            change_after='闵行区市场监督管理局'
        )
        self.assertIn(gac_item, output)

    # def test_上海沪新欧国际运输有限公司(self):
    #     # 该公司主要人员版本只有一个，所以没有变更信息
    #     tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 2331871384, }))
    #     output = self.new_output(tc)
    #     gac_item2 = GeneratedApprChangeInfo(
    #         company_id=2331871384,
    #         change_date=to_date('2023-03-29'),
    #         change_item='高级管理人员备案（董事、监事、经理等）',
    #
    #         change_before=json.dumps(
    #             sorted([
    #                 {"positions": sorted(["监事"]), "staff_name": "师晓鹏"},
    #                 {"positions": sorted(["执行董事"]), "staff_name": "张夏"}
    #             ], key=lambda x: x['staff_name']),
    #             ensure_ascii=False),
    #
    #         change_after=json.dumps(
    #             sorted([
    #                 {"positions": sorted(["监事"]), "staff_name": "师晓鹏"},
    #                 {"positions": sorted(["未知"]), "staff_name": "张夏"},
    #                 {"positions": sorted(["执行董事"]), "staff_name": "宋飞阳"}
    #             ], key=lambda x: x['staff_name']),
    #             ensure_ascii=False)
    #     )
    #     self.assertIn(gac_item2, output)

    def test_上海煌痣贸易有限公司(self):
        tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 2436776108, }))
        output = self.new_output(tc)
        gac_item = GeneratedApprChangeInfo(
            company_id=2436776108,
            change_date=to_date('2023-10-07'),
            change_item='登记机关变更',

            change_before='嘉定区市场监督管理局',

            change_after='奉贤区市场监督管理局'
        )
        self.assertIn(gac_item, output)

    # def test_上海优虎信息技术有限公司(self):
    #     # 该公司主要人员只有一个版本，所以没有变更信息
    #     tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 6608847, }))
    #     output = self.new_output(tc)
    #     gac_item2 = GeneratedApprChangeInfo(
    #         company_id=6608847,
    #         change_date=to_date('2020-08-21'),
    #         change_item='高级管理人员备案（董事、监事、经理等）',
    #
    #         change_before=json.dumps(
    #             sorted([
    #                 {"positions": sorted(["监事"]), "staff_name": "蔡亮"},
    #                 {"positions": sorted(["执行董事"]), "staff_name": "文晓凤"}
    #             ], key=lambda x: x['staff_name']),
    #             ensure_ascii=False),
    #
    #         change_after=json.dumps(
    #             sorted([
    #                 {"positions": sorted(["监事"]), "staff_name": "蔡亮"},
    #                 {"positions": sorted(["执行董事"]), "staff_name": "徐凤珠"}
    #             ], key=lambda x: x['staff_name']),
    #             ensure_ascii=False)
    #     )
    #     self.assertIn(gac_item2, output)

    def test_东晟电子科技靖江有限公司(self):
        #
        tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 2312814339, }))
        output = self.new_output(tc)
        gac_item2 = GeneratedApprChangeInfo(
            company_id=2312814339,
            change_date=to_date('2024-07-12'),
            change_item='高级管理人员备案（董事、监事、经理等）',

            change_before=json.dumps(
                sorted([
                    {"positions": sorted(["执行董事","总经理"]), "staff_name": "周来福"},
                    {"positions": sorted(["监事"]), "staff_name": "王裕芳"}
                ], key=lambda x: x['staff_name']),
                ensure_ascii=False),

            change_after=json.dumps(
                sorted([
                    {"positions": sorted(["总经理"]), "staff_name": "周梅芳"},
                    {"positions": sorted(["监事"]), "staff_name": "黄嘉玮"}
                ], key=lambda x: x['staff_name']),
                ensure_ascii=False)
        )
        self.assertIn(gac_item2, output)

    def test_北京企御云科技有限公司(self):
        tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': *********, }))
        output = self.new_output(tc)
        gac_item = GeneratedApprChangeInfo(
            company_id=*********,
            change_date=to_date('2024-09-10'),
            change_item='注册资本变更（注册资金、资金数额等变更）',

            change_before='200万元人民币',

            change_after='10万元人民币'
        )
        self.assertIn(gac_item, output)

    # def test_北京东郊兴旺商贸有限公司(self):
    #     # 经营范围 标点符号差异 文字部分一致
    #     tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 42705650, }))
    #     output = self.new_output(tc)
    #     gac_item = GeneratedApprChangeInfo(
    #         company_id=42705650,
    #         change_date=to_date('2023-02-16'),
    #         change_item='经营范围变更（含业务范围变更）',
    #
    #         change_before='销售卫生间用品、文具用品、五金交电、纸制品、清洁用品、日用品、装饰材料、建筑材料、工艺品、厨房用具、一次性用品;会议服务;承办展览展示;清洁服务。(企业依法自主选择经营项目,开展经营活动;依法须经批准的项目,经相关部门批准后依批准的内容开展经营活动;不得从事本市产业政策禁止和限制类项目的经营活动。)',
    #
    #         change_after='一般项目:日用杂品销售;卫生洁具销售;文具用品零售;办公用品销售;金属工具销售;纸制品销售;厨具卫具及日用杂品批发;日用品批发;日用陶瓷制品销售;建筑材料销售;工艺美术品及收藏品零售(象牙及其制品除外);搪瓷制品销售;家用电器销售;食品用塑料包装容器工具制品销售;日用木制品销售;第一类医疗器械销售;医护人员防护用品批发;卫生用品和一次性使用医疗用品销售;个人卫生用品销售;第二类医疗器械销售;会议及展览服务;建筑物清洁服务;餐饮器具集中消毒服务;专业保洁、清洗、消毒服务;酒店管理;食品互联网销售(仅销售预包装食品);消毒剂销售(不含危险化学品);食品销售(仅销售预包装食品);服装辅料销售;防腐材料销售;日用玻璃制品销售;日用百货销售。(除依法须经批准的项目外,凭营业执照依法自主开展经营活动开展经营活动)许可项目:烟草制品零售。(依法须经批准的项目,经相关部门批准后方可开展经营活动,具体经营项目以相关部门批准文件或许可证件为准)(不得从事国家和本市产业政策禁止和限制类项目的经营活动。)'
    #     )
    #     self.assertIn(gac_item, output)

    def test_上海弘之骋管理咨询有限公司(self):
        tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 2427554721, }))
        output = self.new_output(tc)
        gac_item2 = GeneratedApprChangeInfo(
            company_id=2427554721,
            change_date=to_date('2024-09-10'),
            change_item='高级管理人员备案（董事、监事、经理等）',

            change_before=json.dumps(
                sorted([
                    {"positions": sorted(["监事"]), "staff_name": "林家铭"},
                    {"positions": sorted(["财务负责人"]), "staff_name": "董传飞"},
                    {"positions": sorted(["执行董事"]), "staff_name": "翁怡莹"}
                ], key=lambda x: x['staff_name']),
                ensure_ascii=False),

            change_after=json.dumps(
                sorted([
                    {"positions": sorted(["监事"]), "staff_name": "林家铭"},
                    {"positions": sorted(["财务负责人"]), "staff_name": "董传飞"},
                    {"positions": sorted(["董事"]), "staff_name": "朱怡霖"}
                ], key=lambda x: x['staff_name']),
                ensure_ascii=False)
        )
        self.assertIn(gac_item2, output)

    # def test_三门峡华亿劳务服务有限公司(self):
    #     # case 不成立
    #     tc = process(TaskContext(inputs={'did': 0, 'company_2.cid': 2326865587, }))
    #     output = self.new_output(tc)
    #     gac_item = GeneratedApprChangeInfo(
    #         company_id=2326865587,
    #         change_date=to_date('2023-02-15'),
    #         change_item='负责人变更（法定代表人、负责人、首席代表、合伙事务执行人等变更）',
    #
    #         change_before='许梦',
    #
    #         change_after='冯圣博'
    #     )
    #     self.assertIn(gac_item, output)
