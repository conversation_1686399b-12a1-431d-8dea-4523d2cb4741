# encoding=utf8

import logging
from datetime import date
from pydantic import Field
from entity.deps.entity import BaseEntity
from dao.deps.mysql_dao import MySQLDao
from libs.env import ConstantProps

logger = logging.getLogger(__name__)


class GeneratedApprChangeInfo(BaseEntity):
    id: int = Field(default=0)
    cid: int = Field(alias='company_id', default=0)
    change_date: date = Field(default=date(year=2000, month=1, day=1))
    change_item: str = Field(default='')
    change_before: str = Field(default='')
    change_after: str = Field(default='')

    def __init__(self, **kwargs):
        if kwargs.get('change_before') is None:
            kwargs['change_before'] = ''
        if kwargs.get('change_after') is None:
            kwargs['change_after'] = ''
        super().__init__(**kwargs)


class GeneratedApprChangeInfoDao(MySQLDao):
    def __init__(self, **kwargs):
        for k, v in ConstantProps.PROPS_GS_OUTER_RW.items():
            kwargs.setdefault(k, v)
        kwargs.setdefault('db_tb_name', 'prism.generated_appr_change_info')
        kwargs.setdefault('pk_name', 'id')
        kwargs.setdefault('entity_class', GeneratedApprChangeInfo)
        super().__init__(**kwargs)


if __name__ == '__main__':
    dao = GeneratedApprChangeInfoDao()
    for item in dao.scan(start=1, total=1000):
        print(item)
