# encoding=utf8
import json
import re
from typing import Dict, List
import logging
import time
from enum import Enum, auto
from dataclasses import dataclass, field
from concurrent.futures import Future
from collections import Counter
from libs.env import get_stack_info
from dao.company import Company, CompanyDao, CompanyGraph, CompanyGraphDao
from dao.human import HumanGraph, HumanGraphDao
from dao.key_person.history_company_staff import HistoryCompanyStaffDao
from dao.key_person.company_staff import CompanyStaffDao
from dao.company_change_info import CompanyChangeInfoDao, CompanyChangeInfo
from dao.investors.equity_ratio import EquityRatioDao, EquityRatio
from gslib.msv import msv_query_base_info, msv_query_list_dim_human
from libs.dt import to_date, to_datetime
from apps.generated_appr_change_info.deps import GeneratedApprChangeInfoDao
from apps.generated_appr_change_info.deps import GeneratedApprChangeInfo

logger = logging.getLogger(__file__)

fs = dict()
process_code_stat = Counter()

company_dao = CompanyDao()
company_graph_dao = CompanyGraphDao()
human_graph_dao = HumanGraphDao()
history_company_staff_dao = HistoryCompanyStaffDao()
company_staff_dao = CompanyStaffDao()
equity_ratio_dao = EquityRatioDao()
company_change_info_dao = CompanyChangeInfoDao()
ga_change_info_dao = GeneratedApprChangeInfoDao()


class ProcessCode(Enum):
    INITIAL = auto()
    EXCEPTION = auto()

    DUP = auto()  # 已经生效过的
    EXCLUDE = auto()  # 非目标集合
    OK = auto()  # 本次目标集合


@dataclass
class TaskContext(object):
    inputs: Dict
    code: ProcessCode = ProcessCode.INITIAL
    count_base_info: int = field(default=0)
    count_staff: int = field(default=0)
    count_investor: int = field(default=0)
    count_num: int = field(default=0)
    context: Dict = field(default_factory=dict)
    output: List[GeneratedApprChangeInfo] = field(default_factory=list)
    ts: float = field(default_factory=time.time)


def process_base(tc: TaskContext, company: Company, change_info_lst: List[CompanyChangeInfo]):
    msv_base_list = []  # 每个appr_date的首次抓取时间
    for item in msv_query_base_info(cid=company.cid):
        msv_appr_date = to_date(item['approved_time'])
        if not msv_appr_date:
            continue
        if len(msv_base_list) == 0 or to_date(msv_base_list[-1]['approved_time']) > to_date(item['approved_time']):
            msv_base_list.append(item)
        elif to_date(msv_base_list[-1]['approved_time']) == to_date(item['approved_time']):
            msv_base_list[-1] = item
    for item in msv_base_list:
        logger.debug(f'msv_base={item["approved_time"]} {to_datetime(item["msv_first"])}')
    tc.context['msv_base_list'] = msv_base_list
    for cur_id in range(len(msv_base_list) - 1):
        msv_base_cur, msv_base_lst = msv_base_list[cur_id], msv_base_list[cur_id + 1]
        for key in (msv_base_cur.keys() | msv_base_lst.keys()):
            if key in ['id', 'msv_deleted', 'msv_first', 'source', 'msv_last_time', 'cid', 'msv_last', 'approved_time', 'legal_person_id']:
                continue
            cur = msv_base_cur.get(key, None)
            lst = msv_base_lst.get(key, None)
            if key == 'reg_capital':
                cur = p_float(parse_amount(cur)) + '万元人民币'
                lst = p_float(parse_amount(lst)) + '万元人民币'
            elif key == 'legal_person_name':
                if '合伙' in company.company_org_type:
                    continue
                if cur == '' or lst == '':
                    continue
                cur = {'legal_person_name': cur, 'legal_person_id': msv_base_cur.get('legal_person_id'), 'legal_person_type': msv_base_cur.get('legal_person_type')}
                lst = {'legal_person_name': lst, 'legal_person_id': msv_base_lst.get('legal_person_id'), 'legal_person_type': msv_base_lst.get('legal_person_type')}
                cur = json.dumps(cur, ensure_ascii=False)
                lst = json.dumps(lst, ensure_ascii=False)
            if cur == lst:
                continue
            if key in ['reg_institute', 'company_org_type'] and (cur in lst or lst in cur):
                continue
            if key == 'business_scope' and (len(lst) < 5 or len(cur) < 5):
                continue
            if key in ['reg_status', 'reg_number', 'from_time']:
                continue
            if key == 'reg_capital' and (lst == '0万元人民币' or cur == '0万元人民币'):
                continue
            if key == 'company_org_type':
                if lst.replace('台港澳', '港澳台') == cur.replace('台港澳', '港澳台'):
                    continue
                if lst.replace('（', '(').replace('）', ')') == cur.replace('（', '(').replace('）', ')'):
                    continue

            if company.credit_code.startswith('9144') and key == 'reg_institute' and (len(cur) == 3 or len(lst) == 3):
                # 深圳登记机关判断 南山局
                continue
            if company.credit_code.startswith('9144') and key == 'business_scope':
                # 深圳 经营范围 先跳过 有格式化变更问题
                continue

            # 判断变更记录是否出现过
            seen = False
            for change_info_item in change_info_lst:
                if to_date(change_info_item.change_time) != to_date(msv_base_cur['approved_time']):
                    continue
                if key == 'reg_capital' and '注册资本' in change_info_item.change_item:
                    seen = True
                if key == 'business_scope' and '经营范围' in change_info_item.change_item:
                    seen = True
                if key == 'to_time' and '营业期限' in change_info_item.change_item:
                    seen = True
                if key == 'legal_person_name' and '法定代表人' in change_info_item.change_item:
                    seen = True
                if key == 'reg_location' and '住所' in change_info_item.change_item:
                    seen = True
                if key == 'company_org_type' and '主体类型' in change_info_item.change_item:
                    seen = True
                if key == 'legal_person_name' and '负责人' in change_info_item.change_item:
                    seen = True
                if key == 'name' and '名称变更' in change_info_item.change_item:
                    seen = True
                # 这个判断逻辑过去宽松，会导致正确的数据被过滤
                # if cur and lst and cur in change_info_item.content_after and lst in change_info_item.content_before:
                #     seen = True

            if seen:
                logger.debug(f'seen {lst} -> {cur}')
                continue

            tc.count_base_info = 1
            tc.count_num += 1

            change_item_dic = {
                'legal_person_name': '负责人变更（法定代表人、负责人、首席代表、合伙事务执行人等变更）',
                'reg_location': '地址变更（住所地址、经营场所、驻在地址等变更）',
                'reg_institute': '登记机关变更',
                'reg_capital': '注册资本变更（注册资金、资金数额等变更）',
                'business_scope': '经营范围变更（含业务范围变更）',
                'company_org_type': '市场主体类型变更',
                'name': '名称变更（字号名称、集团名称等）',
            }
            if key not in change_item_dic:
                logger.info(f'base info ignore key={key}')
                continue
            change_item = change_item_dic.get(key, key)
            appr_date = msv_base_cur['approved_time']
            logger.info(f'>>> {appr_date} {change_item} {lst} -> {cur}')
            # tc.output.append([appr_date, change_item, lst, cur])
            ga_change_info = GeneratedApprChangeInfo(
                company_id=company.cid,
                change_date=appr_date,
                change_item=change_item,
                change_before=lst,
                change_after=cur,
            )
            if ga_change_info:
                tc.output.append(ga_change_info)


def p_float(f):
    s = f'{f:.4f}'
    if '.' in s:
        while s[-1] == '0':
            s = s[:-1]
        if s[-1] == '.':
            s = s[:-1]
    return s


def staff_positions_merge(positions: set):
    position_list = []
    for pos in ['执行董事', '经理', '董事', '财务负责人']:
        if pos in positions:
            position_list.append(pos)
    for pos in positions:
        if pos not in position_list:
            position_list.append(pos)
    return '（' + ','.join(position_list) + '）'


def process_staff(tc: TaskContext, company: Company, change_info_lst: List[CompanyChangeInfo]):
    msv_base_list: List[dict] = tc.context['msv_base_list']

    msv_staff_list = []  # 相同核准日期最多选一个，且把核准日期放到item中
    base_idx = 0  # 当前对齐照面信息
    for msv_first, msv_last, msv_staff in msv_query_list_dim_human(cid=company.cid, table_name='company_staff', source='XA'):
        if len(msv_staff) == 0:  # 空结果 维度为空
            continue
        logger.debug(f'{to_datetime(msv_first)} {msv_staff}')
        # 更新当前主要人员或者增加一个版本主要人员
        while base_idx < len(msv_base_list) and msv_base_list[base_idx]['msv_first'] - 1000 > msv_first * 1000:
            base_idx += 1
        if base_idx == len(msv_base_list):
            logger.info(f'staff fail to match base, break it')
            break
        logger.debug(f'{base_idx} {msv_base_list[base_idx]["msv_last"]} {msv_first}')
        if len(msv_staff_list) == 0:
            msv_staff_list.append([msv_base_list[base_idx]['approved_time'], msv_staff])
        elif msv_base_list[base_idx]['approved_time'] == msv_staff_list[-1][0]:
            # if '未知' in json.dumps(msv_staff_list[-1][1], ensure_ascii=False) or len(json.dumps(msv_staff_list[-1][1], ensure_ascii=False)) < len(json.dumps(msv_staff, ensure_ascii=False)):
            if '未知' in json.dumps(msv_staff_list[-1][1], ensure_ascii=False):
                msv_staff_list[-1][1] = msv_staff
        else:
            msv_staff_list.append([msv_base_list[base_idx]['approved_time'], msv_staff])
        # logger.debug(f'msv_staff_list={msv_staff_list}')
    for item in msv_staff_list:
        logger.debug(f'msv_staff={item[0]} {(item[1])}')

    for cur_id in range(len(msv_staff_list) - 1):
        msv_staff_cur, msv_staff_lst = msv_staff_list[cur_id], msv_staff_list[cur_id + 1]
        if any(x['staff_position'] == '未知' for x in (msv_staff_cur[1] + msv_staff_lst[1])):
            logger.debug('staff position 未知')
            continue

        seen = False
        for change_info_item in change_info_lst:
            if to_date(change_info_item.change_time) != to_date(msv_staff_cur[0]):
                continue
            # if all(x['staff_name'] in change_info_item.content_after for x in msv_staff_cur[1]):
            if re.search(r'高级管理人员|董事|经理|监事', change_info_item.change_item):
                seen = True
        if seen:
            logger.debug(f'seen {msv_staff_lst} -> {msv_staff_cur}')
            continue
        msv_staff_cur_dic, msv_staff_lst_dic = {}, {}
        for item in msv_staff_cur[1]:
            if item['staff_name'] not in msv_staff_cur_dic:
                msv_staff_cur_dic[item['staff_name']] = {
                    'positions': [],
                    'staff_id': item['staff_id'],
                }
            for pos in item['staff_position'].split('兼'):
                if pos not in msv_staff_cur_dic[item['staff_name']]['positions']:
                    msv_staff_cur_dic[item['staff_name']]['positions'].append(pos)
        for item in msv_staff_lst[1]:
            if item['staff_name'] not in msv_staff_lst_dic:
                msv_staff_lst_dic[item['staff_name']] = {
                    'positions': [],
                    'staff_id': item['staff_id'],
                }
            for pos in item['staff_position'].split('兼'):
                if pos not in msv_staff_lst_dic[item['staff_name']]['positions']:
                    msv_staff_lst_dic[item['staff_name']]['positions'].append(pos)

        def cmp(st1, st2):
            st1_tmp, st2_tmp = st1.copy(), st2.copy()
            st1_tmp.append('财务负责人')
            st2_tmp.append('财务负责人')
            return set(st1_tmp) == set(st2_tmp)

        if all(cmp(
                msv_staff_cur_dic.get(k, {'positions': []})['positions'],
                msv_staff_lst_dic.get(k, {'positions': []})['positions']) for k in (msv_staff_cur_dic | msv_staff_lst_dic).keys()):
            logger.info(f'same staff {msv_staff_cur_dic}')
            continue

        # 最新主要人员名单一致性对比
        if to_date(msv_staff_cur[0]) == company.approved_date:
            newest_staff_id_set = set(x.staff_id for x in company_staff_dao.get_many(field='company_id', value=company.cid))
            msv_staff_cur_id_set = set(x['staff_id'] for x in msv_staff_cur_dic.values())
            logger.debug(f'newest_staff_id_set={newest_staff_id_set}')
            logger.debug(f'msv_staff_cur_id_set={msv_staff_cur_id_set}')
            if newest_staff_id_set != msv_staff_cur_id_set:
                logger.warning('newest staff list diff')
                continue

        tc.count_staff = 1
        tc.count_num += 1

        appr_date = msv_staff_cur[0]
        change_item = '高级管理人员备案（董事、监事、经理等）'

        # lst = ' '.join((k + staff_positions_merge(v)) for k, v in msv_staff_lst_dic.items())
        # cur = ' '.join((k + staff_positions_merge(v)) for k, v in msv_staff_cur_dic.items())
        lst = list(v|{'staff_name': k} for k, v in msv_staff_lst_dic.items())
        cur = list(v|{'staff_name': k} for k, v in msv_staff_cur_dic.items())
        logger.info(f'>>> {appr_date} {change_item} {lst} -> {cur}')
        # tc.output.append([appr_date, change_item, lst, cur])

        ga_change_info = GeneratedApprChangeInfo(
            company_id=company.cid,
            change_date=appr_date,
            change_item=change_item,
            change_before=json.dumps(lst, ensure_ascii=False),
            change_after=json.dumps(cur, ensure_ascii=False),
        )
        if ga_change_info:
            tc.output.append(ga_change_info)


def parse_amount(s):
    s = s.replace('万元', '').replace('万', '').replace('人民币', '')
    if not re.fullmatch(r'\d+\.?\d*', s):
        return 0
    return float(s)


def get_subscribe_amount(x):
    capital = x['capital']
    amount = 0
    for item in json.loads(capital):
        amount += parse_amount(item['amomon'])
    return amount


def adjust_investor_info(total: float, msv_investor: dict):

    if total < 1e-6:
        for sh_id, sh_info in msv_investor.items():
            sh_name, investor_type, subscribe = sh_info
            if abs(subscribe) < 1e-6:
                return False
            total += subscribe
    logger.debug(f'total={total} msv_investor={msv_investor}')

    miss_capital_key, left = None, total
    for sh_id, sh_info in msv_investor.items():
        sh_name, investor_type, subscribe = sh_info
        if abs(subscribe) < 1e-6:
            if miss_capital_key:
                return False
            miss_capital_key = sh_id
        else:
            left -= subscribe
            sh_info.append(subscribe/total)
            if left < -1e-6:
                return False
    if miss_capital_key:
        msv_investor[miss_capital_key][2] = left
        msv_investor[miss_capital_key].append(left/total)

    return True


def newest_investor(company: Company):
    cg: CompanyGraph = company_graph_dao.get_by_cid(company.cid)
    if not cg:
        return set()
    shareholder_id_set = set()
    for item in equity_ratio_dao.get_many(value=cg.cgid, field='company_graph_id'):
        item: EquityRatio
        if item.source == 100:
            continue
        if item.shareholder_type == 2:
            company_graph: CompanyGraph = company_graph_dao.get(value=item.shareholder_graph_id, field='graph_id')
            if company_graph:
                shareholder_id_set.add(company_graph.cid)
        else:
            human_graph: HumanGraph = human_graph_dao.get(value=item.shareholder_graph_id, field='graph_id')
            if human_graph:
                shareholder_id_set.add(human_graph.hid)
    return shareholder_id_set


def process_investor(tc: TaskContext, company: Company, change_info_lst: List[CompanyChangeInfo]):
    msv_base_list: List[dict] = tc.context['msv_base_list']

    msv_investor_list = []  # 相同核准日期最多选一个，选数据最丰富的那个，且把核准日期放到item中
    base_idx = 0  # 当前对齐照面信息 只有msv_first有效，是当前核准日期的首次抓取时间
    for msv_first, msv_last, msv_investor in msv_query_list_dim_human(cid=company.cid, table_name='company_investor', source='gsxt_page'):
        if len(msv_investor) == 0:  # 空结果 维度为空
            continue
        logger.debug(f'inv {to_datetime(msv_first)}')
        # 更新当前股东 对应最小的核准日期
        while base_idx < len(msv_base_list) and msv_base_list[base_idx]['msv_first'] - 1000 > msv_first * 1000:
            base_idx += 1
        if base_idx == len(msv_base_list):
            logger.info(f'investor fail to match base, break it')
            break
        logger.debug(f'{base_idx} {to_datetime(msv_base_list[base_idx]["msv_first"])} {to_datetime(msv_base_list[base_idx]["msv_last"])}')
        if len(msv_investor_list) == 0:
            msv_investor_list.append([msv_base_list[base_idx]['approved_time'], msv_investor, parse_amount(msv_base_list[base_idx]['reg_capital'])])
        elif msv_base_list[base_idx]['approved_time'] == msv_investor_list[-1][0]:
            if len(json.dumps(msv_investor_list[-1][1], ensure_ascii=False)) < len(json.dumps(msv_investor, ensure_ascii=False)):
                msv_investor_list[-1][1] = msv_investor
        else:
            msv_investor_list.append([msv_base_list[base_idx]['approved_time'], msv_investor, parse_amount(msv_base_list[base_idx]['reg_capital'])])
    for item in msv_investor_list:
        logger.debug(f'msv_investor={item[0]} {(item[1])}')

    for cur_id in range(len(msv_investor_list) - 1):
        msv_investor_cur, msv_investor_lst = msv_investor_list[cur_id], msv_investor_list[cur_id + 1]
        reg_capital_cur, reg_capital_lst = msv_investor_list[cur_id][2], msv_investor_list[cur_id + 1][2],
        seen = False
        for change_info_item in change_info_lst:
            if to_date(change_info_item.change_time) != to_date(msv_investor_cur[0]):
                continue
            if re.search(r'投资人|股东', change_info_item.change_item):
                seen = True
        if seen:
            logger.debug(f'seen {msv_investor_cur} -> {msv_investor_lst}')
            continue

        msv_investor_cur_dic = dict((x['investor_id'], [x['investor_name'], x['investor_type'], get_subscribe_amount(x)]) for x in msv_investor_cur[1])
        msv_investor_lst_dic = dict((x['investor_id'], [x['investor_name'], x['investor_type'], get_subscribe_amount(x)]) for x in msv_investor_lst[1])

        if not adjust_investor_info(reg_capital_cur, msv_investor_cur_dic):
            logger.info(f'fail to adjust_investor_info {reg_capital_cur} {msv_investor_cur_dic}')
            return
        if not adjust_investor_info(reg_capital_lst, msv_investor_lst_dic):
            logger.info(f'fail to adjust_investor_info {reg_capital_lst} {msv_investor_lst_dic}')
            return

        # if dict((k, v[1]) for k, v in msv_investor_cur_dic.items()) == dict((k, v[1]) for k, v in msv_investor_lst_dic.items()):
        if set(msv_investor_cur_dic.keys()) == set(msv_investor_lst_dic.keys()):
            logger.info(f'same investors {msv_investor_cur_dic}')
            continue

        # 最新股东名单一致性对比
        if to_date(msv_investor_list[cur_id][0]) == company.approved_date:
            newest_investor_id_set = newest_investor(company)
            msv_investor_cur_id_set = set(msv_investor_cur_dic.keys())
            logger.debug(f'newest_investor_id_set={newest_investor_id_set}')
            logger.debug(f'msv_investor_cur_id_set={msv_investor_cur_id_set}')
            if newest_investor_id_set != msv_investor_cur_id_set:
                logger.warning('newest investor list diff')
                continue

        logger.debug(f'{msv_investor_cur_dic}')
        logger.debug(f'{msv_investor_lst_dic}')

        tc.count_num += 1
        tc.count_investor = 1

        appr_date = msv_investor_list[cur_id][0]
        change_item = '投资人变更（包括出资额、出资方式、出资日期、投资人名称等）'
        # lst = ' '.join(f'{v[0]}（出资额:{p_float(v[1])}万元;出资比例:{p_float(v[2]*100)}%）' for k, v in msv_investor_lst_dic.items())
        # cur = ' '.join(f'{v[0]}（出资额:{p_float(v[1])}万元;出资比例:{p_float(v[2]*100)}%）' for k, v in msv_investor_cur_dic.items())
        # lst = dict((v[0], f'出资额:{p_float(v[1])}万元;出资比例:{p_float(v[2] * 100)}%') for k, v in msv_investor_lst_dic.items())
        # cur = dict((v[0], f'出资额:{p_float(v[1])}万元;出资比例:{p_float(v[2] * 100)}%') for k, v in msv_investor_cur_dic.items())
        lst = list((
                    {
                        'investor_id': k,
                        'investor_name': v[0],
                        'investor_type': v[1],
                        'subscribe_str': f'{p_float(v[2])}万元',
                        'subscribe_percent_str': f'{p_float(v[3] * 100)}%',
                    }) for k, v in msv_investor_lst_dic.items())
        cur = list((
                    {
                        'investor_id': k,
                        'investor_name': v[0],
                        'investor_type': v[1],
                        'subscribe_str': f'{p_float(v[2])}万元',
                        'subscribe_percent_str': f'{p_float(v[3] * 100)}%',
                    }) for k, v in msv_investor_cur_dic.items())
        logger.info(f'>>> {appr_date} {change_item} {lst} -> {cur}')
        # tc.output.append([appr_date, change_item, lst, cur])

        ga_change_info = GeneratedApprChangeInfo(
            company_id=company.cid,
            change_date=appr_date,
            change_item=change_item,
            change_before=json.dumps(lst, ensure_ascii=False),
            change_after=json.dumps(cur, ensure_ascii=False),
        )
        if ga_change_info:
            tc.output.append(ga_change_info)


def process(tc: TaskContext):
    cid = int(tc.inputs['cid'])
    company: Company = company_dao.get(cid)
    assert company
    credit_code = company.credit_code
    appr_date = company.approved_date
    if not company.approved_date:
        return tc
    # if company.base not in ['sh', 'bj']:
    #     return tc
    if not re.search(r'存续|在营|开业|在册', company.reg_status):
        return tc
    logger.info(f'{credit_code} {tc.inputs["did"]} {cid} {appr_date}')

    # change_info_lst = list(company_change_info_dao.get_many(field='company_id', value=cid))
    #
    # if any(to_date(x.change_time) > company.approved_date for x in change_info_lst):
    #     logger.warning(f'{cid} change_time > approved_date={company.approved_date}')
    #     return tc

    # 通过msv 检查appr_date的核变内容
    process_base(tc, company, change_info_lst)
    process_staff(tc, company, change_info_lst)
    process_investor(tc, company, change_info_lst)

    # ga_change_info_dao.save_by_group(
    #     items=tc.output,
    #     group_fields=['company_id', ],
    #     group_values=[cid, ],
    #     key_fields=['change_date', 'change_item'],
    # )

    return tc


def callback_fn(f: Future):
    tc: TaskContext = fs[f]
    del fs[f]
    try:
        f.result()
    except Exception as e:
        logger.info(f'error process {tc}  e={e} {get_stack_info()}')
        tc.code = ProcessCode.EXCEPTION
    tc.ts = int(time.time() - tc.ts)
    if tc.code not in [ProcessCode.INITIAL, ProcessCode.EXCLUDE]:
        logger.info(f'TASK {tc.__dict__}')
    process_code_stat[tc.code] += 1

    process_code_stat['count_base_info'] += tc.count_base_info
    process_code_stat['count_staff'] += tc.count_staff
    process_code_stat['count_investor'] += tc.count_investor
    process_code_stat['count_num'] += tc.count_num
    process_code_stat['count_any'] += (tc.count_base_info or tc.count_staff or tc.count_investor)

    if sum(process_code_stat.values()) % 1000 == 0:
        logger.info(f'process_code_stat {process_code_stat}')
        # process_code_stat.clear()

