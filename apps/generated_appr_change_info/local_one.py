# encoding=utf8
import re

from libs.log2 import setup_logger
from dao.company import company_dao
from dao.company import CompanyGraphDao, CompanyGraph
from process import process, TaskContext

company_graph_dao = CompanyGraphDao()


logger = setup_logger(debug=True)
# process(TaskContext(inputs={'did': 0, 'cid': 2445031521, }))  # 清控银杏南通创业投资基金合伙企业（有限合伙）
# process(TaskContext(inputs={'did': 0, 'company_2.cid': company_dao.get(value='上海和司生物科技有限公司', field='name').cid, }))
# process(TaskContext(inputs={'did': 0, 'company_2.cid': company_dao.get(value='上海和司生物科技有限公司', field='name').cid, }))  # tyc少主要人员变更，2022年前的数据
# process(TaskContext(inputs={'did': 0, 'company_2.cid': company_dao.get(value='云南冠业投资发展有限公司', field='name').cid, }))  # qcc 少一个企业经营范围变更
# process(TaskContext(inputs={'did': 0, 'company_2.cid': company_dao.get(value='云南楠梦建筑工程有限公司', field='name').cid, }))  # qcc少一个登记机关变更
# process(TaskContext(inputs={'did': 0, 'company_2.cid': company_dao.get(value='仰山（上海）高分子化工有限公司', field='name').cid, }))  # 去掉仅财务负责人变更
# process(TaskContext(inputs={'did': 0, 'company_2.cid': company_dao.get(value='北京万达中天工程材料有限公司', field='name').cid, }))  # 股东不变的情况下不出结果 SAME
# process(TaskContext(inputs={'did': 0, 'company_2.cid': company_dao.get(value='北京中慧恒通商贸有限公司', field='name').cid, }))  # 我们少一次抓取导致数据错
# process(TaskContext(inputs={'did': 0, 'company_2.cid': company_dao.get(value='北京中景宏远建筑劳务有限公司', field='name').cid, }))  # qcc少一个股东变更
# process(TaskContext(inputs={'did': 0, 'company_2.cid': 3963535, }))  # 北京金堤科技有限公司
# process(TaskContext(inputs={'did': 0, 'company_2.cid': 2373997000, }))  # 有非最新版本的变更记录
#
# with open('test_gid.csv', 'r') as f:
#     for xid, x in enumerate(f):
#         x = x.strip()
#         if re.match(r'\d+', x):
#             o: CompanyGraph = company_graph_dao.get(value=int(x), field='graph_id')
#             if not o:
#                 continue
#             tc = process(TaskContext(inputs={'did': 0, 'cid': o.cid, }))
#             logger.info(f'{xid} OUTPUT={tc.output}')

# tc = process(TaskContext(inputs={'did': 0, 'cid': company_dao.get(value='北京少年攻略文化传媒有限公司', field='name').cid, }))
tc = process(TaskContext(inputs={'did': 0, 'cid': 2462137033, }))
logger.info(f'OUTPUT={tc.output}')
