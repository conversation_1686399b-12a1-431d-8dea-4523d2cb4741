# encoding=utf8
import argparse
from libs.log2 import setup_logger
from libs.concurrent import BoundedExecutor
from libs.env import ConstantProps
from apps.generated_appr_change_info.process import process, fs, TaskContext, callback_fn
from clients.redis.redis_queue import RedisQueue

logger = setup_logger(debug=False)

redis_queue = RedisQueue(
    name='generated_appr_change_info:input',
    max_length=40000,
    **ConstantProps.PROPS_GS_REDIS_ONLINE,
    db=3,
)


def main():
    ap = argparse.ArgumentParser(description='main_realtime.py')
    ap.add_argument('--worker-num', type=int, default=8)
    ap_args = ap.parse_args()
    logger.info(f'ap_args={ap_args}')

    with BoundedExecutor(max_workers=ap_args.worker_num, thread_name_prefix='worker') as worker_pool:
        for did, s in enumerate(redis_queue.generate()):
            tc = TaskContext(inputs={'did': did, 'cid': int(s)})
            f = worker_pool.submit(process, tc)
            f.add_done_callback(callback_fn)
            fs[f] = tc


if __name__ == '__main__':
    main()
