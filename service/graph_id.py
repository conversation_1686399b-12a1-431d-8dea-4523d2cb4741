# encoding=utf8

from logging import getLogger
from dao.company import CompanyGraphDao, CompanyGraph
from dao.human import HumanGraph, HumanGraphDao
from gslib.gs_enum import EntityType

logger = getLogger(__file__)


class GraphIdService(object):
    def __init__(self):
        self.company_graph_dao = CompanyGraphDao()
        self.human_graph_dao = HumanGraphDao()

    def get_graph_id(self, entity_type: EntityType, raw_id: int) -> int:
        """
        :param entity_type: EntityType
        :param raw_id: cid or hid
        :return: graph_id or 0 if errors
        """
        if entity_type == EntityType.HUMAN or entity_type == EntityType.TYPE3:
            human_graph: HumanGraph = self.human_graph_dao.get_by_hid(raw_id)
            if human_graph:
                return human_graph.hgid
            return 0
        if entity_type == EntityType.ORG:
            company_graph: CompanyGraph = self.company_graph_dao.get_by_cid(raw_id)
            if company_graph:
                return company_graph.cgid
            return 0
        logger.warning(f'bad entity_type={entity_type}')
        return 0
