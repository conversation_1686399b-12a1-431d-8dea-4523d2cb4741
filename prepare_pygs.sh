
exit 0

sudo yum install sqlite-devel


# 安装Python
cd /home/<USER>/python
scp  -i ~/Documents/work.pem  ~/Downloads/Python-3.9.18.tar.xz work@************:/home/<USER>/python
tar -xf Python-3.9.18.tar.xz
cd /home/<USER>/python/Python-3.9.18
./configure --prefix=/home/<USER>/python/python3.9
make && make install

# 安装pygs
cd /home/<USER>/
<NAME_EMAIL>:data/gs_data/pygs-work-parent.git
cd pygs-work-parent/
ln -s /data/logs/ logs
../python/python3.9/bin/python3 -m venv venv
venv/bin/pip install --upgrade pip -i  https://pypi.tuna.tsinghua.edu.cn/simple
venv/bin/pip install -r requirements.txt  -i  https://pypi.tuna.tsinghua.edu.cn/simple

# 启动supervisor
# 修改 supervisord.conf 端口号
cd /home/<USER>/pygs-work-parent/supervisord_home
mkdir conf.d
export PYTHONPATH=/home/<USER>/pygs-work-parent/
../venv/bin/supervisord  -c supervisord.conf



